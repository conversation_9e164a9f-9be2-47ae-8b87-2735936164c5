Based on my analysis of the codebase, here's a comprehensive list of integration tests for your
  GovTech SaaS marketplace application:

  Authentication & User Management Flow

  1. User Registration & Confirmation
  - Complete vendor registration with account approval workflow
  - Complete agency registration with account approval workflow
  - Email confirmation process (register → receive email → confirm → login)
  - Account invitation acceptance workflow
  - Team invitation acceptance workflow

  2. Authentication Flow
  - Login/logout for different user types (vendor, agency, admin)
  - Password reset workflow (request → email → reset → login)
  - Session management and timeout handling
  - Orphaned user setup (users without accounts)

  Vendor User Flows

  3. Vendor Onboarding & Profile Setup
  - Complete vendor account creation and approval process
  - Profile setup (company info, logo upload, description)
  - Team member invitation and management
  - Account settings management

  4. Product Management Lifecycle
  - Create new product with all components (basic info, categories, features)
  - Add product media (screenshots, videos, logos)
  - Add product content (features, case studies, customer testimonials)
  - Product publishing workflow (draft → review → published)
  - Product editing and updating
  - Product deletion/archiving

  5. Lead Management
  - View incoming leads from marketplace
  - Update lead status (pending → contacted → qualified → closed)
  - Add notes to leads
  - Lead filtering and search

  Agency User Flows

  6. Marketplace Browsing & Discovery
  - Browse marketplace homepage
  - Category-based product filtering
  - Search functionality with various queries
  - Product detail page viewing
  - Company/vendor profile viewing

  7. Interest Expression & Lead Creation
  - Express interest in products (create leads)
  - Fill out lead forms with contact information
  - View submitted interests/leads
  - Track lead status updates

  Admin User Flows

  8. Account Management
  - Review and approve/reject vendor registrations
  - Review and approve/reject agency registrations
  - Manage user accounts and permissions
  - Send account invitations
  - Team member management for accounts

  9. Content Management
  - Product moderation and approval
  - Category management (create, edit, organize)
  - Certification management
  - Product claims review and approval

  10. System Administration
  - Dashboard overview with key metrics
  - Lead management across all vendors
  - System health monitoring
  - User activity monitoring

  Marketplace Integration Flows

  11. End-to-End Lead Generation
  - Agency user browses → finds product → expresses interest → vendor receives lead → vendor
  responds
  - Complete lead lifecycle from creation to closure
  - Email notifications throughout the process

  12. Multi-tenant Data Isolation
  - Verify vendors only see their own products and leads
  - Verify agencies only see their own interests
  - Verify proper data separation between accounts

  API & Search Integration

  13. Search Functionality
  - Real-time search with autocomplete
  - Category filtering with search
  - Search result pagination
  - Advanced search with multiple filters

  14. File Upload & Management
  - Product logo upload and variants
  - Product screenshot upload and management
  - Video upload and compression workflow
  - User profile photo management

  Error Handling & Edge Cases

  15. Error Page Handling
  - 404 error page navigation
  - 500 error page handling
  - Form validation error handling
  - File upload error scenarios

  16. Security & Permissions
  - Unauthorized access attempts
  - Cross-account data access prevention
  - File upload security validation
  - XSS and CSRF protection

  Performance & Scalability

  17. Large Data Set Handling
  - Marketplace with hundreds of products
  - Pagination performance
  - Search performance with large datasets
  - Image optimization and loading

  Email & Communication Workflows

  18. Email Integration Testing
  - Welcome emails for new users
  - Lead notification emails to vendors
  - Account approval/rejection emails
  - Team invitation emails
  - Password reset emails
