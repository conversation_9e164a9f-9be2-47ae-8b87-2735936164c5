# Comprehensive Unit Test Scope for Mail<PERSON>, Views, and Jobs

Based on the codebase analysis, here's a detailed scope for unit testing components not fully covered by integration tests.

## Mailer Unit Tests

### UserMailer Tests
- **welcome(user, account)**
  - Email addresses and subject line generation
  - Conditional confirmation URL logic (with/without token)
  - Account type-specific dashboard URL generation
  - Template variable assignment and rendering
  - Email delivery with correct headers

- **account_approved(user)**
  - Account type detection and URL routing logic
  - Default URL fallback for unknown account types
  - Subject line customization
  - Template variable assignment

- **account_rejected(user, reason)**
  - Optional reason parameter handling
  - Contact email insertion
  - Subject line consistency
  - Template rendering with/without reason

- **password_reset(user, token)**
  - Token URL generation and validation
  - Security headers and email delivery
  - Template variable assignment

### LeadMailer Tests
- **new_lead_notification(lead)**
  - Complex object relationship loading (lead → product → accounts)
  - Vendor user identification logic
  - Subject line with product name interpolation
  - Dashboard URL generation

- **lead_status_update(lead)**
  - Agency user identification
  - Status change notification logic
  - URL generation for interests page

- **lead_response(lead, message)**
  - Message parameter handling
  - Vendor company name extraction
  - Response email formatting

### AdminMailer Tests
- **new_user_signup(user)**
  - Admin email list generation
  - URL construction for admin panels
  - User information formatting

- **new_account_pending(account)**
  - Account type interpolation in subject
  - Admin URL generation with parameters

- **system_alert(subject, message, details)**
  - Emergency notification formatting
  - Details hash handling
  - Timestamp formatting
  - Subject line prefixing

- **weekly_report(start_date, end_date)**
  - Date range formatting
  - Statistics calculation integration
  - Report URL generation

- **Private Method Tests**
  - `admin_emails` - Super admin user query
  - `super_admin_emails` - Duplicate logic verification
  - `calculate_weekly_stats` - Complex aggregation queries

### AccountInvitationMailer Tests
- Email delivery and template rendering
- Invitation token handling
- Account-specific messaging

## Job Unit Tests

### VideoCompressionJob Tests
- **perform(model_type, model_id, attachment_name)**
  - Model loading and error handling
  - Attachment validation logic
  - Video file type detection
  - Temporary file creation and cleanup
  - FFMPEG integration and error handling
  - File replacement logic
  - Logging and error reporting

- **Private Method Tests**
  - `video_file?(attachment)` - Content type validation
  - `create_temp_file(attachment)` - File creation and binary mode
  - `create_output_path(input_path)` - Path generation logic
  - `compress_video(input_path, output_path)` - FFMPEG options and execution
  - `replace_attachment` - Filename generation and attachment replacement
  - `cleanup_temp_files` - File deletion and error handling

- **Error Scenarios**
  - Missing FFMPEG dependency
  - Corrupted video files
  - Disk space issues
  - Permission errors
  - Network timeouts

### AnalyticsJob Tests
- **perform(analytics_data)**
  - Product and user loading
  - User agent parsing integration
  - IP geolocation integration
  - Metadata merging logic
  - ProductAnalytic record creation
  - Duplicate unique_view handling
  - Error logging and re-raising

- **Error Scenarios**
  - Invalid product_id
  - Invalid user_id
  - Malformed user_agent
  - Geolocation service failures
  - Database constraint violations
  - Network connectivity issues

## View Helper Unit Tests

### SeoHelper Tests
- **page_title(title)**
  - Title presence/absence handling
  - Base title concatenation
  - Default tagline generation

- **meta_description(description)**
  - Custom description handling
  - Default description fallback
  - Character limits and truncation

- **meta_keywords(keywords)**
  - Array and string keyword handling
  - Base keywords merging
  - Deduplication logic

- **canonical_url(url)**
  - Custom URL handling
  - Request URL fallback
  - URL validation

- **og_image(image_url)**
  - Custom image URL handling
  - Default image fallback
  - Asset URL generation

- **structured_data_organization**
  - JSON-LD schema generation
  - Contact point formatting
  - Social media links handling

- **structured_data_product(product)**
  - Product information extraction
  - Company association handling
  - Description HTML stripping and truncation
  - URL generation
  - Null product handling

- **structured_data_marketplace**
  - Search action schema
  - URL template generation
  - Query parameter handling

- **render_structured_data(data)**
  - JSON serialization
  - HTML safety handling
  - Script tag generation

- **breadcrumb_schema(breadcrumbs)**
  - Array iteration and indexing
  - List item schema generation
  - Position numbering

### ApplicationHelper Tests
- Pagy integration testing
- Frontend helper method availability

## View Component Tests

### Key Views Requiring Unit Tests

#### Shared Partials
- **_delete_resource.html.erb**
  - Modal generation with different resource types
  - JavaScript controller integration
  - Confirmation message customization

- **_pagination.html.erb**
  - Pagy helper integration
  - Page number display logic
  - Navigation link generation

- **_flash_messages.html.erb**
  - Message type handling (success, error, notice)
  - Alert styling and dismissal
  - HTML safety for flash content

- **_dashboard_sidebar_nav.html.erb**
  - User role-based navigation
  - Active state detection
  - Permission-based menu items

#### Form Components
- **vendors/products/_form.html.erb**
  - Complex nested form handling
  - File upload integration
  - Validation error display
  - Category selection logic

#### Email Templates
- All mailer templates for:
  - Variable interpolation
  - Conditional content rendering
  - HTML/text formatting
  - Link generation

## Test Infrastructure Requirements

### Shared Test Utilities
- **Mailer Test Helpers**
  - Email queue inspection
  - Template rendering verification
  - Attachment testing utilities

- **Job Test Helpers**
  - Background job queue testing
  - Mock external service calls
  - File system stubbing

- **View Test Helpers**
  - Helper method isolation
  - Request context mocking
  - Asset pipeline stubbing

### Mock and Stub Requirements
- External service integrations (FFMPEG, geolocation)
- File system operations
- Email delivery
- User agent parsing
- Asset pipeline helpers

### Test Data Requirements
- Sample video files for compression testing
- User agent strings for parsing tests
- IP addresses for geolocation testing
- Complex object hierarchies for mailer tests

## Priority Levels

### High Priority
1. Mailer delivery and template rendering
2. Job error handling and retry logic
3. SEO helper data generation
4. Critical shared partials

### Medium Priority
1. Complex form components
2. Analytics job data processing
3. Advanced SEO schema generation
4. Dashboard navigation components

### Low Priority
1. Email template edge cases
2. Video compression optimization parameters
3. Advanced view helper edge cases
4. Static content components

This comprehensive scope ensures that unit tests complement your existing integration tests by focusing on component-level logic, error handling, and edge cases that integration tests might miss.