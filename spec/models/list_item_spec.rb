# == Schema Information
#
# Table name: list_items
#
#  id         :integer          not null, primary key
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  list_id    :integer          not null
#  product_id :integer          not null
#
# Indexes
#
#  index_list_items_on_list_id                 (list_id)
#  index_list_items_on_list_id_and_product_id  (list_id,product_id) UNIQUE
#  index_list_items_on_product_id              (product_id)
#
# Foreign Keys
#
#  list_id     (list_id => lists.id)
#  product_id  (product_id => products.id)
#
require 'rails_helper'

RSpec.describe ListItem, type: :model do
  pending "add some examples to (or delete) #{__FILE__}"
end
