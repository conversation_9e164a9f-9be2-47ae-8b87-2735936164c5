# == Schema Information
#
# Table name: product_contracts
#
#  id          :integer          not null, primary key
#  description :text
#  name        :string           not null
#  position    :integer          default(0)
#  published   :boolean          default(FALSE), not null
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  product_id  :integer          not null
#
# Indexes
#
#  index_product_contracts_on_product_id               (product_id)
#  index_product_contracts_on_product_id_and_position  (product_id,position)
#
# Foreign Keys
#
#  product_id  (product_id => products.id)
#
require 'rails_helper'

RSpec.describe ProductContract, type: :model do
  pending "add some examples to (or delete) #{__FILE__}"
end
