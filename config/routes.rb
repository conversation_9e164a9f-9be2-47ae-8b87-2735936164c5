Rails.application.routes.draw do
  # Direct signup with auto-approval (obscure URL)
  get 'priority-vendor-onboarding', to: 'priority_onboarding_access#new', as: :direct_signup
  post 'priority-vendor-onboarding', to: 'priority_onboarding_access#create'
  mount Mailbin::Engine => :mailbin if Rails.env.development?
  
  get "registrations/new"
  get "registrations/create"
  get "pages/landing"
  get "pages/terms_of_service"
  get "pages/privacy_policy"
  get "about", to: "pages#about"
  get "pending", to: "pages#pending"
  # Authentication
  resource :session
  resources :passwords, param: :token
  
  # Orphaned users (users not part of any account)
  resource :orphaned_users, only: [:show], path: :setup


  # Vendor dashboard
  namespace :vendors do
    resources :products do
      member do
        get :analytics
        get :features
        get :customers
        get :videos
        get :screenshots
        get :case_studies
        get :content
        get :contracts
        get :visibility
        get :manage_featured_video
        patch :update_featured_video
        delete :remove_featured_video
      end
      resources :product_videos do
        member do
          patch :update_position
        end
      end
      resources :product_features do
        member do
          patch :update_position
        end
      end
      resources :case_studies do
        member do
          patch :update_position
        end
      end
      resources :product_contents do
        member do
          patch :update_position
        end
      end
      resources :product_screenshots do
        member do
          patch :update_position
        end
      end
      resources :product_customers do
        member do
          patch :update_position
        end
      end
      resources :product_contracts do
        member do
          patch :update_position
        end
      end
    end
    resources :leads, only: [:index, :show, :update]
    root to: 'overview#index'
  end

  # Agency dashboard
  namespace :agencies do
    get 'dashboard', to: 'dashboard#index'
    
    resources :products, only: [] do
      resources :interests, only: [:create]
    end
    
    resources :interests, only: [:index]
    resources :lists do
      resources :list_items, only: [:create, :destroy], path: :items
    end
    root to: 'dashboard#index'
  end

  # Admin
  namespace :admin do
    resources :accounts do
      member do
        post :approve
        post :reject
        
        # Team management routes
        get :team_members
        get 'team_members/new', to: 'accounts#new_team_member', as: :new_team_member
        post 'team_members', to: 'accounts#create_team_member', as: :create_team_member
        get 'team_members/:team_member_id/edit', to: 'accounts#edit_team_member', as: :edit_team_member
        patch 'team_members/:team_member_id', to: 'accounts#update_team_member', as: :update_team_member
        delete 'team_members/:team_member_id', to: 'accounts#destroy_team_member', as: :destroy_team_member
        post 'team_members/add_existing', to: 'accounts#add_existing_user', as: :add_existing_user
        
        # Team invitation routes
        get 'team_invitations/new', to: 'accounts#new_team_invitation', as: :new_team_invitation
        post 'team_invitations', to: 'accounts#create_team_invitation', as: :create_team_invitation
        delete 'team_invitations/:invitation_id', to: 'accounts#destroy_team_invitation', as: :destroy_team_invitation
      end
    end
    
    # Account invitations
    resources :account_invitations, except: [:show, :edit, :update] do
      member do
        patch :resend
      end
    end
    
    resources :products do
      resources :product_videos do
        member do
          patch :update_position
        end
      end
      resources :product_features do
        member do
          patch :update_position
        end
      end
      resources :case_studies do
        member do
          patch :update_position
        end
      end
      resources :product_contents do
        member do
          patch :update_position
        end
      end
      resources :product_screenshots do
        member do
          patch :update_position
        end
      end
      resources :product_customers do
        member do
          patch :update_position
        end
      end
      resources :product_contracts do
        member do
          patch :update_position
        end
      end
      resources :leads, only: [:index, :show]
    end
    resources :categories
    resources :certifications
    resources :product_claims, only: [:index, :show]
    resources :leads, only: [:index, :show]
    resources :lists, only: [:index, :show, :destroy]
    
    resources :email_previews, only: [:index, :show]
    resource :health, controller: 'health', only: [:show]
    root to: 'overview#index'
  end

  # Mission Control Jobs (Super Admin Only)
  mount MissionControl::Jobs::Engine, at: "/admin/jobs"

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # SEO
  get "sitemap.xml" => "sitemaps#index", as: :sitemap, defaults: { format: 'xml' }
  get "robots.txt" => "sitemaps#robots", as: :robots, defaults: { format: 'txt' }

  # Error pages
  get "errors/not_found", to: "errors#not_found"
  get "errors/unprocessable_entity", to: "errors#unprocessable_entity"
  get "errors/internal_server_error", to: "errors#internal_server_error"
  
  # Standard Rails error handling paths
  get "/404", to: "errors#not_found"
  get "/422", to: "errors#unprocessable_entity"
  get "/500", to: "errors#internal_server_error"

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

  # User registration
  resources :registrations, only: [:new, :create]
  
  # Account confirmation
  get 'confirm/:token', to: 'account_confirmations#show', as: :confirm_account
  get 'resend_confirmation', to: 'account_confirmations#new', as: :new_account_confirmation
  post 'resend_confirmation', to: 'account_confirmations#create', as: :account_confirmations
  
  # Team invitation acceptance
  get 'invitations/:token', to: 'invitation_acceptances#show', as: :invitation
  get 'invitations/:token/accept', to: 'invitation_acceptances#show', as: :accept_invitation
  post 'invitations/:token/accept', to: 'invitation_acceptances#accept'
  get 'invitations/:token/decline', to: 'invitation_acceptances#show', as: :decline_invitation  
  post 'invitations/:token/decline', to: 'invitation_acceptances#decline'
  
  # Account invitation acceptance
  get 'account-invitations/:token', to: 'account_invitation_acceptances#show', as: :accept_account_invitation
  post 'account-invitations/:token', to: 'account_invitation_acceptances#create'
  
  # User account (top-level)
  resource :account, only: [:show, :update] do
    member do
      get :edit_profile
      get :edit_account
      patch :update_profile
      patch :update_account
      patch :update_password
      patch :update_profile_photo
      delete :remove_profile_photo
      
      # Team management actions
      get :team_members
      get 'team_members/:id/edit', to: 'accounts#edit_team_member', as: :edit_team_member
      patch 'team_members/:id', to: 'accounts#update_team_member', as: :update_team_member
      patch 'team_members/:id/role', to: 'accounts#update_team_member_role', as: :update_team_member_role
      delete 'team_members/:id', to: 'accounts#destroy_team_member', as: :destroy_team_member
      get 'team_invitations/new', to: 'accounts#new_team_invitation', as: :new_team_invitation
      post 'team_invitations', to: 'accounts#create_team_invitation', as: :create_team_invitation
      delete 'team_invitations/:id', to: 'accounts#destroy_team_invitation', as: :destroy_team_invitation
    end
  end
  
  # New organized account management routes
  resource :account_settings, only: [:show, :edit, :update]
  
  resource :user_profile, only: [:show, :edit, :update], controller: 'user_profiles' do
    member do
      patch :update_password
      patch :update_profile_photo
      delete :remove_profile_photo
    end
  end
  
  resources :team_management, only: [:index] do
    collection do
      get 'members/:id', to: 'team_management#show_member', as: :show_member
      get 'members/:id/edit', to: 'team_management#edit_member', as: :edit_member
      patch 'members/:id', to: 'team_management#update_member', as: :update_member
      patch 'members/:id/role', to: 'team_management#update_member_role', as: :update_member_role
      delete 'members/:id', to: 'team_management#destroy_member', as: :destroy_member
      get 'invitations/new', to: 'team_management#new_invitation', as: :new_invitation
      post 'invitations', to: 'team_management#create_invitation', as: :create_invitation
      delete 'invitations/:id', to: 'team_management#destroy_invitation', as: :destroy_invitation
    end
  end
  
  
  # Public marketplace (accessible to all users)
  get 'marketplace', to: 'marketplace#index'
  get 'marketplace/search', to: 'marketplace#search', as: :marketplace_search
  get 'api/search', to: 'marketplace#api_search', as: :api_search
  get 'marketplace/products', to: 'marketplace#products', as: :marketplace_products
  get 'marketplace/categories', to: 'marketplace#categories', as: :marketplace_categories
  get 'marketplace/categories/:id', to: 'marketplace#category', as: :marketplace_category
  get 'marketplace/vendors/:id/products', to: 'marketplace#vendor_products', as: :marketplace_vendor_products
  get 'marketplace/products/:id', to: 'marketplace#product', as: :marketplace_product
  post 'marketplace/products/:id/leads', to: 'marketplace#create_lead', as: :marketplace_product_leads
  get 'products/:id/claim', to: 'product_claims#new', as: :new_product_claim
  post 'products/:id/claim', to: 'product_claims#create', as: :product_claims
  get 'marketplace/products/:product_id/videos/:id', to: 'marketplace#product_video', as: :marketplace_product_video
  get 'marketplace/products/:product_id/features/:id', to: 'marketplace#product_feature', as: :marketplace_product_feature
  get 'marketplace/products/:product_id/customers/:id', to: 'marketplace#product_customer', as: :marketplace_product_customer
  get 'marketplace/products/:product_id/case_studies/:id', to: 'marketplace#case_study', as: :marketplace_case_study
  get 'marketplace/products/:product_id/content/:id', to: 'marketplace#product_content', as: :marketplace_product_content
  
  # Content analytics tracking
  post 'marketplace/products/:product_id/track_content_view', to: 'marketplace#track_content_view', as: :track_content_view
  post 'marketplace/products/:product_id/track_content_download', to: 'marketplace#track_content_download', as: :track_content_download
  
  # Video analytics tracking
  post 'marketplace/products/:product_id/track_video_play', to: 'marketplace#track_video_play', as: :track_video_play
  post 'marketplace/products/:product_id/track_video_watch_duration', to: 'marketplace#track_video_watch_duration', as: :track_video_watch_duration
  patch 'marketplace/products/:product_id/update_video_watch_duration', to: 'marketplace#update_video_watch_duration', as: :update_video_watch_duration
  
  # Lists - AJAX endpoints for marketplace
  post 'lists', to: 'agencies/lists#create', as: :create_list
  get 'lists/for_product/:product_id', to: 'agencies/lists#lists_for_product', as: :lists_for_product
  post 'lists/:list_id/items', to: 'agencies/list_items#create', as: :add_to_list

  # Root path
  root to: 'marketplace#index'
end
