Geocoder.configure(
  # Geoapify configuration
  lookup: :geoapify,
  api_key: ENV['GEOAPIFY_API_KEY'],
  
  # Timeout configuration
  timeout: 5,
  
  # Units (metric for kilometers, imperial for miles)
  units: :metric,
  
  # Cache configuration (optional - helps with rate limiting)
  cache: Rails.cache,
  cache_prefix: 'geocoder:',
  
  # Log geocoding requests in development
  logger: Rails.env.development? ? Rails.logger : nil,
  
  # Use HTTPS
  use_https: true,
  
  # Geoapify-specific configuration
  geoapify: {
    api_key: ENV['GEOAPIFY_API_KEY']
  },
  
  # Handle rate limiting gracefully
  always_raise: [
    Geocoder::OverQueryLimitError,
    Geocoder::RequestDenied,
    Geocoder::InvalidRequest,
    Geocoder::InvalidApiKey
  ]
)