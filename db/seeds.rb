# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

# Create Root Categories and Subcategories
root_categories_with_subs = {
  "Community Engagement & Communications" => [
    "Citizen Engagement & Transparency",
    "Public-Private Partnerships & Collaboration",
    "Digital Inclusion & Accessibility",
    "AI Chatbots & Virtual Assistants"
  ],
  "Housing, Urban Development & Planning" => [
    "Affordable Housing Solutions",
    "Land Use & Development Tracking",
    "GIS & Mapping Solutions",
    "AI-Powered Predictive Analytics"
  ],
  "Public Works & Infrastructure" => [
    "Infrastructure & Asset Management",
    "Smart City & Data Solutions",
    "Sustainability, Climate Action & Resilience",
    "Workforce Management & Productivity"
  ],
  "Transportation & Mobility" => [
    "Traffic & Mobility Optimization"
  ],
  "Permitting, Licensing & Code Enforcement" => [
    "Workflow Automation & Digital Transformation",
    "Regulatory Compliance & Reporting",
    "Revenue Management & Cost Efficiency",
    "Cybersecurity & Digital Security"
  ],
  "Finance, Budgeting & Procurement" => [
    "Budget Planning & Forecasting",
    "Grant & Fund Management",
    "Procurement & Contract Management"
  ],
  "Public Safety & Emergency Management" => [
    "Emergency Preparedness & Resilience",
    "Crime & Public Safety Analytics",
    "Cybersecurity & Threat Detection"
  ],
  "Parks, Recreation & Tourism" => [
    "Facility Booking & Event Registration"
  ],
  "Environmental & Sustainability Solutions" => [
    "Carbon Tracking & Climate Resilience",
    "Smart Waste & Recycling Management",
    "Renewable Energy & EV Fleet Management",
    "Water Conservation & Quality Monitoring"
  ],
  "Information Technology & Data Management" => [
    "Cloud Solutions & Secure Data Storage",
    "Data Analytics & Open Data Platforms",
    "AI & Predictive Analytics"
  ]
}

root_categories_with_subs.each do |root_name, subcategory_names|
  root_category = Category.find_or_create_by!(name: root_name, parent_id: nil) do |category|
    category.description = "Solutions and services for #{root_name.downcase}"
  end

  subcategory_names.each do |sub_name|
    Category.find_or_create_by!(name: sub_name, parent: root_category) do |category|
      category.description = "#{sub_name} solutions and services"
    end
  end
end

puts "Created #{Category.count} categories"

# Create Certifications
certifications_data = [
  {
    name: "SOC 2 Type II",
    description: "Service Organization Control 2 Type II compliance demonstrates that an organization has implemented and maintained security controls over time."
  },
  {
    name: "HIPAA Compliant",
    description: "Health Insurance Portability and Accountability Act compliance for handling protected health information."
  },
  {
    name: "FedRAMP Authorized",
    description: "Federal Risk and Authorization Management Program authorization for cloud services used by the federal government."
  },
  {
    name: "ISO 27001",
    description: "International Organization for Standardization 27001 certification for information security management systems."
  },
  {
    name: "PCI DSS",
    description: "Payment Card Industry Data Security Standard compliance for handling credit card information."
  },
  {
    name: "FISMA Compliant",
    description: "Federal Information Security Management Act compliance for federal information systems."
  },
  {
    name: "NIST Cybersecurity Framework",
    description: "National Institute of Standards and Technology cybersecurity framework implementation."
  },
  {
    name: "Section 508 Compliant",
    description: "Section 508 of the Rehabilitation Act compliance for accessibility in federal agencies."
  },
  {
    name: "WCAG 2.1 AA",
    description: "Web Content Accessibility Guidelines 2.1 Level AA compliance for web accessibility."
  },
  {
    name: "GDPR Compliant",
    description: "General Data Protection Regulation compliance for data protection and privacy."
  },
  {
    name: "StateRAMP",
    description: "State Risk and Authorization Management Program for state and local government cloud services."
  },
  {
    name: "CJIS Security Policy",
    description: "Criminal Justice Information Services Security Policy compliance for law enforcement systems."
  }
]

certifications_data.each do |cert_data|
  Certification.find_or_create_by!(name: cert_data[:name]) do |certification|
    certification.description = cert_data[:description]
  end
end

puts "Created #{Certification.count} certifications"

# Create sample users and accounts for development
if Rails.env.development?
  # Create admin user (super admin)
  admin = User.find_or_create_by!(email_address: "<EMAIL>") do |user|
    user.password = "password123"
    user.first_name = "Admin"
    user.last_name = "User"
    user.job_title = "System Administrator"
    user.department = "IT Administration"
    user.super_admin = true
  end
  
  # Ensure admin has super admin privileges if already exists
  admin.update!(super_admin: true) unless admin.super_admin?

  # Create admin account
  admin_account = Account.find_or_create_by!(name: "Platia Admin") do |account|
    account.account_type = "admin"
    account.status = "approved"
    account.approved_at = 1.week.ago
    account.approved_by = admin
    account.owner = admin
    # Set account as confirmed
    account.confirmed_at = 1.week.ago
    account.confirmation_token = SecureRandom.urlsafe_base64(32)
    account.confirmation_sent_at = 2.weeks.ago
  end

  # Create AccountUser relationship for admin
  AccountUser.find_or_create_by!(account: admin_account, user: admin) do |au|
    au.role = "admin"
    au.joined_at = 1.week.ago
  end

  # Create agency user and account
  agency_user = User.find_or_create_by!(email_address: "<EMAIL>") do |user|
    user.password = "password123"
    user.first_name = "Jane"
    user.last_name = "Official"
    user.job_title = "IT Director"
    user.department = "Information Technology"
  end

  agency_account = Account.find_or_create_by!(name: "City of Springfield") do |account|
    account.account_type = "agency"
    account.status = "approved"
    account.approved_at = 1.week.ago
    account.approved_by = admin
    account.owner = agency_user
    # Set account as confirmed
    account.confirmed_at = 1.week.ago
    account.confirmation_token = SecureRandom.urlsafe_base64(32)
    account.confirmation_sent_at = 2.weeks.ago
  end

  AccountUser.find_or_create_by!(account: agency_account, user: agency_user) do |au|
    au.role = "admin"
    au.joined_at = 1.week.ago
  end

  # Product templates for generating diverse products
  product_templates = {
    "Citizen Engagement & Transparency" => [
      { name: "OpenGov Platform", description: "Transparency portal with budget visualization, meeting management, and public comment system", features: ["Budget Visualization", "Meeting Management", "Public Comments", "Transparency Portal"] },
      { name: "CitizenVoice", description: "Survey management with community feedback, analytics dashboard, and mobile app", features: ["Survey Management", "Community Feedback", "Analytics Dashboard", "Mobile App"] },
      { name: "PublicConnect", description: "Two-way communication platform between citizens and government officials", features: ["Direct Messaging", "Public Forums", "Real-time Updates", "Mobile Access"] },
      { name: "CivicFeedback", description: "Advanced feedback collection and analysis for government services", features: ["Sentiment Analysis", "Multi-channel Feedback", "Action Tracking", "Performance Metrics"] },
      { name: "TransparencyHub", description: "Comprehensive transparency and open data platform", features: ["Open Data Portal", "FOIA Management", "Document Publishing", "Search Analytics"] },
      { name: "VoterInfo", description: "Election information and voter education platform", features: ["Candidate Information", "Ballot Tracking", "Polling Locations", "Civic Education"] },
      { name: "CommunityBoard", description: "Digital bulletin board for community announcements and events", features: ["Event Calendar", "Announcements", "Community Groups", "Notification System"] },
      { name: "PublicInsight", description: "Data visualization and insights for public decision making", features: ["Interactive Charts", "Trend Analysis", "Public Dashboards", "Export Tools"] }
    ],
    "Data Analytics & Open Data Platforms" => [
      { name: "DataHub Enterprise", description: "Data integration with ETL pipelines, real-time analytics, and custom dashboards", features: ["ETL Pipelines", "Real-time Analytics", "Custom Dashboards", "Data Integration"] },
      { name: "GovAnalytics", description: "Advanced analytics platform for government data and insights", features: ["Predictive Analytics", "Data Visualization", "Report Generation", "API Access"] },
      { name: "OpenDataPortal", description: "Public data portal with cataloging and API management", features: ["Data Cataloging", "API Management", "Usage Analytics", "Data Quality"] },
      { name: "InsightEngine", description: "Business intelligence and reporting for government agencies", features: ["BI Dashboards", "Automated Reports", "Data Mining", "Performance KPIs"] },
      { name: "DataLake Pro", description: "Scalable data lake solution for government data storage", features: ["Data Lake Storage", "Schema Management", "Data Governance", "Access Control"] },
      { name: "MetricsCenter", description: "Performance measurement and KPI tracking system", features: ["KPI Tracking", "Benchmarking", "Goal Setting", "Progress Reports"] },
      { name: "DataQuality", description: "Data quality management and cleansing platform", features: ["Data Profiling", "Quality Rules", "Cleansing Workflows", "Monitoring"] },
      { name: "AnalyticsWorkbench", description: "Self-service analytics platform for government analysts", features: ["Drag-drop Interface", "Statistical Analysis", "Visualization Tools", "Collaboration"] }
    ],
    "Cybersecurity & Digital Security" => [
      { name: "CyberShield Pro", description: "Advanced threat detection with compliance monitoring, incident response, and security analytics", features: ["Threat Detection", "Compliance Monitoring", "Incident Response", "Security Analytics"] },
      { name: "SecureGov", description: "Comprehensive cybersecurity platform for government networks", features: ["Network Security", "Endpoint Protection", "SIEM Integration", "Threat Intelligence"] },
      { name: "IdentityGuard", description: "Identity and access management solution for government agencies", features: ["Single Sign-On", "Multi-factor Auth", "Role Management", "Audit Trails"] },
      { name: "VulnScanner", description: "Vulnerability assessment and management platform", features: ["Vulnerability Scanning", "Risk Assessment", "Patch Management", "Compliance Reports"] },
      { name: "SecurityOps", description: "Security operations center platform with automated response", features: ["SOC Dashboard", "Automated Response", "Threat Hunting", "Forensics"] },
      { name: "DataGuard", description: "Data loss prevention and encryption management", features: ["Data Classification", "Encryption Management", "DLP Policies", "Access Monitoring"] },
      { name: "ComplianceTracker", description: "Regulation tracking with audit management, risk assessment, and reporting tools", features: ["Regulation Tracking", "Audit Management", "Risk Assessment", "Reporting Tools"] },
      { name: "CyberTraining", description: "Security awareness training and phishing simulation", features: ["Training Modules", "Phishing Simulation", "Progress Tracking", "Certification"] }
    ],
    "Budget Planning & Forecasting" => [
      { name: "BudgetMaster", description: "Comprehensive budget planning and forecasting solution", features: ["Budget Planning", "Forecasting Models", "Scenario Analysis", "Variance Reports"] },
      { name: "FiscalPlanner", description: "Multi-year financial planning and budget management", features: ["Multi-year Planning", "Revenue Forecasting", "Expense Tracking", "Capital Planning"] },
      { name: "CostAnalyzer", description: "Cost analysis and optimization platform", features: ["Cost Analysis", "Optimization Tools", "Benchmark Comparisons", "ROI Calculations"] },
      { name: "RevenuePro", description: "Revenue management and forecasting system", features: ["Revenue Tracking", "Tax Management", "Collection Analytics", "Projection Tools"] },
      { name: "BudgetWorkflow", description: "Collaborative budget development and approval workflow", features: ["Workflow Management", "Approval Chains", "Version Control", "Collaboration Tools"] },
      { name: "FinancialReports", description: "Automated financial reporting and compliance", features: ["Automated Reports", "Compliance Checks", "Custom Templates", "Distribution Tools"] },
      { name: "TreasuryManager", description: "Cash flow and treasury management system", features: ["Cash Flow Analysis", "Investment Tracking", "Liquidity Management", "Risk Assessment"] },
      { name: "GrantTracker", description: "Grant funding and compliance management", features: ["Grant Applications", "Compliance Tracking", "Reporting Tools", "Fund Management"] }
    ],
    "Infrastructure & Asset Management" => [
      { name: "AssetTracker", description: "Comprehensive asset management and maintenance scheduling", features: ["Asset Inventory", "Maintenance Scheduling", "Lifecycle Management", "Cost Tracking"] },
      { name: "FacilityManager", description: "Facility management and space optimization platform", features: ["Space Planning", "Maintenance Requests", "Energy Management", "Occupancy Tracking"] },
      { name: "RoadMaster", description: "Road and infrastructure condition monitoring", features: ["Condition Assessment", "Maintenance Planning", "Work Orders", "Asset Mapping"] },
      { name: "UtilityManager", description: "Utility infrastructure monitoring and management", features: ["System Monitoring", "Outage Management", "Asset Tracking", "Performance Analytics"] },
      { name: "MaintenancePro", description: "Preventive maintenance and work order management", features: ["Work Order Management", "Preventive Maintenance", "Inventory Management", "Mobile Access"] },
      { name: "AssetPlanner", description: "Long-term asset planning and capital budgeting", features: ["Capital Planning", "Asset Lifecycle", "Replacement Scheduling", "Cost Projections"] },
      { name: "InfrastructureGIS", description: "GIS-based infrastructure mapping and analysis", features: ["Asset Mapping", "Spatial Analysis", "Condition Visualization", "Mobile GIS"] },
      { name: "BuildingOps", description: "Building operations and energy management system", features: ["Building Automation", "Energy Monitoring", "Environmental Control", "Predictive Maintenance"] }
    ],
    "Traffic & Mobility Optimization" => [
      { name: "TrafficFlow", description: "Traffic flow optimization and signal management", features: ["Signal Optimization", "Traffic Monitoring", "Incident Detection", "Adaptive Control"] },
      { name: "MobilityHub", description: "Comprehensive mobility and transportation management", features: ["Route Planning", "Transit Integration", "Mobility Analytics", "Multi-modal Support"] },
      { name: "ParkingSmart", description: "Smart parking management and enforcement", features: ["Parking Sensors", "Mobile Payments", "Enforcement Tools", "Analytics Dashboard"] },
      { name: "TransitManager", description: "Public transit scheduling and operations", features: ["Route Scheduling", "Fleet Management", "Passenger Information", "Performance Tracking"] },
      { name: "SmartStreets", description: "Smart street lighting and traffic infrastructure", features: ["LED Lighting", "Traffic Sensors", "Environmental Monitoring", "Remote Control"] },
      { name: "CongestionAnalyzer", description: "Traffic congestion analysis and mitigation", features: ["Congestion Analysis", "Bottleneck Detection", "Mitigation Strategies", "Real-time Monitoring"] },
      { name: "MobilityPlanner", description: "Long-term mobility planning and modeling", features: ["Demand Modeling", "Scenario Planning", "Impact Analysis", "Stakeholder Engagement"] },
      { name: "BikeShareManager", description: "Bike share program management and operations", features: ["Fleet Management", "Station Monitoring", "Usage Analytics", "Maintenance Scheduling"] }
    ],
    "Emergency Preparedness & Resilience" => [
      { name: "EmergencyCommand", description: "Emergency operations center management platform", features: ["Command Center", "Resource Tracking", "Communication Hub", "Situational Awareness"] },
      { name: "DisasterResponse", description: "Disaster response coordination and management", features: ["Incident Management", "Resource Allocation", "Evacuation Planning", "Recovery Tracking"] },
      { name: "AlertSystem", description: "Mass notification and emergency alert system", features: ["Mass Notifications", "Multi-channel Alerts", "Geo-targeting", "Message Templates"] },
      { name: "CrisisComms", description: "Crisis communication and public information management", features: ["Public Information", "Media Relations", "Social Media Monitoring", "Rumor Control"] },
      { name: "ResiliencePlanner", description: "Community resilience planning and assessment", features: ["Risk Assessment", "Vulnerability Analysis", "Mitigation Planning", "Community Engagement"] },
      { name: "FirstResponder", description: "First responder coordination and dispatch", features: ["CAD Integration", "Unit Tracking", "Resource Management", "Communication Tools"] },
      { name: "EvacuationManager", description: "Evacuation planning and route optimization", features: ["Evacuation Routes", "Shelter Management", "Transportation Coordination", "Population Tracking"] },
      { name: "RecoveryTracker", description: "Post-disaster recovery and rebuilding management", features: ["Damage Assessment", "Recovery Planning", "Progress Tracking", "Resource Coordination"] }
    ],
    "Workflow Automation & Digital Transformation" => [
      { name: "PermitPro", description: "Automated permit processing and workflow management", features: ["Permit Applications", "Automated Workflows", "Status Tracking", "Payment Processing"] },
      { name: "DocuFlow", description: "Document management and approval workflows", features: ["Document Management", "Approval Workflows", "Version Control", "Digital Signatures"] },
      { name: "ProcessOptimizer", description: "Business process optimization and automation", features: ["Process Mapping", "Automation Tools", "Performance Analytics", "Continuous Improvement"] },
      { name: "FormBuilder", description: "Dynamic form creation and submission management", features: ["Form Builder", "Conditional Logic", "Integration APIs", "Submission Analytics"] },
      { name: "WorkflowEngine", description: "Enterprise workflow engine and process automation", features: ["Workflow Designer", "Rule Engine", "Task Management", "Integration Hub"] },
      { name: "DigitalServices", description: "Digital service delivery and citizen portal", features: ["Service Catalog", "Online Applications", "Status Tracking", "Payment Gateway"] },
      { name: "TaskManager", description: "Task and project management for government teams", features: ["Task Assignment", "Project Tracking", "Resource Planning", "Progress Reports"] },
      { name: "ApprovalTracker", description: "Approval process management and tracking", features: ["Approval Chains", "Escalation Rules", "Audit Trails", "Performance Metrics"] }
    ]
  }

  # Generate comprehensive company and product data
  company_data = [
    { name: "TechCorp Solutions", headquarters: "San Francisco, CA", website: "https://techcorpsolutions.com", linkedin: "https://linkedin.com/company/techcorp" },
    { name: "CivicTech Solutions", headquarters: "Austin, TX", website: "https://civictechsolutions.com", linkedin: "https://linkedin.com/company/civictech" },
    { name: "GovDataFlow", headquarters: "Washington, DC", website: "https://govdataflow.com", linkedin: "https://linkedin.com/company/govdataflow" },
    { name: "SecureGov Technologies", headquarters: "McLean, VA", website: "https://securegovtech.com", linkedin: "https://linkedin.com/company/securegov" },
    { name: "SmartCity Innovations", headquarters: "Boston, MA", website: "https://smartcityinnovations.com", linkedin: "https://linkedin.com/company/smartcity" },
    { name: "PublicTech Labs", headquarters: "Denver, CO", website: "https://publictechlabs.com", linkedin: "https://linkedin.com/company/publictech" },
    { name: "GovernmentSoft", headquarters: "Seattle, WA", website: "https://governmentsoft.com", linkedin: "https://linkedin.com/company/governmentsoft" },
    { name: "CivicData Systems", headquarters: "Atlanta, GA", website: "https://civicdatasystems.com", linkedin: "https://linkedin.com/company/civicdata" },
    { name: "UrbanTech Solutions", headquarters: "Chicago, IL", website: "https://urbantechsolutions.com", linkedin: "https://linkedin.com/company/urbantech" },
    { name: "PublicSector Pro", headquarters: "Phoenix, AZ", website: "https://publicsectorpro.com", linkedin: "https://linkedin.com/company/publicsector" },
    { name: "GovCloud Systems", headquarters: "Portland, OR", website: "https://govcloudsystems.com", linkedin: "https://linkedin.com/company/govcloud" },
    { name: "CityTech Partners", headquarters: "Miami, FL", website: "https://citytechpartners.com", linkedin: "https://linkedin.com/company/citytech" },
    { name: "Municipal Solutions", headquarters: "Nashville, TN", website: "https://municipalsolutions.com", linkedin: "https://linkedin.com/company/municipal" },
    { name: "DigitalGov Inc", headquarters: "San Diego, CA", website: "https://digitalgovinc.com", linkedin: "https://linkedin.com/company/digitalgov" },
    { name: "StateWare Technologies", headquarters: "Columbus, OH", website: "https://statewaretech.com", linkedin: "https://linkedin.com/company/stateware" },
    { name: "LocalGov Systems", headquarters: "Minneapolis, MN", website: "https://localgovsystems.com", linkedin: "https://linkedin.com/company/localgov" },
    { name: "CitizenFirst Tech", headquarters: "Charlotte, NC", website: "https://citizenfirsttech.com", linkedin: "https://linkedin.com/company/citizenfirst" },
    { name: "GovInnovate", headquarters: "Salt Lake City, UT", website: "https://govinnovate.com", linkedin: "https://linkedin.com/company/govinnovate" },
    { name: "PublicWorks Tech", headquarters: "Kansas City, MO", website: "https://publicworkstech.com", linkedin: "https://linkedin.com/company/publicworks" },
    { name: "CommunityTech", headquarters: "Sacramento, CA", website: "https://communitytech.com", linkedin: "https://linkedin.com/company/communitytech" },
    { name: "ePolicyworks", headquarters: "Richmond, VA", website: "https://epolicyworks.com", linkedin: "https://linkedin.com/company/epolicyworks" },
    { name: "OpenGov Technologies", headquarters: "Raleigh, NC", website: "https://opengovtech.com", linkedin: "https://linkedin.com/company/opengov" },
    { name: "CitizenServices Inc", headquarters: "Tampa, FL", website: "https://citizenservices.com", linkedin: "https://linkedin.com/company/citizenservices" },
    { name: "GovTech Dynamics", headquarters: "Las Vegas, NV", website: "https://govtechdynamics.com", linkedin: "https://linkedin.com/company/govtechdynamics" },
    { name: "MunicipalityPro", headquarters: "Pittsburgh, PA", website: "https://municipalitypro.com", linkedin: "https://linkedin.com/company/municipalitypro" },
    { name: "DigitalCitizen", headquarters: "San Antonio, TX", website: "https://digitalcitizen.com", linkedin: "https://linkedin.com/company/digitalcitizen" },
    { name: "PublicPlatform", headquarters: "Indianapolis, IN", website: "https://publicplatform.com", linkedin: "https://linkedin.com/company/publicplatform" },
    { name: "GovSolutions Hub", headquarters: "Jacksonville, FL", website: "https://govsolutionshub.com", linkedin: "https://linkedin.com/company/govsolutions" },
    { name: "CivicInnovation", headquarters: "Milwaukee, WI", website: "https://civicinnovation.com", linkedin: "https://linkedin.com/company/civicinnovation" },
    { name: "SmartGov Systems", headquarters: "Oklahoma City, OK", website: "https://smartgovsystems.com", linkedin: "https://linkedin.com/company/smartgov" },
    # Additional companies
    { name: "DataTech Government", headquarters: "Louisville, KY", website: "https://datatechgov.com", linkedin: "https://linkedin.com/company/datatech" },
    { name: "NextGen Municipal", headquarters: "Albuquerque, NM", website: "https://nextgenmunicipal.com", linkedin: "https://linkedin.com/company/nextgen" },
    { name: "PublicSphere Tech", headquarters: "Tucson, AZ", website: "https://publicspheretech.com", linkedin: "https://linkedin.com/company/publicsphere" },
    { name: "GovTech Alliance", headquarters: "Virginia Beach, VA", website: "https://govtechalliance.com", linkedin: "https://linkedin.com/company/govtechalliance" },
    { name: "CityWorks Digital", headquarters: "Colorado Springs, CO", website: "https://cityworksdigital.com", linkedin: "https://linkedin.com/company/cityworks" },
    { name: "PublicServices Pro", headquarters: "Mesa, AZ", website: "https://publicservicespro.com", linkedin: "https://linkedin.com/company/publicservices" },
    { name: "SmartMunicipal", headquarters: "Omaha, NE", website: "https://smartmunicipal.com", linkedin: "https://linkedin.com/company/smartmunicipal" },
    { name: "GovData Analytics", headquarters: "Fresno, CA", website: "https://govdataanalytics.com", linkedin: "https://linkedin.com/company/govdataanalytics" },
    { name: "CivicSolutions Hub", headquarters: "Long Beach, CA", website: "https://civicsolutionshub.com", linkedin: "https://linkedin.com/company/civicsolutions" },
    { name: "UrbanSmart Technologies", headquarters: "Oakland, CA", website: "https://urbansmarttech.com", linkedin: "https://linkedin.com/company/urbansmart" },
    { name: "PublicTech Innovations", headquarters: "Minneapolis, MN", website: "https://publictechinnovations.com", linkedin: "https://linkedin.com/company/publictechinnovations" },
    { name: "GovSecure Solutions", headquarters: "Wichita, KS", website: "https://govsecuresolutions.com", linkedin: "https://linkedin.com/company/govsecure" },
    { name: "CityConnect Systems", headquarters: "New Orleans, LA", website: "https://cityconnectsystems.com", linkedin: "https://linkedin.com/company/cityconnect" },
    { name: "DigitalMunicipal", headquarters: "Cleveland, OH", website: "https://digitalmunicipal.com", linkedin: "https://linkedin.com/company/digitalmunicipal" },
    { name: "GovCloud Analytics", headquarters: "Tampa, FL", website: "https://govcloudanalytics.com", linkedin: "https://linkedin.com/company/govcloudanalytics" },
    { name: "SmartCitizen Tech", headquarters: "Honolulu, HI", website: "https://smartcitizentech.com", linkedin: "https://linkedin.com/company/smartcitizen" },
    { name: "PublicAccess Systems", headquarters: "Arlington, TX", website: "https://publicaccesssystems.com", linkedin: "https://linkedin.com/company/publicaccess" },
    { name: "CivicEngine", headquarters: "Bakersfield, CA", website: "https://civicengine.com", linkedin: "https://linkedin.com/company/civicengine" },
    { name: "GovTech Central", headquarters: "Corpus Christi, TX", website: "https://govtechcentral.com", linkedin: "https://linkedin.com/company/govtechcentral" },
    { name: "UrbanGov Solutions", headquarters: "Lexington, KY", website: "https://urbangovsolutions.com", linkedin: "https://linkedin.com/company/urbangov" },
    { name: "PublicFlow Technologies", headquarters: "Stockton, CA", website: "https://publicflowtech.com", linkedin: "https://linkedin.com/company/publicflow" },
    { name: "CityData Pro", headquarters: "Anchorage, AK", website: "https://citydatapro.com", linkedin: "https://linkedin.com/company/citydata" },
    { name: "GovInfrastructure", headquarters: "St. Paul, MN", website: "https://govinfrastructure.com", linkedin: "https://linkedin.com/company/govinfrastructure" },
    { name: "SmartPublic Systems", headquarters: "Newark, NJ", website: "https://smartpublicsystems.com", linkedin: "https://linkedin.com/company/smartpublic" },
    { name: "CivicCloud Technologies", headquarters: "Plano, TX", website: "https://civiccloudtech.com", linkedin: "https://linkedin.com/company/civiccloud" },
    { name: "PublicDigital Hub", headquarters: "Lincoln, NE", website: "https://publicdigitalhub.com", linkedin: "https://linkedin.com/company/publicdigital" },
    { name: "GovAnalytics Pro", headquarters: "Greensboro, NC", website: "https://govanalyticspro.com", linkedin: "https://linkedin.com/company/govanalytics" },
    { name: "UrbanTech Hub", headquarters: "Chandler, AZ", website: "https://urbantechhub.com", linkedin: "https://linkedin.com/company/urbantechhub" },
    { name: "CityTech Solutions", headquarters: "Henderson, NV", website: "https://citytechsolutions.com", linkedin: "https://linkedin.com/company/citytechsolutions" },
    { name: "PublicSmart Technologies", headquarters: "Chula Vista, CA", website: "https://publicsmarttech.com", linkedin: "https://linkedin.com/company/publicsmart" }
  ]

  # Get all categories for product assignment
  all_categories = Category.all.to_a
  all_certifications = Certification.all.to_a

  # Create companies and products
  company_data.each_with_index do |company_info, index|
    
    # Create user for this company
    domain = company_info[:website].gsub('https://', '').gsub('www.', '')
    company_user = User.find_or_create_by!(email_address: "ceo@#{domain}") do |user|
      user.password = "password123"
      user.first_name = "CEO"
      user.last_name = company_info[:name].split.first
      user.job_title = "Chief Executive Officer"
      user.department = "Executive"
    end

    # Create vendor account
    vendor_account = Account.find_or_create_by!(name: company_info[:name]) do |account|
      account.account_type = "vendor"
      account.status = "approved"
      account.approved_at = rand(1..30).days.ago
      account.approved_by = admin
      account.owner = company_user
      account.confirmed_at = rand(1..30).days.ago
      account.confirmation_token = SecureRandom.urlsafe_base64(32)
      account.confirmation_sent_at = rand(31..60).days.ago
      account.headquarters_location = company_info[:headquarters]
      account.linkedin_url = company_info[:linkedin]
    end
    
    # Set the rich text description separately
    if vendor_account.description.blank?
      vendor_account.description = "#{company_info[:name]} specializes in innovative technology solutions for government agencies. We provide cutting-edge software that helps modernize public sector operations and improve citizen services."
      vendor_account.save!
    end

    # Create AccountUser relationship
    AccountUser.find_or_create_by!(account: vendor_account, user: company_user) do |au|
      au.role = "admin"
      au.joined_at = rand(1..30).days.ago
    end

    # Create products for this company
    # First product: always same name as company
    product_name = company_info[:name]
    
    # Select random category and template for the first product
    category = all_categories.sample
    category_name = category.name
    
    # Find product templates for this category or use generic template
    templates = product_templates[category_name] || [
      { name: company_info[:name], description: "Government technology solution", features: ["Feature 1", "Feature 2"] }
    ]
    
    template = templates.sample
    # Override template name with company name
    template[:name] = company_info[:name]
      
    # Create first product (always same name as company)
    product = Product.find_or_create_by!(name: product_name, account: vendor_account) do |p|
      p.published = true
      p.description = template[:description]
      p.website = company_info[:website]
      p.created_at = rand(60.days.ago..Time.current)
    end
    
    # Attach logo to product
    if product.logo.blank?
      logo_files = Dir.glob(Rails.root.join('db', 'seed_files', 'images', 'logo_*.jpg'))
      if logo_files.any?
        product.logo.attach(
          io: File.open(logo_files.sample),
          filename: File.basename(logo_files.sample),
          content_type: 'image/jpeg'
        )
      end
    end

    # Attach featured video to 50% of products
    if product.featured_video.blank? && rand < 0.5
      video_files = Dir.glob(Rails.root.join('db', 'seed_files', 'videos', 'demo_video_*.mp4'))
      if video_files.any?
        product.featured_video.attach(
          io: File.open(video_files.sample),
          filename: File.basename(video_files.sample),
          content_type: 'video/mp4'
        )
      end
    end

    # Create ProductFeature records if they don't exist
    if product.product_features.empty? && template[:features].any?
      template[:features].each_with_index do |feature_name, index|
        product.product_features.create!(
          name: feature_name,
          description: "#{feature_name} - Advanced functionality for #{product.name}",
          position: index + 1,
          published: true
        )
      end
    end

    # Create ProductCustomer records (sample government customers)
    if product.product_customers.empty?
      government_customers = [
        { name: "City of Austin", description: "Using #{product.name} to improve citizen services and streamline operations" },
        { name: "State of California", description: "Implementing #{product.name} across multiple departments for enhanced efficiency" },
        { name: "King County", description: "Leveraging #{product.name} for better data management and reporting" },
        { name: "City of Phoenix", description: "Deployed #{product.name} to modernize legacy systems and improve user experience" },
        { name: "US Department of Agriculture", description: "Utilizing #{product.name} for compliance and regulatory management" },
        { name: "City of Chicago", description: "Running #{product.name} to enhance transparency and public engagement" },
        { name: "State of New York", description: "Using #{product.name} for digital transformation initiatives" },
        { name: "Miami-Dade County", description: "Implementing #{product.name} to improve inter-departmental collaboration" },
        { name: "City of Denver", description: "Adopted #{product.name} for budget management and financial planning" },
        { name: "Texas Education Agency", description: "Deploying #{product.name} for educational data analytics and reporting" },
        { name: "City of Seattle", description: "Using #{product.name} for environmental monitoring and sustainability initiatives" },
        { name: "State of Florida", description: "Implementing #{product.name} for emergency management and disaster response" },
        { name: "Los Angeles County", description: "Leveraging #{product.name} for healthcare administration and patient services" },
        { name: "City of San Francisco", description: "Running #{product.name} for transportation planning and smart city initiatives" },
        { name: "State of Colorado", description: "Using #{product.name} for natural resource management and conservation" }
      ]
      
      # Randomly assign 2-4 customers per product
      num_customers = rand(2..4)
      selected_customers = government_customers.sample(num_customers)
      
      selected_customers.each_with_index do |customer_info, index|
        product.product_customers.create!(
          name: customer_info[:name],
          description: customer_info[:description],
          position: index + 1,
          published: true
        )
      end
    end

    # Assign categories (1-3 categories per product)
    num_categories = rand(1..3)
    selected_categories = all_categories.sample(num_categories)
    
    selected_categories.each do |cat|
      unless product.categories.include?(cat)
        product.categories << cat
      end
    end

    # Assign certifications (0-4 certifications per product)
    num_certifications = rand(0..4)
    selected_certifications = all_certifications.sample(num_certifications)
    
    selected_certifications.each do |cert|
      unless product.certifications.include?(cert)
        product.certifications << cert
      end
    end

    # Attach fake files to product features
    product.product_features.each do |feature|
      if feature.attachment.blank? && rand < 0.7  # 70% chance to have an attachment
        # Randomly choose between image and video
        if rand < 0.7  # 70% of attachments are images
          image_files = Dir.glob(Rails.root.join('db', 'seed_files', 'images', 'placeholder_*.png'))
          if image_files.any?
            feature.attachment.attach(
              io: File.open(image_files.sample),
              filename: File.basename(image_files.sample),
              content_type: 'image/png'
            )
          end
        else  # 30% of attachments are videos
          video_files = Dir.glob(Rails.root.join('db', 'seed_files', 'videos', 'demo_video_*.mp4'))
          if video_files.any?
            feature.attachment.attach(
              io: File.open(video_files.sample),
              filename: File.basename(video_files.sample),
              content_type: 'video/mp4'
            )
          end
        end
      end
    end

    # Attach fake logos to product customers
    product.product_customers.each do |customer|
      if customer.logo.blank?
        logo_files = Dir.glob(Rails.root.join('db', 'seed_files', 'images', 'logo_*.jpg'))
        if logo_files.any?
          customer.logo.attach(
            io: File.open(logo_files.sample),
            filename: File.basename(logo_files.sample),
            content_type: 'image/jpeg'
          )
        end
      end
    end

    # Create product videos with video files
    if product.product_videos.empty?
      video_files = Dir.glob(Rails.root.join('db', 'seed_files', 'videos', 'demo_video_*.mp4'))
      num_videos = rand(1..3)
      
      num_videos.times do |i|
        next if video_files.empty?
        
        video_file = video_files.sample
        product_video = product.product_videos.build(
          title: "#{product.name} Demo Video #{i + 1}",
          description: "Demonstration video showing the key features and capabilities of #{product.name}",
          position: i + 1,
          published: true
        )
        
        product_video.video_file.attach(
          io: File.open(video_file),
          filename: File.basename(video_file),
          content_type: 'video/mp4'
        )
        
        product_video.save!
      end
    end

    # Create product screenshots with image files
    if product.product_screenshots.empty?
      image_files = Dir.glob(Rails.root.join('db', 'seed_files', 'images', 'photo_*.jpg'))
      num_screenshots = rand(3..6)
      
      num_screenshots.times do |i|
        next if image_files.empty?
        
        image_file = image_files.sample
        screenshot = product.product_screenshots.build(
          title: "#{product.name} Screenshot #{i + 1}",
          description: "Interface screenshot showing #{product.name} functionality",
          position: i + 1,
          published: true
        )
        
        screenshot.image.attach(
          io: File.open(image_file),
          filename: File.basename(image_file),
          content_type: 'image/jpeg'
        )
        
        screenshot.save!
      end
    end

    # Create case studies with PDF files
    if product.case_studies.empty?
      pdf_files = Dir.glob(Rails.root.join('db', 'seed_files', 'pdfs', 'case_study_*.pdf'))
      num_case_studies = rand(1..2)
      
      case_study_titles = [
        "Modernizing Government Operations",
        "Digital Transformation Success Story",
        "Improving Citizen Services",
        "Streamlining Administrative Processes",
        "Enhanced Data Management Implementation",
        "Cost Reduction and Efficiency Gains"
      ]
      
      num_case_studies.times do |i|
        next if pdf_files.empty?
        
        pdf_file = pdf_files.sample
        case_study = product.case_studies.build(
          title: case_study_titles.sample,
          description: "A detailed case study showing how #{product.name} helped improve government operations and citizen services.",
          position: i + 1,
          published: true
        )
        
        case_study.upload.attach(
          io: File.open(pdf_file),
          filename: File.basename(pdf_file),
          content_type: 'application/pdf'
        )
        
        case_study.save!
      end
    end

    # Create product content with mixed file types
    if product.product_content.empty?
      content_types = [
        { type: 'document', files: Dir.glob(Rails.root.join('db', 'seed_files', 'pdfs', 'content_doc_*.pdf')), content_type: 'application/pdf' },
        { type: 'image', files: Dir.glob(Rails.root.join('db', 'seed_files', 'images', 'placeholder_*.png')), content_type: 'image/png' },
        { type: 'video', files: Dir.glob(Rails.root.join('db', 'seed_files', 'videos', 'demo_video_*.mp4')), content_type: 'video/mp4' }
      ]
      
      num_content_items = rand(2..4)
      
      content_titles = [
        "Implementation Guide",
        "User Manual",
        "Technical Specifications",
        "Training Materials",
        "Product Overview",
        "Integration Guide",
        "Best Practices",
        "Quick Start Guide"
      ]
      
      num_content_items.times do |i|
        content_type_info = content_types.sample
        next if content_type_info[:files].empty?
        
        file_path = content_type_info[:files].sample
        content_item = product.product_content.build(
          title: content_titles.sample,
          description: "#{content_titles.sample} for #{product.name} - comprehensive resource for users and administrators.",
          position: i + 1,
          published: true
        )
        
        content_item.file.attach(
          io: File.open(file_path),
          filename: File.basename(file_path),
          content_type: content_type_info[:content_type]
        )
        
        content_item.save!
      end
    end

    # Create second product for first 10 companies
    if index < 10
      # Create a similar but different product name
      second_product_names = {
        "TechCorp Solutions" => "TechCorp Analytics",
        "CivicTech Solutions" => "CivicTech Portal", 
        "GovDataFlow" => "GovDataFlow Pro",
        "SecureGov Technologies" => "SecureGov Shield",
        "SmartCity Innovations" => "SmartCity Hub",
        "PublicTech Labs" => "PublicTech Cloud",
        "GovernmentSoft" => "GovernmentSoft Pro",
        "CivicData Systems" => "CivicData Analytics",
        "UrbanTech Solutions" => "UrbanTech Platform",
        "PublicSector Pro" => "PublicSector Enterprise"
      }
      
      second_product_name = second_product_names[company_info[:name]] || "#{company_info[:name]} Pro"
      
      # Select different category for second product
      second_category = all_categories.sample
      second_category_name = second_category.name
      second_templates = product_templates[second_category_name] || [
        { name: second_product_name, description: "Advanced government technology solution", features: ["Advanced Feature 1", "Advanced Feature 2"] }
      ]
      second_template = second_templates.sample
      second_template[:name] = second_product_name
      
      second_product = Product.find_or_create_by!(name: second_product_name, account: vendor_account) do |p|
        p.published = true
        p.description = second_template[:description]
        p.website = company_info[:website]
        p.created_at = rand(60.days.ago..Time.current)
      end
      
      # Attach logo to second product
      if second_product.logo.blank?
        logo_files = Dir.glob(Rails.root.join('db', 'seed_files', 'images', 'logo_*.jpg'))
        if logo_files.any?
          second_product.logo.attach(
            io: File.open(logo_files.sample),
            filename: File.basename(logo_files.sample),
            content_type: 'image/jpeg'
          )
        end
      end

      # Attach featured video to 50% of second products
      if second_product.featured_video.blank? && rand < 0.5
        video_files = Dir.glob(Rails.root.join('db', 'seed_files', 'videos', 'demo_video_*.mp4'))
        if video_files.any?
          second_product.featured_video.attach(
            io: File.open(video_files.sample),
            filename: File.basename(video_files.sample),
            content_type: 'video/mp4'
          )
        end
      end
      
      # Create features for second product
      if second_product.product_features.empty? && second_template[:features].any?
        second_template[:features].each_with_index do |feature_name, index|
          second_product.product_features.create!(
            name: feature_name,
            description: "#{feature_name} - Advanced functionality for #{second_product.name}",
            position: index + 1,
            published: true
          )
        end
      end

      # Create customers for second product
      if second_product.product_customers.empty?
        selected_customers = government_customers.sample(rand(2..4))
        selected_customers.each_with_index do |customer_info, index|
          second_product.product_customers.create!(
            name: customer_info[:name],
            description: customer_info[:description].gsub(product.name, second_product.name),
            position: index + 1,
            published: true
          )
        end
      end

      # Assign categories to second product
      second_selected_categories = all_categories.sample(rand(1..3))
      second_selected_categories.each do |cat|
        unless second_product.categories.include?(cat)
          second_product.categories << cat
        end
      end

      # Assign certifications to second product
      second_selected_certifications = all_certifications.sample(rand(0..4))
      second_selected_certifications.each do |cert|
        unless second_product.certifications.include?(cert)
          second_product.certifications << cert
        end
      end

      # Attach fake files to second product features
      second_product.product_features.each do |feature|
        if feature.attachment.blank? && rand < 0.7  # 70% chance to have an attachment
          # Randomly choose between image and video
          if rand < 0.7  # 70% of attachments are images
            image_files = Dir.glob(Rails.root.join('db', 'seed_files', 'images', 'placeholder_*.png'))
            if image_files.any?
              feature.attachment.attach(
                io: File.open(image_files.sample),
                filename: File.basename(image_files.sample),
                content_type: 'image/png'
              )
            end
          else  # 30% of attachments are videos
            video_files = Dir.glob(Rails.root.join('db', 'seed_files', 'videos', 'demo_video_*.mp4'))
            if video_files.any?
              feature.attachment.attach(
                io: File.open(video_files.sample),
                filename: File.basename(video_files.sample),
                content_type: 'video/mp4'
              )
            end
          end
        end
      end

      # Attach fake logos to second product customers
      second_product.product_customers.each do |customer|
        if customer.logo.blank?
          logo_files = Dir.glob(Rails.root.join('db', 'seed_files', 'images', 'logo_*.jpg'))
          if logo_files.any?
            customer.logo.attach(
              io: File.open(logo_files.sample),
              filename: File.basename(logo_files.sample),
              content_type: 'image/jpeg'
            )
          end
        end
      end
    end

    print "."
  end

  # Regenerate slugs for all accounts and products
  puts "\nRegenerating slugs..."
  Account.find_each(&:save)
  Product.find_each(&:save)

  puts "\n\nCreated development seed data:"
  puts "- Admin user: <EMAIL>"
  puts "- Agency user: <EMAIL>"
  puts "- Password for all: password123"
  puts "- Created #{Account.where(account_type: 'vendor').count} vendor companies"
  puts "- Created #{Product.count} total products"
  
  # Print distribution statistics
  single_product_companies = Account.where(account_type: 'vendor').joins(:products).group('accounts.id').having('COUNT(products.id) = 1').count.size
  two_product_companies = Account.where(account_type: 'vendor').joins(:products).group('accounts.id').having('COUNT(products.id) = 2').count.size
  
  puts "- #{single_product_companies} companies with 1 product"
  puts "- #{two_product_companies} companies with 2 products"
  
  # Print category distribution
  puts "\nProducts per category:"
  Category.joins(:products).group('categories.name').count.each do |category, count|
    puts "  #{category}: #{count} products"
  end
end