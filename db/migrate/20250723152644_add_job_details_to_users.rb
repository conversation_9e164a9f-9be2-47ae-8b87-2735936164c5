class AddJobDetailsToUsers < ActiveRecord::Migration[8.0]
  def change
    # Only add organization_name since job_title and department already exist
    add_column :users, :organization_name, :string, null: false, default: ''
    
    # Make department required (it already exists but isn't required)
    change_column_null :users, :department, false, ''
    
    # Add indexes for common queries
    add_index :users, :department
    add_index :users, :organization_name
  end
end
