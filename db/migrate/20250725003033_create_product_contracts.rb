class CreateProductContracts < ActiveRecord::Migration[8.0]
  def change
    create_table :product_contracts do |t|
      t.references :product, null: false, foreign_key: true
      t.string :name, null: false
      t.text :description
      t.integer :position, default: 0
      t.boolean :published, default: false, null: false

      t.timestamps
    end
    
    add_index :product_contracts, [:product_id, :position]
  end
end
