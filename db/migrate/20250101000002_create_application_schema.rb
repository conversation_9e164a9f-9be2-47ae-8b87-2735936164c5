class CreateApplicationSchema < ActiveRecord::Migration[8.0]
  def change
    # Create users table
    create_table :users do |t|
      t.string :email_address, null: false
      t.string :password_digest, null: false
      t.string :first_name
      t.string :last_name
      t.string :phone
      t.string :job_title
      t.boolean :super_admin, default: false, null: false

      t.timestamps
    end
    add_index :users, :email_address, unique: true
    add_index :users, :super_admin

    # Create sessions table
    create_table :sessions do |t|
      t.references :user, null: false, foreign_key: true
      t.string :ip_address
      t.string :user_agent

      t.timestamps
    end

    # Create accounts table (with company profile fields merged in)
    create_table :accounts do |t|
      t.string :name
      t.string :account_type
      t.string :status
      t.references :owner, null: false, foreign_key: { to_table: :users }
      t.datetime :approved_at
      t.references :approved_by, foreign_key: { to_table: :users }
      t.string :confirmation_token
      t.datetime :confirmation_sent_at
      t.datetime :confirmed_at
      t.string :slug
      
      # Company profile fields (migrated from company_profiles table)
      # Note: website moved to products table
      t.string :headquarters_location
      t.string :linkedin_url
      t.text :description

      t.timestamps
    end
    add_index :accounts, :confirmation_token, unique: true
    add_index :accounts, :slug

    # Create account_users join table
    create_table :account_users do |t|
      t.references :account, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.string :role
      t.datetime :joined_at

      t.timestamps
    end

    # Create categories table (with multi-level hierarchy support)
    create_table :categories do |t|
      t.string :name
      t.string :slug
      t.text :description
      t.references :parent, foreign_key: { to_table: :categories }, null: true

      t.timestamps
    end

    # Create certifications table
    create_table :certifications do |t|
      t.string :name
      t.text :description

      t.timestamps
    end

    # Create products table (updated to use many-to-many with categories)
    create_table :products do |t|
      t.references :account, null: true, foreign_key: true  # Made optional
      t.string :name
      t.text :description
      t.string :slug
      t.boolean :published, default: false
      t.json :features, default: []
      t.string :website  # Moved from accounts table

      t.timestamps
    end
    add_index :products, :slug

    # Create product_categories join table (many-to-many)
    create_table :product_categories do |t|
      t.references :product, null: false, foreign_key: true
      t.references :category, null: false, foreign_key: true

      t.timestamps
    end
    add_index :product_categories, [:product_id, :category_id], unique: true

    # Create product_certifications join table (many-to-many)
    create_table :product_certifications do |t|
      t.references :product, null: false, foreign_key: true
      t.references :certification, null: false, foreign_key: true

      t.timestamps
    end

    # Create product_features table
    create_table :product_features do |t|
      t.references :product, null: false, foreign_key: true
      t.string :name, null: false
      t.text :description
      t.integer :position, default: 0

      t.timestamps
    end
    add_index :product_features, [:product_id, :position]

    # Create product_customers table
    create_table :product_customers do |t|
      t.references :product, null: false, foreign_key: true
      t.string :name, null: false
      t.text :description
      t.integer :position, default: 0
      t.boolean :published, default: true, null: false

      t.timestamps
    end
    add_index :product_customers, [:product_id, :position]

    # Create product_screenshots table
    create_table :product_screenshots do |t|
      t.string :title
      t.text :description
      t.references :product, null: false, foreign_key: true
      t.integer :position
      t.boolean :published, default: false

      t.timestamps
    end

    # Create product_videos table
    create_table :product_videos do |t|
      t.references :product, null: false, foreign_key: true
      t.string :title
      t.text :description
      t.integer :position
      t.boolean :published, default: false

      t.timestamps
    end

    # Create product_content table
    create_table :product_content do |t|
      t.references :product, null: false, foreign_key: true
      t.string :title
      t.text :description
      t.integer :position
      t.boolean :published, default: false

      t.timestamps
    end

    # Create product_claims table
    create_table :product_claims do |t|
      t.references :product, null: false, foreign_key: true
      t.string :name
      t.string :email
      t.string :company
      t.string :title
      t.text :message
      t.string :status

      t.timestamps
    end

    # Create case_studies table
    create_table :case_studies do |t|
      t.references :product, null: false, foreign_key: true
      t.string :title
      t.text :description
      t.integer :position
      t.boolean :published, default: false

      t.timestamps
    end

    # Create leads table
    create_table :leads do |t|
      t.references :product, null: false, foreign_key: true
      t.references :account, null: true, foreign_key: true  # Made optional
      t.references :user, null: true, foreign_key: true     # Made optional
      t.references :agency_account, foreign_key: { to_table: :accounts }
      t.string :status
      t.string :contact_name
      t.string :contact_email
      t.string :contact_phone
      t.string :contact_company
      t.string :timeline
      t.string :budget_range
      t.text :message
      t.text :notes
      t.string :source

      t.timestamps
    end

    # Create account_invitations table
    create_table :account_invitations do |t|
      t.string :email
      t.string :account_type
      t.string :account_name
      t.references :invited_by, null: false, foreign_key: { to_table: :users }
      t.string :token
      t.datetime :expires_at
      t.string :status
      t.string :first_name
      t.string :last_name

      t.timestamps
    end

    # Create team_invitations table
    create_table :team_invitations do |t|
      t.references :account, null: false, foreign_key: true
      t.references :invited_by, null: false, foreign_key: { to_table: :users }
      t.string :email
      t.string :role
      t.string :status, default: "pending"
      t.text :message
      t.string :token
      t.datetime :expires_at

      t.timestamps
    end
    add_index :team_invitations, :token, unique: true
    add_index :team_invitations, [:account_id, :email], unique: true
  end
end