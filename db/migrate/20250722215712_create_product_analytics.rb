class CreateProductAnalytics < ActiveRecord::Migration[8.0]
  def change
    create_table :product_analytics do |t|
      t.references :product, null: false, foreign_key: true
      t.references :user, null: true, foreign_key: true
      t.string :event_type, null: false
      t.string :ip_address, null: false
      t.text :user_agent, null: false
      t.string :visitor_id, null: false
      t.json :metadata

      t.timestamps
    end
    
    add_index :product_analytics, [:product_id, :event_type]
    add_index :product_analytics, [:visitor_id, :product_id], unique: true
    add_index :product_analytics, :created_at
  end
end
