# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_07_25_025734) do
  create_table "account_invitations", force: :cascade do |t|
    t.string "email"
    t.string "account_type"
    t.string "account_name"
    t.integer "invited_by_id", null: false
    t.string "token"
    t.datetime "expires_at"
    t.string "status"
    t.string "first_name"
    t.string "last_name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "phone"
    t.string "job_title"
    t.string "department"
    t.index ["invited_by_id"], name: "index_account_invitations_on_invited_by_id"
  end

  create_table "account_users", force: :cascade do |t|
    t.integer "account_id", null: false
    t.integer "user_id", null: false
    t.string "role"
    t.datetime "joined_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_account_users_on_account_id"
    t.index ["user_id"], name: "index_account_users_on_user_id"
  end

  create_table "accounts", force: :cascade do |t|
    t.string "name"
    t.string "account_type"
    t.string "status"
    t.integer "owner_id", null: false
    t.datetime "approved_at"
    t.integer "approved_by_id"
    t.string "confirmation_token"
    t.datetime "confirmation_sent_at"
    t.datetime "confirmed_at"
    t.string "slug"
    t.string "headquarters_location"
    t.string "linkedin_url"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["approved_by_id"], name: "index_accounts_on_approved_by_id"
    t.index ["confirmation_token"], name: "index_accounts_on_confirmation_token", unique: true
    t.index ["owner_id"], name: "index_accounts_on_owner_id"
    t.index ["slug"], name: "index_accounts_on_slug"
  end

  create_table "action_text_rich_texts", force: :cascade do |t|
    t.string "name", null: false
    t.text "body", limit: ********
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["record_type", "record_id", "name"], name: "index_action_text_rich_texts_uniqueness", unique: true
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "case_studies", force: :cascade do |t|
    t.integer "product_id", null: false
    t.string "title"
    t.text "description"
    t.integer "position"
    t.boolean "published", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_id"], name: "index_case_studies_on_product_id"
  end

  create_table "categories", force: :cascade do |t|
    t.string "name"
    t.string "slug"
    t.text "description"
    t.integer "parent_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["parent_id"], name: "index_categories_on_parent_id"
  end

  create_table "certifications", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "friendly_id_slugs", force: :cascade do |t|
    t.string "slug", null: false
    t.integer "sluggable_id", null: false
    t.string "sluggable_type", limit: 50
    t.string "scope"
    t.datetime "created_at"
    t.index ["slug", "sluggable_type", "scope"], name: "index_friendly_id_slugs_on_slug_and_sluggable_type_and_scope", unique: true
    t.index ["slug", "sluggable_type"], name: "index_friendly_id_slugs_on_slug_and_sluggable_type"
    t.index ["sluggable_type", "sluggable_id"], name: "index_friendly_id_slugs_on_sluggable_type_and_sluggable_id"
  end

  create_table "leads", force: :cascade do |t|
    t.integer "product_id", null: false
    t.integer "account_id"
    t.integer "user_id"
    t.integer "agency_account_id"
    t.string "status"
    t.string "contact_name"
    t.string "contact_email"
    t.string "contact_phone"
    t.string "contact_company"
    t.string "timeline"
    t.string "budget_range"
    t.text "message"
    t.text "notes"
    t.string "source"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_leads_on_account_id"
    t.index ["agency_account_id"], name: "index_leads_on_agency_account_id"
    t.index ["product_id"], name: "index_leads_on_product_id"
    t.index ["user_id"], name: "index_leads_on_user_id"
  end

  create_table "list_items", force: :cascade do |t|
    t.integer "list_id", null: false
    t.integer "product_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["list_id", "product_id"], name: "index_list_items_on_list_id_and_product_id", unique: true
    t.index ["list_id"], name: "index_list_items_on_list_id"
    t.index ["product_id"], name: "index_list_items_on_product_id"
  end

  create_table "lists", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.integer "account_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_lists_on_account_id"
  end

  create_table "product_analytics", force: :cascade do |t|
    t.integer "product_id", null: false
    t.integer "user_id"
    t.string "event_type", null: false
    t.string "ip_address", null: false
    t.text "user_agent", null: false
    t.string "visitor_id", null: false
    t.json "metadata"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_at"], name: "index_product_analytics_on_created_at"
    t.index ["product_id", "event_type"], name: "index_product_analytics_on_product_id_and_event_type"
    t.index ["product_id"], name: "index_product_analytics_on_product_id"
    t.index ["user_id"], name: "index_product_analytics_on_user_id"
    t.index ["visitor_id"], name: "index_product_analytics_on_visitor_id"
  end

  create_table "product_categories", force: :cascade do |t|
    t.integer "product_id", null: false
    t.integer "category_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["category_id"], name: "index_product_categories_on_category_id"
    t.index ["product_id", "category_id"], name: "index_product_categories_on_product_id_and_category_id", unique: true
    t.index ["product_id"], name: "index_product_categories_on_product_id"
  end

  create_table "product_certifications", force: :cascade do |t|
    t.integer "product_id", null: false
    t.integer "certification_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["certification_id"], name: "index_product_certifications_on_certification_id"
    t.index ["product_id"], name: "index_product_certifications_on_product_id"
  end

  create_table "product_claims", force: :cascade do |t|
    t.integer "product_id", null: false
    t.string "name"
    t.string "email"
    t.string "company"
    t.string "title"
    t.text "message"
    t.string "status"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_id"], name: "index_product_claims_on_product_id"
  end

  create_table "product_content", force: :cascade do |t|
    t.integer "product_id", null: false
    t.string "title"
    t.text "description"
    t.integer "position"
    t.boolean "published", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_id"], name: "index_product_content_on_product_id"
  end

  create_table "product_contracts", force: :cascade do |t|
    t.integer "product_id", null: false
    t.string "name", null: false
    t.text "description"
    t.integer "position", default: 0
    t.boolean "published", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_id", "position"], name: "index_product_contracts_on_product_id_and_position"
    t.index ["product_id"], name: "index_product_contracts_on_product_id"
  end

  create_table "product_customers", force: :cascade do |t|
    t.integer "product_id", null: false
    t.string "name", null: false
    t.text "description"
    t.integer "position", default: 0
    t.boolean "published", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_id", "position"], name: "index_product_customers_on_product_id_and_position"
    t.index ["product_id"], name: "index_product_customers_on_product_id"
  end

  create_table "product_features", force: :cascade do |t|
    t.integer "product_id", null: false
    t.string "name", null: false
    t.text "description"
    t.integer "position", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "published", default: false
    t.index ["product_id", "position"], name: "index_product_features_on_product_id_and_position"
    t.index ["product_id"], name: "index_product_features_on_product_id"
  end

  create_table "product_screenshots", force: :cascade do |t|
    t.string "title"
    t.text "description"
    t.integer "product_id", null: false
    t.integer "position"
    t.boolean "published", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_id"], name: "index_product_screenshots_on_product_id"
  end

  create_table "product_videos", force: :cascade do |t|
    t.integer "product_id", null: false
    t.string "title"
    t.text "description"
    t.integer "position"
    t.boolean "published", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["product_id"], name: "index_product_videos_on_product_id"
  end

  create_table "products", force: :cascade do |t|
    t.integer "account_id"
    t.string "name"
    t.text "description"
    t.string "slug"
    t.boolean "published", default: false
    t.json "features", default: []
    t.string "website"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "show_featured_video", default: false, null: false
    t.boolean "show_product_videos", default: false, null: false
    t.boolean "show_product_screenshots", default: false, null: false
    t.boolean "show_product_features", default: false, null: false
    t.boolean "show_product_customers", default: false, null: false
    t.boolean "show_product_content", default: false, null: false
    t.boolean "show_case_studies", default: false, null: false
    t.text "pricing_model"
    t.text "pricing"
    t.boolean "show_product_contracts", default: false, null: false
    t.index ["account_id"], name: "index_products_on_account_id"
    t.index ["slug"], name: "index_products_on_slug"
  end

  create_table "sessions", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "ip_address"
    t.string "user_agent"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_sessions_on_user_id"
  end

  create_table "team_invitations", force: :cascade do |t|
    t.integer "account_id", null: false
    t.integer "invited_by_id", null: false
    t.string "email"
    t.string "role"
    t.string "status", default: "pending"
    t.text "message"
    t.string "token"
    t.datetime "expires_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id", "email"], name: "index_team_invitations_on_account_id_and_email", unique: true
    t.index ["account_id"], name: "index_team_invitations_on_account_id"
    t.index ["invited_by_id"], name: "index_team_invitations_on_invited_by_id"
    t.index ["token"], name: "index_team_invitations_on_token", unique: true
  end

  create_table "users", force: :cascade do |t|
    t.string "email_address", null: false
    t.string "password_digest", null: false
    t.string "first_name"
    t.string "last_name"
    t.string "phone"
    t.string "job_title"
    t.boolean "super_admin", default: false, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "department", null: false
    t.index ["department"], name: "index_users_on_department"
    t.index ["email_address"], name: "index_users_on_email_address", unique: true
    t.index ["super_admin"], name: "index_users_on_super_admin"
  end

  add_foreign_key "account_invitations", "users", column: "invited_by_id"
  add_foreign_key "account_users", "accounts"
  add_foreign_key "account_users", "users"
  add_foreign_key "accounts", "users", column: "approved_by_id"
  add_foreign_key "accounts", "users", column: "owner_id"
  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "case_studies", "products"
  add_foreign_key "categories", "categories", column: "parent_id"
  add_foreign_key "leads", "accounts"
  add_foreign_key "leads", "accounts", column: "agency_account_id"
  add_foreign_key "leads", "products"
  add_foreign_key "leads", "users"
  add_foreign_key "list_items", "lists"
  add_foreign_key "list_items", "products"
  add_foreign_key "lists", "accounts"
  add_foreign_key "product_analytics", "products"
  add_foreign_key "product_analytics", "users"
  add_foreign_key "product_categories", "categories"
  add_foreign_key "product_categories", "products"
  add_foreign_key "product_certifications", "certifications"
  add_foreign_key "product_certifications", "products"
  add_foreign_key "product_claims", "products"
  add_foreign_key "product_content", "products"
  add_foreign_key "product_contracts", "products"
  add_foreign_key "product_customers", "products"
  add_foreign_key "product_features", "products"
  add_foreign_key "product_screenshots", "products"
  add_foreign_key "product_videos", "products"
  add_foreign_key "products", "accounts"
  add_foreign_key "sessions", "users"
  add_foreign_key "team_invitations", "accounts"
  add_foreign_key "team_invitations", "users", column: "invited_by_id"
end
