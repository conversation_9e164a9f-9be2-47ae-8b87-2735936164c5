require "test_helper"

class BasicViewHelpersTest < ActionView::TestCase
  test "pluralize helper works correctly" do
    assert_equal "1 error", pluralize(1, "error")
    assert_equal "2 errors", pluralize(2, "error")
    assert_equal "0 errors", pluralize(0, "error")
  end

  test "content_tag helper generates proper HTML" do
    result = content_tag(:div, "Hello World", class: "greeting")
    assert_includes result, "<div class=\"greeting\">Hello World</div>"
  end

  test "link_to helper generates links" do
    result = link_to("Click here", "/test", class: "button")
    assert_includes result, "<a class=\"button\" href=\"/test\">Click here</a>"
  end

  test "content_tag with multiple attributes" do
    result = content_tag(:button, "Submit", type: "submit", class: "btn btn-primary", disabled: true)
    assert_includes result, "type=\"submit\""
    assert_includes result, "class=\"btn btn-primary\""
    assert_includes result, "disabled=\"disabled\""
    assert_includes result, ">Submit</button>"
  end

  test "content_tag with data attributes" do
    result = content_tag(:div, "Content", data: { controller: "example", action: "click" })
    assert_includes result, "data-controller=\"example\""
    assert_includes result, "data-action=\"click\""
  end

  test "render inline template with variables" do
    name = "Test User"
    rendered = render inline: "Hello <%= name %>!", locals: { name: name }
    assert_equal "Hello Test User!", rendered
  end

  test "render inline template with conditionals" do
    show_message = true
    rendered = render inline: %{
      <% if show_message %>
        <p>Message is shown</p>
      <% else %>
        <p>Message is hidden</p>
      <% end %>
    }, locals: { show_message: show_message }
    
    assert_includes rendered, "Message is shown"
    assert_not_includes rendered, "Message is hidden"
  end

  test "render inline template with loops" do
    items = ["apple", "banana", "cherry"]
    rendered = render inline: %{
      <ul>
        <% items.each do |item| %>
          <li><%= item %></li>
        <% end %>
      </ul>
    }, locals: { items: items }
    
    assert_includes rendered, "<li>apple</li>"
    assert_includes rendered, "<li>banana</li>"
    assert_includes rendered, "<li>cherry</li>"
  end

  test "HTML safety and escaping" do
    unsafe_content = "<script>alert('xss')</script>"
    rendered = render inline: "<%= content %>", locals: { content: unsafe_content }
    
    # Should be escaped
    assert_includes rendered, "&lt;script&gt;"
    assert_not_includes rendered, "<script>"
  end

  test "raw helper bypasses HTML escaping" do
    html_content = "<strong>Bold Text</strong>"
    rendered = render inline: "<%= raw(content) %>", locals: { content: html_content }
    
    # Should not be escaped when using raw
    assert_includes rendered, "<strong>Bold Text</strong>"
  end

  test "CSS class building with arrays" do
    classes = ["btn", "btn-primary", "large"]
    result = content_tag(:button, "Click", class: classes.join(" "))
    assert_includes result, "class=\"btn btn-primary large\""
  end

  test "HTML attributes with nil values" do
    result = content_tag(:input, nil, type: "text", value: nil, placeholder: "Enter text")
    assert_includes result, "type=\"text\""
    assert_includes result, "placeholder=\"Enter text\""
    # Nil values should not appear as attributes
    assert_not_includes result, "value=\"\""
  end

  test "nested content tags" do
    inner = content_tag(:span, "Inner")
    outer = content_tag(:div, inner, class: "outer")
    
    assert_includes outer, "<div class=\"outer\"><span>Inner</span></div>"
  end

  test "simple form elements without routing" do
    rendered = render inline: %{
      <form action="/test" method="post">
        <label for="name">Name:</label>
        <input type="text" id="name" name="name" required>
        <button type="submit">Submit</button>
      </form>
    }
    
    assert_includes rendered, "<form action=\"/test\" method=\"post\">"
    assert_includes rendered, "<label for=\"name\">Name:</label>"
    assert_includes rendered, "type=\"text\""
    assert_includes rendered, "required"
    assert_includes rendered, "<button type=\"submit\">Submit</button>"
  end

  test "conditional CSS classes" do
    is_active = true
    is_primary = false
    
    rendered = render inline: %{
      <div class="<%= 'active' if is_active %> <%= 'primary' if is_primary %>">
        Content
      </div>
    }, locals: { is_active: is_active, is_primary: is_primary }
    
    assert_includes rendered, "class=\"active \""
    assert_not_includes rendered, "primary"
  end
end