require "test_helper"

class FormComponentsTest < ActionView::TestCase
  def setup
    @product = products(:one)
    @form_url = "/vendors/products"
    # Mock categories that the form expects
    @categories = []
    # Create some test categories if available
    if defined?(Category)
      @categories = Category.limit(3)
    end
  end

  test "product form renders basic structure" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url
    }
    
    assert_includes rendered, "form"
    assert_includes rendered, "space-y-6"
    assert_includes rendered, @form_url
  end

  test "product form renders name field" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url
    }
    
    assert_includes rendered, "Name"
    assert_includes rendered, "name=\"product[name]\""
    assert_includes rendered, "type=\"text\""
    assert_includes rendered, "text-gray-700"
  end

  test "product form renders description field" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url
    }
    
    assert_includes rendered, "Description"
    assert_includes rendered, "(optional)"
    assert_includes rendered, "rich_text_area"
  end

  test "product form renders website field" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url
    }
    
    assert_includes rendered, "Website"
    assert_includes rendered, "name=\"product[website]\""
    assert_includes rendered, "type=\"url\""
    assert_includes rendered, "placeholder=\"https://example.com\""
    assert_includes rendered, "Product website or landing page URL"
  end

  test "product form includes dark mode classes" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url
    }
    
    # Check for dark mode classes
    assert_includes rendered, "dark:text-gray-300"
    assert_includes rendered, "dark:border-gray-600"
    assert_includes rendered, "dark:bg-gray-700"
    assert_includes rendered, "dark:text-gray-100"
  end

  test "product form shows validation errors when present" do
    @product.errors.add(:name, "can't be blank")
    @product.errors.add(:description, "is too short")
    
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url
    }
    
    assert_includes rendered, "There were 2 errors with your submission"
    assert_includes rendered, "Name can't be blank"
    assert_includes rendered, "Description is too short"
    assert_includes rendered, "bg-red-50"
    assert_includes rendered, "dark:bg-red-900"
  end

  test "product form renders without errors when product is valid" do
    # Ensure product has no errors
    @product.errors.clear
    
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url
    }
    
    assert_not_includes rendered, "There were"
    assert_not_includes rendered, "errors with your submission"
    assert_not_includes rendered, "bg-red-50"
  end

  test "product form has correct CSS classes for styling" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url
    }
    
    # Check for responsive grid classes
    assert_includes rendered, "grid-cols-1"
    assert_includes rendered, "gap-6"
    
    # Check for form field styling
    assert_includes rendered, "border-gray-300"
    assert_includes rendered, "rounded-md"
    assert_includes rendered, "shadow-sm"
    assert_includes rendered, "focus:ring-blue-500"
    assert_includes rendered, "focus:border-blue-500"
  end

  test "product form includes accessibility attributes" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url
    }
    
    # Labels should be properly associated
    assert_includes rendered, "for=\"product_name\""
    assert_includes rendered, "for=\"product_description\""
    assert_includes rendered, "for=\"product_website\""
    
    # Error list should have proper role
    if @product.errors.any?
      assert_includes rendered, "role=\"list\""
    end
  end

  test "product form handles pre-filled values" do
    @product.name = "Test Product"
    @product.website = "https://example.com"
    
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url
    }
    
    assert_includes rendered, "Test Product"
    assert_includes rendered, "https://example.com"
  end

  test "product form includes helper text" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url
    }
    
    # Check for helper text
    assert_includes rendered, "Product website or landing page URL"
    assert_includes rendered, "text-gray-500"
    assert_includes rendered, "dark:text-gray-400"
  end

  test "product form uses semantic HTML" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url
    }
    
    # Should use proper form elements
    assert_includes rendered, "<form"
    assert_includes rendered, "<label"
    assert_includes rendered, "<input"
    
    # Should have proper input types
    assert_includes rendered, "type=\"text\""
    assert_includes rendered, "type=\"url\""
  end

  test "product form error styling is consistent" do
    @product.errors.add(:name, "can't be blank")
    
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url
    }
    
    # Error container styling
    assert_includes rendered, "border-red-200"
    assert_includes rendered, "dark:border-red-800"
    assert_includes rendered, "text-red-800"
    assert_includes rendered, "dark:text-red-200"
    assert_includes rendered, "text-red-700"
    assert_includes rendered, "dark:text-red-300"
  end

  test "product form handles special characters in values" do
    @product.name = "Product with \"quotes\" & ampersands"
    
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url
    }
    
    # Should handle special characters properly (Rails escapes them)
    assert_not_nil rendered
    assert_includes rendered, "Product with"
  end

  test "product form responsive design classes" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url
    }
    
    # Should include responsive classes
    assert_includes rendered, "sm:grid-cols-1"
    assert_includes rendered, "sm:text-sm"
    
    # Mobile-first approach
    assert_includes rendered, "block w-full"
  end
end