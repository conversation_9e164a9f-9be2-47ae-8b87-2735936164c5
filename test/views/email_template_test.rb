require "test_helper"

class EmailTemplateTest < ActionView::TestCase
  def setup
    @user = users(:one)
    @vendor_account = accounts(:vendor_account)
    @agency_account = accounts(:government_account)
  end

  test "user welcome email template renders" do
    @needs_confirmation = false
    @login_url = "http://example.com/login"
    @account = @vendor_account
    
    rendered = render template: "user_mailer/welcome", locals: {
      user: @user,
      account: @account,
      needs_confirmation: @needs_confirmation,
      login_url: @login_url
    }
    
    assert_includes rendered, "Welcome to Platia!"
    assert_includes rendered, @user.first_name
    assert_includes rendered, @login_url
  end

  test "user welcome email template with confirmation" do
    @needs_confirmation = true
    @confirmation_url = "http://example.com/confirm?token=abc123"
    @account = @vendor_account
    
    rendered = render template: "user_mailer/welcome", locals: {
      user: @user,
      account: @account,  
      needs_confirmation: @needs_confirmation,
      confirmation_url: @confirmation_url
    }
    
    assert_includes rendered, "Welcome to Platia!"
    assert_includes rendered, @confirmation_url
    assert_includes rendered, "confirm"
  end

  test "user account approved email template" do
    @dashboard_url = "http://example.com/dashboard"
    @account = @vendor_account
    
    rendered = render template: "user_mailer/account_approved", locals: {
      user: @user,
      account: @account,
      dashboard_url: @dashboard_url
    }
    
    assert_includes rendered, "approved"
    assert_includes rendered, @user.first_name
    assert_includes rendered, @account.name
    assert_includes rendered, @dashboard_url
  end

  test "user account rejected email template" do
    @reason = "Incomplete documentation"
    @contact_email = "<EMAIL>"
    
    rendered = render template: "user_mailer/account_rejected", locals: {
      user: @user,
      reason: @reason,
      contact_email: @contact_email
    }
    
    assert_includes rendered, @user.first_name
    assert_includes rendered, @reason
    assert_includes rendered, @contact_email
  end

  test "user account rejected email template without reason" do
    @reason = nil
    @contact_email = "<EMAIL>"
    
    rendered = render template: "user_mailer/account_rejected", locals: {
      user: @user,
      reason: @reason,
      contact_email: @contact_email
    }
    
    assert_includes rendered, @user.first_name
    assert_includes rendered, @contact_email
    # Should render gracefully without reason
    assert_not_nil rendered
  end

  test "user password reset email template" do
    @token = "reset123token"
    @reset_url = "http://example.com/reset?token=#{@token}"
    
    rendered = render template: "user_mailer/password_reset", locals: {
      user: @user,
      token: @token,
      reset_url: @reset_url
    }
    
    assert_includes rendered, @user.first_name
    assert_includes rendered, @reset_url
    assert_includes rendered, "reset"
  end

  test "email templates handle special characters in names" do
    @user.update(first_name: "José", last_name: "García-Smith")
    @account = @vendor_account
    @dashboard_url = "http://example.com/dashboard"
    
    rendered = render template: "user_mailer/account_approved", locals: {
      user: @user,
      account: @account,
      dashboard_url: @dashboard_url
    }
    
    assert_includes rendered, "José"
    # Note: The last name is not included in the rendered template, only first name
  end

  test "email templates are HTML safe" do
    @account = @vendor_account
    @dashboard_url = "http://example.com/dashboard"
    
    rendered = render template: "user_mailer/account_approved", locals: {
      user: @user,
      account: @account,
      dashboard_url: @dashboard_url
    }
    
    # Should contain proper HTML structure
    assert_includes rendered, "<html"
    assert_includes rendered, "<body"
    assert_includes rendered, "</html>"
  end

  test "email templates include CSS styling" do
    @account = @vendor_account
    @dashboard_url = "http://example.com/dashboard"
    
    rendered = render template: "user_mailer/account_approved", locals: {
      user: @user,
      account: @account,
      dashboard_url: @dashboard_url
    }
    
    # Should include some CSS styling
    assert_includes rendered, "style"
    assert_includes rendered, "font-family"
  end

  test "email templates handle long content gracefully" do
    @account = @vendor_account
    @account.update(name: "A" * 100)  # Very long account name
    @dashboard_url = "http://example.com/dashboard"
    
    rendered = render template: "user_mailer/account_approved", locals: {
      user: @user,
      account: @account,
      dashboard_url: @dashboard_url
    }
    
    # Should render without errors
    assert_not_nil rendered
    assert_includes rendered, @account.name
  end

  test "email templates include proper email structure" do
    @account = @vendor_account
    @dashboard_url = "http://example.com/dashboard"
    
    rendered = render template: "user_mailer/account_approved", locals: {
      user: @user,
      account: @account,
      dashboard_url: @dashboard_url
    }
    
    # Should have proper email structure
    assert_includes rendered, "<!DOCTYPE html"
    assert_includes rendered, "<head>"
    assert_includes rendered, "<title>"
    assert_includes rendered, "<meta charset=\"utf-8\">"
  end

  test "email templates handle nil values gracefully" do
    @account = nil
    @dashboard_url = "http://example.com/dashboard"
    
    # Should handle nil account gracefully
    assert_nothing_raised do
      render template: "user_mailer/account_approved", locals: {
        user: @user,
        account: @account,
        dashboard_url: @dashboard_url
      }
    end
  end

  test "email templates are responsive" do
    @account = @vendor_account
    @dashboard_url = "http://example.com/dashboard"
    
    rendered = render template: "user_mailer/account_approved", locals: {
      user: @user,
      account: @account,
      dashboard_url: @dashboard_url
    }
    
    # Should include viewport meta tag for mobile responsiveness
    assert_includes rendered, "viewport"
    assert_includes rendered, "width=device-width"
  end

  test "email templates include proper links" do
    @account = @vendor_account
    @dashboard_url = "http://example.com/dashboard"
    
    rendered = render template: "user_mailer/account_approved", locals: {
      user: @user,
      account: @account,
      dashboard_url: @dashboard_url
    }
    
    # Should include proper links
    assert_includes rendered, "href="
    assert_includes rendered, @dashboard_url
  end

  test "email templates handle empty strings" do
    @reason = ""
    @contact_email = "<EMAIL>"
    
    rendered = render template: "user_mailer/account_rejected", locals: {
      user: @user,
      reason: @reason,
      contact_email: @contact_email
    }
    
    # Should handle empty reason gracefully
    assert_not_nil rendered
    assert_includes rendered, @user.first_name
  end
end