require "test_helper"

class SimpleFormTest < ActionView::TestCase
  def setup
    @product = products(:one)
  end

  test "can render a simple product name field" do
    # Test rendering a simple form field without the complex partial
    rendered = render inline: %{
      <%= form_with model: @product, local: true do |form| %>
        <%= form.label :name, "Product Name" %>
        <%= form.text_field :name %>
      <% end %>
    }
    
    assert_includes rendered, "Product Name"
    assert_includes rendered, "name=\"product[name]\""
    assert_includes rendered, "type=\"text\""
  end

  test "form displays validation errors" do
    @product.errors.add(:name, "can't be blank")
    
    rendered = render inline: %{
      <% if @product.errors.any? %>
        <div class="error-messages">
          <% @product.errors.full_messages.each do |message| %>
            <p><%= message %></p>
          <% end %>
        </div>
      <% end %>
    }
    
    assert_includes rendered, "error-messages"
    assert_includes rendered, "Name can't be blank"
  end

  test "form field helpers work correctly" do
    rendered = render inline: %{
      <%= form_with model: @product, local: true do |form| %>
        <%= form.text_field :name, class: "form-input" %>
        <%= form.url_field :website, placeholder: "https://example.com" %>
        <%= form.text_area :description, rows: 3 %>
      <% end %>
    }
    
    assert_includes rendered, "class=\"form-input\""
    assert_includes rendered, "type=\"url\""
    assert_includes rendered, "placeholder=\"https://example.com\""
    assert_includes rendered, "textarea"
    assert_includes rendered, "rows=\"3\""
  end

  test "form with custom CSS classes" do
    rendered = render inline: %{
      <%= form_with model: @product, local: true, class: "custom-form space-y-4" do |form| %>
        <%= form.text_field :name %>
      <% end %>
    }
    
    assert_includes rendered, "class=\"custom-form space-y-4\""
  end

  test "form handles pre-filled values" do
    @product.name = "Test Product Name"
    @product.website = "https://testsite.com"
    
    rendered = render inline: %{
      <%= form_with model: @product, local: true do |form| %>
        <%= form.text_field :name %>
        <%= form.url_field :website %>
      <% end %>
    }
    
    assert_includes rendered, "Test Product Name"
    assert_includes rendered, "https://testsite.com"
  end

  test "form labels are properly associated with inputs" do
    rendered = render inline: %{
      <%= form_with model: @product, local: true do |form| %>
        <%= form.label :name, "Product Name" %>
        <%= form.text_field :name %>
      <% end %>
    }
    
    assert_includes rendered, "for=\"product_name\""
    assert_includes rendered, "id=\"product_name\""
  end

  test "form handles boolean fields" do
    rendered = render inline: %{
      <%= form_with model: @product, local: true do |form| %>
        <%= form.check_box :published %>
        <%= form.label :published, "Published" %>
      <% end %>
    }
    
    assert_includes rendered, "type=\"checkbox\""
    assert_includes rendered, "name=\"product[published]\""
  end

  test "form error handling with multiple errors" do
    @product.errors.add(:name, "can't be blank")
    @product.errors.add(:name, "is too short")
    @product.errors.add(:description, "is required")
    
    rendered = render inline: %{
      <% if @product.errors.any? %>
        <p>Found <%= pluralize(@product.errors.count, "error") %></p>
      <% end %>
    }
    
    assert_includes rendered, "Found 3 errors"
  end

  test "form field styling and accessibility" do
    rendered = render inline: %{
      <%= form_with model: @product, local: true do |form| %>
        <%= form.label :name, "Required Field" %>
        <%= form.text_field :name, required: true, aria: { describedby: "name-help" } %>
        <span id="name-help">This field is required</span>
      <% end %>
    }
    
    assert_includes rendered, "required=\"required\""
    assert_includes rendered, "aria-describedby=\"name-help\""
    assert_includes rendered, "id=\"name-help\""
  end

  test "form submission with method override" do
    rendered = render inline: %{
      <%= form_with model: @product, url: "/products/#{@product.id}", method: :patch, local: true do |form| %>
        <%= form.text_field :name %>
      <% end %>
    }
    
    assert_includes rendered, "method=\"post\""
    assert_includes rendered, "name=\"_method\""
    assert_includes rendered, "value=\"patch\""
  end
end