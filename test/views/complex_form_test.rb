require "test_helper"

class ComplexFormTest < ActionView::TestCase
  def setup
    @product = products(:one)
    @categories = [categories(:cloud_computing), categories(:security), categories(:data_analytics)]
    @certifications = [certifications(:soc2), certifications(:fisma), certifications(:fedramp)]
    @form_url = "/vendors/products"
    
    # Set up proper associations for the product
    @product.account = accounts(:vendor_account)
    @product.categories = [@categories.first]
  end

  test "product form renders with categories" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url,
      submit_text: "Create Product"
    }
    
    assert_includes rendered, "Categories"
    assert_includes rendered, "Cloud Computing"
    assert_includes rendered, "Cybersecurity"
    assert_includes rendered, "Data Analytics"
  end

  test "product form shows selected categories" do
    @product.categories = [categories(:cloud_computing), categories(:security)]
    
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url,
      submit_text: "Create Product"
    }
    
    # Should show checkboxes for categories
    assert_includes rendered, "product[category_ids][]"
    assert_includes rendered, "Cloud Computing"
    assert_includes rendered, "Cybersecurity"
  end

  test "product form handles validation errors with categories" do
    @product.errors.add(:name, "can't be blank")
    @product.errors.add(:categories, "must be selected")
    
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url,
      submit_text: "Create Product"
    }
    
    assert_includes rendered, "There were 2 errors"
    assert_includes rendered, "Name can&#39;t be blank"  # HTML escaped
    assert_includes rendered, "Categories must be selected"
    assert_includes rendered, "bg-red-50"
  end

  test "product form category checkboxes have proper attributes" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url,
      submit_text: "Create Product"
    }
    
    # Check for proper checkbox attributes
    assert_includes rendered, "type=\"checkbox\""
    assert_includes rendered, "name=\"product[category_ids][]\""
    assert_includes rendered, "value=\"#{categories(:cloud_computing).id}\""
    # Check for checkbox IDs (Rails generates them dynamically)
    assert_includes rendered, "product_category_ids_"
  end

  test "product form category labels are properly associated" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url,
      submit_text: "Create Product"
    }
    
    # Labels should be properly associated with checkboxes
    # Labels should be associated with checkboxes (IDs may vary)
    assert_includes rendered, "for=\"product_category_ids_"
    assert_includes rendered, "Cloud Computing"
    assert_includes rendered, "Cybersecurity"
  end

  test "product form shows category descriptions" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url,
      submit_text: "Create Product"
    }
    
    # Should include category names (which might include descriptions)
    assert_includes rendered, "Cloud Computing"
    assert_includes rendered, "Cybersecurity"
    assert_includes rendered, "Data Analytics"
  end

  test "product form with pre-selected categories shows checked state" do
    # Pre-select some categories for the product
    @product.category_ids = [categories(:cloud_computing).id, categories(:security).id]
    
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url,
      submit_text: "Create Product"
    }
    
    # The form should reflect the selected categories
    # Note: The actual checked state depends on the form helper implementation
    assert_includes rendered, "Cloud Computing"
    assert_includes rendered, "Cybersecurity"
  end

  test "product form renders all form fields together" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url,
      submit_text: "Create Product"
    }
    
    # Should include all major form sections
    assert_includes rendered, "Name"
    assert_includes rendered, "Description"
    assert_includes rendered, "Website"
    assert_includes rendered, "Categories"
    assert_includes rendered, "product[name]"
    assert_includes rendered, "product[website]"
    assert_includes rendered, "product[category_ids][]"
  end

  test "product form has proper form structure" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url,
      submit_text: "Create Product"
    }
    
    # Should have proper Rails form structure
    assert_includes rendered, "form"
    assert_includes rendered, @form_url
    assert_includes rendered, "space-y-6"
    assert_includes rendered, "grid-cols-1"
  end

  test "product form handles empty category list" do
    @categories = []
    
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url,
      submit_text: "Create Product"
    }
    
    # Should still render the form even with no categories
    assert_includes rendered, "Categories"
    # The category section should exist but be empty
    assert_not_nil rendered
  end

  test "product form includes accessibility features" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url,
      submit_text: "Create Product"
    }
    
    # Should include proper labels and form associations
    assert_includes rendered, "label"
    assert_includes rendered, "for=\"product_name\""
    assert_includes rendered, "for=\"product_description\""
    assert_includes rendered, "for=\"product_website\""
  end

  test "product form error messages use proper styling" do
    @product.errors.add(:name, "can't be blank")
    @product.errors.add(:description, "is too short")
    
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url,
      submit_text: "Create Product"
    }
    
    # Error styling should be consistent
    assert_includes rendered, "bg-red-50"
    assert_includes rendered, "dark:bg-red-900"
    assert_includes rendered, "border-red-200"
    assert_includes rendered, "dark:border-red-800"
    assert_includes rendered, "text-red-800"
    assert_includes rendered, "dark:text-red-200"
  end

  test "product form responsive design elements" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url,
      submit_text: "Create Product"
    }
    
    # Should include responsive classes
    assert_includes rendered, "sm:grid-cols-1"
    assert_includes rendered, "sm:text-sm"
    assert_includes rendered, "block w-full"
  end

  test "product form with rich text area" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url,
      submit_text: "Create Product"
    }
    
    # Should include rich text area for description
    assert_includes rendered, "Description"
    assert_includes rendered, "(optional)"
    # Rich text area implementation may vary
    assert_not_nil rendered
  end

  test "product form URL field has proper validation attributes" do
    rendered = render partial: "vendors/products/form", locals: {
      form_url: @form_url,
      submit_text: "Create Product"
    }
    
    # URL field should have proper type and placeholder
    assert_includes rendered, "type=\"url\""
    assert_includes rendered, "placeholder=\"https://example.com\""
    assert_includes rendered, "Product website or landing page URL"
  end
end