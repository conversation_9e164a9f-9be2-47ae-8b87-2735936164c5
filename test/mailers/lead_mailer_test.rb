require "test_helper"

class LeadMailerTest < ActionMailer::TestCase
  def setup
    @vendor_account = accounts(:vendor_account)
    @agency_account = accounts(:government_account)
    @vendor_user = @vendor_account.owner
    @agency_user = @agency_account.owner
    @product = products(:one)
    @product.update(account: @vendor_account)
    @lead = leads(:pending_lead)
    @lead.update(product: @product, account: @vendor_account, agency_account: @agency_account)
  end

  test "new lead notification email" do
    mail = LeadMailer.new_lead_notification(@lead)
    
    assert_equal "New inquiry for #{@product.name} - Platia", mail.subject
    assert_equal [@vendor_user.email_address], mail.to
    assert_equal ["<EMAIL>"], mail.from
    assert_match @product.name, mail.body.encoded
    assert_match @agency_account.name, mail.body.encoded
  end

  test "new lead notification includes dashboard URL" do
    mail = LeadMailer.new_lead_notification(@lead)
    
    assert_match "dashboard", mail.body.encoded
    assert_match "leads", mail.body.encoded
  end

  test "new lead notification with special characters in product name" do
    @product.update(name: "Product with \"Special\" Characters & Symbols")
    
    mail = LeadMailer.new_lead_notification(@lead)
    
    assert_match "Product with \"Special\" Characters & Symbols", mail.subject
  end


  test "all lead emails deliver successfully" do
    assert_emails 1 do
      LeadMailer.new_lead_notification(@lead).deliver_now
    end
  end

  test "all lead email templates render without errors" do
    assert_nothing_raised do
      LeadMailer.new_lead_notification(@lead).body.encoded
    end
  end

  test "lead emails handle complex object relationships" do
    mail = LeadMailer.new_lead_notification(@lead)
    
    # Should correctly navigate lead → product → vendor_account relationship
    assert_equal @lead.product, @product
    assert_equal @lead.product.account, @vendor_account
    assert_equal @lead.product.account.owner, @vendor_user
    
    assert_nothing_raised do
      mail.body.encoded
    end
  end
end