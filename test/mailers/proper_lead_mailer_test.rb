require "test_helper"

class ProperLeadMailerTest < ActionMailer::TestCase
  def setup
    @lead = leads(:pending_lead)
    @vendor_account = accounts(:vendor_account)
    @agency_account = accounts(:government_account)
    @vendor_user = @vendor_account.owner
    @agency_user = @agency_account.owner
    @product = products(:one)
    
    # Set up proper associations
    @product.update(account: @vendor_account)
    @lead.update(product: @product, account: @vendor_account, agency_account: @agency_account)
  end

  test "new lead notification email" do
    email = LeadMailer.new_lead_notification(@lead)
    
    assert_emails 1 do
      email.deliver_now
    end
    
    assert_equal ["<EMAIL>"], email.from
    assert_equal [@vendor_user.email_address], email.to
    assert_equal "New inquiry for #{@product.name} - Platia", email.subject
    assert_match @product.name, email.body.encoded
    assert_match @agency_account.name, email.body.encoded
  end


  test "new lead notification includes lead details" do
    email = LeadMailer.new_lead_notification(@lead)
    
    assert_match @lead.contact_name, email.body.encoded
    assert_match @lead.contact_email, email.body.encoded
    assert_match @lead.contact_company, email.body.encoded
  end


  test "all lead emails have correct headers" do
    emails = [
      LeadMailer.new_lead_notification(@lead)
    ]
    
    emails.each do |email|
      assert_equal ["<EMAIL>"], email.from
      assert_not_nil email.subject
      assert_not_empty email.body.encoded
    end
  end


  test "lead emails render without errors" do
    emails = [
      LeadMailer.new_lead_notification(@lead)
    ]
    
    emails.each do |email|
      assert_nothing_raised do
        email.body.encoded
      end
    end
  end

  test "lead emails handle complex object relationships" do
    # Test that the emails correctly navigate lead → product → vendor_account relationship
    email = LeadMailer.new_lead_notification(@lead)
    
    assert_equal @lead.product, @product
    assert_equal @lead.product.account, @vendor_account
    assert_equal @lead.product.account.owner, @vendor_user
    
    assert_nothing_raised do
      email.body.encoded
    end
  end
end