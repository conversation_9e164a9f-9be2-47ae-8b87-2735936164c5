require "test_helper"

class UserMailerTest < ActionMailer::TestCase
  def setup
    @user = users(:one)
    @vendor_account = accounts(:vendor_account)
    @agency_account = accounts(:government_account)
  end

  test "welcome email with confirmation required" do
    @vendor_account.update(confirmation_token: "test_token_123")
    
    mail = UserMailer.welcome(@user, @vendor_account)
    
    assert_equal "Welcome to Platia!", mail.subject
    assert_equal [@user.email_address], mail.to
    assert_equal ["<EMAIL>"], mail.from
    assert_match "test_token_123", mail.body.encoded
    assert_match "confirm", mail.body.encoded
  end

  test "welcome email without confirmation required" do
    @vendor_account.update(confirmation_token: nil)
    
    mail = UserMailer.welcome(@user, @vendor_account)
    
    assert_equal "Welcome to Platia!", mail.subject
    assert_equal [@user.email_address], mail.to
    assert_match @user.first_name, mail.body.encoded
    assert_no_match "confirm", mail.body.encoded
  end

  test "welcome email uses first owned account when no account provided" do
    # Clear existing owned accounts and set a specific one
    @user.owned_accounts.destroy_all
    @vendor_account.update!(owner: @user)
    
    mail = UserMailer.welcome(@user)
    
    assert_equal "Welcome to Platia!", mail.subject
    # Email should include account name since user owns the account
    assert_includes mail.body.encoded, "TechCorp"
  end

  test "account approved email for vendor" do
    @vendor_account.update!(owner: @user)
    
    mail = UserMailer.account_approved(@user)
    
    assert_equal "Your Platia account has been approved!", mail.subject
    assert_equal [@user.email_address], mail.to
    assert_match "approved", mail.body.encoded
  end

  test "account approved email for agency" do
    @agency_account.update!(owner: @user)
    
    mail = UserMailer.account_approved(@user)
    
    assert_equal "Your Platia account has been approved!", mail.subject
    assert_match "/agencies", mail.body.encoded
  end

  test "account approved email defaults to root URL for admin account type" do
    admin_account = Account.create!(name: "Admin Co", account_type: "admin", status: "approved", owner: @user)
    
    mail = UserMailer.account_approved(@user)
    
    assert_match "http://example.com", mail.body.encoded
  end

  test "account rejected email with reason" do
    reason = "Incomplete documentation provided"
    
    mail = UserMailer.account_rejected(@user, reason)
    
    assert_equal "Your Platia account has been rejected", mail.subject
    assert_equal [@user.email_address], mail.to
    assert_match reason, mail.body.encoded
    assert_match "<EMAIL>", mail.body.encoded
  end

  test "account rejected email without reason" do
    mail = UserMailer.account_rejected(@user)
    
    assert_equal "Your Platia account has been rejected", mail.subject
    assert_match "additional information", mail.body.encoded
    assert_match "<EMAIL>", mail.body.encoded
  end


  test "welcome email handles special characters in names" do
    @user.update(first_name: "José", last_name: "García-Smith")
    
    mail = UserMailer.welcome(@user, @vendor_account)
    
    # Check for encoded special characters in email (García → Garc=C3=ADa-Smith)
    assert_includes mail.body.encoded, "Jos"   # José gets encoded to Jos=C3=A9
    assert_includes mail.body.encoded, "=C3=AD"  # í in García gets encoded to =C3=AD
  end

  test "all emails deliver successfully" do
    assert_emails 1 do
      UserMailer.welcome(@user, @vendor_account).deliver_now
    end

    assert_emails 1 do
      UserMailer.account_approved(@user).deliver_now
    end

    assert_emails 1 do
      UserMailer.account_rejected(@user, "Test reason").deliver_now
    end

  end

  test "email templates render without errors" do
    assert_nothing_raised do
      UserMailer.welcome(@user, @vendor_account).body.encoded
    end

    assert_nothing_raised do
      UserMailer.account_approved(@user).body.encoded
    end

    assert_nothing_raised do
      UserMailer.account_rejected(@user).body.encoded
    end

  end
end