require "test_helper"

class AdminMailerTest < ActionMailer::TestCase
  def setup
    @admin_user = users(:admin)
    @admin_user.update(super_admin: true)
    @user = users(:one)
    @vendor_account = accounts(:vendor_account)
  end

  test "new user signup email" do
    mail = AdminMailer.new_user_signup(@user)
    
    assert_equal "New user registration requires approval - #{@user.full_name}", mail.subject
    assert_includes mail.to, @admin_user.email_address
    assert_equal ["<EMAIL>"], mail.from
    assert_match @user.full_name, mail.body.encoded
    assert_match @user.email_address, mail.body.encoded
  end

  test "new user signup email includes admin URLs" do
    mail = AdminMailer.new_user_signup(@user)
    
    assert_match "admin", mail.body.encoded
    assert_match "accounts", mail.body.encoded
  end

  test "new user signup email with special characters in name" do
    @user.update(first_name: "<PERSON>", last_name: "<PERSON><PERSON><PERSON>")
    
    mail = AdminMailer.new_user_signup(@user)
    
    assert_match "<PERSON>", mail.subject
  end


  test "admin_emails method returns super admin emails only" do
    regular_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Regular",
      last_name: "User",
      super_admin: false
    )
    
    another_admin = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Admin",
      last_name: "Two",
      super_admin: true
    )
    
    mailer = AdminMailer.new
    admin_emails = mailer.send(:admin_emails)
    
    assert_includes admin_emails, @admin_user.email_address
    assert_includes admin_emails, another_admin.email_address
    assert_not_includes admin_emails, regular_user.email_address
  end


  test "all admin emails deliver successfully" do
    assert_emails 1 do
      AdminMailer.new_user_signup(@user).deliver_now
    end
  end

  test "all admin email templates render without errors" do
    assert_nothing_raised do
      AdminMailer.new_user_signup(@user).body.encoded
    end
  end
end