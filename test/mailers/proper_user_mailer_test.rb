require "test_helper"

class ProperUserMailerTest < ActionMailer::TestCase
  def setup
    @user = users(:one)
    @vendor_account = accounts(:vendor_account)
    @agency_account = accounts(:government_account)
  end

  test "welcome email" do
    # Create email
    email = UserMailer.welcome(@user, @vendor_account)

    # Test the email was queued
    assert_emails 1 do
      email.deliver_now
    end

    # Test email content
    assert_equal ["<EMAIL>"], email.from
    assert_equal [@user.email_address], email.to
    assert_equal "Welcome to Platia!", email.subject
    assert_match @user.first_name, email.body.encoded
  end

  test "welcome email with confirmation" do
    @vendor_account.update(confirmation_token: "abc123")
    
    email = UserMailer.welcome(@user, @vendor_account)
    
    assert_emails 1 do
      email.deliver_now
    end
    
    assert_match "abc123", email.body.encoded
    assert_match "confirm", email.body.encoded
  end

  test "welcome email without confirmation" do
    @vendor_account.update(confirmation_token: nil)
    
    email = UserMailer.welcome(@user, @vendor_account)
    
    assert_emails 1 do
      email.deliver_now
    end
    
    assert_no_match "confirm", email.body.encoded
  end

  test "account approved email for vendor" do
    AccountUser.create!(user: @user, account: @vendor_account, role: 'admin')
    
    email = UserMailer.account_approved(@user)
    
    assert_emails 1 do
      email.deliver_now
    end
    
    assert_equal [@user.email_address], email.to
    assert_equal "Your Platia account has been approved!", email.subject
    assert_match "approved", email.body.encoded
  end

  test "account approved email for agency" do
    AccountUser.create!(user: @user, account: @agency_account, role: 'admin')
    
    email = UserMailer.account_approved(@user)
    
    assert_emails 1 do
      email.deliver_now
    end
    
    # For agency accounts, check that the approval email is sent
    assert_match "approved", email.body.encoded
  end

  test "account rejected email" do
    reason = "Incomplete documentation"
    
    email = UserMailer.account_rejected(@user, reason)
    
    assert_emails 1 do
      email.deliver_now
    end
    
    assert_equal [@user.email_address], email.to
    assert_equal "Your Platia account has been rejected", email.subject
    assert_match reason, email.body.encoded
    assert_match "<EMAIL>", email.body.encoded
  end

  test "account rejected email without reason" do
    email = UserMailer.account_rejected(@user)
    
    assert_emails 1 do
      email.deliver_now
    end
    
    # Test that the rejection email body contains appropriate content
    assert_match "additional information", email.body.encoded
  end


  test "all user emails have correct headers" do
    emails = [
      UserMailer.welcome(@user, @vendor_account),
      UserMailer.account_approved(@user),
      UserMailer.account_rejected(@user, "test")
    ]
    
    emails.each do |email|
      assert_equal ["<EMAIL>"], email.from
      assert_equal [@user.email_address], email.to
      assert_not_nil email.subject
      assert_not_empty email.body.encoded
    end
  end

  test "welcome email uses default account when none provided" do
    # Set up user with owned accounts
    AccountUser.create!(user: @user, account: @vendor_account, role: 'admin')
    
    email = UserMailer.welcome(@user)  # No account provided
    
    assert_emails 1 do
      email.deliver_now
    end
    
    # Should use the first owned account and render successfully
    assert_match "Welcome", email.body.encoded
  end

  test "email delivery can be performed immediately" do
    email = UserMailer.welcome(@user, @vendor_account)
    
    assert_nothing_raised do
      email.deliver_now
    end
  end

  test "email templates render without errors" do
    emails = [
      UserMailer.welcome(@user, @vendor_account),
      UserMailer.account_approved(@user),
      UserMailer.account_rejected(@user)
    ]
    
    emails.each do |email|
      assert_nothing_raised do
        email.body.encoded
      end
    end
  end
end