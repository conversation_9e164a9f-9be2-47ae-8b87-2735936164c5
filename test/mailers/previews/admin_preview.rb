class AdminPreview < ActionMailer::Preview
  def self.emails
    [:new_user_signup]
  end

  def self.description
    "Administrative email notifications"
  end

  def new_user_signup
    user = sample_user
    account = sample_account
    user.define_singleton_method(:owned_accounts) { [account] }
    AdminMailer.new_user_signup(user)
  end


  private

  def sample_user
    User.new(
      id: 1,
      first_name: "<PERSON>",
      last_name: "<PERSON>",
      email_address: "<EMAIL>",
      created_at: 2.hours.ago
    )
  end

  def sample_account
    user = sample_user
    account = Account.new(
      id: 1,
      name: "City of Los Angeles",
      account_type: "agency",
      status: "pending",
      created_at: 1.hour.ago,
      approved_at: nil
    )
    
    account.define_singleton_method(:owner) { user }
    account
  end
end