class LeadPreview < ActionMailer::Preview
  def self.emails
    [:new_lead_notification]
  end

  def self.description
    "Lead and inquiry related email notifications"
  end

  def new_lead_notification
    LeadMailer.new_lead_notification(sample_lead)
  end


  private

  def sample_lead
    # Create mock objects for preview
    vendor_user = User.new(
      id: 1,
      first_name: "<PERSON>",
      last_name: "<PERSON>",
      email_address: "<EMAIL>",
      department: "Sales",
      job_title: "Sales Director"
    )

    government_user = User.new(
      id: 2,
      first_name: "<PERSON>",
      last_name: "<PERSON>", 
      email_address: "<EMAIL>",
      department: "IT",
      job_title: "IT Director"
    )

    vendor_account = Account.new(
      id: 1,
      name: "TechCorp Solutions",
      account_type: "vendor",
      status: "approved"
    )

    government_account = Account.new(
      id: 2, 
      name: "City of San Francisco",
      account_type: "agency",
      status: "approved"
    )

    product = Product.new(
      id: 1,
      name: "CitizenConnect Portal",
      description: "Modern citizen engagement platform"
    )

    lead = Lead.new(
      id: 1,
      contact_name: "<PERSON>",
      contact_email: "<EMAIL>",
      contact_phone: "(*************",
      contact_company: "IT Department",
      timeline: "Q2 2024",
      budget_range: "$50,000 - $100,000",
      message: "We're looking for a modern citizen engagement platform to improve our digital services. Would love to learn more about your solution and see a demo.",
      status: "pending",
      created_at: 2.hours.ago,
      updated_at: 1.hour.ago
    )

    # Set up associations
    lead.define_singleton_method(:product) { product }
    lead.define_singleton_method(:account) { vendor_account }
    lead.define_singleton_method(:agency_account) { government_account }
    
    vendor_account.define_singleton_method(:owner) { vendor_user }
    government_account.define_singleton_method(:owner) { government_user }

    lead
  end
end