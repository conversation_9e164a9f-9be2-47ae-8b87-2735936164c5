require "test_helper"

class ProductFeaturesTest < ActionDispatch::IntegrationTest
  # Don't use fixtures for this test
  self.use_transactional_tests = true
  
  def self.fixture_sets
    []
  end
  def setup
    @user = User.create!(
      email_address: "features_test_#{SecureRandom.hex(8)}@example.com",
      password: "password123",
      first_name: "<PERSON>",
      last_name: "Vendor"
    )
    
    @account = Account.create!(
      name: "Test Company",
      account_type: "vendor",
      status: "approved",
      owner: @user
    )
    
    @category = Category.create!(name: "Test Category")
  end

  test "product can be created with product features" do
    product = Product.create!(
      name: "Test Product",
      account: @account
    )
    
    # Add category association
    product.categories << @category
    
    # Create product features using the ProductFeature model
    ProductFeature.create!(product: product, name: "Cloud-based", position: 1)
    ProductFeature.create!(product: product, name: "Mobile App", position: 2)
    ProductFeature.create!(product: product, name: "API Integration", position: 3)
    
    product.reload
    
    assert product.persisted?
    assert_equal 3, product.product_features.count
    assert_equal ["Cloud-based", "Mobile App", "API Integration"], product.feature_names
  end

  test "product feature helper methods work correctly" do
    product = Product.create!(
      name: "Test Product",
      account: @account
    )
    
    # Test creating features
    feature = ProductFeature.create!(product: product, name: "New Feature", position: 1)
    
    product.reload
    assert_includes product.feature_names, "New Feature"
    assert_equal ["New Feature"], product.feature_names
    
    # Test removing features
    feature.destroy
    product.reload
    assert_not_includes product.feature_names, "New Feature"
    assert_equal [], product.feature_names
  end

  test "product features are ordered by position" do
    product = Product.create!(
      name: "Test Product",
      account: @account
    )
    
    # Create features out of order
    ProductFeature.create!(product: product, name: "Third Feature", position: 3)
    ProductFeature.create!(product: product, name: "First Feature", position: 1)
    ProductFeature.create!(product: product, name: "Second Feature", position: 2)
    
    product.reload
    
    assert_equal ["First Feature", "Second Feature", "Third Feature"], product.feature_names
  end

  test "product feature validation" do
    product = Product.create!(
      name: "Test Product",
      account: @account
    )
    
    # Test that name is required
    feature = ProductFeature.new(product: product, position: 1)
    assert_not feature.valid?
    assert_includes feature.errors[:name], "can't be blank"
    
    # Test that product is required
    feature = ProductFeature.new(name: "Test Feature", position: 1)
    assert_not feature.valid?
    assert_includes feature.errors[:product], "must exist"
  end

  test "product features can be published and unpublished" do
    product = Product.create!(
      name: "Test Product",
      account: @account
    )
    
    # Create published and unpublished features
    published_feature = ProductFeature.create!(
      product: product, 
      name: "Published Feature", 
      position: 1, 
      published: true
    )
    
    unpublished_feature = ProductFeature.create!(
      product: product, 
      name: "Unpublished Feature", 
      position: 2, 
      published: false
    )
    
    product.reload
    
    # Test that published scope works
    assert_includes product.product_features.published, published_feature
    assert_not_includes product.product_features.published, unpublished_feature
  end
end
