require "test_helper"

class PerformanceScalabilityTest < ActionDispatch::IntegrationTest
  def setup
    @agency_user = users(:two)
    @vendor_user = users(:one)
    @password = "password"
    
    # Clear any existing test data to ensure consistent performance testing
    clear_test_data
  end

  def teardown
    # Clean up after tests
    clear_test_data
  end

  # Test 17: Large Data Set Handling
  test "marketplace with hundreds of products" do
    # Create 200 products for performance testing
    products = create_large_product_dataset(200)
    
    sign_in_as(@agency_user)
    
    # Test marketplace loading time
    start_time = Time.current
    get marketplace_path
    response_time = Time.current - start_time
    
    assert_response :success
    assert response_time < 3.0, "Marketplace took too long to load: #{response_time} seconds"
    
    # Should show products efficiently (marketplace uses group class for products)
    assert_select ".group"
    
    # Test category page with many products
    category = Category.create!(name: "Performance Test", slug: "performance-test")
    products.first(50).each { |p| p.categories << category }
    
    start_time = Time.current
    get marketplace_category_path(category)
    response_time = Time.current - start_time
    
    assert_response :success
    assert response_time < 2.0, "Category page took too long: #{response_time} seconds"
  end

  test "pagination performance" do
    # Create products for pagination testing
    create_large_product_dataset(150)
    
    sign_in_as(@agency_user)
    
    # Test first page
    start_time = Time.current
    get marketplace_products_path
    first_page_time = Time.current - start_time
    
    assert_response :success
    assert first_page_time < 3.0, "First page too slow: #{first_page_time} seconds"
    
    # Test subsequent pages
    (2..5).each do |page|
      start_time = Time.current
      get marketplace_products_path, params: { page: page }
      page_time = Time.current - start_time
      
      assert_response :success
      assert page_time < 3.0, "Page #{page} too slow: #{page_time} seconds"
    end
  end

  test "search performance with large datasets" do
    # Create diverse product dataset
    products = create_large_product_dataset(300)
    
    # Add variety to make search more realistic
    products.each_with_index do |product, index|
      product.update!(
        name: "#{product.name} #{['Software', 'Platform', 'Solution', 'System'][index % 4]}",
        description: "#{product.description} #{['security', 'analytics', 'management', 'integration'][index % 4]}"
      )
    end
    
    sign_in_as(@agency_user)
    
    # Test various search queries
    search_terms = ["software", "security", "platform", "analytics", "system"]
    
    search_terms.each do |term|
      start_time = Time.current
      get marketplace_search_path, params: { q: term }
      search_time = Time.current - start_time
      
      assert_response :success
      assert search_time < 3.0, "Search for '#{term}' took too long: #{search_time} seconds"
      
      # Should return relevant results or show no results message
      # Products are wrapped in .group class, categories don't need .group
      if response.body.include?("No results found")
        assert_select "h3", text: /No results found/
      else
        # If there are results, there should be either categories or products
        assert_select "h2", text: /Categories|Products/
      end
    end
  end

  test "image optimization and loading" do
    sign_in_as(@vendor_user)
    
    # Create product with images
    product = Product.create!(
      name: "Performance Test Product",
      description: "Product for image performance testing",
      account: accounts(:one),
      published: true
    )
    
    # Add multiple screenshots (simulated)
    10.times do |i|
      screenshot = ProductScreenshot.new(
        product: product,
        title: "Screenshot #{i}",
        description: "Test screenshot #{i}",
        position: i + 1
      )
      # Attach a test image file
      screenshot.image.attach(
        io: File.open(Rails.root.join("test/fixtures/files/test_image.jpg")),
        filename: "screenshot_#{i}.jpg",
        content_type: "image/jpeg"
      )
      screenshot.save!
    end
    
    sign_out
    sign_in_as(@agency_user)
    
    # Test product page loading with multiple images
    start_time = Time.current
    get marketplace_product_path(product)
    page_load_time = Time.current - start_time
    
    assert_response :success
    assert page_load_time < 3.0, "Product page with images took too long: #{page_load_time} seconds"
    
    # Should show product screenshots section
    assert_select "h2", text: /Product Screenshots/
  end

  test "concurrent user load simulation" do
    # Simulate multiple users accessing the system
    products = create_large_product_dataset(50)
    
    # Test concurrent marketplace access
    threads = []
    response_times = []
    
    5.times do |i|
      threads << Thread.new do
        # Each thread simulates a different user session
        start_time = Time.current
        get marketplace_path
        response_time = Time.current - start_time
        response_times << response_time
        
        assert_response :success
      end
    end
    
    threads.each(&:join)
    
    # Check that all requests completed in reasonable time
    avg_response_time = response_times.sum / response_times.length
    assert avg_response_time < 5.0, "Average response time too high: #{avg_response_time} seconds"
  end

  test "database query optimization" do
    # Create complex data relationships
    create_complex_data_relationships
    
    sign_in_as(@agency_user)
    
    # Test N+1 query prevention in marketplace
    # Enable query counting if using a gem like bullet or query-count
    start_time = Time.current
    get marketplace_products_path
    query_time = Time.current - start_time
    
    assert_response :success
    assert query_time < 2.0, "Marketplace queries took too long: #{query_time} seconds"
    
    # Test eager loading on product details
    product = products(:published_product)
    
    start_time = Time.current
    get marketplace_product_path(product)
    detail_query_time = Time.current - start_time
    
    assert_response :success
    assert detail_query_time < 2.0, "Product detail queries took too long: #{detail_query_time} seconds"
  end

  test "memory usage with large datasets" do
    # Monitor memory usage during large operations
    initial_memory = get_memory_usage
    
    # Create large dataset
    create_large_product_dataset(100)
    
    sign_in_as(@agency_user)
    
    # Perform memory-intensive operations
    get marketplace_products_path
    assert_response :success
    
    get marketplace_search_path, params: { q: "test" }
    assert_response :success
    
    final_memory = get_memory_usage
    memory_increase = final_memory - initial_memory
    
    # Memory increase should be reasonable (this is a rough check)
    assert memory_increase < 100, "Memory usage increased too much: #{memory_increase}MB"
  end

  test "caching effectiveness" do
    skip "Caching not implemented" unless Rails.cache.respond_to?(:fetch)
    
    # Test page caching
    sign_in_as(@agency_user)
    
    # First request (should populate cache)
    start_time = Time.current
    get marketplace_path
    first_request_time = Time.current - start_time
    
    assert_response :success
    
    # Second request (should use cache)
    start_time = Time.current
    get marketplace_path
    cached_request_time = Time.current - start_time
    
    assert_response :success
    
    # Cached request should be faster (not always guaranteed in tests)
    # This is more of a smoke test than a strict performance test
    assert cached_request_time < first_request_time + 1.0
  end

  test "background job performance" do
    skip "Background jobs not implemented" unless defined?(ActiveJob)
    
    # Test that background jobs don't block the main thread
    sign_in_as(@vendor_user)
    product = products(:one)
    
    # Operation that might trigger background jobs
    start_time = Time.current
    
    patch vendors_product_path(product), params: {
      product: { published: true }
    }
    
    response_time = Time.current - start_time
    
    assert_response :redirect
    assert response_time < 1.0, "Operation took too long (background job might be blocking): #{response_time} seconds"
  end

  test "api endpoint performance" do
    create_large_product_dataset(100)
    
    sign_in_as(@agency_user)
    
    # Test API search performance
    start_time = Time.current
    get api_search_path, params: { q: "test", format: :json }
    api_response_time = Time.current - start_time
    
    assert_response :success
    assert api_response_time < 1.0, "API search took too long: #{api_response_time} seconds"
    
    # Verify JSON response structure is efficient
    json_response = JSON.parse(response.body)
    assert json_response.key?("products")
    
    # Response shouldn't be too large
    response_size = response.body.bytesize
    assert response_size < 1.megabyte, "API response too large: #{response_size} bytes"
  end

  test "file upload performance" do
    sign_in_as(@vendor_user)
    product = products(:one)
    
    # Test multiple file uploads
    start_time = Time.current
    
    # Simulate uploading product screenshots
    3.times do |i|
      image_file = fixture_file_upload("test/fixtures/files/test_image.jpg", "image/jpeg")
      post vendors_product_product_screenshots_path(product), params: {
        product_screenshot: {
          title: "Screenshot #{i}",
          description: "Performance test screenshot",
          image: image_file
        }
      }
      # Accept either redirect or unprocessable_entity (validation errors)
      assert_includes [200, 302, 422], response.status
    end
    
    upload_time = Time.current - start_time
    assert upload_time < 5.0, "File uploads took too long: #{upload_time} seconds"
  end

  private

  def create_large_product_dataset(count)
    vendor_account = accounts(:one)
    
    products = []
    count.times do |i|
      products << Product.create!(
        name: "Performance Test Product #{i}",
        description: "This is a test product #{i} for performance testing with longer description content",
        account: vendor_account,
        published: true,
        website: "https://example#{i}.com"
      )
    end
    
    products
  end

  def create_complex_data_relationships
    # Create categories
    5.times do |i|
      category = Category.create!(name: "Category #{i}", slug: "category-#{i}")
      
      # Associate products with categories
      products(:published_product).categories << category if i.even?
    end
    
    # Create certifications
    3.times do |i|
      certification = Certification.create!(
        name: "Certification #{i}",
        description: "Test certification #{i}"
      )
    end
    
    # Create leads
    5.times do |i|
      Lead.create!(
        product: products(:published_product),
        contact_name: "Contact #{i}",
        contact_email: "contact#{i}@example.com",
        contact_company: "Company #{i}",
        message: "Test lead #{i}",
        status: "pending"
      )
    end
  end

  def clear_test_data
    # Clean up performance test data
    Product.where("name LIKE ?", "Performance Test Product %").destroy_all
    Category.where("name LIKE ?", "Category %").destroy_all
    Certification.where("name LIKE ?", "Certification %").destroy_all
    Lead.where("contact_name LIKE ?", "Contact %").destroy_all
  end

  def get_memory_usage
    # Simple memory usage check (Ruby specific)
    # In a real application, you might use more sophisticated memory monitoring
    GC.start
    ObjectSpace.count_objects[:T_OBJECT] / 1000.0 # Rough estimate in "MB"
  end

  def sign_in_as(user)
    post session_path, params: {
      email_address: user.email_address,
      password: @password
    }
  end

  def sign_out
    delete session_path
  end
end