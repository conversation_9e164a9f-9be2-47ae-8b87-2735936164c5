require "test_helper"

class UserRegistrationFlowTest < ActionDispatch::IntegrationTest
  def setup
    @vendor_params = {
      user: {
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON><PERSON>",
        email_address: "<EMAIL>",
        job_title: "CEO",
        account_name: "Tech Vendor Inc",
        password: "password123",
        password_confirmation: "password123"
      },
      account_type: "vendor"
    }
    
    @agency_params = {
      user: {
        first_name: "<PERSON>",
        last_name: "Agency",
        email_address: "<EMAIL>",
        job_title: "IT Director",
        account_name: "City Technology Department",
        password: "password123",
        password_confirmation: "password123"
      },
      account_type: "agency"
    }
  end

  test "complete vendor registration with account approval workflow" do
    # Step 1: Navigate to registration page
    get registrations_new_path
    assert_response :success
    assert_select "h1", text: /Create Your Account/

    # Step 2: Submit vendor registration
    assert_difference ["User.count", "Account.count"], 1 do
      post registrations_path, params: @vendor_params
    end

    # Should redirect to pending page since account needs approval
    assert_redirected_to pending_path
    follow_redirect!
    assert_select "h1", text: /Pending Approval/

    # Verify user and account were created correctly
    user = User.find_by(email_address: @vendor_params[:user][:email_address])
    assert user.present?
    assert_equal "John", user.first_name
    assert_equal "Vendor", user.last_name

    account = user.owned_accounts.first
    assert account.present?
    assert_equal "Tech Vendor Inc", account.name
    assert_equal "vendor", account.account_type
    assert_equal "pending", account.status

    # Step 3: User confirms their account
    get confirm_account_path(account.confirmation_token)
    assert_response :success
    
    account.reload
    assert account.confirmed_at.present?
    
    # Step 4: Admin approves the account
    admin_user = users(:admin)
    sign_in_as(admin_user)
    
    post approve_admin_account_path(account)
    assert_redirected_to admin_accounts_path
    
    account.reload
    assert_equal "approved", account.status
    assert account.approved_at.present?
    assert_equal admin_user, account.approved_by

    # Step 5: User can now login successfully (account is both confirmed and approved)
    sign_out
    
    post session_path, params: {
      email_address: @vendor_params[:user][:email_address],
      password: @vendor_params[:user][:password]
    }
    
    assert_redirected_to vendors_root_path
    follow_redirect!
    assert_response :success
  end

  test "complete agency registration with account approval workflow" do
    # Step 1: Navigate to registration page
    get registrations_new_path
    assert_response :success

    # Step 2: Submit agency registration
    assert_difference ["User.count", "Account.count"], 1 do
      post registrations_path, params: @agency_params
    end

    # Should redirect to pending page
    assert_redirected_to pending_path
    follow_redirect!

    # Verify user and account were created correctly
    user = User.find_by(email_address: @agency_params[:user][:email_address])
    assert user.present?
    assert_equal "<EMAIL>", user.email_address
    # Note: department is not set during registration, it's a separate field

    account = user.owned_accounts.first
    assert account.present?
    assert_equal "City Technology Department", account.name
    assert_equal "agency", account.account_type
    assert_equal "pending", account.status

    # Step 3: User confirms their account  
    get confirm_account_path(account.confirmation_token)
    assert_response :success
    
    account.reload
    assert account.confirmed_at.present?
    
    # Step 4: Admin approves the account
    admin_user = users(:admin)
    sign_in_as(admin_user)
    
    post approve_admin_account_path(account)
    account.reload
    assert_equal "approved", account.status

    # Step 5: User can login and access agency dashboard
    sign_out
    
    post session_path, params: {
      email_address: @agency_params[:user][:email_address],
      password: @agency_params[:user][:password]
    }
    
    assert_redirected_to agencies_root_path
  end

  test "email confirmation process for new registration" do
    # Step 1: Register new user
    post registrations_path, params: @vendor_params
    
    user = User.find_by(email_address: @vendor_params[:user][:email_address])
    account = user.owned_accounts.first
    
    # Step 2: User receives confirmation email (simulated)
    assert account.confirmation_token.present?
    assert account.confirmation_sent_at.present?
    
    # Step 3: User clicks confirmation link
    get confirm_account_path(account.confirmation_token)
    
    account.reload
    assert account.confirmed?
    assert account.confirmed_at.present?
    
    # Should show confirmation page
    assert_response :success
  end

  test "account invitation acceptance workflow" do
    # Step 1: Admin creates account invitation
    admin_user = users(:admin)
    sign_in_as(admin_user)
    
    invitation_params = {
      account_invitation: {
        email: "<EMAIL>",
        first_name: "Invited",
        last_name: "User",
        account_name: "Invited Company",
        account_type: "vendor"
      }
    }
    
    assert_difference "AccountInvitation.count", 1 do
      post admin_account_invitations_path, params: invitation_params
    end
    
    invitation = AccountInvitation.last
    assert_equal "<EMAIL>", invitation.email
    assert invitation.token.present?
    
    sign_out

    # Step 2: Invited user receives email and clicks acceptance link
    get accept_account_invitation_path(invitation.token)
    assert_response :success
    # Check for page content (may not have h1 tag)
    assert_select "body"

    # Step 3: User completes registration
    completion_params = {
      user: {
        first_name: invitation.first_name,
        last_name: invitation.last_name,
        password: "password123",
        password_confirmation: "password123",
        job_title: "Manager",
        phone: "555-9999"
      },
      account: {
        name: invitation.account_name,
        description: "Invitation-based company"
      }
    }

    assert_difference ["User.count", "Account.count"], 1 do
      post accept_account_invitation_path(invitation.token), params: completion_params
    end

    # Verify user and account creation
    user = User.find_by(email_address: invitation.email)
    assert user.present?
    assert_equal "Invited", user.first_name
    
    account = user.owned_accounts.first
    assert account.present?
    assert_equal "Invited Company", account.name
    assert_equal "vendor", account.account_type
    
    # Invitation should be marked as accepted
    invitation.reload
    assert invitation.accepted?
  end

  test "team invitation acceptance workflow" do
    # Step 1: Account owner creates team invitation
    owner = users(:one)
    account = accounts(:one)
    sign_in_as(owner)
    
    invitation_params = {
      email: "<EMAIL>",
      first_name: "Team",
      last_name: "Member",
      role: "member"
    }
    
    assert_difference "TeamInvitation.count", 1 do
      post create_team_invitation_account_path, params: { team_invitation: invitation_params }
    end
    
    invitation = TeamInvitation.last
    assert_equal account, invitation.account
    assert invitation.token.present?
    
    sign_out

    # Step 2: Invited user receives email and accepts invitation
    get invitation_path(invitation.token)
    assert_response :success
    assert_select "h1", text: /Team Invitation/

    # Step 3: User creates account to join team
    user_params = {
      user: {
        first_name: "Team",
        last_name: "Member",
        password: "password123",
        password_confirmation: "password123",
        job_title: "Developer"
      }
    }

    assert_difference ["User.count", "AccountUser.count"], 1 do
      post accept_invitation_path(invitation.token), params: user_params
    end

    # Verify user was added to account
    user = User.find_by(email_address: "<EMAIL>")
    assert user.present?
    
    account_user = AccountUser.find_by(user: user, account: account)
    assert account_user.present?
    assert_equal "member", account_user.role
    
    # User should be logged in and redirected appropriately
    assert_redirected_to vendors_root_path
  end

  test "registration validation errors are handled properly" do
    # Test missing required fields
    invalid_params = @vendor_params.deep_dup
    invalid_params[:user][:email_address] = ""
    invalid_params[:user][:password] = "short"
    invalid_params[:user][:account_name] = ""

    assert_no_difference ["User.count", "Account.count"] do
      post registrations_path, params: invalid_params
    end

    assert_response :unprocessable_entity
    assert_select "li", text: /can't be blank/
  end

  test "duplicate email registration is rejected" do
    # Create existing user
    existing_user = users(:one)
    
    duplicate_params = @vendor_params.deep_dup
    duplicate_params[:user][:email_address] = existing_user.email_address

    assert_no_difference ["User.count", "Account.count"] do
      post registrations_path, params: duplicate_params
    end

    assert_response :unprocessable_entity
    assert_select "li", text: /already been taken/
  end

  private

  def sign_in_as(user)
    post session_path, params: {
      email_address: user.email_address,
      password: "password"
    }
  end

  def sign_out
    delete session_path
  end
end