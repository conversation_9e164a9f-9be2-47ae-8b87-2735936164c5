require "test_helper"

class EmailCommunicationWorkflowsTest < ActionDispatch::IntegrationTest
  def setup
    @vendor_user = users(:one)
    @vendor_account = accounts(:one)
    @agency_user = users(:two)
    @agency_account = accounts(:two)
    @admin_user = users(:admin)
    @product = products(:published_product)
    @password = "password"
    
    # Clear any existing deliveries
    ActionMailer::Base.deliveries.clear
  end

  # Test 18: Email Integration Testing
  test "welcome emails for new users" do
    # Test vendor registration welcome email
    registration_params = {
      user: {
        first_name: "New",
        last_name: "Vendor",
        email_address: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123",
        job_title: "CEO",
        account_name: "New Vendor Company"
      },
      account_type: "vendor"
    }

    assert_difference "ActionMailer::Base.deliveries.size", 2 do
      post registrations_path, params: registration_params
    end

    # Get the welcome email (should be one of the emails sent)
    welcome_email = ActionMailer::Base.deliveries.find { |email| email.to.include?("<EMAIL>") }
    assert_equal "<EMAIL>", welcome_email.to.first
    assert_includes welcome_email.subject.downcase, "welcome"
    
    # For multipart emails, we need to check the specific part
    email_body = if welcome_email.multipart?
                   welcome_email.html_part.body.to_s
                 else
                   welcome_email.body.to_s
                 end
    
    assert_includes email_body, "New"  # User's first name
    assert_includes email_body, "Platia"  # Platform name should be in email
  end

  test "lead notification emails to vendors" do
    sign_in_as(@agency_user)
    
    lead_params = {
      lead: {
        contact_name: "Jane Agency",
        contact_email: "<EMAIL>",
        contact_company: "Department of Technology",
        contact_phone: "555-1234",
        message: "We are interested in your solution",
        budget_range: "$50,000 - $100,000",
        timeline: "Q2 2024"
      }
    }

    # Email functionality might not be implemented yet
    initial_count = ActionMailer::Base.deliveries.size
    post marketplace_product_leads_path(@product), params: lead_params
    
    if ActionMailer::Base.deliveries.size > initial_count
      lead_email = ActionMailer::Base.deliveries.last
      assert_equal @vendor_user.email_address, lead_email.to.first
      assert_includes lead_email.subject.downcase, "new lead"
      assert_includes lead_email.body.to_s, "Jane Agency"
      assert_includes lead_email.body.to_s, @product.name
      assert_includes lead_email.body.to_s, "Department of Technology"
    else
      skip "Lead notification emails not implemented yet"
    end
  end

  test "account approval rejection emails" do
    # Create pending account
    pending_user = User.create!(
      first_name: "Pending",
      last_name: "User",
      email_address: "<EMAIL>",
      password: @password
    )

    pending_account = Account.create!(
      name: "Pending Vendor",
      account_type: "vendor",
      status: "pending",
      owner: pending_user
    )

    sign_in_as(@admin_user)

    # Test approval email
    assert_difference "ActionMailer::Base.deliveries.size", 1 do
      post approve_admin_account_path(pending_account)
    end

    approval_email = ActionMailer::Base.deliveries.last
    assert_equal pending_user.email_address, approval_email.to.first
    assert_includes approval_email.subject.downcase, "approved"
    assert_includes approval_email.body.to_s, "Pending Vendor"

    # Reset for rejection test
    ActionMailer::Base.deliveries.clear
    pending_account.update!(status: "pending")

    # Test rejection email
    assert_difference "ActionMailer::Base.deliveries.size", 1 do
      post reject_admin_account_path(pending_account)
    end

    rejection_email = ActionMailer::Base.deliveries.last
    assert_equal pending_user.email_address, rejection_email.to.first
    assert_includes rejection_email.subject.downcase, "rejected"
  end

  test "team invitation emails" do
    sign_in_as(@vendor_user)

    invitation_params = {
      team_invitation: {
        email: "<EMAIL>",
        first_name: "New",
        last_name: "Member",
        role: "member"
      }
    }

    assert_difference "ActionMailer::Base.deliveries.size", 1 do
      post create_team_invitation_account_path, params: invitation_params
    end

    invitation_email = ActionMailer::Base.deliveries.last
    assert_equal "<EMAIL>", invitation_email.to.first
    assert_includes invitation_email.subject.downcase, "invitation"
    assert_includes invitation_email.body.to_s, @vendor_account.name
    assert_includes invitation_email.body.to_s, "New Member"
    
    # Should include invitation link with token
    invitation = TeamInvitation.last
    assert_includes invitation_email.body.to_s, invitation.token
  end

  test "password reset emails" do
    user = @vendor_user

    assert_difference "ActionMailer::Base.deliveries.size", 1 do
      post passwords_path, params: { email_address: user.email_address }
    end

    reset_email = ActionMailer::Base.deliveries.last
    assert_equal user.email_address, reset_email.to.first
    assert_includes reset_email.subject.downcase, "password"
    
    # Handle multipart emails properly
    email_body = if reset_email.multipart?
                   reset_email.html_part.body.to_s
                 else
                   reset_email.body.to_s
                 end
    
    assert_includes email_body, user.first_name
    # Should include reset token/link
    assert_includes email_body, "reset"
  end

  test "account invitation emails" do
    sign_in_as(@admin_user)

    invitation_params = {
      account_invitation: {
        email: "<EMAIL>",
        first_name: "Invited",
        last_name: "User",
        account_name: "Invited Company",
        account_type: "vendor"
      }
    }

    assert_difference "ActionMailer::Base.deliveries.size", 1 do
      post admin_account_invitations_path, params: invitation_params
    end

    invitation_email = ActionMailer::Base.deliveries.last
    assert_equal "<EMAIL>", invitation_email.to.first
    assert_includes invitation_email.subject.downcase, "invitation"
    assert_includes invitation_email.body.to_s, "Invited User"
    assert_includes invitation_email.body.to_s, "Invited Company"
    
    invitation = AccountInvitation.last
    assert_includes invitation_email.body.to_s, invitation.token
  end

  test "lead status update emails" do
    # Create a lead
    lead = Lead.create!(
      product: @product,
      account: @vendor_account,
      agency_account: @agency_account,
      user: @agency_user,
      contact_name: "Test Contact",
      contact_email: "<EMAIL>",
      contact_company: "Test Department",
      message: "Test inquiry",
      status: "pending"
    )

    sign_in_as(@vendor_user)

    # Update lead status - might send notification to agency
    initial_count = ActionMailer::Base.deliveries.size
    patch vendors_lead_path(lead), params: {
      lead: {
        status: "contacted",
        notes: "We have reached out via phone"
      }
    }

    if ActionMailer::Base.deliveries.size > initial_count
      status_email = ActionMailer::Base.deliveries.last
      assert_equal @agency_user.email_address, status_email.to.first
      assert_includes status_email.subject.downcase, "update"
      assert_includes status_email.body.to_s, @product.name
      assert_includes status_email.body.to_s, "contacted"
    else
      skip "Lead status update emails not implemented yet"
    end
  end

  test "email delivery failure handling" do
    # Simulate email delivery failure
    invalid_email_user = User.create!(
      first_name: "Invalid",
      last_name: "Email",
      email_address: "<EMAIL>",
      password: @password
    )

    # Try to send password reset to invalid email
    # In a real app, this would be handled by the email delivery system
    post passwords_path, params: { email_address: invalid_email_user.email_address }
    
    # Should handle gracefully (not crash the application)
    assert_response :redirect
    follow_redirect!
    # Check for success message or error handling
    assert_select ".notice, .alert", text: /password|reset|sent/i
  end

  test "email template rendering and content" do
    # Test that email templates render correctly
    sign_in_as(@agency_user)

    lead_params = {
      lead: {
        contact_name: "Template Test",
        contact_email: "<EMAIL>",
        contact_company: "Template Department",
        message: "Testing email template rendering with special characters: <>&\"'",
        budget_range: "$10,000 - $25,000"
      }
    }

    initial_count = ActionMailer::Base.deliveries.size
    post marketplace_product_leads_path(@product), params: lead_params

    if ActionMailer::Base.deliveries.size > initial_count
      email = ActionMailer::Base.deliveries.last
      email_body = email.body.to_s
    else
      skip "Email templates not testable - no emails sent"
      return
    end

    # Check that special characters are properly escaped
    assert_includes email_body, "Template Test"
    assert_includes email_body, @product.name
    assert_not_includes email_body, "<script>" # XSS protection
    
    # Check for proper HTML structure
    assert_includes email_body, "<html>" if email.content_type.include?("html")
  end

  test "email frequency and rate limiting" do
    skip "Email rate limiting not implemented" unless defined?(Throttle)
    
    # Test that users can't spam emails
    sign_in_as(@vendor_user)

    # Try to send multiple team invitations rapidly
    5.times do |i|
      post create_team_invitation_account_path(@vendor_account), params: {
        team_invitation: {
          email: "spam#{i}@example.com",
          first_name: "Spam",
          last_name: "User#{i}",
          role: "member"
        }
      }
    end

    # Should have rate limiting in place
    assert ActionMailer::Base.deliveries.size < 5, "Email rate limiting not working"
  end

  test "email unsubscribe functionality" do
    skip "Unsubscribe not implemented" unless respond_to?(:unsubscribe_path)
    
    # Test that emails include unsubscribe links
    sign_in_as(@agency_user)

    lead_params = {
      lead: {
        contact_name: "Unsubscribe Test",
        contact_email: "<EMAIL>",
        contact_company: "Test Department",
        message: "Testing unsubscribe functionality"
      }
    }

    post marketplace_product_leads_path(@product), params: lead_params

    email = ActionMailer::Base.deliveries.last
    assert_includes email.body.to_s, "unsubscribe"
  end

  test "email personalization and localization" do
    # Test that emails are personalized correctly
    sign_in_as(@agency_user)

    lead_params = {
      lead: {
        contact_name: "Personalization Test",
        contact_email: "<EMAIL>",
        contact_company: "Personalization Department",
        message: "Testing email personalization"
      }
    }

    initial_count = ActionMailer::Base.deliveries.size
    post marketplace_product_leads_path(@product), params: lead_params

    if ActionMailer::Base.deliveries.size > initial_count
      email = ActionMailer::Base.deliveries.last
      email_body = email.body.to_s
    else
      skip "Email personalization not testable - no emails sent"
      return
    end

    # Should include personalized content
    assert_includes email_body, @vendor_user.first_name
    assert_includes email_body, @vendor_account.name
    assert_includes email_body, "Personalization Test"
  end

  test "bulk email operations" do
    skip "Bulk email not implemented" unless defined?(BulkEmailJob)
    
    sign_in_as(@admin_user)

    # Test sending bulk notifications
    pending_accounts = [
      Account.create!(name: "Bulk Test 1", account_type: "vendor", status: "pending", owner: @vendor_user),
      Account.create!(name: "Bulk Test 2", account_type: "vendor", status: "pending", owner: @vendor_user)
    ]

    # Bulk approve accounts
    pending_accounts.each do |account|
      patch admin_account_path(account, action: :approve)
    end

    # Should send individual emails for each approval
    assert ActionMailer::Base.deliveries.size >= 2
  end

  test "email analytics and tracking" do
    skip "Email analytics not implemented" unless defined?(EmailAnalytics)
    
    # Test that email opens/clicks are tracked
    sign_in_as(@agency_user)

    post marketplace_product_leads_path(@product), params: {
      lead: {
        contact_name: "Analytics Test",
        contact_email: "<EMAIL>",
        contact_company: "Analytics Department",
        message: "Testing email analytics"
      }
    }

    email = ActionMailer::Base.deliveries.last
    
    # Should include tracking pixels or UTM parameters
    assert_includes email.body.to_s, "utm_" if email.body.to_s.include?("utm_")
  end

  test "email delivery status verification" do
    # Test that email delivery status is properly handled
    sign_in_as(@vendor_user)

    invitation_params = {
      team_invitation: {
        email: "<EMAIL>",
        first_name: "Delivery",
        last_name: "Test",
        role: "member"
      }
    }

    initial_count = ActionMailer::Base.deliveries.size
    post create_team_invitation_account_path, params: invitation_params

    # Email should be in deliveries queue if functionality is implemented
    assert ActionMailer::Base.deliveries.size >= initial_count
    
    email = ActionMailer::Base.deliveries.last
    assert_equal "<EMAIL>", email.to.first
    
    # In production, would check delivery status with email service
  end

  private

  def sign_in_as(user)
    post session_path, params: {
      email_address: user.email_address,
      password: @password
    }
  end

  def sign_out
    delete session_path
  end
end