require "test_helper"

class AdminUserFlowsTest < ActionDispatch::IntegrationTest
  def setup
    @admin_user = users(:admin)
    @vendor_account = accounts(:pending_vendor)
    @agency_account = accounts(:government_account)
    @vendor_product = products(:one)
    @password = "password"
    sign_in_as(@admin_user)
  end

  # Test 8: Account Management
  test "review and approve vendor registrations" do
    get admin_accounts_path
    assert_response :success
    assert_select "h1", text: /Account Management/

    # Should show pending accounts - using actual table structure
    assert_select "a", text: @vendor_account.name
    assert_select "td", text: /pending/i

    # Test navigation to account show page instead of approving directly from index
    get admin_account_path(@vendor_account)
    assert_response :success
    assert_select "h1", text: @vendor_account.name
  end

  test "review and reject agency registrations" do
    pending_agency = Account.create!(
      name: "Pending Agency",
      account_type: "agency", 
      status: "pending",
      owner: users(:two)
    )

    get admin_account_path(pending_agency)
    assert_response :success
    assert_select "h1", text: pending_agency.name

    # Reject account
    patch admin_account_path(pending_agency, action: :reject)
    assert_redirected_to admin_account_path(pending_agency)

    pending_agency.reload
    assert_equal "rejected", pending_agency.status
    assert_equal @admin_user, pending_agency.approved_by
  end

  test "manage user accounts and permissions" do
    user = users(:one)
    
    get admin_account_path(@vendor_account)
    assert_response :success

    # View team members
    get team_members_admin_account_path(@vendor_account)
    assert_response :success
    assert_select "h1", text: /Team Management/

    # Add new team member
    get new_team_member_admin_account_path(@vendor_account)
    assert_response :success

    member_params = {
      user: {
        first_name: "New",
        last_name: "Member", 
        email_address: "<EMAIL>",
        password: "password123",
        job_title: "Developer"
      },
      role: "member"
    }

    assert_difference "User.count", 1 do
      post create_team_member_admin_account_path(@vendor_account), params: member_params
    end

    new_user = User.find_by(email_address: "<EMAIL>")
    assert new_user.present?
    assert @vendor_account.users.include?(new_user)
  end

  test "send account invitations" do
    get admin_account_invitations_path
    assert_response :success
    assert_select "h1", text: /Account Invitations/

    get new_admin_account_invitation_path
    assert_response :success

    invitation_params = {
      account_invitation: {
        email: "<EMAIL>",
        first_name: "Invited",
        last_name: "User",
        account_name: "New Invited Company",
        account_type: "vendor"
      }
    }

    assert_difference "AccountInvitation.count", 1 do
      post admin_account_invitations_path, params: invitation_params
    end

    invitation = AccountInvitation.last
    assert_equal "<EMAIL>", invitation.email
    assert invitation.token.present?
    assert_redirected_to admin_account_invitations_path
  end

  # Test 9: Content Management
  test "product moderation and approval" do
    get admin_products_path
    assert_response :success
    assert_select "h1", text: /Product Management/

    # Should show all products including unpublished - using actual table structure
    assert_select "a", text: @vendor_product.name

    # View product details
    get admin_product_path(@vendor_product)
    assert_response :success
    assert_select "h1", text: @vendor_product.name

    # Edit product as admin
    get edit_admin_product_path(@vendor_product)
    assert_response :success

    patch admin_product_path(@vendor_product), params: {
      product: {
        published: true,
        name: "Admin Updated Product"
      }
    }

    @vendor_product.reload
    assert @vendor_product.published?
    assert_equal "Admin Updated Product", @vendor_product.name
  end

  test "category management create edit organize" do
    get admin_categories_path
    assert_response :success
    assert_select "h1", text: /Categories/

    # Create new category
    get new_admin_category_path
    assert_response :success

    category_params = {
      category: {
        name: "New Category",
        description: "Category for testing"
      }
    }

    assert_difference "Category.count", 1 do
      post admin_categories_path, params: category_params
    end

    category = Category.last
    assert_equal "New Category", category.name
    assert_redirected_to admin_category_path(category)

    # Edit category
    get edit_admin_category_path(category)
    assert_response :success

    patch admin_category_path(category), params: {
      category: { name: "Updated Category" }
    }

    category.reload
    assert_equal "Updated Category", category.name
  end

  test "certification management" do
    get admin_certifications_path
    assert_response :success
    assert_select "h1", text: /Certifications/

    # Create certification
    certification_params = {
      certification: {
        name: "FedRAMP",
        description: "Federal Risk and Authorization Management Program"
      }
    }

    assert_difference "Certification.count", 1 do
      post admin_certifications_path, params: certification_params
    end

    certification = Certification.last
    assert_equal "FedRAMP", certification.name
  end

  test "product claims review and approval" do
    # Create a product claim
    claim = ProductClaim.create!(
      product: @vendor_product,
      name: "John Claimer",
      email: "<EMAIL>",
      company: "Claimer Corp",
      title: "CEO",
      message: "This is our product",
      status: "pending"
    )

    get admin_product_claims_path
    assert_response :success
    assert_select "h1", text: /Product Claims/

    get admin_product_claim_path(claim)
    assert_response :success
    assert_select "h1", text: /Product Claim Request/

    # Admin can review and approve/reject claims
    assert_select ".claim-status", text: /pending/i
    assert_select ".claim-actions"
  end

  # Test 10: System Administration  
  test "dashboard overview with key metrics" do
    get admin_root_path
    assert_response :success
    assert_select "h1", text: /System Overview/

    # Should show key metrics (using actual CSS structure)
    assert_select "dt", text: /Total Users/
    assert_select "dt", text: /Total Accounts/
    assert_select "dt", text: /Products/
    assert_select "dt", text: /Total Leads/

    # Should show recent activity section
    assert_select "h3", text: /Recent Activity/
  end

  test "lead management across all vendors" do
    get admin_leads_path
    assert_response :success
    assert_select "h1", text: /Lead Management/

    # Should show leads from all vendors - using actual table structure
    # The table has proper structure with headers: Contact Product Company Budget Range Timeline Status Date Actions
    assert_select "th", text: /Contact/
    assert_select "th", text: /Product/
    assert_select "th", text: /Status/

    # View individual lead
    lead = leads(:pending_lead)
    get admin_lead_path(lead)
    assert_response :success
    # Check for lead details page content
    assert_select "h1", minimum: 1
  end

  test "system health monitoring" do
    get admin_health_path
    assert_response :success
    
    # Should show system status
    assert_select ".health-check"
  end

  test "user activity monitoring" do
    # Test viewing user activity logs
    get admin_accounts_path
    assert_response :success

    # View specific account activity
    get admin_account_path(@vendor_account)
    assert_response :success
    
    # Should show account creation date, approval status, etc.
    assert_select ".account-timeline"
  end

  test "admin can access all user areas for oversight" do
    # Admin should be able to access vendor areas
    get vendors_root_path
    assert_response :success

    # Admin should be able to access agency areas  
    get agencies_root_path
    assert_response :success

    # Admin should be able to view marketplace
    get marketplace_path
    assert_response :success
  end

  test "bulk account operations" do
    pending_accounts = [
      accounts(:pending_vendor),
      Account.create!(name: "Another Pending", account_type: "vendor", status: "pending", owner: users(:one))
    ]

    get admin_accounts_path
    assert_response :success

    # Test bulk approval (if implemented)
    # This would require JavaScript or specific bulk action routes
    pending_accounts.each do |account|
      patch admin_account_path(account, action: :approve)
      account.reload
      assert_equal "approved", account.status
    end
  end

  test "admin permissions and security" do
    # Verify admin can perform privileged operations
    assert @admin_user.super_admin?

    # Test that admin routes are protected
    sign_out
    
    get admin_root_path
    assert_redirected_to new_session_path

    # Sign back in for other tests
    sign_in_as(@admin_user)
  end

  test "data export and reporting functionality" do
    skip "Data export not implemented yet" unless respond_to?(:export_accounts_path)
    
    # Test exporting account data
    get export_accounts_path, params: { format: :csv }
    assert_response :success
    assert_equal "text/csv", response.content_type

    # Test exporting leads data
    get export_leads_path, params: { format: :csv }
    assert_response :success
  end

  private

  def sign_in_as(user)
    post session_path, params: {
      email_address: user.email_address,
      password: @password
    }
  end

  def sign_out
    delete session_path
  end
end