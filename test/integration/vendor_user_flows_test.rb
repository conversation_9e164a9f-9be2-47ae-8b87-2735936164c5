require "test_helper"

class VendorUserFlowsTest < ActionDispatch::IntegrationTest
  def setup
    @vendor_user = users(:one)
    @vendor_account = accounts(:one)
    @password = "password"
    sign_in_as(@vendor_user)
  end

  # Test 3: Vendor Onboarding & Profile Setup
  test "complete vendor account creation and approval process" do
    sign_out
    
    # Register as new vendor
    get registrations_new_path
    assert_response :success

    vendor_params = {
      user: {
        first_name: "New",
        last_name: "Vendor",
        email_address: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123",
        job_title: "CEO"
      },
      account: {
        name: "New Tech Company",
        account_type: "vendor",
        description: "Innovative technology solutions"
      }
    }

    assert_difference ["User.count", "Account.count"], 1 do
      post registrations_path, params: vendor_params
    end

    new_user = User.find_by(email_address: "<EMAIL>")
    new_account = new_user.owned_accounts.first
    
    assert_equal "pending", new_account.status
    assert_redirected_to pending_path
  end

  test "profile setup with company info and logo upload" do
    get edit_account_account_path
    assert_response :success
    assert_select "form"

    # Test updating account information
    patch update_account_account_path, params: {
      account: {
        name: "Updated Company Name",
        description: "Updated description",
        headquarters_location: "San Francisco, CA",
        linkedin_url: "https://linkedin.com/company/updated"
      }
    }

    @vendor_account.reload
    assert_equal "Updated Company Name", @vendor_account.name
    assert_equal "San Francisco, CA", @vendor_account.headquarters_location
    assert_redirected_to account_path
  end

  test "team member invitation and management" do
    # Access team management
    get team_members_account_path
    assert_response :success
    assert_select "h1", text: /Team Members/

    # Create team invitation
    get new_team_invitation_account_path
    assert_response :success

    invitation_params = {
      team_invitation: {
        email: "<EMAIL>",
        role: "member",
        message: "Welcome to our team!"
      }
    }

    assert_difference "TeamInvitation.count", 1 do
      post create_team_invitation_account_path, params: invitation_params
    end

    invitation = TeamInvitation.last
    assert_equal "<EMAIL>", invitation.email
    assert_equal @vendor_account, invitation.account
    assert_redirected_to team_members_account_path
  end

  # Test 4: Product Management Lifecycle
  test "create new product with all components" do
    get new_vendors_product_path
    assert_response :success
    assert_select "form[action='#{vendors_products_path}']"

    product_params = {
      product: {
        name: "New CRM Solution",
        description: "Advanced customer relationship management",
        website: "https://example.com/crm"
      }
    }

    assert_difference "Product.count", 1 do
      post vendors_products_path, params: product_params
    end

    product = Product.last
    assert_equal "New CRM Solution", product.name
    assert_equal @vendor_account, product.account
    assert_redirected_to vendors_product_path(product)
  end

  test "add product media and content" do
    product = products(:one)
    
    # Add product features
    get new_vendors_product_product_feature_path(product)
    assert_response :success

    feature_params = {
      product_feature: {
        name: "Advanced Analytics",
        description: "Comprehensive data analysis tools"
      }
    }

    assert_difference "ProductFeature.count", 1 do
      post vendors_product_product_features_path(product), params: feature_params
    end

    # Add product videos
    video_params = {
      product_video: {
        title: "Product Demo",
        description: "Overview of key features",
        video_file: fixture_file_upload("test_video.mp4", "video/mp4")
      }
    }

    assert_difference "ProductVideo.count", 1 do
      post vendors_product_product_videos_path(product), params: video_params
    end

    # Add case studies
    case_study_params = {
      case_study: {
        title: "City Implementation",
        description: "Successful deployment in major city",
        upload: fixture_file_upload("test_document.pdf", "application/pdf")
      }
    }

    assert_difference "CaseStudy.count", 1 do
      post vendors_product_case_studies_path(product), params: case_study_params
    end
  end

  test "product publishing workflow" do
    product = products(:one)  # This product belongs to account one and is unpublished
    assert_not product.published?

    # Edit product
    get edit_vendors_product_path(product)
    assert_response :success

    # Publish product
    patch vendors_product_path(product), params: {
      product: { published: true }
    }

    product.reload
    assert product.published?
    assert_redirected_to vendors_product_path(product)
  end

  test "product editing and updating" do
    product = products(:one)
    
    get edit_vendors_product_path(product)
    assert_response :success

    patch vendors_product_path(product), params: {
      product: {
        name: "Updated Product Name",
        description: "Updated description"
      }
    }

    product.reload
    assert_equal "Updated Product Name", product.name
    assert_redirected_to vendors_product_path(product)
  end

  # Test 5: Lead Management
  test "view incoming leads from marketplace" do
    get vendors_leads_path
    assert_response :success
    assert_select "dt", text: /Total/

    # Should show leads for vendor's products
    assert_select "td", text: /John Smith/
    assert_select "td", text: /<EMAIL>/
  end

  test "update lead status and add notes" do
    lead = leads(:pending_lead)
    
    get vendors_lead_path(lead)
    assert_response :success
    assert_select "h1", text: /Lead Details/

    # Update lead status
    patch vendors_lead_path(lead), params: {
      lead: {
        status: "contacted",
        notes: "Initial contact made via phone"
      }
    }

    lead.reload
    assert_equal "contacted", lead.status
    assert_includes lead.notes, "Initial contact made"
    assert_redirected_to vendors_lead_path(lead)
  end

  test "lead filtering and search" do
    get vendors_leads_path
    assert_response :success

    # Test status filtering
    get vendors_leads_path, params: { status: "pending" }
    assert_response :success
    
    # Should only show pending leads
    assert_select "td", text: /John Smith/
  end

  test "vendor dashboard overview" do
    get vendors_root_path
    assert_response :success
    assert_select "dt", text: /Total Products/

    # Should show key metrics
    assert_select "dt", text: /Products/
    assert_select "dt", text: /Leads/
  end

  test "vendor cannot access other vendor's data" do
    other_vendor_account = accounts(:vendor_account)
    other_product = products(:draft_product)
    
    # Try to access other vendor's product - should redirect to error page
    get edit_vendors_product_path(other_product)
    assert_redirected_to "/errors/not_found"
    
    # Try to access other vendor's leads - should redirect to error page
    other_lead = leads(:contacted_lead)
    get vendors_lead_path(other_lead)
    assert_redirected_to "/errors/not_found"
  end

  private

  def sign_in_as(user)
    post session_path, params: {
      email_address: user.email_address,
      password: @password
    }
  end

  def sign_out
    delete session_path
  end
end