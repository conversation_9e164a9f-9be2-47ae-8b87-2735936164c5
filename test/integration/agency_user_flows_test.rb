require "test_helper"

class AgencyUserFlowsTest < ActionDispatch::IntegrationTest
  def setup
    @agency_user = users(:two)
    @agency_account = accounts(:two)
    @vendor_product = products(:published_product)
    @password = "password"
    sign_in_as(@agency_user)
  end

  # Test 6: Marketplace Browsing & Discovery
  test "browse marketplace homepage" do
    get marketplace_path
    assert_response :success
    assert_select "h1", text: /Discover Government Technology Solutions/
    assert_select ".bg-gradient-to-r", text: /Government Technology/
    
    # Should show categories if any exist
    # Note: categories come from the controller's @root_categories
    assert_select ".category-item", minimum: 0
  end

  test "category-based product filtering" do
    # Get the first root category that exists
    root_category = Category.root_categories.first
    # Associate product with category
    ProductCategory.create!(product: @vendor_product, category: root_category)
    
    get marketplace_categories_path
    assert_response :success
    assert_select "h1", text: /Categories/

    # Click on category
    get marketplace_category_path(root_category)
    assert_response :success
    assert_select "h1", text: /#{root_category.name}/
    
    # Should show products in this category - using actual view structure
    assert_select ".group", minimum: 0
  end

  test "search functionality with various queries" do
    # Search for something that should match our published product
    get marketplace_search_path, params: { q: "Email" }
    assert_response :success
    assert_select "h1", text: /Search Results/
    
    # Test empty search
    get marketplace_search_path, params: { q: "" }
    assert_response :success
    assert_select "p", text: /Enter a search term/
    
    # Test no results
    get marketplace_search_path, params: { q: "nonexistentproduct" }
    assert_response :success
    assert_select "h3", text: /No results found/
  end

  test "product detail page viewing" do
    get marketplace_product_path(@vendor_product)
    assert_response :success
    assert_select "h1", text: @vendor_product.name
    
    # Should show product details in the actual structure
    assert_select "p", text: /by #{@vendor_product.company_name}/
    assert_select ".rounded-full", minimum: 1  # Categories/features shown as pills
    
    # Should show features if any
    if @vendor_product.features.present?
      @vendor_product.feature_names.each do |feature_name|
        assert_select ".rounded-full", text: feature_name
      end
    end
  end

  test "company vendor profile viewing" do
    get marketplace_vendor_products_path(@vendor_product.account)
    assert_response :success
    assert_select "h1", text: /Products by #{@vendor_product.account.name}/
    
    # Should show vendor's products - using the actual structure from the view
    assert_select "p", text: /Showing.*product/
  end

  # Test 7: Interest Expression & Lead Creation
  test "express interest in products create leads" do
    get marketplace_product_path(@vendor_product)
    assert_response :success

    # Fill out interest form with required fields only
    lead_params = {
      lead: {
        contact_name: "Jane Agency",
        contact_email: "<EMAIL>",
        contact_company: "Department of Technology", 
        message: "We are interested in this solution for our department"
      }
    }

    assert_difference "Lead.count", 1 do
      post marketplace_product_leads_path(@vendor_product), params: lead_params
    end

    lead = Lead.last
    assert_equal @vendor_product, lead.product
    assert_equal @agency_account, lead.agency_account
    assert_equal @agency_user, lead.user
    assert_equal "pending", lead.status
    
    assert_redirected_to marketplace_product_path(@vendor_product)
    follow_redirect!
    # Check for flash message in the structure that actually exists
    assert_select ".bg-green-50", text: /Interest.*Submitted/i
  end

  test "fill out lead forms with contact information" do
    get marketplace_product_path(@vendor_product)
    assert_response :success

    # Test form validation - check that form exists first
    assert_select "form[action='#{marketplace_product_leads_path(@vendor_product)}']"
    
    # Test successful form submission with valid data
    lead_params = {
      lead: {
        contact_name: "Test User",
        contact_email: "<EMAIL>",
        contact_company: "Test Agency",
        message: "Test message"
      }
    }

    assert_difference "Lead.count", 1 do
      post marketplace_product_leads_path(@vendor_product), params: lead_params
    end

    # Should redirect on success
    assert_redirected_to marketplace_product_path(@vendor_product)
  end

  test "view submitted interests and leads" do
    # Create a lead for this agency user
    lead = Lead.create!(
      product: @vendor_product,
      agency_account: @agency_account,
      user: @agency_user,
      contact_name: "Jane Agency",
      contact_email: "<EMAIL>",
      contact_company: "Department of Technology",
      message: "Test interest",
      status: "pending"
    )

    get agencies_interests_path
    assert_response :success
    assert_select "h1", text: /My Product Interests/
    
    # Should show user's submitted interests - using actual view structure
    assert_select "a", text: @vendor_product.name
    assert_select ".rounded-full", text: /pending/i
  end

  test "track lead status updates" do
    # Create a lead with 'contacted' status
    lead = Lead.create!(
      product: @vendor_product,
      agency_account: @agency_account,
      user: @agency_user,
      contact_name: "Jane Agency",
      contact_email: "<EMAIL>",
      contact_company: "Department of Technology", 
      message: "Test interest",
      status: "contacted"
    )

    get agencies_interests_path
    assert_response :success
    
    # Should show current status using actual view structure
    assert_select ".rounded-full", text: /contacted/i
  end

  test "agency dashboard shows relevant information" do
    get agencies_dashboard_path
    assert_response :success
    
    # Should show agency-specific statistics
    assert_select "dt", text: /Total Interests/
    assert_select ".bg-white.dark\\:bg-gray-800", minimum: 1
  end

  test "agency user cannot access vendor areas" do
    # Try to access vendor dashboard
    get vendors_root_path
    assert_redirected_to root_path
    
    # Try to access vendor products
    get vendors_products_path
    assert_redirected_to root_path
    
    # Try to access vendor leads
    get vendors_leads_path
    assert_redirected_to root_path
  end

  test "agency user cannot access admin areas" do
    # Try to access admin dashboard
    get admin_root_path
    assert_redirected_to root_path
    
    # Try to access admin accounts
    get admin_accounts_path
    assert_redirected_to root_path
  end

  test "marketplace search with filters and pagination" do
    # Test category filter
    category = Category.create!(name: "Analytics", slug: "analytics")
    
    get marketplace_products_path, params: { category: category.slug }
    assert_response :success
    
    # Test pagination (if implemented)
    get marketplace_products_path, params: { page: 2 }
    assert_response :success
  end

  test "product comparison and bookmarking functionality" do
    # This test assumes comparison/bookmarking features exist
    skip "Product comparison not implemented yet" unless defined?(ProductComparison)
    
    product1 = @vendor_product
    product2 = products(:agency_product)
    
    # Add products to comparison
    post compare_products_path, params: { product_ids: [product1.id, product2.id] }
    assert_response :success
    
    # View comparison
    get compare_products_path
    assert_response :success
    assert_select ".comparison-table"
  end

  test "agency specific features and permissions" do
    # Test agency-specific views or features
    get agencies_root_path
    assert_response :success
    
    # Should show dashboard content
    assert_select "dt", minimum: 1
    
    # Verify agency user has access to their dashboard
    assert_select ".bg-white.dark\\:bg-gray-800", minimum: 1
  end

  private

  def sign_in_as(user)
    post session_path, params: {
      email_address: user.email_address,
      password: @password
    }
  end

  def sign_out
    delete session_path
  end
end