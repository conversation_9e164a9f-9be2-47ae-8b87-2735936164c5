# == Schema Information
#
# Table name: product_features
#
#  id          :integer          not null, primary key
#  description :text
#  name        :string           not null
#  position    :integer          default(0)
#  published   :boolean          default(FALSE)
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  product_id  :integer          not null
#
# Indexes
#
#  index_product_features_on_product_id               (product_id)
#  index_product_features_on_product_id_and_position  (product_id,position)
#
# Foreign Keys
#
#  product_id  (product_id => products.id)
#
require "test_helper"

class ProductFeatureTest < ActiveSupport::TestCase
  def setup
    @user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "<PERSON>",
      last_name: "Vendor"
    )
    
    @account = Account.create!(
      name: "Test Company",
      account_type: "vendor",
      status: "approved",
      approved_at: 1.week.ago,
      owner: @user
    )
    
    @product = Product.create!(
      name: "Test Product",
      account: @account
    )
    
    @product_feature = ProductFeature.new(
      name: "Test Feature",
      description: "A test feature",
      product: @product
    )
  end

  # === VALIDATION TESTS ===
  
  test "should be valid with minimal attributes" do
    feature = ProductFeature.new(
      name: "Minimal Feature",
      product: @product
    )
    assert feature.valid?
  end

  test "should be valid with valid attributes" do
    assert @product_feature.valid?
  end

  test "should require name" do
    @product_feature.name = nil
    assert_not @product_feature.valid?
    assert_includes @product_feature.errors[:name], "can't be blank"
  end

  test "should require name to not be empty string" do
    @product_feature.name = ""
    assert_not @product_feature.valid?
    assert_includes @product_feature.errors[:name], "can't be blank"
  end

  test "should require name to not be only whitespace" do
    @product_feature.name = "   "
    assert_not @product_feature.valid?
    assert_includes @product_feature.errors[:name], "can't be blank"
  end

  test "should require product" do
    @product_feature.product = nil
    assert_not @product_feature.valid?
    assert_includes @product_feature.errors[:product], "must exist"
  end

  test "should validate position when explicitly set to nil" do
    # Setting position to nil should fail validation since position is required
    feature = ProductFeature.new(
      name: "Test Feature",
      product: @product,
      position: nil
    )
    assert_not feature.valid?
    assert_includes feature.errors[:position], "can't be blank"
  end

  test "should validate position is numeric" do
    @product_feature.position = "not_a_number"
    assert_not @product_feature.valid?
    assert_includes @product_feature.errors[:position], "is not a number"
  end

  test "should validate position is greater than or equal to zero" do
    @product_feature.position = -1
    assert_not @product_feature.valid?
    assert_includes @product_feature.errors[:position], "must be greater than or equal to 0"
  end

  test "should allow position to be zero" do
    @product_feature.position = 0
    assert @product_feature.valid?
  end

  test "should allow position to be positive integer" do
    @product_feature.position = 5
    assert @product_feature.valid?
  end

  test "should allow very long names" do
    @product_feature.name = "A" * 255
    assert @product_feature.valid?
  end

  test "should allow very long descriptions" do
    @product_feature.description = "A" * 10000
    assert @product_feature.valid?
  end

  test "should allow nil description" do
    @product_feature.description = nil
    assert @product_feature.valid?
  end

  # === BUSINESS LOGIC TESTS ===
  
  test "should create features with explicit positions" do
    feature1 = @product.product_features.create!(name: "Feature 1", position: 1)
    feature2 = @product.product_features.create!(name: "Feature 2", position: 2) 
    feature3 = @product.product_features.create!(name: "Feature 3", position: 3)
    
    assert_equal 1, feature1.position
    assert_equal 2, feature2.position
    assert_equal 3, feature3.position
  end

  test "should not auto-assign position when position is already set" do
    existing_feature = @product.product_features.create!(name: "Existing", position: 5)
    new_feature = @product.product_features.create!(name: "New", position: 2)
    
    assert_equal 5, existing_feature.position
    assert_equal 2, new_feature.position
  end

  test "should allow manual position assignment" do
    # Create features with gaps in positions
    @product.product_features.create!(name: "Feature 1", position: 1)
    @product.product_features.create!(name: "Feature 2", position: 5)
    
    # New feature with explicit position
    new_feature = @product.product_features.create!(name: "New Feature", position: 10)
    assert_equal 10, new_feature.position
  end

  test "should handle explicit position for new product" do
    new_product = Product.create!(name: "Empty Product", account: @account)
    feature = new_product.product_features.create!(name: "First Feature", position: 1)
    
    assert_equal 1, feature.position
  end

  test "should return correct published status" do
    @product_feature.published = true
    assert @product_feature.published?
    
    @product_feature.published = false
    assert_not @product_feature.published?
    
    @product_feature.published = nil
    assert_not @product_feature.published?
  end

  # === SCOPE TESTS ===
  
  test "should have ordered scope" do
    feature1 = @product.product_features.create!(name: "Feature 1", position: 3)
    feature2 = @product.product_features.create!(name: "Feature 2", position: 1)
    feature3 = @product.product_features.create!(name: "Feature 3", position: 2)
    
    ordered_features = @product.product_features.ordered
    assert_equal [feature2, feature3, feature1], ordered_features.to_a
  end

  test "should have published scope" do
    published_feature = @product.product_features.create!(name: "Published", published: true)
    draft_feature = @product.product_features.create!(name: "Draft", published: false)
    nil_published_feature = @product.product_features.create!(name: "Nil Published")
    
    published_features = @product.product_features.published
    assert_includes published_features, published_feature
    assert_not_includes published_features, draft_feature
    assert_not_includes published_features, nil_published_feature
  end

  test "should chain scopes correctly" do
    feature1 = @product.product_features.create!(name: "Feature 1", position: 2, published: true)
    feature2 = @product.product_features.create!(name: "Feature 2", position: 1, published: true)
    feature3 = @product.product_features.create!(name: "Feature 3", position: 3, published: false)
    
    published_ordered = @product.product_features.published.ordered
    assert_equal [feature2, feature1], published_ordered.to_a
  end

  # === ATTACHMENT TESTS ===
  
  test "should detect attachment type for images" do
    @product_feature.save!
    
    # Simulate an attached image file
    @product_feature.attachment.attach(
      io: StringIO.new("fake image data"),
      filename: "test.jpg",
      content_type: "image/jpeg"
    )
    
    assert_equal "image", @product_feature.attachment_type
    assert @product_feature.image?
    assert_not @product_feature.video?
  end

  test "should detect attachment type for videos" do
    @product_feature.save!
    
    # Simulate an attached video file
    @product_feature.attachment.attach(
      io: StringIO.new("fake video data"),
      filename: "test.mp4",
      content_type: "video/mp4"
    )
    
    assert_equal "video", @product_feature.attachment_type
    assert @product_feature.video?
    assert_not @product_feature.image?
  end

  test "should return other for non-image/video attachments" do
    @product_feature.save!
    
    # Simulate an attached document file
    @product_feature.attachment.attach(
      io: StringIO.new("fake document data"),
      filename: "test.pdf",
      content_type: "application/pdf"
    )
    
    assert_equal "other", @product_feature.attachment_type
    assert_not @product_feature.image?
    assert_not @product_feature.video?
  end

  test "should return nil attachment_type when no attachment" do
    @product_feature.save!
    
    assert_nil @product_feature.attachment_type
    assert_not @product_feature.image?
    assert_not @product_feature.video?
  end

  test "should handle various image content types" do
    @product_feature.save!
    
    image_types = [
      "image/png",
      "image/gif",
      "image/webp",
      "image/svg+xml"
    ]
    
    image_types.each do |content_type|
      @product_feature.attachment.attach(
        io: StringIO.new("fake image data"),
        filename: "test.#{content_type.split('/').last}",
        content_type: content_type
      )
      
      assert_equal "image", @product_feature.attachment_type
      assert @product_feature.image?
      
      @product_feature.attachment.purge
    end
  end

  test "should handle various video content types" do
    @product_feature.save!
    
    video_types = [
      "video/avi",
      "video/quicktime",
      "video/webm",
      "video/x-msvideo"
    ]
    
    video_types.each do |content_type|
      @product_feature.attachment.attach(
        io: StringIO.new("fake video data"),
        filename: "test.#{content_type.split('/').last}",
        content_type: content_type
      )
      
      assert_equal "video", @product_feature.attachment_type
      assert @product_feature.video?
      
      @product_feature.attachment.purge
    end
  end

  test "should attach real image file" do
    @product_feature.save!
    
    file_path = Rails.root.join('test', 'fixtures', 'files', 'test_image.jpg')
    @product_feature.attachment.attach(
      io: File.open(file_path),
      filename: 'test_image.jpg',
      content_type: 'image/jpeg'
    )
    
    assert @product_feature.attachment.attached?
    assert_equal "image", @product_feature.attachment_type
    assert @product_feature.image?
  end

  test "should attach real video file" do
    @product_feature.save!
    
    file_path = Rails.root.join('test', 'fixtures', 'files', 'test_video.mp4')
    @product_feature.attachment.attach(
      io: File.open(file_path),
      filename: 'test_video.mp4',
      content_type: 'video/mp4'
    )
    
    assert @product_feature.attachment.attached?
    assert_equal "video", @product_feature.attachment_type
    assert @product_feature.video?
  end

  test "should attach real document file" do
    @product_feature.save!
    
    file_path = Rails.root.join('test', 'fixtures', 'files', 'test_document.pdf')
    @product_feature.attachment.attach(
      io: File.open(file_path),
      filename: 'test_document.pdf',
      content_type: 'application/pdf'
    )
    
    assert @product_feature.attachment.attached?
    assert_equal "other", @product_feature.attachment_type
    assert_not @product_feature.image?
    assert_not @product_feature.video?
  end

  test "should handle attachment with nil content type" do
    @product_feature.save!
    
    # In practice, Active Storage will assign a default content type
    # when one is not provided, so we need to simulate this differently
    @product_feature.attachment.attach(
      io: StringIO.new("data"),
      filename: "test.unknown"
      # content_type is omitted, Active Storage will determine it
    )
    
    # The attachment_type method should handle various content types gracefully
    result = @product_feature.attachment_type
    assert_not_nil result
    assert_includes ["image", "video", "other"], result
  end

  test "should handle attachment with empty content type" do
    @product_feature.save!
    
    @product_feature.attachment.attach(
      io: StringIO.new("data"),
      filename: "test.unknown",
      content_type: ""
    )
    
    assert_equal "other", @product_feature.attachment_type
  end

  # === ASSOCIATION TESTS ===
  
  test "should belong to product" do
    @product_feature.save!
    assert_equal @product, @product_feature.product
  end

  test "should be destroyed when product is destroyed" do
    @product_feature.save!
    feature_id = @product_feature.id
    
    @product.destroy!
    
    assert_not ProductFeature.exists?(feature_id)
  end

  test "should be accessible through product association" do
    @product_feature.save!
    
    assert_includes @product.product_features, @product_feature
    assert_equal @product_feature, @product.product_features.find(@product_feature.id)
  end

  test "should maintain association integrity" do
    @product_feature.save!
    original_product = @product_feature.product
    
    new_product = Product.create!(name: "New Product", account: @account)
    @product_feature.update!(product: new_product)
    
    assert_equal new_product, @product_feature.product
    assert_not_includes original_product.product_features.reload, @product_feature
    assert_includes new_product.product_features.reload, @product_feature
  end

  # === DEFAULT VALUES TESTS ===
  
  test "should have default published value" do
    feature = ProductFeature.new(name: "Test", product: @product)
    assert_equal false, feature.published
  end

  test "should have default position value" do
    feature = ProductFeature.new(name: "Test", product: @product)
    assert_equal 0, feature.position
  end

  test "should create with default position when no position specified" do
    feature = @product.product_features.create!(name: "Default Position")
    # Since position defaults to 0 in the database, and 0 is not nil,
    # the before_create callback won't trigger
    assert_equal 0, feature.position
  end

  # === EDGE CASE TESTS ===
  
  test "should handle multiple features with same position" do
    feature1 = @product.product_features.create!(name: "Feature 1", position: 1)
    feature2 = @product.product_features.create!(name: "Feature 2", position: 1)
    
    # Both should be valid and saved
    assert feature1.persisted?
    assert feature2.persisted?
    assert_equal 1, feature1.position
    assert_equal 1, feature2.position
  end

  test "should handle very large position numbers" do
    @product_feature.position = 999999999
    assert @product_feature.valid?
  end

  test "should handle decimal positions" do
    @product_feature.position = 1.5
    assert @product_feature.valid?
    # Note: SQLite converts decimal to integer for integer columns
    # In production with PostgreSQL, this might behave differently
    assert @product_feature.position.is_a?(Numeric)
  end

  test "should handle special characters in name" do
    special_names = [
      "Feature with émojis 🚀",
      "Feature & Symbols!",
      "Feature (with parentheses)",
      "Feature-with-hyphens",
      "Feature_with_underscores",
      "Feature with 'quotes'",
      'Feature with "double quotes"'
    ]
    
    special_names.each do |name|
      feature = ProductFeature.new(name: name, product: @product)
      assert feature.valid?, "Should be valid with name: #{name}"
    end
  end

  test "should handle unicode characters in description" do
    @product_feature.description = "Unicode test: 中文, العربية, 🌟, Ω, ∑"
    assert @product_feature.valid?
  end

  # === DATABASE CONSTRAINT TESTS ===
  
  test "should enforce database constraints" do
    # Test that we can't create a feature without a product_id at the database level
    assert_raises(ActiveRecord::RecordInvalid) do
      ProductFeature.create!(name: "Test", product_id: nil)
    end
  end

  test "should enforce foreign key constraint" do
    # Test that we can't create a feature with non-existent product_id
    # In Rails with validations, this will raise RecordInvalid before hitting the database
    assert_raises(ActiveRecord::RecordInvalid) do
      ProductFeature.create!(name: "Test", product_id: 99999)
    end
  end

  # === PERFORMANCE TESTS ===
  
  test "should efficiently query ordered features" do
    # Create multiple features
    10.times do |i|
      @product.product_features.create!(name: "Feature #{i}", position: i)
    end
    
    # This should use the database index
    ordered_features = @product.product_features.ordered
    assert_equal 10, ordered_features.count
    
    # Verify they're actually ordered
    positions = ordered_features.pluck(:position)
    assert_equal positions.sort, positions
  end

  test "should efficiently query published features" do
    # Create mix of published and unpublished features
    5.times do |i|
      @product.product_features.create!(name: "Published #{i}", published: true)
      @product.product_features.create!(name: "Draft #{i}", published: false)
    end
    
    published_features = @product.product_features.published
    assert_equal 5, published_features.count
    
    published_features.each do |feature|
      assert feature.published?
    end
  end

  # === ATTACHMENT CLEANUP TESTS ===
  
  test "should clean up attachments when feature is destroyed" do
    @product_feature.save!
    
    @product_feature.attachment.attach(
      io: StringIO.new("fake data"),
      filename: "test.jpg",
      content_type: "image/jpeg"
    )
    
    assert @product_feature.attachment.attached?
    
    @product_feature.destroy!
    
    # Attachment should be cleaned up (this is handled by Active Storage automatically)
    assert_not ProductFeature.exists?(@product_feature.id)
  end
end
