# == Schema Information
#
# Table name: sessions
#
#  id         :integer          not null, primary key
#  ip_address :string
#  user_agent :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  user_id    :integer          not null
#
# Indexes
#
#  index_sessions_on_user_id  (user_id)
#
# Foreign Keys
#
#  user_id  (user_id => users.id)
#
require "test_helper"

class SessionTest < ActiveSupport::TestCase
  def setup
    @user = users(:one)
    @session = Session.new(
      user: @user,
      ip_address: "***********",
      user_agent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
    )
  end

  # ===============================
  # ASSOCIATION TESTS
  # ===============================

  test "should belong to user" do
    assert_respond_to @session, :user
    assert_instance_of User, @session.user
  end

  test "should be invalid without user" do
    @session.user = nil
    assert_not @session.valid?, "Session should be invalid without user"
    assert_includes @session.errors[:user], "must exist"
  end

  # ===============================
  # VALIDATION TESTS
  # ===============================

  test "should be valid with valid attributes" do
    assert @session.valid?, "Session should be valid with all required attributes"
  end

  test "should be valid without ip_address" do
    @session.ip_address = nil
    assert @session.valid?, "Session should be valid without ip_address"

    @session.ip_address = ""
    assert @session.valid?, "Session should be valid with empty ip_address"
  end

  test "should be valid without user_agent" do
    @session.user_agent = nil
    assert @session.valid?, "Session should be valid without user_agent"

    @session.user_agent = ""
    assert @session.valid?, "Session should be valid with empty user_agent"
  end

  # ===============================
  # ATTRIBUTE TESTS
  # ===============================

  test "should allow setting and getting all attributes" do
    session = Session.new(
      user: users(:two),
      ip_address: "********",
      user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    )

    assert_equal users(:two), session.user
    assert_equal "********", session.ip_address
    assert_equal "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", session.user_agent
  end

  test "should handle various IP address formats" do
    valid_ips = [
      "127.0.0.1",
      "***********",
      "********",
      "**********",
      "***********",
      "2001:db8:85a3::8a2e:370:7334", # IPv6
      "::1", # IPv6 localhost
      "fe80::1%lo0" # IPv6 with scope
    ]

    valid_ips.each do |ip|
      @session.ip_address = ip
      assert @session.valid?, "#{ip} should be a valid IP address format"
    end
  end

  test "should handle long user agent strings" do
    long_user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59" * 5

    @session.user_agent = long_user_agent
    assert @session.valid?, "Session should be valid with long user_agent"
    assert_equal long_user_agent, @session.user_agent
  end

  test "should handle special characters in user_agent" do
    special_user_agents = [
      "Custom-Bot/1.0 (+http://example.com/bot)",
      "User-Agent: Mozilla/5.0; charset=UTF-8",
      "Bot with spaces and (parentheses) & symbols!",
      "Mozilla/5.0 [en] (X11; U; Linux x86_64; en-US)",
      "AppName/1.0 CFNetwork/1240.0.4 Darwin/20.6.0"
    ]

    special_user_agents.each do |ua|
      @session.user_agent = ua
      assert @session.valid?, "#{ua} should be valid user_agent"
    end
  end

  test "should handle unicode characters" do
    unicode_ip = "***********" # IP addresses don't typically contain unicode
    unicode_user_agent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) 测试浏览器/1.0"

    @session.ip_address = unicode_ip
    @session.user_agent = unicode_user_agent

    assert @session.valid?, "Session should be valid with unicode characters"
  end

  # ===============================
  # TIMESTAMPS TESTS
  # ===============================

  test "should automatically set created_at and updated_at" do
    session = Session.create!(
      user: @user,
      ip_address: "***********",
      user_agent: "Test Agent"
    )

    assert_not_nil session.created_at, "created_at should be set automatically"
    assert_not_nil session.updated_at, "updated_at should be set automatically"
    assert_kind_of Time, session.created_at
    assert_kind_of Time, session.updated_at
  end

  test "should update updated_at when session is modified" do
    session = Session.create!(
      user: @user,
      ip_address: "***********",
      user_agent: "Test Agent"
    )

    original_updated_at = session.updated_at
    
    # Wait a moment to ensure timestamp difference
    sleep(0.01)
    
    session.update!(ip_address: "***********")
    
    assert session.updated_at > original_updated_at, "updated_at should be updated when session is modified"
  end

  # ===============================
  # EDGE CASE TESTS
  # ===============================

  test "should handle nil values for optional fields" do
    session = Session.new(
      user: @user,
      ip_address: nil,
      user_agent: nil
    )

    assert session.valid?, "Session should be valid with nil optional fields"
  end

  test "should handle empty strings for optional fields" do
    session = Session.new(
      user: @user,
      ip_address: "",
      user_agent: ""
    )

    assert session.valid?, "Session should be valid with empty optional fields"
  end

  test "should handle very long ip address strings" do
    # Test with a very long string that might come from proxy headers
    long_ip = "***********, ********, **********, ***********" * 10

    @session.ip_address = long_ip
    assert @session.valid?, "Session should handle long IP address strings"
  end

  test "should handle malformed but string ip addresses" do
    malformed_ips = [
      "999.999.999.999",
      "not.an.ip.address",
      "192.168.1",
      "***********.1",
      "***********:8080",
      "http://***********"
    ]

    malformed_ips.each do |ip|
      @session.ip_address = ip
      # We're just storing as string, so these should be valid
      assert @session.valid?, "#{ip} should be storable as string"
    end
  end

  # ===============================
  # ASSOCIATION CASCADE TESTS
  # ===============================

  test "should be destroyed when user is destroyed" do
    user = User.create!(
      first_name: "Test",
      last_name: "User",
      email_address: "<EMAIL>",
      password: "password123"
    )

    session = Session.create!(
      user: user,
      ip_address: "***********",
      user_agent: "Test Agent"
    )

    session_id = session.id

    user.destroy

    assert_raises(ActiveRecord::RecordNotFound) do
      Session.find(session_id)
    end
  end

  # ===============================
  # DATABASE CONSTRAINT TESTS
  # ===============================

  test "should enforce foreign key constraint" do
    # This test verifies that the association is set up correctly
    assert_nothing_raised do
      @session.user
    end
  end

  test "should save and reload correctly" do
    assert @session.save, "Session should save successfully"
    
    reloaded_session = Session.find(@session.id)
    
    # Compare key attributes (excluding id which changes after save and timestamps)
    assert_equal @session.user_id, reloaded_session.user_id, "User ID should match"
    assert_equal @session.ip_address, reloaded_session.ip_address, "IP address should match"
    assert_equal @session.user_agent, reloaded_session.user_agent, "User agent should match"
    assert_not_nil reloaded_session.created_at, "Created at should be set"
    assert_not_nil reloaded_session.updated_at, "Updated at should be set"
  end

  test "should handle concurrent session creation for same user" do
    # Test that multiple sessions can be created for the same user
    sessions = []
    
    5.times do |i|
      sessions << Session.create!(
        user: @user,
        ip_address: "192.168.1.#{i + 1}",
        user_agent: "Agent #{i + 1}"
      )
    end

    assert_equal 5, sessions.count
    assert_equal @user, sessions.first.user
    assert_equal @user, sessions.last.user
  end

  # ===============================
  # SECURITY-RELATED TESTS
  # ===============================

  test "should store potentially malicious user agent strings safely" do
    malicious_user_agents = [
      "<script>alert('xss')</script>",
      "'; DROP TABLE sessions; --",
      "User-Agent: ../../../etc/passwd",
      "Mozilla/5.0 \x00\x01\x02\x03",
      "#{"\x00" * 100}Mozilla/5.0"
    ]

    malicious_user_agents.each do |ua|
      @session.user_agent = ua
      assert @session.valid?, "Should safely store malicious user agent: #{ua}"
      
      if @session.save
        reloaded = Session.find(@session.id)
        # The string should be stored as-is (Rails handles escaping)
        assert_equal ua, reloaded.user_agent, "Should preserve malicious user agent exactly"
      end
    end
  end

  test "should store potentially malicious ip addresses safely" do
    malicious_ips = [
      "'; DROP TABLE sessions; --",
      "<script>alert('xss')</script>",
      "***********'; UPDATE users SET email_address='<EMAIL>",
      "\x00\x01\x02***********"
    ]

    malicious_ips.each do |ip|
      @session.ip_address = ip
      assert @session.valid?, "Should safely store malicious IP: #{ip}"
      
      if @session.save
        reloaded = Session.find(@session.id)
        assert_equal ip, reloaded.ip_address, "Should preserve malicious IP exactly"
      end
    end
  end
end