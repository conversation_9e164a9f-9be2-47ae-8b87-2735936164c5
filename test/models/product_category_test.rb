# == Schema Information
#
# Table name: product_categories
#
#  id          :integer          not null, primary key
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  category_id :integer          not null
#  product_id  :integer          not null
#
# Indexes
#
#  index_product_categories_on_category_id                 (category_id)
#  index_product_categories_on_product_id                  (product_id)
#  index_product_categories_on_product_id_and_category_id  (product_id,category_id) UNIQUE
#
# Foreign Keys
#
#  category_id  (category_id => categories.id)
#  product_id   (product_id => products.id)
#
require "test_helper"

class ProductCategoryTest < ActiveSupport::TestCase
  def setup
    # Create user and account for product
    @user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Test",
      last_name: "User"
    )
    
    @account = Account.create!(
      name: "Test Company",
      account_type: "vendor",
      status: "approved",
      owner: @user
    )
    
    @product = Product.create!(
      name: "Test Product",
      account: @account
    )
    
    @category = Category.create!(
      name: "Technology",
      description: "Technology products and services"
    )
    
    @product_category = ProductCategory.new(
      product: @product,
      category: @category
    )
  end

  # ===============================
  # BASIC VALIDATION TESTS
  # ===============================

  test "should be valid with valid attributes" do
    assert @product_category.valid?
  end

  test "should require product" do
    @product_category.product = nil
    assert_not @product_category.valid?
    assert_includes @product_category.errors[:product], "must exist"
  end

  test "should require category" do
    @product_category.category = nil
    assert_not @product_category.valid?
    assert_includes @product_category.errors[:category], "must exist"
  end

  test "should validate product_id and category_id uniqueness" do
    @product_category.save!
    
    # Try to create duplicate product-category combination
    duplicate_product_category = ProductCategory.new(
      product: @product,
      category: @category
    )
    
    assert_not duplicate_product_category.valid?
    assert_includes duplicate_product_category.errors[:product_id], "has already been taken"
  end

  test "should allow same product with different categories" do
    @product_category.save!
    
    other_category = Category.create!(
      name: "Software",
      description: "Software solutions"
    )
    
    other_product_category = ProductCategory.new(
      product: @product,
      category: other_category
    )
    
    assert other_product_category.valid?
  end

  test "should allow same category with different products" do
    @product_category.save!
    
    other_product = Product.create!(
      name: "Other Product",
      account: @account
    )
    
    other_product_category = ProductCategory.new(
      product: other_product,
      category: @category
    )
    
    assert other_product_category.valid?
  end

  # ===============================
  # ASSOCIATION TESTS
  # ===============================

  test "should belong to product" do
    assert_respond_to @product_category, :product
    assert_instance_of Product, @product_category.product
  end

  test "should belong to category" do
    assert_respond_to @product_category, :category
    assert_instance_of Category, @product_category.category
  end

  test "should be accessible through product categories association" do
    @product_category.save!
    
    assert_includes @product.product_categories, @product_category
    assert_includes @product.categories, @category
  end

  test "should be accessible through category product_categories association" do
    @product_category.save!
    
    assert_includes @category.product_categories, @product_category
    assert_includes @category.products, @product
  end

  # ===============================
  # DATABASE CONSTRAINT TESTS
  # ===============================

  test "should enforce foreign key constraints" do
    assert_nothing_raised do
      @product_category.product
      @product_category.category
    end
  end

  test "should save and reload correctly" do
    assert @product_category.save, "ProductCategory should save successfully"
    
    reloaded_product_category = ProductCategory.find(@product_category.id)
    
    assert_equal @product_category.product_id, reloaded_product_category.product_id
    assert_equal @product_category.category_id, reloaded_product_category.category_id
    assert_not_nil reloaded_product_category.created_at
    assert_not_nil reloaded_product_category.updated_at
  end

  test "should handle timestamps correctly" do
    before_create = Time.current
    @product_category.save!
    after_create = Time.current
    
    assert @product_category.created_at >= before_create
    assert @product_category.created_at <= after_create
    assert @product_category.updated_at >= before_create
    assert @product_category.updated_at <= after_create
  end

  test "should update updated_at when modified" do
    @product_category.save!
    original_updated_at = @product_category.updated_at
    
    # Wait a moment to ensure timestamp difference
    sleep(0.01)
    
    # Touch to update timestamps
    @product_category.touch
    
    assert @product_category.updated_at > original_updated_at
  end

  # ===============================
  # BUSINESS LOGIC TESTS
  # ===============================

  test "should support multiple categories per product" do
    @product_category.save!
    
    # Add second category to same product
    software_category = Category.create!(name: "Software", description: "Software solutions")
    hardware_category = Category.create!(name: "Hardware", description: "Hardware solutions")
    
    ProductCategory.create!(product: @product, category: software_category)
    ProductCategory.create!(product: @product, category: hardware_category)
    
    product_categories = ProductCategory.where(product: @product)
    assert_equal 3, product_categories.count
    
    category_names = @product.categories.pluck(:name).sort
    assert_equal ["Hardware", "Software", "Technology"], category_names
  end

  test "should support multiple products per category" do
    @product_category.save!
    
    # Add second product to same category
    product2 = Product.create!(name: "Product 2", account: @account)
    product3 = Product.create!(name: "Product 3", account: @account)
    
    ProductCategory.create!(product: product2, category: @category)
    ProductCategory.create!(product: product3, category: @category)
    
    category_products = ProductCategory.where(category: @category)
    assert_equal 3, category_products.count
    
    product_names = @category.products.pluck(:name).sort
    assert_equal ["Product 2", "Product 3", "Test Product"], product_names
  end

  test "should handle product deletion" do
    @product_category.save!
    product_id = @product.id
    
    # Delete product should also delete product_category (dependent: :destroy)
    @product.destroy
    
    assert_raises(ActiveRecord::RecordNotFound) { Product.find(product_id) }
    assert_raises(ActiveRecord::RecordNotFound) { ProductCategory.find(@product_category.id) }
  end

  test "should handle category deletion" do
    @product_category.save!
    category_id = @category.id
    
    # Delete category should also delete product_category (dependent: :destroy)
    @category.destroy
    
    assert_raises(ActiveRecord::RecordNotFound) { Category.find(category_id) }
    assert_raises(ActiveRecord::RecordNotFound) { ProductCategory.find(@product_category.id) }
  end

  # ===============================
  # EDGE CASE TESTS
  # ===============================

  test "should handle large number of product-category relationships" do
    # Create multiple categories and assign them to the product
    categories = []
    10.times do |i|
      category = Category.create!(name: "Category #{i}", description: "Description #{i}")
      categories << category
      ProductCategory.create!(product: @product, category: category)
    end
    
    assert_equal 10, @product.categories.count
    categories.each do |category|
      assert_includes @product.categories, category
    end
  end

  test "should handle hierarchical categories" do
    # Test with parent and child categories
    parent_category = Category.create!(name: "Parent Tech")
    child_category = Category.create!(name: "Child Tech", parent: parent_category)
    
    ProductCategory.create!(product: @product, category: parent_category)
    ProductCategory.create!(product: @product, category: child_category)
    
    assert_includes @product.categories, parent_category
    assert_includes @product.categories, child_category
    assert_equal 2, @product.categories.count
  end

  # ===============================
  # QUERY TESTS
  # ===============================

  test "should support finding products by category" do
    @product_category.save!
    
    products_in_category = Product.joins(:categories).where(categories: { id: @category.id })
    assert_includes products_in_category, @product
  end

  test "should support finding categories by product" do
    @product_category.save!
    
    categories_for_product = Category.joins(:products).where(products: { id: @product.id })
    assert_includes categories_for_product, @category
  end

  test "should support counting relationships" do
    @product_category.save!
    
    # Add more relationships
    software_category = Category.create!(name: "Software")
    ProductCategory.create!(product: @product, category: software_category)
    
    assert_equal 2, @product.product_categories.count
    assert_equal 2, @product.categories.count
    assert_equal 1, @category.product_categories.count
    assert_equal 1, @category.products.count
  end

  # ===============================
  # INTEGRATION TESTS
  # ===============================

  test "should work with product creation through categories association" do
    # Test creating product category through product's categories association
    new_category = Category.create!(name: "New Category")
    
    @product.categories << new_category
    
    assert_includes @product.categories, new_category
    assert_includes new_category.products, @product
    
    # Should create ProductCategory record
    product_category = ProductCategory.find_by(product: @product, category: new_category)
    assert_not_nil product_category
  end

  test "should work with category creation through products association" do
    # Test creating product category through category's products association
    new_product = Product.create!(name: "New Product", account: @account)
    
    @category.products << new_product
    
    assert_includes @category.products, new_product
    assert_includes new_product.categories, @category
    
    # Should create ProductCategory record
    product_category = ProductCategory.find_by(product: new_product, category: @category)
    assert_not_nil product_category
  end

  test "should handle batch operations" do
    # Create multiple categories
    categories = []
    5.times do |i|
      categories << Category.create!(name: "Batch Category #{i}")
    end
    
    # Batch assign categories to product
    @product.categories = categories
    
    assert_equal 5, @product.categories.count
    assert_equal 5, ProductCategory.where(product: @product).count
    
    categories.each do |category|
      assert_includes @product.categories, category
    end
  end
end
