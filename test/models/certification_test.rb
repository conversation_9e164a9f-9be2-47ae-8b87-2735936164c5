# == Schema Information
#
# Table name: certifications
#
#  id          :integer          not null, primary key
#  description :text
#  name        :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#
require "test_helper"

class CertificationTest < ActiveSupport::TestCase
  def setup
    @certification = Certification.new(
      name: "ISO 27001 #{SecureRandom.hex(4)}",
      description: "Information security management systems certification"
    )
  end

  # ===============================
  # BASIC VALIDATION TESTS
  # ===============================

  test "should be valid with valid attributes" do
    assert @certification.valid?
  end

  test "should require name" do
    @certification.name = nil
    assert_not @certification.valid?
    assert_includes @certification.errors[:name], "can't be blank"

    @certification.name = ""
    assert_not @certification.valid?
    assert_includes @certification.errors[:name], "can't be blank"

    @certification.name = "   "
    assert_not @certification.valid?
    assert_includes @certification.errors[:name], "can't be blank"
  end

  test "should validate name uniqueness" do
    @certification.save!
    
    duplicate_certification = Certification.new(
      name: "ISO 27001",
      description: "Another ISO certification"
    )
    
    assert_not duplicate_certification.valid?
    assert_includes duplicate_certification.errors[:name], "has already been taken"
  end

  test "should validate name uniqueness case sensitively" do
    @certification.save!
    
    # Different case should be allowed (case-sensitive uniqueness)
    different_case_certification = Certification.new(
      name: @certification.name.downcase,  # Use the same name in lowercase
      description: "Lowercase name"
    )
    
    assert different_case_certification.valid?, "Different case should be allowed with case-sensitive uniqueness"
  end

  test "should allow nil description" do
    @certification.description = nil
    assert @certification.valid?
  end

  test "should allow empty description" do
    @certification.description = ""
    assert @certification.valid?
  end

  test "should validate description length" do
    @certification.description = "A" * 1001
    assert_not @certification.valid?
    assert_includes @certification.errors[:description], "is too long (maximum is 1000 characters)"

    @certification.description = "A" * 1000
    assert @certification.valid?
  end

  # ===============================
  # ASSOCIATION TESTS
  # ===============================

  test "should have many product_certifications" do
    @certification.save!
    
    # Create user, account and product for testing
    user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Test",
      last_name: "User"
    )
    account = Account.create!(
      name: "Test Account",
      account_type: "vendor",
      status: "approved",
      owner: user
    )
    product = Product.create!(name: "Test Product", account: account)
    
    pc1 = ProductCertification.create!(product: product, certification: @certification)
    pc2 = ProductCertification.create!(
      product: Product.create!(name: "Another Product", account: account),
      certification: @certification
    )
    
    assert_includes @certification.product_certifications, pc1
    assert_includes @certification.product_certifications, pc2
    assert_equal 2, @certification.product_certifications.count
  end

  test "should have many products through product_certifications" do
    @certification.save!
    
    # Create user, account and products for testing
    user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Test",
      last_name: "User"
    )
    account = Account.create!(
      name: "Test Account",
      account_type: "vendor",
      status: "approved",
      owner: user
    )
    
    product1 = Product.create!(name: "Product 1", account: account)
    product2 = Product.create!(name: "Product 2", account: account)
    
    @certification.products << product1
    @certification.products << product2
    
    assert_includes @certification.products, product1
    assert_includes @certification.products, product2
    assert_equal 2, @certification.products.count
  end

  # ===============================
  # SCOPE TESTS
  # ===============================

  test "should have alphabetical scope" do
    # Clear existing certifications and their associations
    ProductCertification.delete_all
    Certification.delete_all
    
    cert_z = Certification.create!(name: "Zebra Certification", description: "Z cert")
    cert_a = Certification.create!(name: "Alpha Certification", description: "A cert") 
    cert_b = Certification.create!(name: "Beta Certification", description: "B cert")
    
    alphabetical = Certification.alphabetical.pluck(:name)
    assert_equal ["Alpha Certification", "Beta Certification", "Zebra Certification"], alphabetical
  end

  test "should have with_products scope" do
    @certification.save!
    
    # Create certification without products
    cert_without_products = Certification.create!(
      name: "Unused Certification",
      description: "No products have this"
    )
    
    # Create certification with products
    user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Test",
      last_name: "User"
    )
    account = Account.create!(
      name: "Test Account",
      account_type: "vendor",
      status: "approved",
      owner: user
    )
    product = Product.create!(name: "Test Product", account: account)
    
    @certification.products << product
    
    certs_with_products = Certification.with_products
    assert_includes certs_with_products, @certification
    assert_not_includes certs_with_products, cert_without_products
  end

  # ===============================
  # INSTANCE METHOD TESTS
  # ===============================

  test "should return products count" do
    @certification.save!
    
    # No products initially
    assert_equal 0, @certification.products_count
    
    # Add products
    user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Test",
      last_name: "User"
    )
    account = Account.create!(
      name: "Test Account",
      account_type: "vendor",
      status: "approved",
      owner: user
    )
    
    3.times do |i|
      product = Product.create!(name: "Product #{i}", account: account)
      @certification.products << product
    end
    
    assert_equal 3, @certification.products_count
  end

  # ===============================
  # DEPENDENT DESTROY TESTS
  # ===============================

  test "should destroy product_certifications when certification is destroyed" do
    @certification.save!
    
    user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Test",
      last_name: "User"
    )
    account = Account.create!(
      name: "Test Account",
      account_type: "vendor",
      status: "approved",
      owner: user
    )
    product = Product.create!(name: "Test Product", account: account)
    
    product_cert = ProductCertification.create!(
      product: product,
      certification: @certification
    )
    product_cert_id = product_cert.id
    
    @certification.destroy
    
    assert_raises(ActiveRecord::RecordNotFound) { ProductCertification.find(product_cert_id) }
    # Product should still exist
    assert_nothing_raised { Product.find(product.id) }
  end

  # ===============================
  # EDGE CASE TESTS
  # ===============================

  test "should handle special characters in name" do
    special_names = [
      "ISO 27001:2013",
      "SOC 2® Type II",
      "GDPR/CCPA Compliance",
      "PCI-DSS Level 1",
      "HIPAA & HITECH"
    ]
    
    special_names.each do |name|
      cert = Certification.new(name: name, description: "Test certification")
      assert cert.valid?, "#{name} should be a valid certification name"
    end
  end

  test "should handle unicode characters" do
    unicode_text = "Certificación Técnica 🏆"
    
    @certification.name = unicode_text
    @certification.description = "Unicode description: héllo wörld 你好"
    
    assert @certification.valid?, "Certification should be valid with unicode characters"
  end

  test "should handle very long names" do
    long_name = "A" * 255
    
    @certification.name = long_name
    assert @certification.valid?, "Certification should be valid with long name"
  end

  test "should handle leading and trailing whitespace in name" do
    @certification.name = "  ISO 27001  "
    @certification.save!
    
    # Name should be stored with whitespace preserved
    assert_equal "  ISO 27001  ", @certification.name
  end

  # ===============================
  # BUSINESS LOGIC TESTS
  # ===============================

  test "should support multiple products with same certification" do
    @certification.save!
    
    user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Test",
      last_name: "User"
    )
    account = Account.create!(
      name: "Test Account",
      account_type: "vendor",
      status: "approved",
      owner: user
    )
    
    products = []
    5.times do |i|
      product = Product.create!(name: "Product #{i}", account: account)
      products << product
      @certification.products << product
    end
    
    assert_equal 5, @certification.products.count
    products.each do |product|
      assert_includes @certification.products, product
    end
  end

  test "should handle product removal from certification" do
    @certification.save!
    
    user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Test",
      last_name: "User"
    )
    account = Account.create!(
      name: "Test Account",
      account_type: "vendor",
      status: "approved",
      owner: user
    )
    
    product1 = Product.create!(name: "Product 1", account: account)
    product2 = Product.create!(name: "Product 2", account: account)
    
    @certification.products << product1
    @certification.products << product2
    
    assert_equal 2, @certification.products.count
    
    # Remove one product
    @certification.products.delete(product1)
    
    assert_equal 1, @certification.products.count
    assert_not_includes @certification.products, product1
    assert_includes @certification.products, product2
  end

  # ===============================
  # DATABASE CONSTRAINT TESTS
  # ===============================

  test "should save and reload correctly" do
    @certification.save!
    
    reloaded_certification = Certification.find(@certification.id)
    
    assert_equal @certification.name, reloaded_certification.name
    assert_equal @certification.description, reloaded_certification.description
    assert_not_nil reloaded_certification.created_at
    assert_not_nil reloaded_certification.updated_at
  end

  test "should handle timestamps correctly" do
    before_create = Time.current
    @certification.save!
    after_create = Time.current
    
    assert @certification.created_at >= before_create
    assert @certification.created_at <= after_create
    assert @certification.updated_at >= before_create
    assert @certification.updated_at <= after_create
  end

  test "should update updated_at when modified" do
    @certification.save!
    original_updated_at = @certification.updated_at
    
    # Wait a moment to ensure timestamp difference
    sleep(0.01)
    
    @certification.update!(description: "Updated description")
    
    assert @certification.updated_at > original_updated_at
  end

  # ===============================
  # QUERY TESTS
  # ===============================

  test "should support finding by name" do
    @certification.save!
    
    found_cert = Certification.find_by(name: @certification.name)
    assert_equal @certification, found_cert
  end

  test "should support case-sensitive name searches" do
    @certification.save!
    
    # Exact case match should work
    found_cert = Certification.find_by(name: @certification.name)
    assert_not_nil found_cert
    
    # Different case should not match (case-sensitive database search)
    not_found = Certification.find_by(name: @certification.name.downcase)
    assert_nil not_found
  end

  test "should support description searches" do
    @certification.save!
    
    # Should be able to search in description
    found_certs = Certification.where("description LIKE ?", "%security%")
    assert_includes found_certs, @certification
  end
end