# == Schema Information
#
# Table name: leads
#
#  id                :integer          not null, primary key
#  budget_range      :string
#  contact_company   :string
#  contact_email     :string
#  contact_name      :string
#  contact_phone     :string
#  message           :text
#  notes             :text
#  source            :string
#  status            :string
#  timeline          :string
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  account_id        :integer
#  agency_account_id :integer
#  product_id        :integer          not null
#  user_id           :integer
#
# Indexes
#
#  index_leads_on_account_id         (account_id)
#  index_leads_on_agency_account_id  (agency_account_id)
#  index_leads_on_product_id         (product_id)
#  index_leads_on_user_id            (user_id)
#
# Foreign Keys
#
#  account_id         (account_id => accounts.id)
#  agency_account_id  (agency_account_id => accounts.id)
#  product_id         (product_id => products.id)
#  user_id            (user_id => users.id)
#
require "test_helper"

class LeadTest < ActiveSupport::TestCase
  def setup
    @lead = leads(:pending_lead)
    @minimal_lead = leads(:minimal_lead)
  end

  # ===============================
  # ASSOCIATION TESTS
  # ===============================

  test "should belong to product" do
    assert_respond_to @lead, :product
    assert_instance_of Product, @lead.product
  end

  test "should belong to account optionally" do
    assert_respond_to @lead, :account
    assert_instance_of Account, @lead.account

    # Test that account can be nil
    @minimal_lead.account = nil
    assert @minimal_lead.valid?, "Lead should be valid without account"
  end

  test "should belong to user optionally" do
    assert_respond_to @lead, :user
    assert_instance_of User, @lead.user

    # Test that user can be nil
    @minimal_lead.user = nil
    assert @minimal_lead.valid?, "Lead should be valid without user"
  end

  test "should belong to agency_account optionally" do
    assert_respond_to @lead, :agency_account
    assert_instance_of Account, @lead.agency_account

    # Test that agency_account can be nil
    @lead.agency_account = nil
    assert @lead.valid?, "Lead should be valid without agency_account"
  end

  # ===============================
  # VALIDATION TESTS
  # ===============================

  test "should be valid with valid attributes" do
    assert @lead.valid?, "Lead should be valid with all required attributes"
  end

  test "should be invalid without product" do
    @lead.product = nil
    assert_not @lead.valid?, "Lead should be invalid without product"
    assert_includes @lead.errors[:product], "must exist"
  end

  test "should be invalid without contact_name" do
    @lead.contact_name = nil
    assert_not @lead.valid?, "Lead should be invalid without contact_name"
    assert_includes @lead.errors[:contact_name], "can't be blank"

    @lead.contact_name = ""
    assert_not @lead.valid?, "Lead should be invalid with empty contact_name"
    assert_includes @lead.errors[:contact_name], "can't be blank"
  end

  test "should be invalid without contact_email" do
    @lead.contact_email = nil
    assert_not @lead.valid?, "Lead should be invalid without contact_email"
    assert_includes @lead.errors[:contact_email], "can't be blank"
  end

  test "should validate contact_email format" do
    # Valid email formats
    valid_emails = [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>"
    ]

    valid_emails.each do |email|
      @lead.contact_email = email
      assert @lead.valid?, "#{email} should be a valid email format"
    end

    # Invalid email formats
    invalid_emails = [
      "invalid_email",
      "@example.com",
      "user@",
      "user@.com",
      ""
    ]

    invalid_emails.each do |email|
      @lead.contact_email = email
      assert_not @lead.valid?, "#{email} should be invalid email format"
      assert_includes @lead.errors[:contact_email], "is invalid"
    end
  end

  test "should be invalid without contact_company on create" do
    new_lead = Lead.new(
      product: products(:one),
      contact_name: "Test User",
      contact_email: "<EMAIL>",
      status: "pending"
    )
    
    assert_not new_lead.valid?, "Lead should be invalid without contact_company on create"
    assert_includes new_lead.errors[:contact_company], "can't be blank"

    # But should be valid when updating existing lead
    @lead.contact_company = nil
    assert @lead.valid?, "Existing lead should be valid without contact_company on update"
  end

  test "should validate status inclusion" do
    valid_statuses = %w[pending contacted qualified closed]
    
    valid_statuses.each do |status|
      @lead.status = status
      assert @lead.valid?, "#{status} should be a valid status"
    end

    # Test nil and empty string (which Rails allows but our validation should catch)
    [nil, ""].each do |status|
      @lead.status = status
      assert_not @lead.valid?, "#{status.inspect} should be invalid status"
      assert_includes @lead.errors[:status], "is not included in the list"
    end

    # Test that Rails enum raises ArgumentError for invalid values
    assert_raises(ArgumentError, "Should raise ArgumentError for invalid enum value") do
      @lead.status = "invalid"
    end
  end

  # ===============================
  # ENUM TESTS
  # ===============================

  test "should have correct enum values" do
    assert_equal "pending", Lead.statuses[:pending]
    assert_equal "contacted", Lead.statuses[:contacted]
    assert_equal "qualified", Lead.statuses[:qualified]
    assert_equal "closed", Lead.statuses[:closed]
  end

  test "should respond to enum predicate methods" do
    lead = leads(:pending_lead)
    assert lead.pending?, "Lead with pending status should respond true to pending?"
    assert_not lead.contacted?, "Lead with pending status should respond false to contacted?"

    lead = leads(:contacted_lead)
    assert lead.contacted?, "Lead with contacted status should respond true to contacted?"
    assert_not lead.pending?, "Lead with contacted status should respond false to pending?"

    lead = leads(:qualified_lead)
    assert lead.qualified?, "Lead with qualified status should respond true to qualified?"
    assert_not lead.contacted?, "Lead with qualified status should respond false to contacted?"

    lead = leads(:closed_lead)
    assert lead.closed?, "Lead with closed status should respond true to closed?"
    assert_not lead.qualified?, "Lead with closed status should respond false to qualified?"
  end

  test "should allow setting status using enum methods" do
    @lead.pending!
    assert @lead.pending?, "Lead should be pending after calling pending!"

    @lead.contacted!
    assert @lead.contacted?, "Lead should be contacted after calling contacted!"

    @lead.qualified!
    assert @lead.qualified?, "Lead should be qualified after calling qualified!"

    @lead.closed!
    assert @lead.closed?, "Lead should be closed after calling closed!"
  end

  # ===============================
  # SCOPE TESTS
  # ===============================

  test "recent scope should order by created_at desc" do
    # Clear existing leads to ensure clean test
    Lead.destroy_all
    
    old_lead = Lead.create!(
      product: products(:one),
      contact_name: "Old Lead",
      contact_email: "<EMAIL>",
      contact_company: "Old Company",
      status: "pending",
      created_at: 2.days.ago
    )

    new_lead = Lead.create!(
      product: products(:one),
      contact_name: "New Lead",
      contact_email: "<EMAIL>",
      contact_company: "New Company",
      status: "pending",
      created_at: 1.day.ago
    )

    recent_leads = Lead.recent.limit(2)
    assert_equal new_lead.id, recent_leads.first.id, "Most recent lead should be first"
    assert_equal old_lead.id, recent_leads.second.id, "Older lead should be second"
  end

  # ===============================
  # ATTRIBUTE TESTS
  # ===============================

  test "should allow setting and getting all attributes" do
    lead = Lead.new(
      product: products(:one),
      account: accounts(:one),
      user: users(:one),
      agency_account: accounts(:two),
      contact_name: "Test Contact",
      contact_email: "<EMAIL>",
      contact_company: "Test Company",
      contact_phone: "(*************",
      message: "Test message",
      notes: "Test notes",
      budget_range: "$10,000 - $25,000",
      timeline: "6 months",
      source: "website",
      status: "pending"
    )

    assert_equal products(:one), lead.product
    assert_equal accounts(:one), lead.account
    assert_equal users(:one), lead.user
    assert_equal accounts(:two), lead.agency_account
    assert_equal "Test Contact", lead.contact_name
    assert_equal "<EMAIL>", lead.contact_email
    assert_equal "Test Company", lead.contact_company
    assert_equal "(*************", lead.contact_phone
    assert_equal "Test message", lead.message
    assert_equal "Test notes", lead.notes
    assert_equal "$10,000 - $25,000", lead.budget_range
    assert_equal "6 months", lead.timeline
    assert_equal "website", lead.source
    assert_equal "pending", lead.status
  end

  test "should allow long text in message and notes fields" do
    long_text = "A" * 10000  # 10,000 character string

    @lead.message = long_text
    @lead.notes = long_text

    assert @lead.valid?, "Lead should be valid with long text in message and notes"
    assert_equal long_text, @lead.message
    assert_equal long_text, @lead.notes
  end

  # ===============================
  # EDGE CASE TESTS
  # ===============================

  test "should handle nil values for optional fields" do
    @lead.account = nil
    @lead.user = nil
    @lead.agency_account = nil
    @lead.contact_phone = nil
    @lead.message = nil
    @lead.notes = nil
    @lead.budget_range = nil
    @lead.timeline = nil
    @lead.source = nil

    assert @lead.valid?, "Lead should be valid with nil optional fields"
  end

  test "should handle empty strings for optional fields" do
    @lead.contact_phone = ""
    @lead.message = ""
    @lead.notes = ""
    @lead.budget_range = ""
    @lead.timeline = ""
    @lead.source = ""

    assert @lead.valid?, "Lead should be valid with empty optional fields"
  end

  test "should handle special characters in text fields" do
    special_chars = "Special chars: !@#$%^&*()[]{}|;':\",./<>?`~"
    
    @lead.contact_name = special_chars
    @lead.contact_company = special_chars
    @lead.message = special_chars
    @lead.notes = special_chars

    assert @lead.valid?, "Lead should be valid with special characters"
  end

  test "should handle unicode characters" do
    unicode_text = "Unicode: héllo wörld 你好 🌟"
    
    @lead.contact_name = unicode_text
    @lead.contact_company = unicode_text
    @lead.message = unicode_text
    @lead.notes = unicode_text

    assert @lead.valid?, "Lead should be valid with unicode characters"
  end

  # ===============================
  # DATABASE CONSTRAINT TESTS
  # ===============================

  test "should enforce foreign key constraints" do
    # This would typically be tested in integration tests,
    # but we can verify the associations are set up correctly
    assert_nothing_raised do
      @lead.product
      @lead.account
      @lead.user
      @lead.agency_account
    end
  end

  test "should save and reload correctly" do
    original_attributes = @lead.attributes.dup
    
    assert @lead.save, "Lead should save successfully"
    
    reloaded_lead = Lead.find(@lead.id)
    
    # Compare all attributes except timestamps which might have slight differences
    comparable_attributes = original_attributes.except('created_at', 'updated_at')
    reloaded_attributes = reloaded_lead.attributes.except('created_at', 'updated_at')
    
    assert_equal comparable_attributes, reloaded_attributes, "Reloaded lead should have same attributes"
  end
end
