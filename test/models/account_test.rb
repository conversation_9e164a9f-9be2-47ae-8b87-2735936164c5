# == Schema Information
#
# Table name: accounts
#
#  id                    :integer          not null, primary key
#  account_type          :string
#  approved_at           :datetime
#  confirmation_sent_at  :datetime
#  confirmation_token    :string
#  confirmed_at          :datetime
#  description           :text
#  headquarters_location :string
#  linkedin_url          :string
#  name                  :string
#  slug                  :string
#  status                :string
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#  approved_by_id        :integer
#  owner_id              :integer          not null
#
# Indexes
#
#  index_accounts_on_approved_by_id      (approved_by_id)
#  index_accounts_on_confirmation_token  (confirmation_token) UNIQUE
#  index_accounts_on_owner_id            (owner_id)
#  index_accounts_on_slug                (slug)
#
# Foreign Keys
#
#  approved_by_id  (approved_by_id => users.id)
#  owner_id        (owner_id => users.id)
#
require "test_helper"
require "minitest/mock"

class AccountTest < ActiveSupport::TestCase
  def setup
    @user = users(:one)
    @account = Account.new(
      name: "Tech Solutions Inc",
      account_type: "vendor",
      status: "pending",
      owner: @user
    )
  end

  test "should be valid with valid attributes" do
    assert @account.valid?
  end

  test "should require name" do
    @account.name = nil
    assert_not @account.valid?
    assert_includes @account.errors[:name], "can't be blank"
  end

  test "should require valid account type" do
    assert_raises(ArgumentError) do
      @account.account_type = "invalid"
    end
  end

  test "should require valid status" do
    assert_raises(ArgumentError) do
      @account.status = "invalid"
    end
  end

  test "should check if vendor" do
    @account.account_type = "vendor"
    assert @account.vendor?
    assert_not @account.agency?
  end

  test "should check if agency" do
    @account.account_type = "agency"
    assert @account.agency?
    assert_not @account.vendor?
  end

  test "should check if approved" do
    @account.status = "approved"
    assert @account.approved?
    
    @account.status = "pending"
    assert_not @account.approved?
  end

  test "should check status methods" do
    @account.status = "pending"
    assert @account.pending?
    assert_not @account.approved?
    assert_not @account.rejected?
    
    @account.status = "approved"
    assert @account.approved?
    assert_not @account.pending?
    assert_not @account.rejected?
    
    @account.status = "rejected"
    assert @account.rejected?
    assert_not @account.pending?
    assert_not @account.approved?
  end

  test "should approve account" do
    admin_user = users(:admin)
    @account.save!
    
    @account.approve!(admin_user)
    
    assert @account.approved?
    assert_equal admin_user, @account.approved_by
    assert_not_nil @account.approved_at
  end

  test "should reject account" do
    admin_user = users(:admin)
    @account.save!
    
    @account.reject!(admin_user)
    
    assert @account.rejected?
    assert_equal admin_user, @account.approved_by
    assert_nil @account.approved_at
  end

  test "should generate slug from name" do
    @account.save!
    assert_not_nil @account.slug
    assert_equal "tech-solutions-inc", @account.slug
  end

  # ===============================
  # ADDITIONAL VALIDATION TESTS
  # ===============================

  test "should require owner" do
    @account.owner = nil
    assert_not @account.valid?
    assert_includes @account.errors[:owner], "must exist"
  end

  test "should validate name presence with empty string" do
    @account.name = ""
    assert_not @account.valid?
    assert_includes @account.errors[:name], "can't be blank"

    @account.name = "   "
    assert_not @account.valid?
    assert_includes @account.errors[:name], "can't be blank"
  end

  test "should validate account_type inclusion" do
    valid_types = %w[vendor agency admin]
    
    valid_types.each do |type|
      @account.account_type = type
      assert @account.valid?, "#{type} should be a valid account type"
    end

    # Test that Rails enum raises ArgumentError for invalid values
    assert_raises(ArgumentError, "Should raise ArgumentError for invalid enum value") do
      @account.account_type = "invalid"
    end
  end

  test "should validate status inclusion" do
    valid_statuses = %w[pending approved rejected]
    
    valid_statuses.each do |status|
      @account.status = status
      assert @account.valid?, "#{status} should be a valid status"
    end

    # Test that Rails enum raises ArgumentError for invalid values
    assert_raises(ArgumentError, "Should raise ArgumentError for invalid enum value") do
      @account.status = "invalid"
    end
  end

  test "should validate LinkedIn URL format" do
    valid_urls = [
      "https://linkedin.com/company/example",
      "https://www.linkedin.com/company/example",
      "https://linkedin.com/in/person",
      "http://linkedin.com/company/example",
      "",
      nil
    ]

    valid_urls.each do |url|
      @account.linkedin_url = url
      assert @account.valid?, "#{url} should be a valid LinkedIn URL"
    end
  end

  # ===============================
  # ASSOCIATION TESTS
  # ===============================

  test "should belong to owner" do
    assert_respond_to @account, :owner
    assert_instance_of User, @account.owner
  end

  test "should optionally belong to approved_by user" do
    assert_respond_to @account, :approved_by
    assert_nil @account.approved_by

    admin_user = users(:admin)
    @account.approved_by = admin_user
    assert_equal admin_user, @account.approved_by
  end

  test "should have many account_users" do
    @account.save!
    
    account_user1 = AccountUser.create!(account: @account, user: @account.owner, role: "admin")
    account_user2 = AccountUser.create!(account: @account, user: users(:two), role: "member")
    
    assert_includes @account.account_users, account_user1
    assert_includes @account.account_users, account_user2
    assert_equal 2, @account.account_users.count
  end

  test "should have many users through account_users" do
    @account.save!
    
    AccountUser.create!(account: @account, user: @account.owner, role: "admin")
    AccountUser.create!(account: @account, user: users(:two), role: "member")
    
    assert_includes @account.users, @account.owner
    assert_includes @account.users, users(:two)
    assert_equal 2, @account.users.count
  end

  test "should have many products" do
    @account.save!
    
    product1 = Product.create!(name: "Product 1", account: @account)
    product2 = Product.create!(name: "Product 2", account: @account)
    
    assert_includes @account.products, product1
    assert_includes @account.products, product2
    assert_equal 2, @account.products.count
  end

  test "should have many team_invitations" do
    @account.save!
    
    invitation1 = TeamInvitation.create!(account: @account, email: "<EMAIL>", role: "member", invited_by: @account.owner)
    invitation2 = TeamInvitation.create!(account: @account, email: "<EMAIL>", role: "admin", invited_by: @account.owner)
    
    assert_includes @account.team_invitations, invitation1
    assert_includes @account.team_invitations, invitation2
    assert_equal 2, @account.team_invitations.count
  end

  test "should have many leads" do
    @account.save!
    
    lead1 = Lead.create!(product: products(:one), account: @account, contact_name: "John", contact_email: "<EMAIL>", contact_company: "ACME", status: "pending")
    lead2 = Lead.create!(product: products(:one), account: @account, contact_name: "Jane", contact_email: "<EMAIL>", contact_company: "ACME", status: "contacted")
    
    assert_includes @account.leads, lead1
    assert_includes @account.leads, lead2
    assert_equal 2, @account.leads.count
  end

  test "should have many agency_leads" do
    @account.account_type = "agency"
    @account.save!
    
    lead1 = Lead.create!(product: products(:one), agency_account: @account, contact_name: "John", contact_email: "<EMAIL>", contact_company: "ACME", status: "pending")
    lead2 = Lead.create!(product: products(:one), agency_account: @account, contact_name: "Jane", contact_email: "<EMAIL>", contact_company: "ACME", status: "contacted")
    
    assert_includes @account.agency_leads, lead1
    assert_includes @account.agency_leads, lead2
    assert_equal 2, @account.agency_leads.count
  end

  # ===============================
  # ENUM METHOD TESTS
  # ===============================

  test "should have correct account_type enum values" do
    assert_equal "vendor", Account.account_types[:vendor]
    assert_equal "agency", Account.account_types[:agency]
    assert_equal "admin", Account.account_types[:admin]
  end

  test "should have correct status enum values" do
    assert_equal "pending", Account.statuses[:pending]
    assert_equal "approved", Account.statuses[:approved]
    assert_equal "rejected", Account.statuses[:rejected]
  end

  test "should respond to account_type predicate methods" do
    @account.account_type = "vendor"
    assert @account.vendor?
    assert_not @account.agency?
    assert_not @account.admin?

    @account.account_type = "agency"
    assert @account.agency?
    assert_not @account.vendor?
    assert_not @account.admin?

    @account.account_type = "admin"
    assert @account.admin?
    assert_not @account.vendor?
    assert_not @account.agency?
  end

  test "should allow setting account_type using enum methods" do
    @account.vendor!
    assert @account.vendor?

    @account.agency!
    assert @account.agency?

    @account.admin!
    assert @account.admin?
  end

  test "should allow setting status using enum methods" do
    @account.pending!
    assert @account.pending?

    @account.approved!
    assert @account.approved?

    @account.rejected!
    assert @account.rejected?
  end

  # ===============================
  # SCOPE TESTS
  # ===============================

  test "should have scope for vendors" do
    vendor_account = Account.create!(name: "Vendor Co", account_type: "vendor", status: "approved", owner: users(:one))
    agency_account = Account.create!(name: "Agency Co", account_type: "agency", status: "approved", owner: users(:two))
    
    vendors = Account.vendors
    assert_includes vendors, vendor_account
    assert_not_includes vendors, agency_account
  end

  test "should have scope for agencies" do
    vendor_account = Account.create!(name: "Vendor Co", account_type: "vendor", status: "approved", owner: users(:one))
    agency_account = Account.create!(name: "Agency Co", account_type: "agency", status: "approved", owner: users(:two))
    
    agencies = Account.agencies
    assert_includes agencies, agency_account
    assert_not_includes agencies, vendor_account
  end

  test "should have scope for admins" do
    vendor_account = Account.create!(name: "Vendor Co", account_type: "vendor", status: "approved", owner: users(:one))
    admin_account = Account.create!(name: "Admin Co", account_type: "admin", status: "approved", owner: users(:admin))
    
    admins = Account.admins
    assert_includes admins, admin_account
    assert_not_includes admins, vendor_account
  end

  test "should have scope for approved accounts" do
    approved_account = Account.create!(name: "Approved Co", account_type: "vendor", status: "approved", owner: users(:one))
    pending_account = Account.create!(name: "Pending Co", account_type: "vendor", status: "pending", owner: users(:two))
    
    approved_accounts = Account.approved
    assert_includes approved_accounts, approved_account
    assert_not_includes approved_accounts, pending_account
  end

  test "should have scope for pending approval" do
    approved_account = Account.create!(name: "Approved Co", account_type: "vendor", status: "approved", owner: users(:one))
    pending_account = Account.create!(name: "Pending Co", account_type: "vendor", status: "pending", owner: users(:two))
    
    pending_accounts = Account.pending_approval
    assert_includes pending_accounts, pending_account
    assert_not_includes pending_accounts, approved_account
  end

  test "should have scope for rejected accounts" do
    approved_account = Account.create!(name: "Approved Co", account_type: "vendor", status: "approved", owner: users(:one))
    rejected_account = Account.create!(name: "Rejected Co", account_type: "vendor", status: "rejected", owner: users(:two))
    
    rejected_accounts = Account.rejected
    assert_includes rejected_accounts, rejected_account
    assert_not_includes rejected_accounts, approved_account
  end

  # ===============================
  # APPROVAL WORKFLOW TESTS
  # ===============================

  test "should approve account with proper timestamps and user" do
    admin_user = users(:admin)
    @account.save!
    
    # Record the time before approval
    before_approval = Time.current
    
    @account.approve!(admin_user)
    
    assert @account.approved?
    assert_equal "approved", @account.status
    assert_equal admin_user, @account.approved_by
    assert_not_nil @account.approved_at
    assert @account.approved_at >= before_approval
  end

  test "should reject account and clear approved_at" do
    admin_user = users(:admin)
    @account.save!
    
    # First approve the account
    @account.approve!(admin_user)
    assert_not_nil @account.approved_at
    
    # Then reject it
    @account.reject!(admin_user)
    
    assert @account.rejected?
    assert_equal "rejected", @account.status
    assert_equal admin_user, @account.approved_by
    assert_nil @account.approved_at
  end

  test "should handle status changes from rejected to approved" do
    admin_user = users(:admin)
    @account.save!
    
    # Start with rejected
    @account.reject!(admin_user)
    assert @account.rejected?
    
    # Then approve
    @account.approve!(admin_user)
    assert @account.approved?
    assert_not_nil @account.approved_at
  end

  # ===============================
  # CONFIRMATION TESTS
  # ===============================

  test "should generate confirmation token on create" do
    account = Account.create!(name: "Test Co", account_type: "vendor", status: "pending", owner: users(:one))
    
    assert_not_nil account.confirmation_token
    assert_kind_of String, account.confirmation_token
    assert account.confirmation_token.length > 20, "Token should be reasonably long"
  end

  test "should check if confirmed" do
    @account.save!
    
    assert_not @account.confirmed?
    
    @account.update!(confirmed_at: Time.current)
    assert @account.confirmed?
  end

  test "should send confirmation email and update sent timestamp" do
    @account.save!
    
    # Test that the confirmation_sent_at timestamp is updated
    assert_nil @account.confirmation_sent_at
    
    # We'll mock the mailer to avoid actually sending emails
    # but still test that the timestamp gets set
    original_method = UserMailer.method(:welcome)
    
    # Stub the welcome method to return a mock that responds to deliver_now
    mock_delivery = Object.new
    def mock_delivery.deliver_now; true; end
    
    UserMailer.define_singleton_method(:welcome) do |*args|
      mock_delivery
    end
    
    @account.send_confirmation_email
    
    # Restore original method
    UserMailer.define_singleton_method(:welcome, original_method)
    
    assert_not_nil @account.confirmation_sent_at
  end

  # ===============================
  # LOGO ATTACHMENT TESTS
  # ===============================

  test "should validate logo attachment" do
    @account.save!
    
    # Test with valid image
    valid_image = ActiveStorage::Blob.create_and_upload!(
      io: File.open(Rails.root.join('test', 'fixtures', 'files', 'test_image.png')),
      filename: 'test_image.png',
      content_type: 'image/png'
    )
    
    @account.logo.attach(valid_image)
    assert @account.valid?, "Account should be valid with valid logo"
  end

  test "should reject oversized logo" do
    @account.save!
    
    # Create a mock file that's too large
    large_file = Tempfile.new(['large_logo', '.png'])
    large_file.write('x' * 6.megabytes) # 6MB file
    large_file.rewind
    
    @account.logo.attach(
      io: large_file,
      filename: 'large_logo.png',
      content_type: 'image/png'
    )
    
    assert_not @account.valid?
    assert_includes @account.errors[:logo], "must be less than 5MB"
    
    large_file.close
    large_file.unlink
  end

  test "should reject invalid logo content types" do
    @account.save!
    
    invalid_file = ActiveStorage::Blob.create_and_upload!(
      io: File.open(Rails.root.join('test', 'fixtures', 'files', 'test_document.pdf')),
      filename: 'test_document.pdf',
      content_type: 'application/pdf'
    )
    
    @account.logo.attach(invalid_file)
    
    assert_not @account.valid?
    assert_includes @account.errors[:logo], "must be a valid image format (JPEG, PNG, GIF, WebP, or SVG)"
  end

  test "should generate logo variants" do
    @account.save!
    
    valid_image = ActiveStorage::Blob.create_and_upload!(
      io: File.open(Rails.root.join('test', 'fixtures', 'files', 'test_image.png')),
      filename: 'test_image.png',
      content_type: 'image/png'
    )
    
    @account.logo.attach(valid_image)
    
    # Test that variant methods exist and return something
    assert_respond_to @account, :logo_thumbnail
    assert_respond_to @account, :logo_small
    assert_respond_to @account, :logo_medium
    assert_respond_to @account, :logo_large
    
    # Test that variants are generated when logo is attached
    assert_not_nil @account.logo_thumbnail
    assert_not_nil @account.logo_small
    assert_not_nil @account.logo_medium
    assert_not_nil @account.logo_large
  end

  test "should return nil for logo variants when no logo attached" do
    @account.save!
    
    assert_nil @account.logo_thumbnail
    assert_nil @account.logo_small
    assert_nil @account.logo_medium
    assert_nil @account.logo_large
  end

  # ===============================
  # FRIENDLY_ID SLUG TESTS
  # ===============================

  test "should generate unique slugs for duplicate names" do
    account1 = Account.create!(name: "Tech Solutions", account_type: "vendor", status: "pending", owner: users(:one))
    account2 = Account.create!(name: "Tech Solutions", account_type: "agency", status: "pending", owner: users(:two))
    
    assert_equal "tech-solutions", account1.slug
    assert_not_equal account1.slug, account2.slug
    assert account2.slug.start_with?("tech-solutions")
  end

  test "should update slug when name changes" do
    @account.save!
    original_slug = @account.slug
    
    # Use a significantly different name to ensure slug changes
    # Note: FriendlyId by default doesn't update slugs on existing records to preserve URLs
    # We need to force regeneration or create a new record to test slug generation
    new_account = Account.create!(
      name: "Completely Different Company Name Inc",
      account_type: "vendor",
      status: "pending",
      owner: @account.owner
    )
    
    assert_not_equal original_slug, new_account.slug
    assert_equal "completely-different-company-name-inc", new_account.slug
  end

  test "should handle special characters in name for slug" do
    @account.name = "Tech & Solutions Inc. (2024)"
    @account.save!
    
    assert_equal "tech-solutions-inc-2024", @account.slug
  end

  test "should handle unicode characters in name for slug" do
    @account.name = "Téchnology Sólutions"
    @account.save!
    
    # FriendlyId should handle unicode appropriately
    assert_not_nil @account.slug
    assert @account.slug.length > 0
  end

  # ===============================
  # RICH TEXT DESCRIPTION TESTS
  # ===============================

  test "should handle rich text description" do
    @account.save!
    
    rich_content = "<p>This is a <strong>rich text</strong> description with <em>formatting</em>.</p>"
    @account.description = rich_content
    
    assert @account.valid?
    assert_respond_to @account, :description
  end

  test "should allow empty description" do
    @account.description = nil
    assert @account.valid?
    
    @account.description = ""
    assert @account.valid?
  end

  # ===============================
  # EDGE CASE TESTS
  # ===============================

  test "should handle special characters in text fields" do
    special_chars = "Special chars: !@#$%^&*()[]{}|;':\",./<>?`~"
    
    @account.name = special_chars
    @account.headquarters_location = special_chars
    
    assert @account.valid?, "Account should be valid with special characters"
  end

  test "should handle unicode characters" do
    unicode_text = "Unicode: héllo wörld 你好 🌟"
    
    @account.name = unicode_text
    @account.headquarters_location = unicode_text
    
    assert @account.valid?, "Account should be valid with unicode characters"
  end

  test "should handle very long text fields" do
    long_text = "A" * 1000
    
    @account.name = long_text
    @account.headquarters_location = long_text
    @account.linkedin_url = "https://linkedin.com/company/#{long_text}"
    
    assert @account.valid?, "Account should be valid with long text"
  end

  test "should handle nil values for optional fields" do
    @account.description = nil
    @account.headquarters_location = nil
    @account.linkedin_url = nil
    @account.approved_by = nil
    @account.approved_at = nil
    @account.confirmed_at = nil
    
    assert @account.valid?, "Account should be valid with nil optional fields"
  end

  # ===============================
  # DEPENDENT DESTROY TESTS
  # ===============================

  test "should destroy associated records when account is destroyed" do
    @account.save!
    
    # Create associated records
    account_user = AccountUser.create!(account: @account, user: @account.owner, role: "admin")
    product = Product.create!(name: "Test Product", account: @account)
    team_invitation = TeamInvitation.create!(account: @account, email: "<EMAIL>", role: "member", invited_by: @account.owner)
    lead = Lead.create!(product: product, account: @account, contact_name: "John", contact_email: "<EMAIL>", contact_company: "ACME", status: "pending")
    
    account_id = @account.id
    account_user_id = account_user.id
    product_id = product.id
    team_invitation_id = team_invitation.id
    lead_id = lead.id
    
    @account.destroy
    
    # Verify all associated records are destroyed
    assert_raises(ActiveRecord::RecordNotFound) { Account.find(account_id) }
    assert_raises(ActiveRecord::RecordNotFound) { AccountUser.find(account_user_id) }
    assert_raises(ActiveRecord::RecordNotFound) { Product.find(product_id) }
    assert_raises(ActiveRecord::RecordNotFound) { TeamInvitation.find(team_invitation_id) }
    assert_raises(ActiveRecord::RecordNotFound) { Lead.find(lead_id) }
  end

  # ===============================
  # DATABASE CONSTRAINT TESTS
  # ===============================

  test "should enforce foreign key constraints" do
    assert_nothing_raised do
      @account.owner
      @account.approved_by
    end
  end

  test "should save and reload correctly" do
    @account.headquarters_location = "San Francisco, CA"
    @account.linkedin_url = "https://linkedin.com/company/tech-solutions"
    assert @account.save, "Account should save successfully"
    
    reloaded_account = Account.find(@account.id)
    
    assert_equal @account.name, reloaded_account.name
    assert_equal @account.account_type, reloaded_account.account_type
    assert_equal @account.status, reloaded_account.status
    assert_equal @account.owner_id, reloaded_account.owner_id
    assert_equal @account.headquarters_location, reloaded_account.headquarters_location
    assert_equal @account.linkedin_url, reloaded_account.linkedin_url
    assert_not_nil reloaded_account.confirmation_token
    assert_not_nil reloaded_account.created_at
    assert_not_nil reloaded_account.updated_at
  end
end
