# == Schema Information
#
# Table name: users
#
#  id              :integer          not null, primary key
#  department      :string           not null
#  email_address   :string           not null
#  first_name      :string
#  job_title       :string
#  last_name       :string
#  password_digest :string           not null
#  phone           :string
#  super_admin     :boolean          default(FALSE), not null
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#
# Indexes
#
#  index_users_on_department     (department)
#  index_users_on_email_address  (email_address) UNIQUE
#  index_users_on_super_admin    (super_admin)
#
require "test_helper"

class UserTest < ActiveSupport::TestCase
  include ActionDispatch::TestProcess
  def setup
    @user = User.new(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON>",
      job_title: "Software Engineer",
      department: "Engineering",
      
    )
  end

  test "should be valid with valid attributes" do
    assert @user.valid?
  end

  test "should require email address" do
    @user.email_address = nil
    assert_not @user.valid?
    assert_includes @user.errors[:email_address], "can't be blank"
  end

  test "should require first name" do
    @user.first_name = nil
    assert_not @user.valid?
    assert_includes @user.errors[:first_name], "can't be blank"
  end

  test "should require last name" do
    @user.last_name = nil
    assert_not @user.valid?
    assert_includes @user.errors[:last_name], "can't be blank"
  end

  test "should require job title" do
    @user.job_title = nil
    assert_not @user.valid?
    assert_includes @user.errors[:job_title], "can't be blank"
  end

  test "should normalize email address" do
    @user.email_address = "  <EMAIL>  "
    @user.save!
    assert_equal "<EMAIL>", @user.email_address
  end

  test "should return full name" do
    assert_equal "John Doe", @user.full_name
  end

  test "should check if confirmed" do
    @user.save!
    assert_not @user.confirmed?
    
    account = Account.create!(
      name: "Test Account",
      account_type: "vendor",
      status: "approved",
      confirmed_at: Time.current,
      owner: @user
    )
    AccountUser.create!(account: account, user: @user, role: "admin")
    
    @user.reload
    assert @user.confirmed?
  end

  test "should check if approved based on account status" do
    @user.save!
    assert_not @user.approved?
    
    # Create an approved account for the user
    account = Account.create!(
      name: "Test Company",
      account_type: "vendor",
      status: "approved",
      owner: @user
    )
    AccountUser.create!(account: account, user: @user, role: "admin")
    
    # Reload user to get fresh associations
    @user.reload
    assert @user.approved?
  end

  test "should check super admin status" do
    assert_not @user.super_admin?
    assert_not @user.admin?
    
    @user.super_admin = true
    assert @user.super_admin?
    assert @user.admin?
  end

  test "should have scope for super admins" do
    @user.super_admin = true
    @user.save!
    
    regular_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Regular",
      last_name: "User",
      job_title: "User",
      department: "General",
      
      super_admin: false
    )
    
    super_admins = User.super_admins
    assert_includes super_admins, @user
    assert_not_includes super_admins, regular_user
  end

  # ===============================
  # ADDITIONAL VALIDATION TESTS
  # ===============================

  test "should validate email format" do
    valid_emails = [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>"
    ]

    valid_emails.each do |email|
      @user.email_address = email
      assert @user.valid?, "#{email} should be a valid email format"
    end

    invalid_emails = [
      "invalid_email",
      "@example.com",
      "user@",
      "user@.com",
      "",
      "user <EMAIL>"
    ]

    invalid_emails.each do |email|
      @user.email_address = email
      assert_not @user.valid?, "#{email} should be invalid email format"
      assert_includes @user.errors[:email_address], "is invalid"
    end
  end

  test "should require unique email address" do
    @user.save!
    
    duplicate_user = User.new(
      email_address: @user.email_address,
      password: "password123",
      first_name: "Jane",
      last_name: "Smith",
      job_title: "Manager",
      department: "Marketing",
      
    )
    
    assert_not duplicate_user.valid?
    assert_includes duplicate_user.errors[:email_address], "has already been taken"
  end

  test "should validate password length on create" do
    user = User.new(
      email_address: "<EMAIL>",
      first_name: "New",
      last_name: "User",
      job_title: "Tester",
      department: "QA",
      
      password: "short"
    )
    
    assert_not user.valid?
    assert_includes user.errors[:password], "is too short (minimum is 8 characters)"
  end

  test "should validate password length on update" do
    @user.save!
    @user.password = "short"
    
    assert_not @user.valid?
    assert_includes @user.errors[:password], "is too short (minimum is 8 characters)"
  end

  test "should not validate password length if password unchanged" do
    @user.save!
    @user.first_name = "Updated Name"
    
    assert @user.valid?, "Should be valid when password not changed"
  end

  # ===============================
  # ASSOCIATION TESTS
  # ===============================

  test "should have many sessions" do
    @user.save!
    
    session1 = Session.create!(user: @user, ip_address: "***********")
    session2 = Session.create!(user: @user, ip_address: "***********")
    
    assert_includes @user.sessions, session1
    assert_includes @user.sessions, session2
    assert_equal 2, @user.sessions.count
  end

  test "should have many owned accounts" do
    @user.save!
    
    account1 = Account.create!(name: "Company 1", account_type: "vendor", status: "approved", owner: @user)
    account2 = Account.create!(name: "Company 2", account_type: "agency", status: "pending", owner: @user)
    
    assert_includes @user.owned_accounts, account1
    assert_includes @user.owned_accounts, account2
    assert_equal 2, @user.owned_accounts.count
  end

  test "should have many account_users and accounts through association" do
    @user.save!
    
    account = Account.create!(name: "Test Company", account_type: "vendor", status: "approved", owner: @user)
    account_user = AccountUser.create!(account: account, user: @user, role: "admin")
    
    assert_includes @user.account_users, account_user
    assert_includes @user.accounts, account
  end

  # ===============================
  # HELPER METHOD TESTS
  # ===============================

  test "should check if user has vendor account" do
    @user.save!
    
    assert_not @user.has_vendor_account?
    assert_not @user.vendor?
    
    vendor_account = Account.create!(name: "Vendor Co", account_type: "vendor", status: "approved", owner: @user)
    AccountUser.create!(account: vendor_account, user: @user, role: "admin")
    
    @user.reload
    assert @user.has_vendor_account?
    assert @user.vendor?
  end

  test "should check if user has agency account" do
    @user.save!
    
    assert_not @user.has_agency_account?
    assert_not @user.agency?
    
    agency_account = Account.create!(name: "Agency Co", account_type: "agency", status: "approved", owner: @user)
    AccountUser.create!(account: agency_account, user: @user, role: "admin")
    
    @user.reload
    assert @user.has_agency_account?
    assert @user.agency?
  end

  test "should handle full name with empty names" do
    user = User.new(first_name: "", last_name: "")
    assert_equal "", user.full_name.strip
    
    user.first_name = "John"
    user.last_name = ""
    assert_equal "John", user.full_name.strip
    
    user.first_name = ""
    user.last_name = "Doe"
    assert_equal "Doe", user.full_name.strip
  end

  # ===============================
  # SCOPE TESTS
  # ===============================

  test "should have scope for users with approved accounts" do
    @user.save!
    
    # User with approved account
    approved_account = Account.create!(name: "Approved Co", account_type: "vendor", status: "approved", owner: @user)
    AccountUser.create!(account: approved_account, user: @user, role: "admin")
    
    # User with pending account
    pending_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Pending",
      last_name: "User",
      job_title: "Pending User",
      department: "Pending",
      
    )
    pending_account = Account.create!(name: "Pending Co", account_type: "vendor", status: "pending", owner: pending_user)
    AccountUser.create!(account: pending_account, user: pending_user, role: "admin")
    
    approved_users = User.with_approved_accounts
    assert_includes approved_users, @user
    assert_not_includes approved_users, pending_user
  end

  test "should have scope for users with pending accounts" do
    @user.save!
    
    # User with pending account  
    pending_account = Account.create!(name: "Pending Co", account_type: "vendor", status: "pending", owner: @user)
    AccountUser.create!(account: pending_account, user: @user, role: "admin")
    
    # User with approved account
    approved_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123", 
      first_name: "Approved",
      last_name: "User",
      job_title: "Approved User",
      department: "Approved",
      
    )
    approved_account = Account.create!(name: "Approved Co", account_type: "vendor", status: "approved", owner: approved_user)
    AccountUser.create!(account: approved_account, user: approved_user, role: "admin")
    
    pending_users = User.with_pending_accounts
    assert_includes pending_users, @user
    assert_not_includes pending_users, approved_user
  end

  # ===============================
  # PROFILE PHOTO TESTS
  # ===============================

  test "should validate profile photo attachment" do
    @user.save!
    
    # Test with valid image
    valid_image = fixture_file_upload(
      Rails.root.join('test', 'fixtures', 'files', 'test_image.png'),
      'image/png'
    )
    
    @user.profile_photo.attach(valid_image)
    assert @user.valid?, "User should be valid with valid profile photo"
  end

  test "should reject oversized profile photo" do
    @user.save!
    
    # Create a mock file that's too large
    large_file = Tempfile.new(['large_image', '.png'])
    large_file.write('x' * 6.megabytes) # 6MB file
    large_file.rewind
    
    @user.profile_photo.attach(
      io: large_file,
      filename: 'large_image.png',
      content_type: 'image/png'
    )
    
    assert_not @user.valid?
    assert_includes @user.errors[:profile_photo], "must be less than 5MB"
    
    large_file.close
    large_file.unlink
  end

  test "should reject invalid profile photo content types" do
    @user.save!
    
    invalid_file = fixture_file_upload(
      Rails.root.join('test', 'fixtures', 'files', 'test_document.pdf'),
      'application/pdf'
    )
    
    @user.profile_photo.attach(invalid_file)
    
    assert_not @user.valid?
    assert_includes @user.errors[:profile_photo], "must be a valid image format (JPEG, PNG, GIF, or WebP)"
  end

  # ===============================
  # EDGE CASE TESTS
  # ===============================

  test "should handle special characters in names" do
    special_chars = "José María O'Connor-Smith"
    
    @user.first_name = special_chars
    @user.last_name = special_chars
    
    assert @user.valid?, "User should be valid with special characters in names"
    assert_equal "#{special_chars} #{special_chars}", @user.full_name
  end

  test "should handle unicode characters" do
    @user.first_name = "测试"
    @user.last_name = "用户"
    @user.job_title = "软件工程师"
    
    assert @user.valid?, "User should be valid with unicode characters"
    assert_equal "测试 用户", @user.full_name
  end

  test "should handle very long names" do
    long_name = "A" * 255
    
    @user.first_name = long_name
    @user.last_name = long_name
    @user.job_title = long_name
    
    assert @user.valid?, "User should be valid with long names"
  end

  test "should handle nil and empty optional fields" do
    @user.phone = nil
    
    assert @user.valid?, "User should be valid with nil optional fields"
    
    @user.phone = ""
    
    assert @user.valid?, "User should be valid with empty optional fields"
  end

  # ===============================
  # SECURITY TESTS
  # ===============================

  test "should hash password securely" do
    @user.save!
    
    assert_not_equal "password123", @user.password_digest
    assert @user.password_digest.starts_with?("$2a$"), "Should use bcrypt hashing"
    assert @user.authenticate("password123"), "Should authenticate with correct password"
    assert_not @user.authenticate("wrongpassword"), "Should not authenticate with wrong password"
  end

  test "should generate password reset tokens" do
    @user.save!
    
    token = @user.generate_token_for(:password_reset)
    assert_not_nil token
    assert_kind_of String, token
    
    # Token should be different each time
    token2 = @user.generate_token_for(:password_reset)
    assert_not_equal token, token2
  end
end
