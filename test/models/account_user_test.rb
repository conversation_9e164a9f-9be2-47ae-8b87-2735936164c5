# == Schema Information
#
# Table name: account_users
#
#  id         :integer          not null, primary key
#  joined_at  :datetime
#  role       :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  account_id :integer          not null
#  user_id    :integer          not null
#
# Indexes
#
#  index_account_users_on_account_id  (account_id)
#  index_account_users_on_user_id     (user_id)
#
# Foreign Keys
#
#  account_id  (account_id => accounts.id)
#  user_id     (user_id => users.id)
#
require "test_helper"

class AccountUserTest < ActiveSupport::TestCase
  def setup
    # Create fresh user and account to avoid fixture conflicts
    @user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Test",
      last_name: "User"
    )
    
    @account = Account.create!(
      name: "Test Account",
      account_type: "vendor",
      status: "approved",
      owner: @user
    )
    
    @account_user = AccountUser.new(
      user: @user,
      account: @account,
      role: "member"
    )
  end

  # ===============================
  # BASIC VALIDATION TESTS
  # ===============================

  test "should be valid with valid attributes" do
    assert @account_user.valid?
  end

  test "should require user" do
    @account_user.user = nil
    assert_not @account_user.valid?
    assert_includes @account_user.errors[:user], "must exist"
  end

  test "should require account" do
    @account_user.account = nil
    assert_not @account_user.valid?
    assert_includes @account_user.errors[:account], "must exist"
  end

  test "should validate role inclusion" do
    valid_roles = %w[admin member]
    
    valid_roles.each do |role|
      @account_user.role = role
      assert @account_user.valid?, "#{role} should be a valid role"
    end

    # Test that Rails enum raises ArgumentError for invalid values
    assert_raises(ArgumentError, "Should raise ArgumentError for invalid enum value") do
      @account_user.role = "invalid"
    end
  end

  test "should validate user uniqueness per account" do
    @account_user.save!
    
    # Same user can't be added to the same account twice
    duplicate_account_user = AccountUser.new(
      user: @user,
      account: @account,
      role: "admin"
    )
    
    assert_not duplicate_account_user.valid?
    assert_includes duplicate_account_user.errors[:user_id], "has already been taken"
  end

  test "should allow same user in different accounts" do
    @account_user.save!
    
    other_account = Account.create!(
      name: "Other Account",
      account_type: "agency",
      status: "approved",
      owner: @user
    )
    other_account_user = AccountUser.new(
      user: @user,
      account: other_account,
      role: "admin"
    )
    
    assert other_account_user.valid?
  end

  test "should allow different users in same account" do
    @account_user.save!
    
    other_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Other",
      last_name: "User"
    )
    other_account_user = AccountUser.new(
      user: other_user,
      account: @account,
      role: "admin"
    )
    
    assert other_account_user.valid?
  end

  # ===============================
  # ASSOCIATION TESTS
  # ===============================

  test "should belong to user" do
    assert_respond_to @account_user, :user
    assert_instance_of User, @account_user.user
  end

  test "should belong to account" do
    assert_respond_to @account_user, :account
    assert_instance_of Account, @account_user.account
  end

  # ===============================
  # ENUM TESTS
  # ===============================

  test "should have correct role enum values" do
    assert_equal "admin", AccountUser.roles[:admin]
    assert_equal "member", AccountUser.roles[:member]
  end

  test "should respond to role predicate methods" do
    @account_user.role = "admin"
    assert @account_user.admin?
    assert_not @account_user.member?

    @account_user.role = "member"
    assert @account_user.member?
    assert_not @account_user.admin?
  end

  test "should allow setting role using enum methods" do
    @account_user.admin!
    assert @account_user.admin?
    assert_equal "admin", @account_user.role

    @account_user.member!
    assert @account_user.member?
    assert_equal "member", @account_user.role
  end

  # ===============================
  # SCOPE TESTS
  # ===============================

  test "should have scope for admins" do
    admin_user = AccountUser.create!(user: @user, account: @account, role: "admin")
    
    other_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Member",
      last_name: "User"
    )
    member_user = AccountUser.create!(user: other_user, account: @account, role: "member")
    
    admins = AccountUser.admins
    assert_includes admins, admin_user
    assert_not_includes admins, member_user
  end

  test "should have scope for members" do
    admin_user = AccountUser.create!(user: @user, account: @account, role: "admin")
    
    other_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Member",
      last_name: "User2"
    )
    member_user = AccountUser.create!(user: other_user, account: @account, role: "member")
    
    members = AccountUser.members
    assert_includes members, member_user
    assert_not_includes members, admin_user
  end

  # ===============================
  # METHOD TESTS
  # ===============================

  test "should check admin status" do
    @account_user.role = "admin"
    assert @account_user.admin?
    assert_not @account_user.member?
  end

  test "should check member status" do
    @account_user.role = "member"
    assert @account_user.member?
    assert_not @account_user.admin?
  end

  # ===============================
  # ATTRIBUTE TESTS
  # ===============================

  test "should allow setting and getting all attributes" do
    account_user = AccountUser.new(
      user: users(:one),
      account: accounts(:one),
      role: "admin",
      joined_at: 1.week.ago
    )

    assert_equal users(:one), account_user.user
    assert_equal accounts(:one), account_user.account
    assert_equal "admin", account_user.role
    assert_not_nil account_user.joined_at
  end

  test "should handle nil joined_at" do
    @account_user.joined_at = nil
    assert @account_user.valid?
  end

  test "should automatically set created_at and updated_at" do
    account_user = AccountUser.create!(
      user: @user,
      account: @account,
      role: "member"
    )

    assert_not_nil account_user.created_at
    assert_not_nil account_user.updated_at
    assert_kind_of Time, account_user.created_at
    assert_kind_of Time, account_user.updated_at
  end

  test "should update updated_at when modified" do
    account_user = AccountUser.create!(
      user: @user,
      account: @account,
      role: "member"
    )

    original_updated_at = account_user.updated_at
    
    # Wait a moment to ensure timestamp difference
    sleep(0.01)
    
    account_user.update!(role: "admin")
    
    assert account_user.updated_at > original_updated_at
  end

  # ===============================
  # EDGE CASE TESTS
  # ===============================

  test "should handle role changes" do
    @account_user.save!
    
    # Change from member to admin
    @account_user.update!(role: "admin")
    assert @account_user.admin?
    
    # Change from admin to member
    @account_user.update!(role: "member")
    assert @account_user.member?
  end

  test "should handle joined_at timestamp" do
    join_time = 2.weeks.ago
    @account_user.joined_at = join_time
    @account_user.save!
    
    assert_equal join_time.to_i, @account_user.joined_at.to_i
  end

  # ===============================
  # DATABASE CONSTRAINT TESTS
  # ===============================

  test "should enforce foreign key constraints" do
    assert_nothing_raised do
      @account_user.user
      @account_user.account
    end
  end

  test "should save and reload correctly" do
    @account_user.joined_at = 1.week.ago
    assert @account_user.save, "AccountUser should save successfully"
    
    reloaded_account_user = AccountUser.find(@account_user.id)
    
    assert_equal @account_user.user_id, reloaded_account_user.user_id
    assert_equal @account_user.account_id, reloaded_account_user.account_id
    assert_equal @account_user.role, reloaded_account_user.role
    assert_equal @account_user.joined_at.to_i, reloaded_account_user.joined_at.to_i
    assert_not_nil reloaded_account_user.created_at
    assert_not_nil reloaded_account_user.updated_at
  end

  # ===============================
  # BUSINESS LOGIC TESTS
  # ===============================

  test "should support multiple roles per account" do
    # Create admin
    admin = AccountUser.create!(user: @user, account: @account, role: "admin")
    
    # Create member
    member = AccountUser.create!(user: users(:two), account: @account, role: "member")
    
    # Both should exist in the same account
    account_users = AccountUser.where(account: @account)
    assert_includes account_users, admin
    assert_includes account_users, member
    assert_equal 2, account_users.count
  end

  test "should support user in multiple accounts" do
    # Add user to first account
    account_user1 = AccountUser.create!(user: @user, account: accounts(:one), role: "admin")
    
    # Add same user to second account
    account_user2 = AccountUser.create!(user: @user, account: accounts(:two), role: "member")
    
    # User should be in both accounts
    user_accounts = AccountUser.where(user: @user)
    assert_includes user_accounts, account_user1
    assert_includes user_accounts, account_user2
    assert_equal 2, user_accounts.count
  end
end
