# == Schema Information
#
# Table name: categories
#
#  id          :integer          not null, primary key
#  description :text
#  name        :string
#  slug        :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  parent_id   :integer
#
# Indexes
#
#  index_categories_on_parent_id  (parent_id)
#
# Foreign Keys
#
#  parent_id  (parent_id => categories.id)
#
require "test_helper"

class CategoryTest < ActiveSupport::TestCase
  def setup
    @root_category = Category.new(
      name: "Technology",
      description: "Technology products and services"
    )
  end

  # ===============================
  # BASIC VALIDATION TESTS
  # ===============================

  test "should be valid with valid attributes" do
    assert @root_category.valid?
  end

  test "should require name" do
    @root_category.name = nil
    assert_not @root_category.valid?
    assert_includes @root_category.errors[:name], "can't be blank"

    @root_category.name = ""
    assert_not @root_category.valid?
    assert_includes @root_category.errors[:name], "can't be blank"
  end

  test "should validate name uniqueness within parent scope" do
    parent = Category.create!(name: "Software")
    
    child1 = Category.create!(name: "CRM", parent: parent)
    child2 = Category.new(name: "CRM", parent: parent)
    
    assert_not child2.valid?
    assert_includes child2.errors[:name], "has already been taken"
  end

  test "should allow same name for different parents" do
    parent1 = Category.create!(name: "Software")
    parent2 = Category.create!(name: "Hardware")
    
    child1 = Category.create!(name: "Security", parent: parent1)
    child2 = Category.new(name: "Security", parent: parent2)
    
    assert child2.valid?
  end

  test "should allow same name at root level and child level" do
    root = Category.create!(name: "Security")
    parent = Category.create!(name: "Software")
    child = Category.new(name: "Security", parent: parent)
    
    assert child.valid?
  end

  test "should prevent being parent of itself" do
    category = Category.create!(name: "Test Category")
    category.parent_id = category.id
    
    assert_not category.valid?
    assert_includes category.errors[:parent_id], "cannot be the same as the category itself"
  end

  test "should prevent circular references" do
    grandparent = Category.create!(name: "Grandparent")
    parent = Category.create!(name: "Parent", parent: grandparent)
    child = Category.create!(name: "Child", parent: parent)
    
    # Try to make grandparent a child of its own descendant
    grandparent.parent_id = child.id
    
    assert_not grandparent.valid?
    assert_includes grandparent.errors[:parent_id], "would create a circular reference"
  end

  test "should prevent products if has subcategories" do
    # Create account and product for testing
    user = User.create!(email_address: "<EMAIL>", password: "password123", first_name: "Test", last_name: "User")
    account = Account.create!(name: "Test Account", account_type: "vendor", status: "approved", owner: user)
    
    category = Category.create!(name: "Software")
    subcategory = Category.create!(name: "CRM", parent: category)
    
    # Create product in the category
    product = Product.create!(name: "Test Product", account: account)
    product.categories << category
    
    # This should be invalid because category has subcategories and products
    assert_not category.valid?
    assert_includes category.errors[:base], "Categories with subcategories cannot have products. Products can only be added to leaf categories (categories without subcategories)."
  end

  # ===============================
  # ASSOCIATION TESTS
  # ===============================

  test "should optionally belong to parent" do
    # Root category has no parent
    assert_nil @root_category.parent
    
    # Child category has parent
    child = Category.new(name: "Software", parent: @root_category)
    assert_equal @root_category, child.parent
  end

  test "should have many subcategories" do
    @root_category.save!
    
    child1 = Category.create!(name: "Software", parent: @root_category)
    child2 = Category.create!(name: "Hardware", parent: @root_category)
    
    assert_includes @root_category.subcategories, child1
    assert_includes @root_category.subcategories, child2
    assert_equal 2, @root_category.subcategories.count
  end

  test "should have many product_categories" do
    @root_category.save!
    
    user = User.create!(email_address: "<EMAIL>", password: "password123", first_name: "Test", last_name: "User")
    account = Account.create!(name: "Test Account", account_type: "vendor", status: "approved", owner: user)
    product = Product.create!(name: "Test Product", account: account)
    
    product_category = ProductCategory.create!(product: product, category: @root_category)
    
    assert_includes @root_category.product_categories, product_category
    assert_equal 1, @root_category.product_categories.count
  end

  test "should have many products through product_categories" do
    @root_category.save!
    
    user = User.create!(email_address: "<EMAIL>", password: "password123", first_name: "Test", last_name: "User")
    account = Account.create!(name: "Test Account", account_type: "vendor", status: "approved", owner: user)
    product1 = Product.create!(name: "Test Product 1", account: account)
    product2 = Product.create!(name: "Test Product 2", account: account)
    
    @root_category.products << product1
    @root_category.products << product2
    
    assert_includes @root_category.products, product1
    assert_includes @root_category.products, product2
    assert_equal 2, @root_category.products.count
  end

  # ===============================
  # SCOPE TESTS
  # ===============================

  test "should have scope for root categories" do
    root1 = Category.create!(name: "Technology")
    root2 = Category.create!(name: "Business")
    child = Category.create!(name: "Software", parent: root1)
    
    root_categories = Category.root_categories
    assert_includes root_categories, root1
    assert_includes root_categories, root2
    assert_not_includes root_categories, child
  end

  test "should have scope for child categories" do
    root = Category.create!(name: "Technology")
    child1 = Category.create!(name: "Software", parent: root)
    child2 = Category.create!(name: "Hardware", parent: root)
    
    child_categories = Category.child_categories
    assert_includes child_categories, child1
    assert_includes child_categories, child2
    assert_not_includes child_categories, root
  end

  test "should have scope for categories with products" do
    user = User.create!(email_address: "<EMAIL>", password: "password123", first_name: "Test", last_name: "User")
    account = Account.create!(name: "Test Account", account_type: "vendor", status: "approved", owner: user)
    
    category_with_products = Category.create!(name: "Software")
    category_without_products = Category.create!(name: "Hardware")
    
    product = Product.create!(name: "Test Product", account: account)
    category_with_products.products << product
    
    categories_with_products = Category.with_products
    assert_includes categories_with_products, category_with_products
    assert_not_includes categories_with_products, category_without_products
  end

  test "should have alphabetical scope" do
    # Create unique categories to avoid fixture interference
    zebra = Category.create!(name: "Test Zebra")
    alpha = Category.create!(name: "Test Alpha")
    beta = Category.create!(name: "Test Beta")
    
    alphabetical = Category.where(id: [alpha.id, beta.id, zebra.id]).alphabetical.pluck(:name)
    assert_equal ["Test Alpha", "Test Beta", "Test Zebra"], alphabetical
  end

  test "should have scope for leaf categories" do
    root = Category.create!(name: "Technology")
    parent = Category.create!(name: "Software", parent: root)
    leaf = Category.create!(name: "CRM", parent: parent)
    
    leaf_categories = Category.leaf_categories
    assert_includes leaf_categories, leaf
    assert_not_includes leaf_categories, parent
    assert_not_includes leaf_categories, root
  end

  # ===============================
  # HIERARCHY METHOD TESTS
  # ===============================

  test "should check if root category" do
    root = Category.create!(name: "Technology")
    child = Category.create!(name: "Software", parent: root)
    
    assert root.root_category?
    assert_not child.root_category?
  end

  test "should check if child category" do
    root = Category.create!(name: "Technology")
    child = Category.create!(name: "Software", parent: root)
    
    assert_not root.child_category?
    assert child.child_category?
  end

  test "should check if leaf category" do
    root = Category.create!(name: "Technology")
    parent = Category.create!(name: "Software", parent: root)
    leaf = Category.create!(name: "CRM", parent: parent)
    
    assert_not root.leaf_category?
    assert_not parent.leaf_category?
    assert leaf.leaf_category?
  end

  test "should calculate depth correctly" do
    root = Category.create!(name: "Technology")
    parent = Category.create!(name: "Software", parent: root)
    child = Category.create!(name: "CRM", parent: parent)
    grandchild = Category.create!(name: "Sales CRM", parent: child)
    
    assert_equal 0, root.depth
    assert_equal 1, parent.depth
    assert_equal 2, child.depth
    assert_equal 3, grandchild.depth
  end

  test "should get ancestors correctly" do
    root = Category.create!(name: "Technology")
    parent = Category.create!(name: "Software", parent: root)
    child = Category.create!(name: "CRM", parent: parent)
    
    assert_equal [], root.ancestors
    assert_equal [root], parent.ancestors
    assert_equal [root, parent], child.ancestors
  end

  test "should get root category correctly" do
    root = Category.create!(name: "Technology")
    parent = Category.create!(name: "Software", parent: root)
    child = Category.create!(name: "CRM", parent: parent)
    
    assert_equal root, root.root_category
    assert_equal root, parent.root_category
    assert_equal root, child.root_category
  end

  test "should get descendants correctly" do
    root = Category.create!(name: "Technology")
    parent1 = Category.create!(name: "Software", parent: root)
    parent2 = Category.create!(name: "Hardware", parent: root)
    child1 = Category.create!(name: "CRM", parent: parent1)
    child2 = Category.create!(name: "ERP", parent: parent1)
    grandchild = Category.create!(name: "Sales CRM", parent: child1)
    
    descendants = root.descendants
    assert_includes descendants, parent1
    assert_includes descendants, parent2
    assert_includes descendants, child1
    assert_includes descendants, child2
    assert_includes descendants, grandchild
    assert_equal 5, descendants.count
  end

  test "should generate full name correctly" do
    root = Category.create!(name: "Technology")
    parent = Category.create!(name: "Software", parent: root)
    child = Category.create!(name: "CRM", parent: parent)
    
    assert_equal "Technology", root.full_name
    assert_equal "Technology > Software", parent.full_name
    assert_equal "Technology > Software > CRM", child.full_name
  end

  test "should generate breadcrumb path correctly" do
    root = Category.create!(name: "Technology")
    parent = Category.create!(name: "Software", parent: root)
    child = Category.create!(name: "CRM", parent: parent)
    
    assert_equal [root], root.breadcrumb_path
    assert_equal [root, parent], parent.breadcrumb_path
    assert_equal [root, parent, child], child.breadcrumb_path
  end

  # ===============================
  # FRIENDLY_ID SLUG TESTS
  # ===============================

  test "should generate slug from name" do
    @root_category.save!
    assert_not_nil @root_category.slug
    assert_equal "technology", @root_category.slug
  end

  test "should generate slug with special characters" do
    # Test that slug is generated correctly from name with special characters
    category = Category.create!(name: "Test Technology")
    assert_equal "test-technology", category.slug
  end
  
  test "should handle duplicate slugs with friendly_id" do
    # Create categories in different parent scopes to allow same names
    parent1 = Category.create!(name: "Parent One")
    parent2 = Category.create!(name: "Parent Two")
    
    category1 = Category.create!(name: "Technology", parent: parent1)
    category2 = Category.create!(name: "Technology", parent: parent2)
    
    # Both should have valid slugs
    assert_not_nil category1.slug
    assert_not_nil category2.slug
    assert category1.slug.include?("technology")
    assert category2.slug.include?("technology")
  end

  test "should handle special characters in name for slug" do
    category = Category.create!(name: "Technology & Software (2024)")
    assert_equal "technology-software-2024", category.slug
  end

  test "should handle unicode characters in name for slug" do
    category = Category.create!(name: "Téchnology Sóftware")
    assert_not_nil category.slug
    assert category.slug.length > 0
  end

  # ===============================
  # DEPENDENT DESTROY TESTS
  # ===============================

  test "should destroy subcategories when parent is destroyed" do
    root = Category.create!(name: "Technology")
    parent = Category.create!(name: "Software", parent: root)
    child = Category.create!(name: "CRM", parent: parent)
    
    parent_id = parent.id
    child_id = child.id
    
    root.destroy
    
    assert_raises(ActiveRecord::RecordNotFound) { Category.find(parent_id) }
    assert_raises(ActiveRecord::RecordNotFound) { Category.find(child_id) }
  end

  test "should destroy product_categories when category is destroyed" do
    user = User.create!(email_address: "<EMAIL>", password: "password123", first_name: "Test", last_name: "User")
    account = Account.create!(name: "Test Account", account_type: "vendor", status: "approved", owner: user)
    category = Category.create!(name: "Software")
    product = Product.create!(name: "Test Product", account: account)
    
    product_category = ProductCategory.create!(product: product, category: category)
    product_category_id = product_category.id
    
    category.destroy
    
    assert_raises(ActiveRecord::RecordNotFound) { ProductCategory.find(product_category_id) }
    # Product should still exist
    assert_nothing_raised { Product.find(product.id) }
  end

  # ===============================
  # EDGE CASE TESTS
  # ===============================

  test "should handle special characters in text fields" do
    special_chars = "Special chars: !@#$%^&*()[]{}|;':\",./<>?`~"
    
    category = Category.new(
      name: special_chars,
      description: special_chars
    )
    
    assert category.valid?, "Category should be valid with special characters"
  end

  test "should handle unicode characters" do
    unicode_text = "Unicode: héllo wörld 你好 🌟"
    
    category = Category.new(
      name: unicode_text,
      description: unicode_text
    )
    
    assert category.valid?, "Category should be valid with unicode characters"
  end

  test "should handle very long text fields" do
    long_name = "A" * 255
    long_description = "B" * 2000
    
    category = Category.new(
      name: long_name,
      description: long_description
    )
    
    assert category.valid?, "Category should be valid with long text"
  end

  test "should handle nil description" do
    @root_category.description = nil
    assert @root_category.valid?, "Category should be valid with nil description"
  end

  test "should handle empty description" do
    @root_category.description = ""
    assert @root_category.valid?, "Category should be valid with empty description"
  end

  # ===============================
  # COMPLEX HIERARCHY TESTS
  # ===============================

  test "should handle deep hierarchy" do
    categories = []
    current_parent = nil
    
    # Create 5-level deep hierarchy
    5.times do |i|
      category = Category.create!(name: "Level #{i}", parent: current_parent)
      categories << category
      current_parent = category
    end
    
    # Test depths
    categories.each_with_index do |category, index|
      assert_equal index, category.depth
    end
    
    # Test root category
    categories.each do |category|
      assert_equal categories.first, category.root_category
    end
    
    # Test ancestors
    assert_equal 4, categories.last.ancestors.count
    assert_equal categories[0..3], categories.last.ancestors
  end

  test "should handle multiple children and complex tree" do
    #     Root
    #    /    \
    #   A      B
    #  / \    / \
    # A1 A2  B1 B2
    #         |
    #        B1a
    
    root = Category.create!(name: "Root")
    a = Category.create!(name: "A", parent: root)
    b = Category.create!(name: "B", parent: root)
    a1 = Category.create!(name: "A1", parent: a)
    a2 = Category.create!(name: "A2", parent: a)
    b1 = Category.create!(name: "B1", parent: b)
    b2 = Category.create!(name: "B2", parent: b)
    b1a = Category.create!(name: "B1a", parent: b1)
    
    # Test subcategories
    assert_equal [a, b], root.subcategories.sort_by(&:name)
    assert_equal [a1, a2], a.subcategories.sort_by(&:name)
    assert_equal [b1, b2], b.subcategories.sort_by(&:name)
    assert_equal [b1a], b1.subcategories
    
    # Test descendants
    root_descendants = root.descendants
    assert_equal 7, root_descendants.count
    assert_includes root_descendants, a
    assert_includes root_descendants, b1a
    
    # Test leaf categories
    leaf_categories = [a1, a2, b2, b1a]
    leaf_categories.each { |cat| assert cat.leaf_category? }
    [root, a, b, b1].each { |cat| assert_not cat.leaf_category? }
  end

  # ===============================
  # DATABASE CONSTRAINT TESTS
  # ===============================

  test "should enforce foreign key constraints" do
    parent = Category.create!(name: "Parent")
    child = Category.create!(name: "Child", parent: parent)
    
    assert_nothing_raised do
      child.parent
    end
  end

  test "should save and reload correctly" do
    @root_category.description = "Technology products and services for enterprises"
    assert @root_category.save, "Category should save successfully"
    
    reloaded_category = Category.find(@root_category.id)
    
    assert_equal @root_category.name, reloaded_category.name
    assert_equal @root_category.description, reloaded_category.description
    assert_equal @root_category.parent_id, reloaded_category.parent_id
    assert_not_nil reloaded_category.slug
    assert_not_nil reloaded_category.created_at
    assert_not_nil reloaded_category.updated_at
  end
end
