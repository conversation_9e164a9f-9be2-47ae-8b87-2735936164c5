# == Schema Information
#
# Table name: product_certifications
#
#  id               :integer          not null, primary key
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  certification_id :integer          not null
#  product_id       :integer          not null
#
# Indexes
#
#  index_product_certifications_on_certification_id  (certification_id)
#  index_product_certifications_on_product_id        (product_id)
#
# Foreign Keys
#
#  certification_id  (certification_id => certifications.id)
#  product_id        (product_id => products.id)
#
require "test_helper"

class ProductCertificationTest < ActiveSupport::TestCase
  def setup
    # Create user and account for product
    @user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Test",
      last_name: "User"
    )
    
    @account = Account.create!(
      name: "Test Company",
      account_type: "vendor",
      status: "approved",
      owner: @user
    )
    
    @product = Product.create!(
      name: "Test Product",
      account: @account
    )
    
    @certification = Certification.create!(
      name: "ISO 27001 #{SecureRandom.hex(4)}",
      description: "Information security management"
    )
    
    @product_certification = ProductCertification.new(
      product: @product,
      certification: @certification
    )
  end

  # ===============================
  # BASIC VALIDATION TESTS
  # ===============================

  test "should be valid with valid attributes" do
    assert @product_certification.valid?
  end

  test "should require product" do
    @product_certification.product = nil
    assert_not @product_certification.valid?
    assert_includes @product_certification.errors[:product], "must exist"
  end

  test "should require certification" do
    @product_certification.certification = nil
    assert_not @product_certification.valid?
    assert_includes @product_certification.errors[:certification], "must exist"
  end

  test "should validate product_id and certification_id uniqueness" do
    @product_certification.save!
    
    # Try to create duplicate product-certification combination
    duplicate_product_certification = ProductCertification.new(
      product: @product,
      certification: @certification
    )
    
    assert_not duplicate_product_certification.valid?
    assert_includes duplicate_product_certification.errors[:product_id], "has already been taken"
  end

  test "should allow same product with different certifications" do
    @product_certification.save!
    
    other_certification = Certification.create!(
      name: "SOC 2",
      description: "Service organization control"
    )
    
    other_product_certification = ProductCertification.new(
      product: @product,
      certification: other_certification
    )
    
    assert other_product_certification.valid?
  end

  test "should allow same certification with different products" do
    @product_certification.save!
    
    other_product = Product.create!(
      name: "Other Product",
      account: @account
    )
    
    other_product_certification = ProductCertification.new(
      product: other_product,
      certification: @certification
    )
    
    assert other_product_certification.valid?
  end

  # ===============================
  # ASSOCIATION TESTS
  # ===============================

  test "should belong to product" do
    assert_respond_to @product_certification, :product
    assert_instance_of Product, @product_certification.product
  end

  test "should belong to certification" do
    assert_respond_to @product_certification, :certification
    assert_instance_of Certification, @product_certification.certification
  end

  test "should be accessible through product certifications association" do
    @product_certification.save!
    
    assert_includes @product.product_certifications, @product_certification
    assert_includes @product.certifications, @certification
  end

  test "should be accessible through certification product_certifications association" do
    @product_certification.save!
    
    assert_includes @certification.product_certifications, @product_certification
    assert_includes @certification.products, @product
  end

  # ===============================
  # DATABASE CONSTRAINT TESTS
  # ===============================

  test "should enforce foreign key constraints" do
    assert_nothing_raised do
      @product_certification.product
      @product_certification.certification
    end
  end

  test "should save and reload correctly" do
    assert @product_certification.save, "ProductCertification should save successfully"
    
    reloaded_product_certification = ProductCertification.find(@product_certification.id)
    
    assert_equal @product_certification.product_id, reloaded_product_certification.product_id
    assert_equal @product_certification.certification_id, reloaded_product_certification.certification_id
    assert_not_nil reloaded_product_certification.created_at
    assert_not_nil reloaded_product_certification.updated_at
  end

  test "should handle timestamps correctly" do
    before_create = Time.current
    @product_certification.save!
    after_create = Time.current
    
    assert @product_certification.created_at >= before_create
    assert @product_certification.created_at <= after_create
    assert @product_certification.updated_at >= before_create
    assert @product_certification.updated_at <= after_create
  end

  test "should update updated_at when modified" do
    @product_certification.save!
    original_updated_at = @product_certification.updated_at
    
    # Wait a moment to ensure timestamp difference
    sleep(0.01)
    
    # Touch to update timestamps
    @product_certification.touch
    
    assert @product_certification.updated_at > original_updated_at
  end

  # ===============================
  # BUSINESS LOGIC TESTS
  # ===============================

  test "should support multiple certifications per product" do
    @product_certification.save!
    
    # Add second certification to same product
    soc2_cert = Certification.create!(name: "SOC 2", description: "Service Organization Control")
    pci_cert = Certification.create!(name: "PCI DSS", description: "Payment Card Industry")
    
    ProductCertification.create!(product: @product, certification: soc2_cert)
    ProductCertification.create!(product: @product, certification: pci_cert)
    
    product_certifications = ProductCertification.where(product: @product)
    assert_equal 3, product_certifications.count
    
    certification_names = @product.certifications.pluck(:name).sort
    expected_names = [@certification.name, "PCI DSS", "SOC 2"].sort
    assert_equal expected_names, certification_names
  end

  test "should support multiple products per certification" do
    @product_certification.save!
    
    # Add second product to same certification
    product2 = Product.create!(name: "Product 2", account: @account)
    product3 = Product.create!(name: "Product 3", account: @account)
    
    ProductCertification.create!(product: product2, certification: @certification)
    ProductCertification.create!(product: product3, certification: @certification)
    
    certification_products = ProductCertification.where(certification: @certification)
    assert_equal 3, certification_products.count
    
    product_names = @certification.products.pluck(:name).sort
    assert_equal ["Product 2", "Product 3", "Test Product"], product_names
  end

  test "should handle product deletion" do
    @product_certification.save!
    product_id = @product.id
    
    # Delete product should also delete product_certification (dependent: :destroy)
    @product.destroy
    
    assert_raises(ActiveRecord::RecordNotFound) { Product.find(product_id) }
    assert_raises(ActiveRecord::RecordNotFound) { ProductCertification.find(@product_certification.id) }
  end

  test "should handle certification deletion" do
    @product_certification.save!
    certification_id = @certification.id
    
    # Delete certification should also delete product_certification (dependent: :destroy)
    @certification.destroy
    
    assert_raises(ActiveRecord::RecordNotFound) { Certification.find(certification_id) }
    assert_raises(ActiveRecord::RecordNotFound) { ProductCertification.find(@product_certification.id) }
  end

  # ===============================
  # EDGE CASE TESTS
  # ===============================

  test "should handle large number of product-certification relationships" do
    # Create multiple certifications and assign them to the product
    certifications = []
    10.times do |i|
      certification = Certification.create!(name: "Certification #{i}", description: "Description #{i}")
      certifications << certification
      ProductCertification.create!(product: @product, certification: certification)
    end
    
    assert_equal 10, @product.certifications.count
    certifications.each do |certification|
      assert_includes @product.certifications, certification
    end
  end

  test "should handle different account products with same certification" do
    @product_certification.save!
    
    # Create another account and product
    other_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Other",
      last_name: "User"
    )
    other_account = Account.create!(
      name: "Other Company",
      account_type: "vendor",
      status: "approved",
      owner: other_user
    )
    other_product = Product.create!(name: "Other Product", account: other_account)
    
    other_product_certification = ProductCertification.new(
      product: other_product,
      certification: @certification
    )
    
    assert other_product_certification.valid?
    other_product_certification.save!
    
    # Same certification should be associated with both products
    assert_includes @certification.products, @product
    assert_includes @certification.products, other_product
    assert_equal 2, @certification.products.count
  end

  # ===============================
  # QUERY TESTS
  # ===============================

  test "should support finding products by certification" do
    @product_certification.save!
    
    products_with_certification = Product.joins(:certifications).where(certifications: { id: @certification.id })
    assert_includes products_with_certification, @product
  end

  test "should support finding certifications by product" do
    @product_certification.save!
    
    certifications_for_product = Certification.joins(:products).where(products: { id: @product.id })
    assert_includes certifications_for_product, @certification
  end

  test "should support counting relationships" do
    @product_certification.save!
    
    # Add more relationships
    soc2_cert = Certification.create!(name: "SOC 2", description: "Service control")
    ProductCertification.create!(product: @product, certification: soc2_cert)
    
    assert_equal 2, @product.product_certifications.count
    assert_equal 2, @product.certifications.count
    assert_equal 1, @certification.product_certifications.count
    assert_equal 1, @certification.products.count
  end

  # ===============================
  # INTEGRATION TESTS
  # ===============================

  test "should work with product creation through certifications association" do
    # Test creating product certification through product's certifications association
    new_certification = Certification.create!(name: "New Certification", description: "New cert")
    
    @product.certifications << new_certification
    
    assert_includes @product.certifications, new_certification
    assert_includes new_certification.products, @product
    
    # Should create ProductCertification record
    product_certification = ProductCertification.find_by(product: @product, certification: new_certification)
    assert_not_nil product_certification
  end

  test "should work with certification creation through products association" do
    # Test creating product certification through certification's products association
    new_product = Product.create!(name: "New Product", account: @account)
    
    @certification.products << new_product
    
    assert_includes @certification.products, new_product
    assert_includes new_product.certifications, @certification
    
    # Should create ProductCertification record
    product_certification = ProductCertification.find_by(product: new_product, certification: @certification)
    assert_not_nil product_certification
  end

  test "should handle batch operations" do
    # Create multiple certifications
    certifications = []
    5.times do |i|
      certifications << Certification.create!(name: "Batch Certification #{i}", description: "Batch #{i}")
    end
    
    # Batch assign certifications to product
    @product.certifications = certifications
    
    assert_equal 5, @product.certifications.count
    assert_equal 5, ProductCertification.where(product: @product).count
    
    certifications.each do |certification|
      assert_includes @product.certifications, certification
    end
  end

  test "should handle certification replacement" do
    @product_certification.save!
    
    # Replace with new certifications
    new_certifications = [
      Certification.create!(name: "New Cert 1", description: "First new"),
      Certification.create!(name: "New Cert 2", description: "Second new")
    ]
    
    @product.certifications = new_certifications
    
    # Old certification should be removed
    assert_not_includes @product.certifications, @certification
    
    # New certifications should be added
    new_certifications.each do |cert|
      assert_includes @product.certifications, cert
    end
    
    assert_equal 2, @product.certifications.count
  end
end