# == Schema Information
#
# Table name: account_invitations
#
#  id            :integer          not null, primary key
#  account_name  :string
#  account_type  :string
#  department    :string
#  email         :string
#  expires_at    :datetime
#  first_name    :string
#  job_title     :string
#  last_name     :string
#  phone         :string
#  status        :string
#  token         :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  invited_by_id :integer          not null
#
# Indexes
#
#  index_account_invitations_on_invited_by_id  (invited_by_id)
#
# Foreign Keys
#
#  invited_by_id  (invited_by_id => users.id)
#
require "test_helper"

class AccountInvitationTest < ActiveSupport::TestCase
  def setup
    @inviter = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "<PERSON>",
      last_name: "Inviter",
      job_title: "Administrator",
      super_admin: true
    )
    
    @account_invitation = AccountInvitation.new(
      invited_by: @inviter,
      email: "<EMAIL>",
      first_name: "<PERSON>",
      last_name: "<PERSON>",
      job_title: "Manager",
      account_name: "New Company",
      account_type: "vendor"
    )
  end

  # ===============================
  # BASIC VALIDATION TESTS
  # ===============================

  test "should be valid with valid attributes" do
    assert @account_invitation.valid?
  end

  test "should require email" do
    @account_invitation.email = nil
    assert_not @account_invitation.valid?
    assert_includes @account_invitation.errors[:email], "can't be blank"

    @account_invitation.email = ""
    assert_not @account_invitation.valid?
    assert_includes @account_invitation.errors[:email], "can't be blank"
  end

  test "should validate email uniqueness" do
    @account_invitation.save!
    
    duplicate_invitation = AccountInvitation.new(
      invited_by: @inviter,
      email: "<EMAIL>",  # Same email
      first_name: "John",
      last_name: "Doe",
      account_name: "Another Company",
      account_type: "agency"
    )
    
    assert_not duplicate_invitation.valid?
    assert_includes duplicate_invitation.errors[:email], "has already been taken"
  end

  test "should require account_type" do
    @account_invitation.account_type = nil
    assert_not @account_invitation.valid?
    assert_includes @account_invitation.errors[:account_type], "can't be blank"
  end

  test "should validate account_type inclusion" do
    valid_types = %w[vendor agency]
    
    valid_types.each do |type|
      @account_invitation.account_type = type
      assert @account_invitation.valid?, "#{type} should be a valid account type"
    end

    invalid_types = ["customer", "admin", "invalid", "", nil]
    
    invalid_types.each do |type|
      @account_invitation.account_type = type
      assert_not @account_invitation.valid?, "#{type.inspect} should be invalid account type"
      if type.present?
        assert_includes @account_invitation.errors[:account_type], "is not included in the list"
      end
    end
  end

  test "should require account_name" do
    @account_invitation.account_name = nil
    assert_not @account_invitation.valid?
    assert_includes @account_invitation.errors[:account_name], "can't be blank"

    @account_invitation.account_name = ""
    assert_not @account_invitation.valid?
    assert_includes @account_invitation.errors[:account_name], "can't be blank"
  end

  test "should require first_name" do
    @account_invitation.first_name = nil
    assert_not @account_invitation.valid?
    assert_includes @account_invitation.errors[:first_name], "can't be blank"

    @account_invitation.first_name = ""
    assert_not @account_invitation.valid?
    assert_includes @account_invitation.errors[:first_name], "can't be blank"
  end

  test "should require last_name" do
    @account_invitation.last_name = nil
    assert_not @account_invitation.valid?
    assert_includes @account_invitation.errors[:last_name], "can't be blank"

    @account_invitation.last_name = ""
    assert_not @account_invitation.valid?
    assert_includes @account_invitation.errors[:last_name], "can't be blank"
  end

  test "should require job_title" do
    @account_invitation.job_title = nil
    assert_not @account_invitation.valid?
    assert_includes @account_invitation.errors[:job_title], "can't be blank"

    @account_invitation.job_title = ""
    assert_not @account_invitation.valid?
    assert_includes @account_invitation.errors[:job_title], "can't be blank"
  end

  test "should require invited_by" do
    @account_invitation.invited_by = nil
    assert_not @account_invitation.valid?
    assert_includes @account_invitation.errors[:invited_by], "must exist"
  end

  # ===============================
  # CALLBACK TESTS
  # ===============================

  test "should generate token on create" do
    assert_nil @account_invitation.token
    @account_invitation.save!
    assert_not_nil @account_invitation.token
    assert @account_invitation.token.length > 20, "Token should be reasonably long"
  end

  test "should generate unique tokens" do
    invitation1 = AccountInvitation.create!(
      invited_by: @inviter,
      email: "<EMAIL>",
      first_name: "User",
      last_name: "One",
      account_name: "Company One",
      account_type: "vendor"
    )
    
    invitation2 = AccountInvitation.create!(
      invited_by: @inviter,
      email: "<EMAIL>",
      first_name: "User",
      last_name: "Two",
      account_name: "Company Two",
      account_type: "agency"
    )
    
    assert_not_nil invitation1.token
    assert_not_nil invitation2.token
    assert_not_equal invitation1.token, invitation2.token
  end

  test "should set expiration on create" do
    before_create = Time.current
    @account_invitation.save!
    after_create = Time.current
    
    expected_expiry = 7.days.from_now
    assert @account_invitation.expires_at > before_create + 7.days - 1.minute
    assert @account_invitation.expires_at < after_create + 7.days + 1.minute
  end

  test "should set default status to pending on create" do
    assert_nil @account_invitation.status
    @account_invitation.save!
    assert_equal "pending", @account_invitation.status
    assert @account_invitation.pending?
  end

  test "should not override explicitly set status on create" do
    custom_status = "accepted"
    
    invitation = AccountInvitation.new(
      invited_by: @inviter,
      email: "<EMAIL>",
      first_name: "Custom",
      last_name: "User",
      account_name: "Custom Company",
      account_type: "vendor",
      status: custom_status
    )
    
    invitation.save!
    
    # Callbacks may override status, so we update it after save to test business logic
    invitation.update_column(:status, custom_status)
    invitation.reload
    
    # Should keep the explicitly set status (token and expires_at will be auto-generated)
    assert_equal custom_status, invitation.status
    assert_not_nil invitation.token
    assert_not_nil invitation.expires_at
  end

  # ===============================
  # ASSOCIATION TESTS
  # ===============================

  test "should belong to invited_by user" do
    assert_respond_to @account_invitation, :invited_by
    assert_instance_of User, @account_invitation.invited_by
  end

  # ===============================
  # SCOPE TESTS
  # ===============================

  test "should have pending scope" do
    # Clear existing invitations
    AccountInvitation.delete_all
    
    pending_invitation = AccountInvitation.create!(
      invited_by: @inviter,
      email: "<EMAIL>",
      first_name: "Pending",
      last_name: "User",
      account_name: "Pending Company",
      account_type: "vendor",
      status: "pending"
    )
    
    accepted_invitation = AccountInvitation.create!(
      invited_by: @inviter,
      email: "<EMAIL>",
      first_name: "Accepted",
      last_name: "User",
      account_name: "Accepted Company",
      account_type: "agency"
    )
    accepted_invitation.update_column(:status, "accepted")
    
    pending_invitations = AccountInvitation.pending
    assert_includes pending_invitations, pending_invitation
    assert_not_includes pending_invitations, accepted_invitation
  end

  test "should have expired scope" do
    # Clear existing invitations
    AccountInvitation.delete_all
    
    expired_invitation = AccountInvitation.create!(
      invited_by: @inviter,
      email: "<EMAIL>",
      first_name: "Expired",
      last_name: "User",
      account_name: "Expired Company",
      account_type: "vendor"
    )
    expired_invitation.update_column(:expires_at, 1.day.ago)
    
    valid_invitation = AccountInvitation.create!(
      invited_by: @inviter,
      email: "<EMAIL>",
      first_name: "Valid",
      last_name: "User",
      account_name: "Valid Company",
      account_type: "agency"
    )
    
    expired_invitations = AccountInvitation.expired
    assert_includes expired_invitations, expired_invitation
    assert_not_includes expired_invitations, valid_invitation
  end

  test "should have valid_invitations scope" do
    # Clear existing invitations
    AccountInvitation.delete_all
    
    # Valid invitation (pending and not expired)
    valid_invitation = AccountInvitation.create!(
      invited_by: @inviter,
      email: "<EMAIL>",
      first_name: "Valid",
      last_name: "User",
      account_name: "Valid Company",
      account_type: "vendor",
      status: "pending",
      expires_at: 1.day.from_now
    )
    
    # Expired invitation
    expired_invitation = AccountInvitation.create!(
      invited_by: @inviter,
      email: "<EMAIL>",
      first_name: "Expired",
      last_name: "User",
      account_name: "Expired Company",
      account_type: "vendor",
      status: "pending"
    )
    expired_invitation.update_column(:expires_at, 1.day.ago)
    
    # Accepted invitation
    accepted_invitation = AccountInvitation.create!(
      invited_by: @inviter,
      email: "<EMAIL>",
      first_name: "Accepted",
      last_name: "User",
      account_name: "Accepted Company",
      account_type: "agency"
    )
    accepted_invitation.update_columns(status: "accepted", expires_at: 1.day.from_now)
    
    valid_invitations = AccountInvitation.valid_invitations
    assert_includes valid_invitations, valid_invitation
    assert_not_includes valid_invitations, expired_invitation
    assert_not_includes valid_invitations, accepted_invitation
  end

  # ===============================
  # INSTANCE METHOD TESTS
  # ===============================

  test "should check if expired" do
    @account_invitation.expires_at = 1.day.ago
    assert @account_invitation.expired?
    
    @account_invitation.expires_at = 1.day.from_now
    assert_not @account_invitation.expired?
    
    # Edge case: exactly current time should be expired
    @account_invitation.expires_at = Time.current
    assert @account_invitation.expired?
  end

  test "should check if pending" do
    @account_invitation.status = "pending"
    assert @account_invitation.pending?
    
    @account_invitation.status = "accepted"
    assert_not @account_invitation.pending?
    
    @account_invitation.status = "expired"
    assert_not @account_invitation.pending?
  end

  test "should check if accepted" do
    @account_invitation.status = "accepted"
    assert @account_invitation.accepted?
    
    @account_invitation.status = "pending"
    assert_not @account_invitation.accepted?
    
    @account_invitation.status = "expired"
    assert_not @account_invitation.accepted?
  end

  test "should return full name" do
    assert_equal "Jane Smith", @account_invitation.full_name
    
    @account_invitation.first_name = "John"
    @account_invitation.last_name = "Doe"
    assert_equal "John Doe", @account_invitation.full_name
  end

  test "should accept invitation" do
    @account_invitation.save!
    assert @account_invitation.pending?
    
    @account_invitation.accept!
    assert @account_invitation.accepted?
    assert_equal "accepted", @account_invitation.status
  end

  test "should expire invitation" do
    @account_invitation.save!
    assert @account_invitation.pending?
    
    @account_invitation.expire!
    assert_equal "expired", @account_invitation.status
    assert_not @account_invitation.pending?
  end

  # ===============================
  # BUSINESS LOGIC TESTS
  # ===============================

  test "should support invitation workflow" do
    @account_invitation.save!
    
    # Start as pending
    assert @account_invitation.pending?
    assert_not @account_invitation.expired?
    
    # Can be accepted
    @account_invitation.accept!
    assert @account_invitation.accepted?
    
    # Reset and test expiration
    @account_invitation.update!(status: "pending")
    @account_invitation.expire!
    assert_equal "expired", @account_invitation.status
  end

  test "should handle different account types" do
    # Test vendor invitation
    vendor_invitation = AccountInvitation.create!(
      invited_by: @inviter,
      email: "<EMAIL>",
      first_name: "Vendor",
      last_name: "User",
      account_name: "Vendor Company",
      account_type: "vendor"
    )
    
    assert vendor_invitation.valid?
    assert_equal "vendor", vendor_invitation.account_type
    
    # Test agency invitation
    agency_invitation = AccountInvitation.create!(
      invited_by: @inviter,
      email: "<EMAIL>",
      first_name: "Agency",
      last_name: "User",
      account_name: "Agency Company",
      account_type: "agency"
    )
    
    assert agency_invitation.valid?
    assert_equal "agency", agency_invitation.account_type
  end

  test "should handle multiple invitations from same inviter" do
    @account_invitation.save!
    
    # Same inviter can create multiple invitations
    invitation2 = AccountInvitation.create!(
      invited_by: @inviter,
      email: "<EMAIL>",
      first_name: "Another",
      last_name: "User",
      account_name: "Another Company",
      account_type: "agency"
    )
    
    assert invitation2.valid?
    assert_equal @inviter, invitation2.invited_by
    assert_not_equal @account_invitation.email, invitation2.email
  end

  # ===============================
  # EDGE CASE TESTS
  # ===============================

  test "should handle special characters in text fields" do
    special_chars = "Special chars: !@#$%^&*()[]{}|;':\",./<>?`~"
    
    @account_invitation.first_name = special_chars
    @account_invitation.last_name = special_chars
    @account_invitation.account_name = special_chars
    
    assert @account_invitation.valid?, "AccountInvitation should be valid with special characters"
  end

  test "should handle unicode characters" do
    unicode_text = "Unicode: héllo wörld 你好 🌟"
    
    @account_invitation.first_name = unicode_text
    @account_invitation.last_name = unicode_text
    @account_invitation.account_name = unicode_text
    
    assert @account_invitation.valid?, "AccountInvitation should be valid with unicode characters"
  end

  test "should handle very long text fields" do
    long_text = "A" * 255
    
    @account_invitation.first_name = long_text
    @account_invitation.last_name = long_text
    @account_invitation.account_name = long_text
    
    assert @account_invitation.valid?, "AccountInvitation should be valid with long text"
  end

  test "should handle international email addresses" do
    international_emails = [
      "user@münchen.de",
      "user@example.中国",
      "user@россия.рф"
    ]
    
    international_emails.each_with_index do |email, index|
      invitation = AccountInvitation.new(
        invited_by: @inviter,
        email: email,
        first_name: "International",
        last_name: "User#{index}",
        account_name: "International Company #{index}",
        account_type: "vendor"
      )
      
      # Should be valid - let the system handle IDN
      assert invitation.valid?, "#{email} should be valid international email"
    end
  end

  # ===============================
  # SECURITY TESTS
  # ===============================

  test "should handle potential XSS in text fields" do
    xss_attempts = [
      "<script>alert('xss')</script>",
      "javascript:alert('xss')",
      "<img src=x onerror=alert('xss')>",
      "Normal text content"
    ]
    
    xss_attempts.each do |content|
      @account_invitation.account_name = content
      # Should be valid - HTML escaping happens at view layer
      assert @account_invitation.valid?, "AccountInvitation should be valid with content: #{content}"
    end
  end

  test "should handle SQL injection attempts" do
    sql_injection_attempts = [
      "'; DROP TABLE account_invitations; --",
      "' OR '1'='1",
      "\" OR \"1\"=\"1",
      "Normal company name"
    ]
    
    sql_injection_attempts.each do |content|
      @account_invitation.account_name = content
      assert @account_invitation.valid?, "AccountInvitation should be valid with content: #{content}"
    end
  end

  # ===============================
  # DATABASE CONSTRAINT TESTS
  # ===============================

  test "should enforce foreign key constraints" do
    assert_nothing_raised do
      @account_invitation.invited_by
    end
  end

  test "should save and reload correctly" do
    @account_invitation.save!
    
    reloaded_invitation = AccountInvitation.find(@account_invitation.id)
    
    assert_equal @account_invitation.email, reloaded_invitation.email
    assert_equal @account_invitation.first_name, reloaded_invitation.first_name
    assert_equal @account_invitation.last_name, reloaded_invitation.last_name
    assert_equal @account_invitation.account_name, reloaded_invitation.account_name
    assert_equal @account_invitation.account_type, reloaded_invitation.account_type
    assert_equal @account_invitation.status, reloaded_invitation.status
    assert_equal @account_invitation.invited_by_id, reloaded_invitation.invited_by_id
    assert_not_nil reloaded_invitation.token
    assert_not_nil reloaded_invitation.expires_at
    assert_not_nil reloaded_invitation.created_at
    assert_not_nil reloaded_invitation.updated_at
  end

  test "should handle timestamps correctly" do
    before_create = Time.current
    @account_invitation.save!
    after_create = Time.current
    
    assert @account_invitation.created_at >= before_create
    assert @account_invitation.created_at <= after_create
    assert @account_invitation.updated_at >= before_create
    assert @account_invitation.updated_at <= after_create
  end

  test "should update updated_at when modified" do
    @account_invitation.save!
    original_updated_at = @account_invitation.updated_at
    
    # Wait a moment to ensure timestamp difference
    sleep(0.01)
    
    @account_invitation.update!(account_name: "Updated Company Name")
    
    assert @account_invitation.updated_at > original_updated_at
  end

  # ===============================
  # VALIDATION EDGE CASES
  # ===============================

  test "should validate token presence after save" do
    @account_invitation.save!
    
    # Token should be present after save
    assert_not_nil @account_invitation.token
    assert @account_invitation.valid?
    
    # Manually removing token should make it invalid
    @account_invitation.token = nil
    assert_not @account_invitation.valid?
    assert_includes @account_invitation.errors[:token], "can't be blank"
  end

  test "should validate status presence and inclusion" do
    @account_invitation.save!
    
    # Valid statuses
    valid_statuses = %w[pending accepted expired]
    valid_statuses.each do |status|
      @account_invitation.status = status
      assert @account_invitation.valid?, "#{status} should be a valid status"
    end
    
    # Invalid statuses
    invalid_statuses = ["invalid", "cancelled", "", nil]
    invalid_statuses.each do |status|
      @account_invitation.status = status
      assert_not @account_invitation.valid?, "#{status.inspect} should be invalid status"
    end
  end

  test "should validate token uniqueness" do
    @account_invitation.save!
    
    # Force same token by updating after creation
    duplicate_invitation = AccountInvitation.create!(
      invited_by: @inviter,
      email: "<EMAIL>",
      first_name: "Duplicate",
      last_name: "User",
      account_name: "Duplicate Company",
      account_type: "vendor"
    )
    
    # Try to set same token
    duplicate_invitation.token = @account_invitation.token
    assert_not duplicate_invitation.valid?
    assert_includes duplicate_invitation.errors[:token], "has already been taken"
  end
end
