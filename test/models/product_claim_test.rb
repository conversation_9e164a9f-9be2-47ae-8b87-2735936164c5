# == Schema Information
#
# Table name: product_claims
#
#  id         :integer          not null, primary key
#  company    :string
#  email      :string
#  message    :text
#  name       :string
#  status     :string
#  title      :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  product_id :integer          not null
#
# Indexes
#
#  index_product_claims_on_product_id  (product_id)
#
# Foreign Keys
#
#  product_id  (product_id => products.id)
#
require "test_helper"

class ProductClaimTest < ActiveSupport::TestCase
  def setup
    # Create user and account for product
    @user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Test",
      last_name: "User"
    )
    
    @account = Account.create!(
      name: "Test Company",
      account_type: "vendor",
      status: "approved",
      owner: @user
    )
    
    @product = Product.create!(
      name: "Test Product",
      account: @account
    )
    
    @product_claim = ProductClaim.new(
      product: @product,
      name: "<PERSON> Doe",
      email: "<EMAIL>",
      company: "ACME Corporation",
      title: "Product Manager",
      message: "I would like to claim this product for our company."
    )
  end

  # ===============================
  # BASIC VALIDATION TESTS
  # ===============================

  test "should be valid with valid attributes" do
    assert @product_claim.valid?
  end

  test "should require name" do
    @product_claim.name = nil
    assert_not @product_claim.valid?
    assert_includes @product_claim.errors[:name], "can't be blank"

    @product_claim.name = ""
    assert_not @product_claim.valid?
    assert_includes @product_claim.errors[:name], "can't be blank"

    @product_claim.name = "   "
    assert_not @product_claim.valid?
    assert_includes @product_claim.errors[:name], "can't be blank"
  end

  test "should require email" do
    @product_claim.email = nil
    assert_not @product_claim.valid?
    assert_includes @product_claim.errors[:email], "can't be blank"

    @product_claim.email = ""
    assert_not @product_claim.valid?
    assert_includes @product_claim.errors[:email], "can't be blank"
  end

  test "should validate email format" do
    valid_emails = [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>"
    ]
    
    valid_emails.each do |email|
      @product_claim.email = email
      assert @product_claim.valid?, "#{email} should be a valid email format"
    end

    invalid_emails = [
      "invalid_email",
      "@example.com",
      "user@",
      "user@.com",
      "user <EMAIL>"
    ]
    
    invalid_emails.each do |email|
      @product_claim.email = email
      assert_not @product_claim.valid?, "#{email} should be invalid email format"
      assert_includes @product_claim.errors[:email], "is invalid"
    end
  end

  test "should require company" do
    @product_claim.company = nil
    assert_not @product_claim.valid?
    assert_includes @product_claim.errors[:company], "can't be blank"

    @product_claim.company = ""
    assert_not @product_claim.valid?
    assert_includes @product_claim.errors[:company], "can't be blank"
  end

  test "should require title" do
    @product_claim.title = nil
    assert_not @product_claim.valid?
    assert_includes @product_claim.errors[:title], "can't be blank"

    @product_claim.title = ""
    assert_not @product_claim.valid?
    assert_includes @product_claim.errors[:title], "can't be blank"
  end

  test "should require message" do
    @product_claim.message = nil
    assert_not @product_claim.valid?
    assert_includes @product_claim.errors[:message], "can't be blank"

    @product_claim.message = ""
    assert_not @product_claim.valid?
    assert_includes @product_claim.errors[:message], "can't be blank"
  end

  test "should require product" do
    @product_claim.product = nil
    assert_not @product_claim.valid?
    assert_includes @product_claim.errors[:product], "must exist"
  end

  # ===============================
  # ASSOCIATION TESTS
  # ===============================

  test "should belong to product" do
    assert_respond_to @product_claim, :product
    assert_instance_of Product, @product_claim.product
  end

  # ===============================
  # ENUM TESTS
  # ===============================

  test "should have correct status enum values" do
    assert_equal "pending", ProductClaim.statuses[:pending]
    assert_equal "reviewed", ProductClaim.statuses[:reviewed]
    assert_equal "approved", ProductClaim.statuses[:approved]
    assert_equal "rejected", ProductClaim.statuses[:rejected]
  end

  test "should respond to status predicate methods" do
    @product_claim.status = "pending"
    assert @product_claim.pending?
    assert_not @product_claim.reviewed?
    assert_not @product_claim.approved?
    assert_not @product_claim.rejected?

    @product_claim.status = "reviewed"
    assert @product_claim.reviewed?
    assert_not @product_claim.pending?
    assert_not @product_claim.approved?
    assert_not @product_claim.rejected?

    @product_claim.status = "approved"
    assert @product_claim.approved?
    assert_not @product_claim.pending?
    assert_not @product_claim.reviewed?
    assert_not @product_claim.rejected?

    @product_claim.status = "rejected"
    assert @product_claim.rejected?
    assert_not @product_claim.pending?
    assert_not @product_claim.reviewed?
    assert_not @product_claim.approved?
  end

  test "should allow setting status using enum methods" do
    @product_claim.save! # Need to save first to use enum methods
    
    @product_claim.pending!
    assert @product_claim.pending?
    assert_equal "pending", @product_claim.status

    @product_claim.reviewed!
    assert @product_claim.reviewed?
    assert_equal "reviewed", @product_claim.status

    @product_claim.approved!
    assert @product_claim.approved?
    assert_equal "approved", @product_claim.status

    @product_claim.rejected!
    assert @product_claim.rejected?
    assert_equal "rejected", @product_claim.status
  end

  # ===============================
  # CALLBACK TESTS
  # ===============================

  test "should set default status to pending on create" do
    claim = ProductClaim.new(
      product: @product,
      name: "Jane Smith",
      email: "<EMAIL>",
      company: "Example Corp",
      title: "Manager",
      message: "Claim message"
    )
    
    # Status should be nil before save
    assert_nil claim.status
    
    claim.save!
    
    # Status should be set to pending after save
    assert_equal "pending", claim.status
    assert claim.pending?
  end

  test "should not override explicitly set status on create" do
    claim = ProductClaim.new(
      product: @product,
      name: "Jane Smith",
      email: "<EMAIL>",
      company: "Example Corp",
      title: "Manager",
      message: "Claim message",
      status: "reviewed"
    )
    
    claim.save!
    
    # Should keep the explicitly set status
    assert_equal "reviewed", claim.status
    assert claim.reviewed?
  end

  # ===============================
  # BUSINESS LOGIC TESTS
  # ===============================

  test "should support claim workflow" do
    @product_claim.save!
    
    # Start as pending
    assert @product_claim.pending?
    
    # Move to reviewed
    @product_claim.reviewed!
    assert @product_claim.reviewed?
    
    # Move to approved
    @product_claim.approved!
    assert @product_claim.approved?
    
    # Can also be rejected from any state
    @product_claim.rejected!
    assert @product_claim.rejected?
  end

  test "should handle multiple claims for same product" do
    @product_claim.save!
    
    # Create second claim for same product
    claim2 = ProductClaim.create!(
      product: @product,
      name: "Jane Smith",
      email: "<EMAIL>",
      company: "Another Corp",
      title: "Director",
      message: "Another claim message"
    )
    
    # Both claims should be valid and associated with product
    product_claims = ProductClaim.where(product: @product)
    assert_equal 2, product_claims.count
    assert_includes product_claims, @product_claim
    assert_includes product_claims, claim2
  end

  test "should handle same claimant for different products" do
    @product_claim.save!
    
    # Create another product
    other_product = Product.create!(name: "Other Product", account: @account)
    
    # Same person claims different product
    claim2 = ProductClaim.create!(
      product: other_product,
      name: "John Doe",  # Same name
      email: "<EMAIL>",  # Same email
      company: "ACME Corporation",  # Same company
      title: "Product Manager",
      message: "Claiming this other product too"
    )
    
    assert claim2.valid?
    assert_not_equal @product_claim.product, claim2.product
  end

  # ===============================
  # SCOPE TESTS
  # ===============================

  test "should support scope for each status" do
    # Clear existing claims
    ProductClaim.delete_all
    
    pending_claim = ProductClaim.create!(
      product: @product,
      name: "Pending User",
      email: "<EMAIL>",
      company: "Pending Corp",
      title: "Manager",
      message: "Pending message",
      status: "pending"
    )
    
    reviewed_claim = ProductClaim.create!(
      product: @product,
      name: "Reviewed User",
      email: "<EMAIL>",
      company: "Reviewed Corp",
      title: "Manager",
      message: "Reviewed message",
      status: "reviewed"
    )
    
    approved_claim = ProductClaim.create!(
      product: @product,
      name: "Approved User",
      email: "<EMAIL>",
      company: "Approved Corp",
      title: "Manager",
      message: "Approved message",
      status: "approved"
    )
    
    rejected_claim = ProductClaim.create!(
      product: @product,
      name: "Rejected User",
      email: "<EMAIL>",
      company: "Rejected Corp",
      title: "Manager",
      message: "Rejected message",
      status: "rejected"
    )
    
    # Test scopes
    pending_claims = ProductClaim.pending
    assert_includes pending_claims, pending_claim
    assert_not_includes pending_claims, reviewed_claim
    
    reviewed_claims = ProductClaim.reviewed
    assert_includes reviewed_claims, reviewed_claim
    assert_not_includes reviewed_claims, pending_claim
    
    approved_claims = ProductClaim.approved
    assert_includes approved_claims, approved_claim
    assert_not_includes approved_claims, rejected_claim
    
    rejected_claims = ProductClaim.rejected
    assert_includes rejected_claims, rejected_claim
    assert_not_includes rejected_claims, approved_claim
  end

  # ===============================
  # EDGE CASE TESTS
  # ===============================

  test "should handle special characters in text fields" do
    special_chars = "Special chars: !@#$%^&*()[]{}|;':\",./<>?`~"
    
    @product_claim.name = special_chars
    @product_claim.company = special_chars
    @product_claim.title = special_chars
    @product_claim.message = special_chars
    
    assert @product_claim.valid?, "ProductClaim should be valid with special characters"
  end

  test "should handle unicode characters" do
    unicode_text = "Unicode: héllo wörld 你好 🌟"
    
    @product_claim.name = unicode_text
    @product_claim.company = unicode_text
    @product_claim.title = unicode_text
    @product_claim.message = unicode_text
    
    assert @product_claim.valid?, "ProductClaim should be valid with unicode characters"
  end

  test "should handle very long text fields" do
    long_text = "A" * 500
    
    @product_claim.name = long_text
    @product_claim.company = long_text
    @product_claim.title = long_text
    @product_claim.message = long_text * 4  # Very long message
    
    assert @product_claim.valid?, "ProductClaim should be valid with long text"
  end

  test "should handle international email addresses" do
    # Note: Ruby's URI::MailTo::EMAIL_REGEXP may not support all international domains
    # This test focuses on basic international email format
    simple_international_emails = [
      "<EMAIL>",  # Standard domain
      "<EMAIL>"  # Multi-part TLD
    ]
    
    simple_international_emails.each do |email|
      @product_claim.email = email
      assert @product_claim.valid?, "#{email} should be valid email"
    end
  end

  # ===============================
  # DATABASE CONSTRAINT TESTS
  # ===============================

  test "should enforce foreign key constraints" do
    assert_nothing_raised do
      @product_claim.product
    end
  end

  test "should save and reload correctly" do
    @product_claim.save!
    
    reloaded_claim = ProductClaim.find(@product_claim.id)
    
    assert_equal @product_claim.name, reloaded_claim.name
    assert_equal @product_claim.email, reloaded_claim.email
    assert_equal @product_claim.company, reloaded_claim.company
    assert_equal @product_claim.title, reloaded_claim.title
    assert_equal @product_claim.message, reloaded_claim.message
    assert_equal @product_claim.status, reloaded_claim.status
    assert_equal @product_claim.product_id, reloaded_claim.product_id
    assert_not_nil reloaded_claim.created_at
    assert_not_nil reloaded_claim.updated_at
  end

  test "should handle timestamps correctly" do
    before_create = Time.current
    @product_claim.save!
    after_create = Time.current
    
    assert @product_claim.created_at >= before_create
    assert @product_claim.created_at <= after_create
    assert @product_claim.updated_at >= before_create
    assert @product_claim.updated_at <= after_create
  end

  test "should update updated_at when modified" do
    @product_claim.save!
    original_updated_at = @product_claim.updated_at
    
    # Wait a moment to ensure timestamp difference
    sleep(0.01)
    
    @product_claim.update!(message: "Updated message")
    
    assert @product_claim.updated_at > original_updated_at
  end

  # ===============================
  # SECURITY TESTS
  # ===============================

  test "should handle potential XSS in text fields" do
    xss_attempts = [
      "<script>alert('xss')</script>",
      "javascript:alert('xss')",
      "<img src=x onerror=alert('xss')>",
      "Normal text content"
    ]
    
    xss_attempts.each do |content|
      @product_claim.message = content
      # Should be valid - HTML escaping happens at view layer
      assert @product_claim.valid?, "ProductClaim should be valid with content: #{content}"
    end
  end

  test "should handle SQL injection attempts" do
    sql_injection_attempts = [
      "'; DROP TABLE product_claims; --",
      "' OR '1'='1",
      "\" OR \"1\"=\"1",
      "Normal company name"
    ]
    
    sql_injection_attempts.each do |company|
      @product_claim.company = company
      assert @product_claim.valid?, "ProductClaim should be valid with company: #{company}"
    end
  end

  # ===============================
  # DEPENDENT DESTROY TESTS
  # ===============================

  test "should be destroyed when product is destroyed" do
    @product_claim.save!
    claim_id = @product_claim.id
    
    @product.destroy
    
    assert_raises(ActiveRecord::RecordNotFound) { ProductClaim.find(claim_id) }
  end
end