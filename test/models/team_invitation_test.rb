# == Schema Information
#
# Table name: team_invitations
#
#  id            :integer          not null, primary key
#  email         :string
#  expires_at    :datetime
#  message       :text
#  role          :string
#  status        :string           default("pending")
#  token         :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  account_id    :integer          not null
#  invited_by_id :integer          not null
#
# Indexes
#
#  index_team_invitations_on_account_id            (account_id)
#  index_team_invitations_on_account_id_and_email  (account_id,email) UNIQUE
#  index_team_invitations_on_invited_by_id         (invited_by_id)
#  index_team_invitations_on_token                 (token) UNIQUE
#
# Foreign Keys
#
#  account_id     (account_id => accounts.id)
#  invited_by_id  (invited_by_id => users.id)
#
require "test_helper"

class TeamInvitationTest < ActiveSupport::TestCase
  def setup
    @user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "<PERSON>",
      last_name: "Inviter"
    )
    
    @account = Account.create!(
      name: "Test Company",
      account_type: "vendor",
      status: "approved",
      owner: @user,
      approved_at: 1.week.ago
    )
    
    @invitation = TeamInvitation.new(
      account: @account,
      invited_by: @user,
      email: "<EMAIL>",
      role: "member"
    )
  end

  test "should be valid with valid attributes" do
    assert @invitation.valid?
  end

  test "should require email" do
    @invitation.email = nil
    assert_not @invitation.valid?
    assert_includes @invitation.errors[:email], "can't be blank"
  end

  test "should require email with empty string" do
    # Skip domain validation by making invited_by a super admin
    @user.update!(super_admin: true)
    @invitation.email = ""
    assert_not @invitation.valid?
    assert_includes @invitation.errors[:email], "can't be blank"
  end

  test "should require valid email format" do
    @invitation.email = "invalid_email"
    assert_not @invitation.valid?
    assert_includes @invitation.errors[:email], "is invalid"
  end

  test "should require valid role" do
    @invitation.role = "invalid"
    assert_not @invitation.valid?
    assert_includes @invitation.errors[:role], "is not included in the list"
  end

  test "should generate token and expiry on create" do
    @invitation.save!
    assert_not_nil @invitation.token
    assert_not_nil @invitation.expires_at
    assert @invitation.expires_at > Time.current
  end

  test "should not allow duplicate email for same account" do
    @invitation.save!
    
    duplicate = TeamInvitation.new(
      account: @account,
      invited_by: @user,
      email: "<EMAIL>",
      role: "admin"
    )
    
    assert_not duplicate.valid?
    assert_includes duplicate.errors[:email], "has already been invited to this account"
  end

  test "should check if expired" do
    @invitation.expires_at = 1.day.ago
    assert @invitation.expired?
    
    @invitation.expires_at = 1.day.from_now
    assert_not @invitation.expired?
  end

  # ===============================
  # ADDITIONAL VALIDATION TESTS
  # ===============================

  test "should require account" do
    @invitation.account = nil
    assert_not @invitation.valid?
    assert_includes @invitation.errors[:account], "must exist"
  end

  test "should require invited_by user" do
    @invitation.invited_by = nil
    assert_not @invitation.valid?
    assert_includes @invitation.errors[:invited_by], "must exist"
  end

  test "should validate email format comprehensively" do
    # Test with same domain as account owner to avoid domain validation
    @user.update!(email_address: "<EMAIL>")
    
    valid_emails = [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>"
    ]

    valid_emails.each do |email|
      @invitation.email = email
      assert @invitation.valid?, "#{email} should be a valid email format"
    end

    invalid_emails = [
      "invalid_email",
      "@example.com", 
      "user@",
      "user@.com",
      "user <EMAIL>"
    ]

    invalid_emails.each do |email|
      @invitation.email = email
      assert_not @invitation.valid?, "#{email} should be invalid email format"
      assert_includes @invitation.errors[:email], "is invalid"
    end
    
    # Test empty string separately (skip domain validation)
    @user.update!(super_admin: true) 
    @invitation.email = ""
    assert_not @invitation.valid?
    assert_includes @invitation.errors[:email], "can't be blank"
  end

  test "should validate role inclusion" do
    valid_roles = %w[admin member]
    
    valid_roles.each do |role|
      @invitation.role = role
      assert @invitation.valid?, "#{role} should be a valid role"
    end

    invalid_roles = ["owner", "guest", "invalid", "", nil]
    
    invalid_roles.each do |role|
      @invitation.role = role
      assert_not @invitation.valid?, "#{role.inspect} should be invalid role"
      assert_includes @invitation.errors[:role], "is not included in the list"
    end
  end

  test "should validate email domain matches account when not super admin" do
    # Set up account owner with specific domain
    @user.update!(email_address: "<EMAIL>")
    
    # Same domain should be valid
    @invitation.email = "<EMAIL>"
    assert @invitation.valid?
    
    # Different domain should be invalid
    @invitation.email = "<EMAIL>"
    assert_not @invitation.valid?
    assert_includes @invitation.errors[:email], "must have the same email domain as the account (@company.com)"
  end

  test "should allow any email domain when invited by super admin" do
    @user.update!(super_admin: true)
    
    # Different domain should be valid when invited by super admin
    @invitation.email = "<EMAIL>"
    assert @invitation.valid?
  end

  # ===============================
  # ASSOCIATION TESTS
  # ===============================

  test "should belong to account" do
    assert_respond_to @invitation, :account
    assert_instance_of Account, @invitation.account
  end

  test "should belong to invited_by user" do
    assert_respond_to @invitation, :invited_by
    assert_instance_of User, @invitation.invited_by
  end

  # ===============================
  # ENUM TESTS
  # ===============================

  test "should have correct status enum values" do
    assert_equal "pending", TeamInvitation.statuses[:pending]
    assert_equal "accepted", TeamInvitation.statuses[:accepted]
    assert_equal "declined", TeamInvitation.statuses[:declined]
    assert_equal "expired", TeamInvitation.statuses[:expired]
  end

  test "should respond to status predicate methods" do
    @invitation.save! # Need to save to set expires_at for expired? method
    
    @invitation.status = "pending"
    assert @invitation.pending?
    assert_not @invitation.accepted?
    assert_not @invitation.declined?
    # Note: expired? checks expires_at vs current time, not status
    
    @invitation.status = "accepted"
    assert @invitation.accepted?
    assert_not @invitation.pending?
    assert_not @invitation.declined?
  end

  test "should allow setting status using enum methods" do
    @invitation.save! # Need to save first to use enum methods
    
    @invitation.pending!
    assert @invitation.pending?
    assert_equal "pending", @invitation.status

    @invitation.accepted!
    assert @invitation.accepted?
    assert_equal "accepted", @invitation.status

    @invitation.declined!
    assert @invitation.declined?
    assert_equal "declined", @invitation.status

    @invitation.expired!
    assert_equal "expired", @invitation.status
    # Note: expired! sets status, but expired? method checks expires_at vs time
  end

  # ===============================
  # SCOPE TESTS
  # ===============================

  test "should have active scope" do
    # Clear existing invitations to avoid interference
    TeamInvitation.delete_all
    
    # Create active invitation
    active_invitation = TeamInvitation.create!(
      account: @account,
      invited_by: @user,
      email: "<EMAIL>",
      role: "member",
      status: "pending",
      expires_at: 1.day.from_now
    )
    
    # Create expired invitation  
    expired_invitation = TeamInvitation.create!(
      account: @account,
      invited_by: @user,
      email: "<EMAIL>",
      role: "member",
      status: "pending"
    )
    expired_invitation.update_column(:expires_at, 1.day.ago) # Skip validations
    
    # Create accepted invitation
    accepted_invitation = TeamInvitation.create!(
      account: @account,
      invited_by: @user,
      email: "<EMAIL>",
      role: "member",
      status: "accepted",
      expires_at: 1.day.from_now
    )
    
    active_invitations = TeamInvitation.active
    assert_includes active_invitations, active_invitation
    assert_not_includes active_invitations, expired_invitation
    assert_not_includes active_invitations, accepted_invitation
  end

  # ===============================
  # TOKEN AND EXPIRY TESTS
  # ===============================

  test "should generate unique token on create" do
    invitation1 = TeamInvitation.create!(
      account: @account,
      invited_by: @user,
      email: "<EMAIL>",
      role: "member"
    )
    
    invitation2 = TeamInvitation.create!(
      account: @account,
      invited_by: @user,
      email: "<EMAIL>",
      role: "member"
    )
    
    assert_not_nil invitation1.token
    assert_not_nil invitation2.token
    assert_not_equal invitation1.token, invitation2.token
    assert invitation1.token.length > 20, "Token should be reasonably long"
  end

  test "should set expiry to 7 days from now on create" do
    before_create = Time.current
    @invitation.save!
    after_create = Time.current
    
    expected_expiry = 7.days.from_now
    assert @invitation.expires_at > before_create + 7.days - 1.minute
    assert @invitation.expires_at < after_create + 7.days + 1.minute
  end

  test "should check expired status correctly" do
    # Future expiry should not be expired
    @invitation.expires_at = 1.hour.from_now
    assert_not @invitation.expired?
    
    # Past expiry should be expired
    @invitation.expires_at = 1.hour.ago
    assert @invitation.expired?
    
    # Current time should be expired (edge case)
    @invitation.expires_at = Time.current
    assert @invitation.expired?
  end

  # ===============================
  # BUSINESS LOGIC TESTS
  # ===============================

  test "should accept invitation successfully" do
    @invitation.save!
    
    invitee = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "New",
      last_name: "Member"
    )
    
    result = @invitation.accept!(invitee)
    
    assert result, "Accept should return true"
    assert @invitation.accepted?, "Invitation should be accepted"
    
    # Check that AccountUser was created
    account_user = AccountUser.find_by(user: invitee, account: @account)
    assert_not_nil account_user, "AccountUser should be created"
    assert_equal @invitation.role, account_user.role
    assert_not_nil account_user.joined_at
  end

  test "should not accept expired invitation" do
    # Save first to set the token and initial expires_at
    @invitation.save!
    # Then update expires_at to past
    @invitation.update!(expires_at: 1.day.ago)
    
    invitee = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "New",
      last_name: "Member"
    )
    
    result = @invitation.accept!(invitee)
    
    assert_not result, "Accept should return false for expired invitation"
    assert_not @invitation.accepted?, "Invitation should not be accepted"
    
    # Check that AccountUser was not created
    account_user = AccountUser.find_by(user: invitee, account: @account)
    assert_nil account_user, "AccountUser should not be created"
  end

  test "should decline invitation" do
    @invitation.save!
    
    @invitation.decline!
    
    assert @invitation.declined?, "Invitation should be declined"
    assert_equal "declined", @invitation.status
  end

  test "should handle duplicate acceptance gracefully" do
    @invitation.save!
    
    # Create existing account user
    invitee = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "New",
      last_name: "Member"
    )
    
    AccountUser.create!(
      user: invitee,
      account: @account,
      role: "member"
    )
    
    # Try to accept invitation - should fail due to uniqueness constraint
    result = @invitation.accept!(invitee)
    
    assert_not result, "Accept should return false when user already in account"
    assert_not @invitation.accepted?, "Invitation should not be accepted"
  end

  # ===============================
  # HELPER METHOD TESTS
  # ===============================

  test "should get account email domain" do
    @user.update!(email_address: "<EMAIL>")
    assert_equal "company.com", @invitation.account_email_domain
    
    # Test with account owner having different email
    @user.update!(email_address: "<EMAIL>")
    assert_equal "example.org", @invitation.account_email_domain
  end

  test "should get invitation email domain" do
    @invitation.email = "<EMAIL>"
    assert_equal "example.org", @invitation.invitation_email_domain
    
    @invitation.email = nil
    assert_nil @invitation.invitation_email_domain
  end

  test "should check if invited by super admin" do
    assert_not @invitation.invited_by_super_admin?
    
    @user.update!(super_admin: true)
    assert @invitation.invited_by_super_admin?
  end

  # ===============================
  # EDGE CASE TESTS
  # ===============================

  test "should handle special characters in email" do
    special_emails = [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>"
    ]
    
    special_emails.each do |email|
      @invitation.email = email
      assert @invitation.valid?, "#{email} should be valid"
    end
  end

  test "should handle unicode characters in message" do
    unicode_message = "Welcome to our team! 欢迎 🎉"
    
    @invitation.message = unicode_message
    assert @invitation.valid?, "Should handle unicode in message"
    assert_equal unicode_message, @invitation.message
  end

  test "should handle very long message" do
    long_message = "A" * 2000
    
    @invitation.message = long_message
    assert @invitation.valid?, "Should handle long message"
    assert_equal long_message, @invitation.message
  end

  test "should handle nil message" do
    @invitation.message = nil
    assert @invitation.valid?, "Should allow nil message"
  end

  # ===============================
  # DATABASE CONSTRAINT TESTS
  # ===============================

  test "should enforce foreign key constraints" do
    assert_nothing_raised do
      @invitation.account
      @invitation.invited_by
    end
  end

  test "should save and reload correctly" do
    @invitation.message = "Welcome to the team!"
    assert @invitation.save, "TeamInvitation should save successfully"
    
    reloaded_invitation = TeamInvitation.find(@invitation.id)
    
    assert_equal @invitation.email, reloaded_invitation.email
    assert_equal @invitation.role, reloaded_invitation.role
    assert_equal @invitation.status, reloaded_invitation.status
    assert_equal @invitation.message, reloaded_invitation.message
    assert_equal @invitation.account_id, reloaded_invitation.account_id
    assert_equal @invitation.invited_by_id, reloaded_invitation.invited_by_id
    assert_not_nil reloaded_invitation.token
    assert_not_nil reloaded_invitation.expires_at
    assert_not_nil reloaded_invitation.created_at
    assert_not_nil reloaded_invitation.updated_at
  end

  test "should allow same email for different accounts" do
    @invitation.save!
    
    other_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Other",
      last_name: "User"
    )
    
    other_account = Account.create!(
      name: "Other Company",
      account_type: "vendor",
      status: "approved",
      owner: other_user
    )
    
    other_invitation = TeamInvitation.new(
      account: other_account,
      invited_by: other_user,
      email: "<EMAIL>", # Same email as @invitation
      role: "member"
    )
    
    assert other_invitation.valid?, "Same email should be valid for different accounts"
  end

  # ===============================
  # STATUS WORKFLOW TESTS
  # ===============================

  test "should handle complete invitation workflow" do
    # Start with pending invitation
    @invitation.save!
    assert @invitation.pending?
    assert_not @invitation.expired?
    
    # Create invitee
    invitee = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "New",
      last_name: "Member"
    )
    
    # Accept invitation
    result = @invitation.accept!(invitee)
    assert result
    assert @invitation.accepted?
    
    # Verify AccountUser was created with correct attributes
    account_user = AccountUser.find_by(user: invitee, account: @account)
    assert_not_nil account_user
    assert_equal "member", account_user.role
    assert_not_nil account_user.joined_at
  end
end
