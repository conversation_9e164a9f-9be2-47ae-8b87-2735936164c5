require "test_helper"

class ApplicationHelperTest < ActionView::TestCase
  include ApplicationHelper

  test "includes Pagy::Frontend module" do
    assert_includes ApplicationHelper.included_modules, Pagy::Frontend
  end

  test "has access to pagy helper methods" do
    # Test that pagy helper methods are available
    assert_respond_to self, :pagy_nav
    assert_respond_to self, :pagy_info
    assert_respond_to self, :pagy_url_for
  end

  test "pagy_nav generates navigation HTML" do
    # Create a simple pagy object for testing
    pagy = Pagy.new(count: 100, page: 2, limit: 10)
    
    nav_html = pagy_nav(pagy)
    
    assert_not_nil nav_html
    assert_kind_of String, nav_html
    assert_includes nav_html, 'nav'
  end

  test "pagy_info generates page information" do
    pagy = Pagy.new(count: 100, page: 2, limit: 10)
    
    info_html = pagy_info(pagy)
    
    assert_not_nil info_html
    assert_kind_of String, info_html
    # Should contain information about current page and total
    assert_includes info_html.downcase, 'displaying'
  end

  test "pagy_url_for generates correct URLs" do
    pagy = Pagy.new(count: 100, page: 2, limit: 10)
    
    url = pagy_url_for(pagy, 3)
    
    assert_not_nil url
    assert_includes url, "page=3"
  end

  test "pagy handles edge cases" do
    # Test with single page
    pagy_single = Pagy.new(count: 5, page: 1, limit: 10)
    nav_single = pagy_nav(pagy_single)
    
    assert_not_nil nav_single
    
    # Test with first page
    pagy_first = Pagy.new(count: 100, page: 1, limit: 10)
    nav_first = pagy_nav(pagy_first)
    
    assert_not_nil nav_first
    
    # Test with last page
    pagy_last = Pagy.new(count: 100, page: 10, limit: 10)
    nav_last = pagy_nav(pagy_last)
    
    assert_not_nil nav_last
  end

  test "pagy respects configuration options" do
    # Test with custom page size
    pagy_custom = Pagy.new(count: 100, page: 1, limit: 5)
    
    assert_equal 5, pagy_custom.limit
    assert_equal 20, pagy_custom.pages
    
    info = pagy_info(pagy_custom)
    assert_not_nil info
  end

  test "pagy_nav handles empty results" do
    pagy_empty = Pagy.new(count: 0, page: 1, limit: 10)
    nav_empty = pagy_nav(pagy_empty)
    
    # Should handle empty results gracefully
    assert_not_nil nav_empty
  end

  test "pagy helper methods are safe for HTML output" do
    pagy = Pagy.new(count: 50, page: 1, limit: 10)
    
    # All pagy helpers should return strings
    assert_kind_of String, pagy_nav(pagy)
    assert_kind_of String, pagy_info(pagy)
  end

  test "application helper responds to standard Rails helpers" do
    # Verify we have access to standard Rails view helpers
    assert_respond_to self, :content_tag
    assert_respond_to self, :link_to
    assert_respond_to self, :url_for
  end
end