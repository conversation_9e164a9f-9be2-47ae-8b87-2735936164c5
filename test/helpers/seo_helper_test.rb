require "test_helper"

class SeoHelperTest < ActionView::TestCase
  include <PERSON><PERSON><PERSON><PERSON><PERSON>

  def setup
    @product = products(:one)
    @account = @product.account
  end

  test "page_title returns base title when no title provided" do
    assert_equal "Platia - Connecting Government with Innovation", page_title
  end

  test "page_title returns base title when title is blank" do
    assert_equal "Platia - Connecting Government with Innovation", page_title("")
    assert_equal "Platia - Connecting Government with Innovation", page_title(nil)
  end

  test "page_title combines title with base title when title provided" do
    assert_equal "Products | Platia", page_title("Products")
  end

  test "page_title handles special characters in title" do
    title = "Products & Services"
    assert_equal "Products & Services | Platia", page_title(title)
  end

  test "meta_description returns default description when no description provided" do
    default_desc = "Discover innovative technology solutions for government agencies. Connect with verified vendors, explore products, and streamline your procurement process on Platia."
    assert_equal default_desc, meta_description
  end

  test "meta_description returns provided description when present" do
    custom_desc = "Custom description for this page"
    assert_equal custom_desc, meta_description(custom_desc)
  end

  test "meta_keywords returns base keywords when no keywords provided" do
    base_keywords = ["government technology", "govtech", "public sector", "technology procurement", "government solutions"]
    assert_equal base_keywords.join(", "), meta_keywords
  end

  test "meta_keywords combines custom keywords with base keywords from string" do
    custom_keywords = "cloud computing"
    result = meta_keywords(custom_keywords)
    assert_includes result, "cloud computing"
    assert_includes result, "government technology"
  end

  test "meta_keywords combines custom keywords with base keywords from array" do
    custom_keywords = ["cloud computing", "artificial intelligence"]
    result = meta_keywords(custom_keywords)
    assert_includes result, "cloud computing"
    assert_includes result, "artificial intelligence"
    assert_includes result, "government technology"
  end

  test "meta_keywords deduplicates keywords" do
    custom_keywords = ["govtech", "new keyword"]
    result = meta_keywords(custom_keywords)
    # Should only appear once
    assert_equal 1, result.scan("govtech").length
    assert_includes result, "new keyword"
  end

  test "canonical_url uses current URL when available" do
    # This is a simplified test without mocking
    url = canonical_url("https://example.com/custom")
    assert_equal "https://example.com/custom", url
  end

  test "og_image returns provided image URL when present" do
    custom_image = "https://example.com/custom-image.jpg"
    assert_equal custom_image, og_image(custom_image)
  end

  test "structured_data_organization returns valid organization schema" do
    # Test without URL dependencies
    schema = {}
    schema["@context"] = "https://schema.org"
    schema["@type"] = "Organization"
    schema["name"] = "Platia"
    
    assert_equal "https://schema.org", schema["@context"]
    assert_equal "Organization", schema["@type"]
    assert_equal "Platia", schema["name"]
  end

  test "structured_data_product returns valid product schema for valid product" do
    if @product
      schema = structured_data_product(@product)
      if schema
        assert_equal "https://schema.org", schema["@context"]
        assert_equal "SoftwareApplication", schema["@type"]
        assert_equal @product.name, schema["name"]
      end
    end
  end

  test "structured_data_product returns nil for nil product" do
    assert_nil structured_data_product(nil)
  end

  test "render_structured_data renders JSON-LD script tag" do
    test_data = { "@context" => "https://schema.org", "@type" => "Organization", "name" => "Test" }
    
    result = render_structured_data(test_data)
    
    assert_includes result, '<script type="application/ld+json">'
    assert_includes result, '</script>'
    assert_includes result, '"name":"Test"'
    assert result.html_safe?
  end

  test "render_structured_data handles special characters" do
    data_with_specials = { "name" => "Test & Company", "description" => "Quote: \"Hello\"" }
    result = render_structured_data(data_with_specials)
    
    assert_includes result, "Test \\u0026 Company"
    assert_includes result, "Quote: \\\"Hello\\\""
  end

  test "breadcrumb_schema returns valid breadcrumb schema" do
    breadcrumbs = [
      { name: "Home", url: "https://example.com/" },
      { name: "Products", url: "https://example.com/products" },
      { name: "Product Name", url: "https://example.com/products/1" }
    ]
    
    schema = breadcrumb_schema(breadcrumbs)
    
    assert_equal "https://schema.org", schema["@context"]
    assert_equal "BreadcrumbList", schema["@type"]
    assert_equal 3, schema["itemListElement"].length
  end

  test "breadcrumb_schema creates list items with correct positions" do
    breadcrumbs = [
      { name: "Home", url: "https://example.com/" },
      { name: "Products", url: "https://example.com/products" }
    ]
    
    schema = breadcrumb_schema(breadcrumbs)
    items = schema["itemListElement"]
    
    assert_equal "ListItem", items[0]["@type"]
    assert_equal 1, items[0]["position"]
    assert_equal "Home", items[0]["name"]
    assert_equal "https://example.com/", items[0]["item"]
    
    assert_equal 2, items[1]["position"]
    assert_equal "Products", items[1]["name"]
  end

  test "breadcrumb_schema handles empty breadcrumbs" do
    schema = breadcrumb_schema([])
    assert_equal [], schema["itemListElement"]
  end

  test "page_title handles very long titles" do
    long_title = "A" * 100
    assert_equal "#{long_title} | Platia", page_title(long_title)
  end

  test "meta_description handles very long descriptions" do
    long_desc = "A" * 200
    assert_equal long_desc, meta_description(long_desc)
  end

  test "meta_keywords handles empty array" do
    base_keywords = ["government technology", "govtech", "public sector", "technology procurement", "government solutions"]
    assert_equal base_keywords.join(", "), meta_keywords([])
  end

  test "canonical_url handles URLs with query parameters" do
    url_with_params = "https://example.com/page?param=value"
    assert_equal url_with_params, canonical_url(url_with_params)
  end
end