require "test_helper"

class VideoCompressionJobTest < ActiveJob::TestCase
  def setup
    @product = products(:one)
    @job = VideoCompressionJob.new
  end

  test "should be enqueued" do
    assert_enqueued_jobs 1 do
      VideoCompressionJob.perform_later("Product", @product.id, "video")
    end
  end

  test "should use default queue" do
    assert_equal "default", VideoCompressionJob.queue_name
  end

  test "create_output_path generates correct output path" do
    input_path = "/tmp/video_input123.mp4"
    output_path = @job.send(:create_output_path, input_path)
    
    assert_equal "/tmp/video_input123_compressed.mp4", output_path
  end

  test "create_output_path handles different extensions" do
    input_path = "/tmp/video.avi"
    output_path = @job.send(:create_output_path, input_path)
    
    assert_equal "/tmp/video_compressed.mp4", output_path
  end

  test "create_output_path handles files without extensions" do
    input_path = "/tmp/video_file"
    output_path = @job.send(:create_output_path, input_path)
    
    assert_equal "/tmp/video_file_compressed.mp4", output_path
  end

  test "cleanup_temp_files deletes existing files" do
    # Create temporary test files
    temp_file1 = Rails.root.join('tmp', 'test_cleanup1.tmp')
    temp_file2 = Rails.root.join('tmp', 'test_cleanup2.tmp')
    
    File.write(temp_file1, "temp data 1")
    File.write(temp_file2, "temp data 2")
    
    @job.send(:cleanup_temp_files, temp_file1.to_s, temp_file2.to_s)
    
    assert_not File.exist?(temp_file1)
    assert_not File.exist?(temp_file2)
  end

  test "cleanup_temp_files handles non-existent files gracefully" do
    non_existent = "/tmp/non_existent_file.tmp"
    
    assert_nothing_raised do
      @job.send(:cleanup_temp_files, non_existent)
    end
  end

  test "cleanup_temp_files handles nil paths gracefully" do
    temp_file = Rails.root.join('tmp', 'test_cleanup3.tmp')
    File.write(temp_file, "temp data")
    
    assert_nothing_raised do
      @job.send(:cleanup_temp_files, temp_file.to_s, nil)
    end
    
    assert_not File.exist?(temp_file)
  end

  test "perform raises error for non-existent model" do
    assert_raises(ActiveRecord::RecordNotFound) do
      @job.perform("Product", 999999, "video")
    end
  end

  test "perform raises error for invalid model class" do
    assert_raises(NameError) do
      @job.perform("InvalidModel", 1, "video")
    end
  end

  test "job has correct queue name and can be enqueued" do
    # Test job queue configuration without trying to perform
    assert_equal "default", VideoCompressionJob.queue_name
    
    # Test that job can be enqueued
    assert_enqueued_jobs 1 do
      VideoCompressionJob.perform_later("Product", @product.id, "video")
    end
  end
end