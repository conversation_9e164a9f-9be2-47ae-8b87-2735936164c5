# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: case_studies
#
#  id          :integer          not null, primary key
#  description :text
#  position    :integer
#  published   :boolean          default(FALSE)
#  title       :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  product_id  :integer          not null
#
# Indexes
#
#  index_case_studies_on_product_id  (product_id)
#
# Foreign Keys
#
#  product_id  (product_id => products.id)
#
one:
  product: one
  title: MyString
  description: MyText
  position: 1
  published: false

two:
  product: two
  title: MyString
  description: MyText
  position: 1
  published: false
