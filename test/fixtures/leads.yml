# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: leads
#
#  id                :integer          not null, primary key
#  budget_range      :string
#  contact_company   :string
#  contact_email     :string
#  contact_name      :string
#  contact_phone     :string
#  message           :text
#  notes             :text
#  source            :string
#  status            :string
#  timeline          :string
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  account_id        :integer
#  agency_account_id :integer
#  product_id        :integer          not null
#  user_id           :integer
#
# Indexes
#
#  index_leads_on_account_id         (account_id)
#  index_leads_on_agency_account_id  (agency_account_id)
#  index_leads_on_product_id         (product_id)
#  index_leads_on_user_id            (user_id)
#
# Foreign Keys
#
#  account_id         (account_id => accounts.id)
#  agency_account_id  (agency_account_id => accounts.id)
#  product_id         (product_id => products.id)
#  user_id            (user_id => users.id)
#
pending_lead:
  product: one
  account: one
  user: one
  agency_account: two
  status: pending
  contact_name: <PERSON>
  contact_email: <EMAIL>
  contact_company: Department of Technology
  contact_phone: "(*************"
  message: We are interested in this product for our digital transformation initiative
  notes: Initial inquiry from agency
  budget_range: "$50,000 - $100,000"
  timeline: "Q2 2024"
  source: website

contacted_lead:
  product: two
  account: two
  user: two
  agency_account: one
  status: contacted
  contact_name: Jane Doe
  contact_email: <EMAIL>
  contact_company: City of Springfield
  contact_phone: "(*************"
  message: Looking for a solution to improve citizen services
  notes: |
    First contact made on 2024-01-15
    Follow-up scheduled for next week
  budget_range: "$25,000 - $50,000"
  timeline: "3-6 months"
  source: referral

qualified_lead:
  product: one
  account: one
  user: one
  status: qualified
  contact_name: Robert Johnson
  contact_email: <EMAIL>
  contact_company: State Department of Health
  contact_phone: "(*************"
  message: Need immediate implementation for compliance requirements
  notes: |
    Qualified lead with approved budget
    Decision maker confirmed
    Technical requirements reviewed
  budget_range: "$100,000+"
  timeline: "ASAP"
  source: conference

closed_lead:
  product: two
  account: two
  user: two
  status: closed
  contact_name: Sarah Wilson
  contact_email: <EMAIL>
  contact_company: County Public Works
  contact_phone: "(*************"
  message: Project completed successfully
  notes: |
    Deal closed on 2024-02-01
    Contract signed for $75,000
    Implementation scheduled for Q2
  budget_range: "$50,000 - $100,000"
  timeline: "Completed"
  source: webinar

# Lead without optional associations for testing
minimal_lead:
  product: one
  status: pending
  contact_name: Mike Brown
  contact_email: <EMAIL>
  contact_company: Independent Agency
  message: Basic inquiry without user or account association
