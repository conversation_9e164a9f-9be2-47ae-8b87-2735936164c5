# == Schema Information
#
# Table name: product_features
#
#  id          :integer          not null, primary key
#  description :text
#  name        :string           not null
#  position    :integer          default(0)
#  published   :boolean          default(FALSE)
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  product_id  :integer          not null
#
# Indexes
#
#  index_product_features_on_product_id               (product_id)
#  index_product_features_on_product_id_and_position  (product_id,position)
#
# Foreign Keys
#
#  product_id  (product_id => products.id)
#

product_one_sso:
  product: one
  name: "Single Sign-On (SSO)"
  description: "Seamless integration with existing identity providers including SAML 2.0, OAuth 2.0, and OpenID Connect for secure authentication."
  position: 1
  published: true

product_one_reporting:
  product: one
  name: "Advanced Analytics & Reporting"
  description: "Comprehensive dashboards and customizable reports with real-time data visualization and export capabilities."
  position: 2
  published: true

product_one_api:
  product: one
  name: "RESTful API"
  description: "Full-featured REST API with comprehensive documentation, webhooks, and SDKs for popular programming languages."
  position: 3
  published: true

product_one_mobile:
  product: one
  name: "Mobile Application"
  description: "Native iOS and Android applications with offline capability and push notifications."
  position: 4
  published: false

product_two_workflow:
  product: two
  name: "Automated Workflows"
  description: "Customizable workflow automation with approval processes, notifications, and integration capabilities."
  position: 1
  published: true

product_two_search:
  product: two
  name: "AI-Powered Search"
  description: "Intelligent document search with natural language processing and content recognition capabilities."
  position: 2
  published: true

product_two_collaboration:
  product: two
  name: "Real-time Collaboration"
  description: "Multi-user collaboration tools with version control, comments, and real-time editing capabilities."
  position: 3
  published: true

published_product_threat_detection:
  product: published_product
  name: "Advanced Threat Detection"
  description: "Machine learning-powered threat detection that identifies and blocks sophisticated email attacks including phishing, malware, and business email compromise."
  position: 1
  published: true

published_product_encryption:
  product: published_product
  name: "End-to-End Encryption"
  description: "Military-grade encryption for email content and attachments with key management and secure delivery options."
  position: 2
  published: true

published_product_compliance:
  product: published_product
  name: "Compliance Monitoring"
  description: "Automated compliance monitoring and reporting for regulations including HIPAA, SOX, and government security standards."
  position: 3
  published: true

draft_feature:
  product: one
  name: "Blockchain Integration"
  description: "Experimental blockchain-based audit trail for enhanced security and transparency. Currently in development."
  position: 5
  published: false