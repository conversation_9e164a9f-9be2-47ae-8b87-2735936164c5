# == Schema Information
#
# Table name: product_certifications
#
#  id               :integer          not null, primary key
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  certification_id :integer          not null
#  product_id       :integer          not null
#
# Indexes
#
#  index_product_certifications_on_certification_id  (certification_id)
#  index_product_certifications_on_product_id        (product_id)
#
# Foreign Keys
#
#  certification_id  (certification_id => certifications.id)
#  product_id        (product_id => products.id)
#

product_one_soc2:
  product: one
  certification: soc2

product_one_fisma:
  product: one
  certification: fisma

product_one_fedramp:
  product: one
  certification: fedramp

product_two_iso27001:
  product: two
  certification: iso27001

product_two_hipaa:
  product: two
  certification: hipaa

published_product_soc2:
  product: published_product
  certification: soc2

published_product_gdpr:
  product: published_product
  certification: gdpr

published_product_nist:
  product: published_product
  certification: nist