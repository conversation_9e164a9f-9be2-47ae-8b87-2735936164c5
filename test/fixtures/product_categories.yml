# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: product_categories
#
#  id          :integer          not null, primary key
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  category_id :integer          not null
#  product_id  :integer          not null
#
# Indexes
#
#  index_product_categories_on_category_id                 (category_id)
#  index_product_categories_on_product_id                  (product_id)
#  index_product_categories_on_product_id_and_category_id  (product_id,category_id) UNIQUE
#
# Foreign Keys
#
#  category_id  (category_id => categories.id)
#  product_id   (product_id => products.id)
#
product_one_cloud:
  product: one
  category: cloud_storage

product_one_security:
  product: one  
  category: identity_management

product_two_analytics:
  product: two
  category: data_analytics
