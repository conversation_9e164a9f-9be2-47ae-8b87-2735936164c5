# == Schema Information
#
# Table name: product_content
#
#  id          :integer          not null, primary key
#  description :text
#  position    :integer
#  published   :boolean          default(FALSE)
#  title       :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  product_id  :integer          not null
#
# Indexes
#
#  index_product_content_on_product_id  (product_id)
#
# Foreign Keys
#
#  product_id  (product_id => products.id)
#

product_one_guide:
  product: one
  title: "Implementation Guide"
  description: "Comprehensive step-by-step guide for implementing our CRM solution in your organization. Includes best practices, configuration examples, and troubleshooting tips."
  position: 1
  published: true

product_one_whitepaper:
  product: one
  title: "Security Architecture Whitepaper"
  description: "Detailed technical documentation outlining the security architecture, encryption methods, and compliance frameworks used in our platform."
  position: 2
  published: true

product_one_datasheet:
  product: one
  title: "Technical Specifications"
  description: "Complete technical specifications including system requirements, API documentation, and integration capabilities."
  position: 3
  published: false

product_two_manual:
  product: two
  title: "User Manual"
  description: "Complete user manual covering all features and functionality of the Document Management System."
  position: 1
  published: true

product_two_training:
  product: two
  title: "Training Materials"
  description: "Training videos and materials for administrators and end users to get up to speed quickly."
  position: 2
  published: true

published_product_overview:
  product: published_product
  title: "Product Overview"
  description: "High-level overview of the Email Security Gateway features, benefits, and use cases."
  position: 1
  published: true

published_product_case_study:
  product: published_product
  title: "Government Case Study"
  description: "Real-world case study showing how a federal agency implemented our email security solution to meet compliance requirements."
  position: 2
  published: true

draft_product_content:
  product: one
  title: "Advanced Configuration Guide"
  description: "Advanced configuration options for power users and system administrators. This content is still being reviewed."
  position: 4
  published: false