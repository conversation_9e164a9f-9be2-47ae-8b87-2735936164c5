# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: accounts
#
#  id                    :integer          not null, primary key
#  account_type          :string
#  approved_at           :datetime
#  confirmation_sent_at  :datetime
#  confirmation_token    :string
#  confirmed_at          :datetime
#  description           :text
#  headquarters_location :string
#  linkedin_url          :string
#  name                  :string
#  slug                  :string
#  status                :string
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#  approved_by_id        :integer
#  owner_id              :integer          not null
#
# Indexes
#
#  index_accounts_on_approved_by_id      (approved_by_id)
#  index_accounts_on_confirmation_token  (confirmation_token) UNIQUE
#  index_accounts_on_owner_id            (owner_id)
#  index_accounts_on_slug                (slug)
#
# Foreign Keys
#
#  approved_by_id  (approved_by_id => users.id)
#  owner_id        (owner_id => users.id)
#
one:
  name: TechCorp Solutions
  account_type: vendor
  status: approved
  approved_at: <%= 1.week.ago %>
  confirmed_at: <%= 1.week.ago %>
  approved_by: admin
  owner: one
  slug: techcorp-solutions

two:
  name: City of Springfield
  account_type: agency
  status: approved
  approved_at: <%= 1.week.ago %>
  confirmed_at: <%= 1.week.ago %>
  approved_by: admin
  owner: two
  slug: city-of-springfield

vendor_account:
  name: TechCorp Solutions
  account_type: vendor
  status: approved
  approved_at: <%= 1.week.ago %>
  approved_by: admin
  owner: one
  slug: techcorp-solutions-2

government_account:
  name: City of Springfield
  account_type: agency
  status: approved
  approved_at: <%= 1.week.ago %>
  approved_by: admin
  owner: two
  slug: city-of-springfield-2

pending_vendor:
  name: StartupTech Inc
  account_type: vendor
  status: pending
  owner: one
  slug: startuptech-inc

rejected_vendor:
  name: RejectedCorp
  account_type: vendor
  status: rejected
  approved_by: admin
  owner: one
  slug: rejectedcorp

agency:
  name: Federal Agency of Technology
  account_type: agency
  status: approved
  approved_at: <%= 1.week.ago %>
  confirmed_at: <%= 1.week.ago %>
  approved_by: admin
  owner: two
  slug: federal-agency-technology
