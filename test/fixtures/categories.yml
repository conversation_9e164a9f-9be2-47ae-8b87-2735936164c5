# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: categories
#
#  id          :integer          not null, primary key
#  description :text
#  name        :string
#  slug        :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  parent_id   :integer
#
# Indexes
#
#  index_categories_on_parent_id  (parent_id)
#
# Foreign Keys
#
#  parent_id  (parent_id => categories.id)
#
cloud_computing:
  name: Cloud Computing
  slug: cloud-computing
  description: Cloud-based solutions and services
  
security:
  name: Cybersecurity
  slug: cybersecurity
  description: Security tools and solutions

data_analytics:
  name: Data Analytics
  slug: data-analytics
  description: Data analysis and visualization tools

# Child categories
cloud_storage:
  name: Cloud Storage
  slug: cloud-storage
  description: Cloud storage solutions
  parent: cloud_computing

identity_management:
  name: Identity Management
  slug: identity-management
  description: Identity and access management
  parent: security
