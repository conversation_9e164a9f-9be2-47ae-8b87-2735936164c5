# == Schema Information
#
# Table name: account_invitations
#
#  id            :integer          not null, primary key
#  account_name  :string
#  account_type  :string
#  department    :string
#  email         :string
#  expires_at    :datetime
#  first_name    :string
#  job_title     :string
#  last_name     :string
#  phone         :string
#  status        :string
#  token         :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  invited_by_id :integer          not null
#
# Indexes
#
#  index_account_invitations_on_invited_by_id  (invited_by_id)
#
# Foreign Keys
#
#  invited_by_id  (invited_by_id => users.id)
#

pending_vendor_invitation:
  email: "<EMAIL>"
  account_type: "vendor"
  account_name: "TechSolutions Inc"
  first_name: "<PERSON>"
  last_name: "<PERSON>"
  phone: "(*************"
  job_title: "CEO"
  department: "Executive"
  status: "pending"
  token: "secure_token_123456789abcdef"
  expires_at: <%= 5.days.from_now %>
  invited_by: admin

pending_agency_invitation:
  email: "<EMAIL>"
  account_type: "agency"
  account_name: "City Government IT Department"
  first_name: "<PERSON>"
  last_name: "<PERSON>"
  phone: "(*************"
  job_title: "IT Director"
  department: "Information Technology"
  status: "pending"
  token: "secure_token_987654321fedcba"
  expires_at: <%= 6.days.from_now %>
  invited_by: admin

expired_invitation:
  email: "<EMAIL>"
  account_type: "vendor"
  account_name: "Expired Corp"
  first_name: "John"
  last_name: "Expired"
  job_title: "Manager"
  status: "pending"
  token: "expired_token_abcdef123456789"
  expires_at: <%= 1.day.ago %>
  invited_by: admin

accepted_invitation:
  email: "<EMAIL>"
  account_type: "vendor"
  account_name: "New Company LLC"
  first_name: "Sarah"
  last_name: "Accepted"
  job_title: "Founder"
  status: "accepted"
  token: "accepted_token_123abc456def789"
  expires_at: <%= 3.days.from_now %>
  invited_by: admin

recent_vendor_invitation:
  email: "<EMAIL>"
  account_type: "vendor"
  account_name: "Innovative Tech Startup"
  first_name: "David"
  last_name: "Innovation"
  job_title: "CTO"
  status: "pending"
  token: "recent_token_xyz789abc123def"
  expires_at: <%= 7.days.from_now %>
  invited_by: admin

agency_invitation_by_user:
  email: "<EMAIL>"
  account_type: "agency"
  account_name: "State Agency Department"
  first_name: "Jennifer"
  last_name: "State"
  job_title: "Director"
  status: "pending"
  token: "user_invite_token_def456ghi789"
  expires_at: <%= 4.days.from_now %>
  invited_by: one
