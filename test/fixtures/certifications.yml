# == Schema Information
#
# Table name: certifications
#
#  id          :integer          not null, primary key
#  description :text
#  name        :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#

soc2:
  name: "SOC 2 Type II"
  description: "System and Organization Controls 2 (SOC 2) Type II certification demonstrates that the organization has effective controls in place for security, availability, processing integrity, confidentiality, and privacy."

fisma:
  name: "FISMA Moderate"
  description: "Federal Information Security Management Act (FISMA) Moderate designation indicates the system has been assessed and authorized to operate with appropriate security controls for moderate-impact data."

fedramp:
  name: "FedRAMP Authorized"
  description: "Federal Risk and Authorization Management Program (FedRAMP) authorization provides a standardized approach to security assessment, authorization, and continuous monitoring for cloud products and services."

iso27001:
  name: "ISO 27001"
  description: "ISO/IEC 27001 is an international standard that provides requirements for establishing, implementing, maintaining and continually improving an information security management system."

hipaa:
  name: "HIPAA Compliant"
  description: "Health Insurance Portability and Accountability Act (HIPAA) compliance ensures the protection of sensitive patient health information."

pci_dss:
  name: "PCI DSS Level 1"
  description: "Payment Card Industry Data Security Standard (PCI DSS) Level 1 certification for organizations processing over 6 million credit card transactions annually."

gdpr:
  name: "GDPR Compliant"
  description: "General Data Protection Regulation (GDPR) compliance ensures proper handling of personal data for EU residents."

nist:
  name: "NIST Cybersecurity Framework"
  description: "Adherence to the National Institute of Standards and Technology Cybersecurity Framework for identifying, protecting, detecting, responding to, and recovering from cybersecurity threats."