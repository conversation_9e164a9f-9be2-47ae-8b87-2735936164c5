# == Schema Information
#
# Table name: product_screenshots
#
#  id          :integer          not null, primary key
#  description :text
#  position    :integer
#  published   :boolean          default(FALSE)
#  title       :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  product_id  :integer          not null
#
# Indexes
#
#  index_product_screenshots_on_product_id  (product_id)
#
# Foreign Keys
#
#  product_id  (product_id => products.id)
#

product_one_dashboard:
  product: one
  title: "Main Dashboard"
  description: "Overview of the main dashboard showing key metrics, recent activities, and quick action buttons for common tasks."
  position: 1
  published: true

product_one_reports:
  product: one
  title: "Analytics & Reports"
  description: "Comprehensive reporting interface with customizable charts, filters, and export options for data analysis."
  position: 2
  published: true

product_one_settings:
  product: one
  title: "Configuration Settings"
  description: "Administrative settings panel for system configuration, user management, and integration setup."
  position: 3
  published: true

product_one_mobile:
  product: one
  title: "Mobile Interface"
  description: "Responsive mobile interface showing the same functionality optimized for smartphones and tablets."
  position: 4
  published: false

product_two_document_view:
  product: two
  title: "Document Viewer"
  description: "Advanced document viewer with annotation tools, version history, and collaboration features."
  position: 1
  published: true

product_two_workflow:
  product: two
  title: "Workflow Builder"
  description: "Visual workflow builder allowing administrators to create custom approval processes and automation rules."
  position: 2
  published: true

product_two_search:
  product: two
  title: "AI Search Interface"
  description: "Intelligent search interface with natural language processing and advanced filtering capabilities."
  position: 3
  published: true

published_product_threat_dashboard:
  product: published_product
  title: "Threat Detection Dashboard"
  description: "Real-time threat detection dashboard showing security alerts, blocked attacks, and system status."
  position: 1
  published: true

published_product_email_flow:
  product: published_product
  title: "Email Flow Visualization"
  description: "Visual representation of email flow through security filters with detailed inspection results."
  position: 2
  published: true

published_product_compliance:
  product: published_product
  title: "Compliance Reporting"
  description: "Automated compliance reporting interface with audit trails and regulatory requirement tracking."
  position: 3
  published: true

published_product_admin:
  product: published_product
  title: "Administrative Console"
  description: "Comprehensive administrative console for policy management, user configuration, and system monitoring."
  position: 4
  published: true

draft_screenshot:
  product: one
  title: "Beta Features Preview"
  description: "Preview of upcoming beta features including AI-powered insights and predictive analytics."
  position: 5
  published: false