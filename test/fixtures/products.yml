# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: products
#
#  id                       :integer          not null, primary key
#  description              :text
#  features                 :json
#  name                     :string
#  pricing                  :text
#  pricing_model            :text
#  published                :boolean          default(FALSE)
#  show_case_studies        :boolean          default(FALSE), not null
#  show_featured_video      :boolean          default(FALSE), not null
#  show_product_content     :boolean          default(FALSE), not null
#  show_product_contracts   :boolean          default(FALSE), not null
#  show_product_customers   :boolean          default(FALSE), not null
#  show_product_features    :boolean          default(FALSE), not null
#  show_product_screenshots :boolean          default(FALSE), not null
#  show_product_videos      :boolean          default(FALSE), not null
#  slug                     :string
#  website                  :string
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  account_id               :integer
#
# Indexes
#
#  index_products_on_account_id  (account_id)
#  index_products_on_slug        (slug)
#
# Foreign Keys
#
#  account_id  (account_id => accounts.id)
#
one:
  account: one
  name: "Enterprise CRM Solution"
  slug: "enterprise-crm-solution"
  description: "Comprehensive customer relationship management platform designed specifically for government agencies and large enterprises. Features advanced security, compliance monitoring, and integration capabilities."
  website: "https://www.techcorp.com/crm"
  published: false
  features: '[{"name": "Single Sign-On (SSO)"}, {"name": "Advanced Analytics"}, {"name": "Mobile App"}, {"name": "API Integration"}]'

two:
  account: two
  name: "Document Management System"
  slug: "document-management-system"
  description: "Secure document management and workflow automation platform for government agencies. Includes automated approvals, version control, and compliance tracking."
  website: "https://www.citytech.gov/dms"
  published: false
  features: '[{"name": "Automated Workflows"}, {"name": "Version Control"}, {"name": "Digital Signatures"}, {"name": "Audit Trail"}]'

published_product:
  account: one
  name: "Email Security Gateway"
  slug: "email-security-gateway"
  description: "Advanced email security solution protecting government communications from cyber threats. Features AI-powered threat detection, encryption, and compliance monitoring."
  website: "https://www.techcorp.com/email-security"
  published: true
  features: '[{"name": "AI Threat Detection"}, {"name": "End-to-End Encryption"}, {"name": "Compliance Monitoring"}, {"name": "Real-time Protection"}]'

draft_product:
  account: vendor_account
  name: "Network Security Monitor"
  slug: "network-security-monitor"
  description: "Real-time network monitoring and threat detection system for government networks. Currently in development with beta testing scheduled for Q2."
  website: "https://www.techcorp.com/network-monitor"
  published: false
  features: '[{"name": "Real-time Monitoring"}, {"name": "Threat Intelligence"}, {"name": "Automated Response"}, {"name": "Custom Dashboards"}]'

agency_product:
  account: government_account
  name: "Citizen Services Portal"
  slug: "citizen-services-portal"
  description: "Self-service portal for citizens to access government services, submit applications, and track requests. Designed for municipal and state governments."
  website: "https://www.cityservices.gov/portal"
  published: true
  features: '[{"name": "Online Applications"}, {"name": "Request Tracking"}, {"name": "Document Upload"}, {"name": "Payment Processing"}]'
