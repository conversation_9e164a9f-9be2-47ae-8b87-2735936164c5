# == Schema Information
#
# Table name: product_customers
#
#  id          :integer          not null, primary key
#  description :text
#  name        :string           not null
#  position    :integer          default(0)
#  published   :boolean          default(TRUE), not null
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  product_id  :integer          not null
#
# Indexes
#
#  index_product_customers_on_product_id               (product_id)
#  index_product_customers_on_product_id_and_position  (product_id,position)
#
# Foreign Keys
#
#  product_id  (product_id => products.id)
#

product_one_dept_defense:
  product: one
  name: "Department of Defense"
  description: "Successfully implemented our CRM solution across multiple military branches, improving inter-agency collaboration and streamlining procurement processes."
  position: 1
  published: true

product_one_city_chicago:
  product: one
  name: "City of Chicago"
  description: "Deployed city-wide to manage citizen services and municipal operations, resulting in 40% improvement in response times."
  position: 2
  published: true

product_one_state_california:
  product: one
  name: "State of California IT Department"
  description: "Statewide implementation supporting over 50 departments and agencies with centralized data management and reporting."
  position: 3
  published: true

product_one_federal_gsa:
  product: one
  name: "General Services Administration"
  description: "GSA uses our platform to manage federal procurement processes and vendor relationships efficiently."
  position: 4
  published: false

product_two_dept_education:
  product: two
  name: "Department of Education"
  description: "Document management system supporting educational policy development and regulatory compliance across all regional offices."
  position: 1
  published: true

product_two_irs:
  product: two
  name: "Internal Revenue Service"
  description: "Secure document management for tax processing and audit documentation with advanced encryption and access controls."
  position: 2
  published: true

product_two_city_boston:
  product: two
  name: "City of Boston"
  description: "Municipal document management covering permits, licenses, and public records with citizen self-service portal."
  position: 3
  published: true

published_product_fbi:
  product: published_product
  name: "Federal Bureau of Investigation"
  description: "Email security solution protecting sensitive law enforcement communications and case management systems."
  position: 1
  published: true

published_product_treasury:
  product: published_product
  name: "U.S. Department of Treasury"
  description: "Enterprise email security protecting financial data and regulatory communications with advanced threat detection."
  position: 2
  published: true

published_product_medicare:
  product: published_product
  name: "Centers for Medicare & Medicaid Services"
  description: "Healthcare-focused email security ensuring HIPAA compliance and protecting patient information in communications."
  position: 3
  published: true

draft_customer:
  product: one
  name: "Department of Homeland Security"
  description: "Pilot program currently in evaluation phase for national security applications."
  position: 5
  published: false