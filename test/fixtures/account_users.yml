# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: account_users
#
#  id         :integer          not null, primary key
#  joined_at  :datetime
#  role       :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  account_id :integer          not null
#  user_id    :integer          not null
#
# Indexes
#
#  index_account_users_on_account_id  (account_id)
#  index_account_users_on_user_id     (user_id)
#
# Foreign Keys
#
#  account_id  (account_id => accounts.id)
#  user_id     (user_id => users.id)
#
one:
  account: one
  user: one
  role: admin
  joined_at: 2025-07-07 15:22:39

two:
  account: two
  user: two
  role: member
  joined_at: 2025-07-07 15:22:39

# Removed to prevent agency user from having access to vendor account
# three:
#   account: one
#   user: two
#   role: member
#   joined_at: 2025-07-08 10:30:00
