# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: product_videos
#
#  id          :integer          not null, primary key
#  description :text
#  position    :integer
#  published   :boolean          default(FALSE)
#  title       :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  product_id  :integer          not null
#
# Indexes
#
#  index_product_videos_on_product_id  (product_id)
#
# Foreign Keys
#
#  product_id  (product_id => products.id)
#
one:
  product: one
  title: Demo Video
  description: Product demonstration video
  position: 1
  published: true

two:
  product: two
  title: Tutorial Video
  description: How to use the product
  position: 1
  published: false
