# == Schema Information
#
# Table name: product_claims
#
#  id         :integer          not null, primary key
#  company    :string
#  email      :string
#  message    :text
#  name       :string
#  status     :string
#  title      :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  product_id :integer          not null
#
# Indexes
#
#  index_product_claims_on_product_id  (product_id)
#
# Foreign Keys
#
#  product_id  (product_id => products.id)
#

pending_claim_one:
  product: one
  name: "<PERSON>"
  email: "<EMAIL>"
  company: "TechCompany Inc"
  title: "Senior Developer"
  message: "I believe this product description contains inaccurate information about the security features. Our team has tested this solution and found several discrepancies."
  status: "pending"

reviewed_claim_two:
  product: two
  name: "<PERSON>"
  email: "<EMAIL>"
  company: "ConsulTech Solutions"
  title: "IT Consultant"
  message: "The pricing information listed seems outdated. We received a different quote directly from the vendor."
  status: "reviewed"

approved_claim_published:
  product: published_product
  name: "<PERSON>"
  email: "<EMAIL>"
  company: "State IT Department"
  title: "Procurement Specialist"
  message: "The compliance certifications mentioned in the product description need verification. We could not find public records for some of the listed certifications."
  status: "approved"

rejected_claim_one:
  product: one
  name: "<PERSON>"
  email: "r<PERSON><PERSON>@example.com"
  company: "<PERSON> Tech"
  title: "CTO"
  message: "This claim appears to be based on outdated information and has been reviewed by our team."
  status: "rejected"

pending_claim_published:
  product: published_product
  name: "Lisa Martinez"
  email: "<EMAIL>"
  company: "Federal IT Agency"
  title: "Security Analyst"
  message: "We need clarification on the data encryption methods used by this product. The current description is too vague for our security requirements."
  status: "pending"