# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: team_invitations
#
#  id            :integer          not null, primary key
#  email         :string
#  expires_at    :datetime
#  message       :text
#  role          :string
#  status        :string           default("pending")
#  token         :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  account_id    :integer          not null
#  invited_by_id :integer          not null
#
# Indexes
#
#  index_team_invitations_on_account_id            (account_id)
#  index_team_invitations_on_account_id_and_email  (account_id,email) UNIQUE
#  index_team_invitations_on_invited_by_id         (invited_by_id)
#  index_team_invitations_on_token                 (token) UNIQUE
#
# Foreign Keys
#
#  account_id     (account_id => accounts.id)
#  invited_by_id  (invited_by_id => users.id)
#
one:
  account: one
  invited_by: one
  email: <EMAIL>
  role: member
  status: pending
  message: Welcome to our team!
  token: unique_token_abc123
  expires_at: <%= 7.days.from_now %>

two:
  account: two
  invited_by: two
  email: <EMAIL>
  role: admin
  status: pending
  message: Join our organization!
  token: unique_token_def456
  expires_at: <%= 7.days.from_now %>
