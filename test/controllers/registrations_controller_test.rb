require "test_helper"

class RegistrationsControllerTest < ActionController::TestCase
  # New Action Tests
  test "should get new registration page" do
    get :new
    assert_response :success
    assert_not_nil assigns(:user)
    assert_instance_of User, assigns(:user)
    assert assigns(:user).new_record?
  end

  test "should allow unauthenticated access to new" do
    # Test that registration page is accessible without authentication
    get :new
    assert_response :success
  end

  # Create Action Tests - Basic Registration
  test "should create vendor user and account successfully" do
    unique_email = "vendor#{Time.current.to_i}@example.com"
    user_params = {
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON>", 
      email_address: unique_email,
      password: "password123",
      password_confirmation: "password123",
      job_title: "CEO",
      department: "Leadership",
      
    }

    assert_difference('User.count', 1) do
      assert_difference('Account.count', 1) do
        assert_difference('AccountUser.count', 1) do
          post :create, params: {
            user: user_params,
            account_type: "vendor"
          }
        end
      end
    end

    user = User.last
    account = Account.last

    # Test user attributes
    assert_equal "<PERSON>", user.first_name
    assert_equal "Doe", user.last_name
    assert_equal unique_email, user.email_address
    assert_equal "CEO", user.job_title

    # Test account attributes  
    assert_equal "vendor", account.account_type
    assert_equal "pending", account.status
    assert_equal user, account.owner
    assert_not_nil account.confirmation_sent_at

    # Test account user association
    account_user = account.account_users.first
    assert_equal user, account_user.user
    assert_equal "admin", account_user.role

    assert_redirected_to pending_path
  end

  test "should create agency user and account successfully" do
    unique_email = "agency#{Time.current.to_i}@example.com"
    user_params = {
      first_name: "Jane",
      last_name: "Smith",
      email_address: unique_email,
      password: "password123",
      password_confirmation: "password123",
      job_title: "IT Director",
      department: "Information Technology",
      account_name: "City of Test"
    }

    assert_difference(['User.count', 'Account.count'], 1) do
      post :create, params: {
        user: user_params,
        account_type: "agency"
      }
    end

    account = Account.last
    assert_equal "agency", account.account_type
    assert_equal "City of Test", account.name  # Uses organization_name when provided
  end

  test "should default to vendor account type when not specified" do
    unique_email = "default#{Time.current.to_i}@example.com"
    user_params = {
      first_name: "Default",
      last_name: "User",
      email_address: unique_email,
      password: "password123",
      password_confirmation: "password123",
      job_title: "User",
      department: "General",
      
    }

    post :create, params: { user: user_params }

    account = Account.last
    assert_equal "vendor", account.account_type
  end

  # Validation Error Tests
  test "should not create user with invalid data" do
    user_params = {
      first_name: "",
      last_name: "",
      email_address: "invalid-email",
      password: "123",  # Too short
      password_confirmation: "456"  # Doesn't match
    }

    assert_no_difference(['User.count', 'Account.count']) do
      post :create, params: { user: user_params }
    end

    assert_response :unprocessable_entity
    assert_not_nil assigns(:user)
    assert assigns(:user).errors.any?
  end

  test "should not create user with duplicate email" do
    # Use existing fixture user email
    existing_email = users(:one).email_address
    
    user_params = {
      first_name: "Second",
      last_name: "User",
      email_address: existing_email,
      password: "password123",
      password_confirmation: "password123"
    }

    assert_no_difference(['User.count', 'Account.count']) do
      post :create, params: { user: user_params }
    end

    assert_response :unprocessable_entity
  end

  # Security Tests
  test "should require strong password" do
    unique_email = "weak#{Time.current.to_i}@example.com"
    user_params = {
      first_name: "Weak",
      last_name: "Password",
      email_address: unique_email,
      password: "123",
      password_confirmation: "123"
    }

    assert_no_difference('User.count') do
      post :create, params: { user: user_params }
    end

    assert_response :unprocessable_entity
    user = assigns(:user)
    assert user.errors[:password].any?
  end

  test "should require password confirmation" do
    unique_email = "noconf#{Time.current.to_i}@example.com"
    user_params = {
      first_name: "No",
      last_name: "Confirmation",
      email_address: unique_email,
      password: "password123",
      password_confirmation: "different"
    }

    assert_no_difference('User.count') do
      post :create, params: { user: user_params }
    end

    assert_response :unprocessable_entity
  end

  # Email Delivery Tests  
  test "should send welcome email after successful registration" do
    unique_email = "email#{Time.current.to_i}@example.com"
    user_params = {
      first_name: "Email",
      last_name: "Test",
      email_address: unique_email,
      password: "password123",
      password_confirmation: "password123",
      job_title: "Tester",
      department: "Testing",
      
    }

    # Test that emails are queued (checking the job queue instead)
    post :create, params: { user: user_params }
    
    # If successful, should redirect to pending
    assert_redirected_to pending_path
    
    # Test that user and account were created
    user = User.last
    assert_equal unique_email, user.email_address
  end

  # Parameter Security Tests
  test "should only permit allowed parameters" do
    unique_email = "safe#{Time.current.to_i}@example.com"
    user_params = {
      first_name: "Safe",
      last_name: "User",
      email_address: unique_email,
      password: "password123",
      password_confirmation: "password123",
      job_title: "Safe User",
      department: "Security",
      
      super_admin: true,  # Should be ignored
      admin: true         # Should be ignored
    }

    post :create, params: { user: user_params }

    user = User.last
    assert_equal false, user.super_admin?
  end
end