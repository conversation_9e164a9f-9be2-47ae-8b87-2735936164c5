require "test_helper"

class PagesControllerTest < ActionController::TestCase
  setup do
    @user = users(:one)
    @vendor_account = accounts(:one)
    @agency_account = accounts(:two)

    # Configure accounts properly
    @vendor_account.update!(
      account_type: 'vendor',
      status: 'approved',
      confirmed_at: Time.current,
      owner: @user
    )

    @agency_account.update!(
      account_type: 'agency',
      status: 'pending',
      confirmed_at: nil,
      owner: @user
    )

    # Ensure account users associations exist
    @vendor_account.account_users.find_or_create_by(user: @user, role: 'admin')
    @agency_account.account_users.find_or_create_by(user: @user, role: 'admin')
  end

  # Public pages accessible without authentication
  test "should get landing page without authentication" do
    get :landing
    assert_response :success
  end

  test "should get terms of service without authentication" do
    get :terms_of_service
    assert_response :success
  end

  test "should get privacy policy without authentication" do
    get :privacy_policy
    assert_response :success
  end

  test "should get about page without authentication" do
    get :about
    assert_response :success
  end

  # Pending page tests (requires authentication)
  test "should show pending page without authentication" do
    get :pending
    assert_response :success
    assert_equal [], assigns(:user_accounts)
    assert_equal [], assigns(:approved_accounts)
    assert_equal [], assigns(:confirmed_accounts)
    assert_equal [], assigns(:unconfirmed_accounts)
  end

  test "should get pending with authenticated user" do
    sign_in @user
    
    get :pending
    assert_response :success
    assert_not_nil assigns(:user_accounts)
    assert_not_nil assigns(:approved_accounts)
    assert_not_nil assigns(:confirmed_accounts)
    assert_not_nil assigns(:unconfirmed_accounts)
  end

  test "should display user accounts on pending page" do
    sign_in @user
    
    get :pending
    
    user_accounts = assigns(:user_accounts)
    approved_accounts = assigns(:approved_accounts)
    confirmed_accounts = assigns(:confirmed_accounts)
    unconfirmed_accounts = assigns(:unconfirmed_accounts)
    
    assert_equal 2, user_accounts.count
    assert_includes user_accounts, @vendor_account
    assert_includes user_accounts, @agency_account
    
    # Test account filtering
    assert_equal 1, approved_accounts.count
    assert_includes approved_accounts, @vendor_account
    
    assert_equal 1, confirmed_accounts.count
    assert_includes confirmed_accounts, @vendor_account
    
    assert_equal 1, unconfirmed_accounts.count
    assert_includes unconfirmed_accounts, @agency_account
  end

  test "should handle user with no accounts on pending page" do
    user_with_no_accounts = User.create!(
      first_name: "Test", 
      last_name: "User",
      email_address: "<EMAIL>",
      password: "password123"
    )
    
    sign_in user_with_no_accounts
    
    get :pending
    assert_response :success
    
    user_accounts = assigns(:user_accounts)
    assert_equal 0, user_accounts.count
    assert_equal [], assigns(:approved_accounts)
    assert_equal [], assigns(:confirmed_accounts)
    assert_equal [], assigns(:unconfirmed_accounts)
  end

  # Test security for static pages
  test "should allow unauthenticated access to all public pages" do
    # Test that all public pages are accessible without authentication
    get :landing
    assert_response :success
    
    get :terms_of_service
    assert_response :success
    
    get :privacy_policy
    assert_response :success
    
    get :about
    assert_response :success
  end

  test "should have proper page titles and content structure" do
    get :landing
    assert_response :success
    # Add assertions for expected content/elements if needed
    
    get :terms_of_service
    assert_response :success
    
    get :privacy_policy
    assert_response :success
    
    get :about
    assert_response :success
  end

  private

  def sign_in(user)
    session_record = user.sessions.create!(
      user_agent: 'Test User Agent',
      ip_address: '127.0.0.1'
    )
    Current.session = session_record
    cookies.signed[:session_id] = session_record.id
  end
end