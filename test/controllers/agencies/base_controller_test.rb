require "test_helper"

module Agencies
  class BaseControllerTest < ActionController::TestCase
    # Since BaseController is abstract, we'll test through a concrete controller
    class TestController < BaseController
      def index
        render plain: "agency index"
      end
      
      def show
        render plain: "agency show"
      end
    end
    
    def setup
      @controller = TestController.new
      
      # Set up routes for our test controller
      Rails.application.routes.draw do
        namespace :agencies do
          resources :test, only: [:index, :show]
        end
      end
      
      # Create test users and accounts
      @vendor_user = users(:one)
      @agency_user = users(:two)
      @admin_user = users(:admin)
      
      @vendor_account = accounts(:one)
      @agency_account = accounts(:two)
      
      # Ensure accounts have proper setup
      @vendor_account.update!(account_type: 'vendor', status: 'approved', confirmed_at: Time.current)
      @agency_account.update!(account_type: 'agency', status: 'approved', confirmed_at: Time.current)
      
      # Create unapproved agency user
      @unapproved_user = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "Unapproved",
        last_name: "User"
      )
      @unapproved_account = Account.create!(
        name: "Unapproved Agency",
        account_type: "agency",
        status: "pending",
        confirmed_at: Time.current,
        owner: @unapproved_user
      )
      
      # Create user without accounts
      @user_without_accounts = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "No",
        last_name: "Accounts"
      )
    end
    
    def teardown
      # Restore original routes
      Rails.application.reload_routes!
    end

    # ===============================
    # AUTHENTICATION TESTS
    # ===============================

    test "should require authentication" do
      get :index
      assert_redirected_to new_session_path
    end

    test "should allow authenticated agency users" do
      sign_in @agency_user
      get :index
      assert_response :success
      assert_equal "agency index", response.body
    end

    test "should store return_to path when requiring authentication" do
      get :index
      assert_equal agencies_test_index_url, session[:return_to_after_authenticating]
    end

    # ===============================
    # AGENCY ACCOUNT REQUIREMENT TESTS
    # ===============================

    test "should redirect non-agency users" do
      sign_in @vendor_user
      get :index
      assert_redirected_to root_path
    end

    test "should redirect users without accounts" do
      sign_in @user_without_accounts
      get :index
      assert_redirected_to orphaned_users_path
    end

    test "should allow admin users with agency accounts" do
      # Give admin user an agency account
      agency_account_for_admin = Account.create!(
        name: "Admin's Agency Account",
        account_type: "agency",
        status: "approved",
        confirmed_at: Time.current,
        owner: @admin_user
      )
      
      sign_in @admin_user
      get :index
      assert_response :success
    end

    # ===============================
    # ACCOUNT APPROVAL REQUIREMENT TESTS
    # ===============================

    test "should redirect users with pending agency accounts" do
      sign_in @unapproved_user
      get :index
      assert_redirected_to root_path
    end

    test "should allow users with approved agency accounts" do
      sign_in @agency_user
      get :index
      assert_response :success
      assert_equal "agency index", response.body
    end

    test "should redirect when agency account is not approved" do
      # Change account status to pending
      @agency_account.update!(status: 'pending')
      
      sign_in @agency_user
      get :index
      assert_redirected_to root_path
    end

    # ===============================
    # CURRENT ACCOUNT TESTS
    # ===============================

    test "should return agency account from session when available" do
      sign_in @agency_user
      session[:account_id] = @agency_account.id
      
      get :index
      assert_equal @agency_account, @controller.send(:current_account)
    end

    test "should return first agency account when no session account_id" do
      sign_in @agency_user
      session.delete(:account_id)
      
      get :index
      assert_equal @agency_account, @controller.send(:current_account)
    end

    test "should return nil when user has no agency accounts" do
      sign_in @vendor_user
      
      # This will redirect, but we can still test the current_account method
      assert_nil @controller.send(:current_account)
    end

    test "should ignore non-agency accounts in current_account" do
      # Create user with both vendor and agency accounts
      mixed_user = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "Mixed",
        last_name: "User"
      )
      
      vendor_acct = Account.create!(
        name: "Mixed Vendor",
        account_type: "vendor",
        status: "approved",
        confirmed_at: Time.current,
        owner: mixed_user
      )
      
      agency_acct = Account.create!(
        name: "Mixed Agency",
        account_type: "agency",
        status: "approved",
        confirmed_at: Time.current,
        owner: mixed_user
      )
      
      sign_in mixed_user
      session[:account_id] = vendor_acct.id  # Try to set vendor account
      
      get :index
      # Should return agency account, not vendor account
      assert_equal agency_acct, @controller.send(:current_account)
    end

    test "should handle invalid account_id in session" do
      sign_in @agency_user
      session[:account_id] = 99999  # Non-existent account
      
      get :index
      # Should fall back to first agency account
      assert_equal @agency_account, @controller.send(:current_account)
    end

    # ===============================
    # LAYOUT TESTS
    # ===============================

    test "should use dashboard layout" do
      sign_in @agency_user
      get :index
    end

    # ===============================
    # FILTER CHAIN TESTS
    # ===============================

    test "should run all required filters in correct order" do
      # Test that all before_actions are executed
      
      # Without authentication - should stop at require_authentication
      get :index
      assert_redirected_to new_session_path
      
      # With vendor user - should stop at require_agency_account
      sign_in @vendor_user
      get :index
      assert_redirected_to root_path
      
      # With unapproved agency - should stop at require_approved_account
      sign_in @unapproved_user
      get :index
      assert_redirected_to root_path
      
      # With approved agency - should succeed
      sign_in @agency_user
      get :index
      assert_response :success
    end

    # ===============================
    # EDGE CASE TESTS
    # ===============================

    test "should handle user with multiple agency accounts" do
      # Create second agency account for user
      second_agency = Account.create!(
        name: "Second Agency",
        account_type: "agency",
        status: "approved",
        confirmed_at: Time.current,
        owner: @agency_user
      )
      
      sign_in @agency_user
      
      # Should return first agency account by default
      get :index
      assert_equal @agency_account, @controller.send(:current_account)
      
      # Should return specific account when set in session
      session[:account_id] = second_agency.id
      get :show, params: { id: 1 }
      assert_equal second_agency, @controller.send(:current_account)
    end

    test "should handle user with only unapproved agency accounts" do
      all_pending_user = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "All Pending",
        last_name: "User"
      )
      
      Account.create!(
        name: "Pending Agency 1",
        account_type: "agency",
        status: "pending",
        confirmed_at: Time.current,
        owner: all_pending_user
      )
      
      Account.create!(
        name: "Pending Agency 2",
        account_type: "agency",
        status: "pending",
        confirmed_at: Time.current,
        owner: all_pending_user
      )
      
      sign_in all_pending_user
      get :index
      assert_redirected_to root_path
    end

    # ===============================
    # SECURITY TESTS
    # ===============================

    test "should not allow bypassing authentication" do
      get :index
      assert_redirected_to new_session_path
      
      # Setting session manually shouldn't work without proper session
      session[:user_id] = @agency_user.id
      get :index
      assert_redirected_to new_session_path
    end

    test "should not allow bypassing agency requirement" do
      sign_in @vendor_user
      
      # Try to force agency account type
      @vendor_account.update!(account_type: 'agency')
      get :index
      # Should still redirect because require_agency_account checks user's accounts
      assert_redirected_to root_path
    end

    test "should not allow bypassing approval requirement" do
      # Create user with agency account but not approved
      pending_user = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "Still Pending",
        last_name: "User"
      )
      
      pending_account = Account.create!(
        name: "Still Pending Agency",
        account_type: "agency",
        status: "pending",
        confirmed_at: Time.current,
        owner: pending_user
      )
      
      sign_in pending_user
      get :index
      assert_redirected_to root_path
    end

    # ===============================
    # INTEGRATION TESTS
    # ===============================

    test "should work end-to-end for valid agency user" do
      sign_in @agency_user
      
      # Should be able to access index
      get :index
      assert_response :success
      assert_equal "agency index", response.body
      
      # Should be able to access show
      get :show, params: { id: 1 }
      assert_response :success
      assert_equal "agency show", response.body
      
      # Should have correct current_account
      assert_equal @agency_account, @controller.send(:current_account)
    end

    test "should maintain session across requests" do
      sign_in @agency_user
      
      get :index
      assert_response :success
      first_account = @controller.send(:current_account)
      
      get :show, params: { id: 1 }
      assert_response :success
      second_account = @controller.send(:current_account)
      
      assert_equal first_account, second_account
    end

    private

    def sign_in(user)
      # Simulate signing in by creating a session
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      @request.cookies.signed[:session_id] = session_record.id
    end
  end
end