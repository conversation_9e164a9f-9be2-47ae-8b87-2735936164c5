require "test_helper"

class Agencies::DashboardControllerTest < ActionController::TestCase
  setup do
    @vendor_user = users(:one)
    @vendor_account = accounts(:one)
    @agency_user = users(:two) 
    @agency_account = accounts(:two)

    # Configure agency account properly
    @agency_account.update!(
      account_type: 'agency',
      status: 'approved',
      confirmed_at: Time.current,
      owner: @agency_user
    )

    # Configure vendor account
    @vendor_account.update!(
      account_type: 'vendor',
      status: 'approved',
      confirmed_at: Time.current,
      owner: @vendor_user
    )

    # Clear existing account users to avoid duplicates
    @agency_account.account_users.destroy_all
    @agency_account.account_users.create!(user: @agency_user, role: 'admin')

    # Clear existing data to ensure clean test state
    @vendor_account.products.destroy_all
    Lead.where(agency_account: @agency_account).destroy_all

    # Create test products and leads
    @product1 = Product.create!(
      account: @vendor_account,
      name: "Test Product 1",
      description: "A test product",
      published: true
    )
    
    @product2 = Product.create!(
      account: @vendor_account,
      name: "Test Product 2",
      description: "Another test product",
      published: true
    )

    # Create test leads (agency_leads)
    @lead1 = Lead.create!(
      product: @product1,
      account: @vendor_account,
      agency_account: @agency_account,
      user: @agency_user,
      status: :pending,
      message: "Interested in this product",
      contact_name: "<PERSON>",
      contact_email: "<EMAIL>",
      contact_company: "City Government"
    )
    
    @lead2 = Lead.create!(
      product: @product1,
      account: @vendor_account,
      agency_account: @agency_account,
      user: @agency_user,
      status: :contacted,
      message: "Follow up needed",
      contact_name: "John Doe",
      contact_email: "<EMAIL>",
      contact_company: "City Government"
    )

    @lead3 = Lead.create!(
      product: @product2,
      account: @vendor_account,
      agency_account: @agency_account,
      user: @agency_user,
      status: :qualified,
      message: "Ready to purchase",
      contact_name: "Bob Wilson",
      contact_email: "<EMAIL>",
      contact_company: "City Government"
    )
  end

  # Authentication Tests
  test "should require authentication" do
    get :index
    assert_response :redirect
    assert_redirected_to new_session_path
  end

  test "should require agency account" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index
    assert_response :redirect
  end

  test "should require approved account" do
    @agency_account.update!(status: 'pending')
    
    sign_in @agency_user
    session[:account_id] = @agency_account.id

    get :index
    assert_response :redirect
  end

  # Dashboard Tests
  test "should get dashboard index with agency user and account" do
    sign_in @agency_user
    session[:account_id] = @agency_account.id

    get :index
    assert_response :success
    assert_not_nil assigns(:recent_interests)
    assert_not_nil assigns(:interest_stats)
    assert_not_nil assigns(:featured_companies)
    assert_not_nil assigns(:featured_products)
  end

  test "should display recent interests limited to 5" do
    sign_in @agency_user
    session[:account_id] = @agency_account.id

    # Create 7 additional leads
    5.times do |i|
      Lead.create!(
        product: @product1,
        account: @vendor_account,
        agency_account: @agency_account,
        user: @agency_user,
        status: :pending,
        message: "Lead #{i}",
        contact_name: "Contact #{i}",
        contact_email: "contact#{i}@example.com",
        contact_company: "Company #{i}"
      )
    end

    get :index
    interests = assigns(:recent_interests)
    
    assert interests.count <= 5
  end

  test "should order recent interests by created_at desc" do
    sign_in @agency_user
    session[:account_id] = @agency_account.id

    get :index
    interests = assigns(:recent_interests)
    
    # Should be ordered by newest first
    assert interests.first.created_at >= interests.last.created_at
  end

  test "should include associated data to avoid N+1 queries" do
    sign_in @agency_user
    session[:account_id] = @agency_account.id

    get :index
    interests = assigns(:recent_interests)
    
    # Test that associations are loaded
    interests.each do |interest|
      assert_not_nil interest.product
      assert_not_nil interest.account
    end
  end

  test "should calculate correct interest stats" do
    sign_in @agency_user
    session[:account_id] = @agency_account.id

    get :index
    stats = assigns(:interest_stats)
    
    assert_equal 3, stats[:total]
    assert_equal 1, stats[:pending]
    assert_equal 1, stats[:contacted]
    assert_equal 1, stats[:qualified]
  end

  test "should scope interests to current agency account only" do
    # Create another agency with leads
    other_agency = Account.create!(
      name: "Other Agency",
      account_type: 'agency',
      status: 'approved',
      owner: @agency_user
    )
    
    Lead.create!(
      product: @product1,
      account: @vendor_account,
      agency_account: other_agency,
      user: @agency_user,
      status: :pending,
      message: "Lead for other agency",
      contact_name: "Other Contact",
      contact_email: "<EMAIL>",
      contact_company: "Other Agency"
    )

    sign_in @agency_user
    session[:account_id] = @agency_account.id

    get :index
    interests = assigns(:recent_interests)
    stats = assigns(:interest_stats)
    
    # Should only show leads for current agency
    assert_equal 3, interests.count
    assert_equal 3, stats[:total]
    interests.each do |interest|
      assert_equal @agency_account, interest.agency_account
    end
  end

  test "should display featured companies" do
    sign_in @agency_user
    session[:account_id] = @agency_account.id

    get :index
    companies = assigns(:featured_companies)
    
    assert companies.any?
    assert companies.count <= 6
    companies.each do |company|
      assert_equal 'vendor', company.account_type
      assert_equal 'approved', company.status
    end
  end

  test "should display featured products" do
    sign_in @agency_user
    session[:account_id] = @agency_account.id

    get :index
    products = assigns(:featured_products)
    
    assert products.any?
    assert products.count <= 6
    products.each do |product|
      assert product.published?
      assert_not_nil product.account # Test eager loading
    end
  end

  test "should handle empty data gracefully" do
    # Create a clean agency account with no leads
    clean_agency = Account.create!(
      name: "Clean Agency",
      account_type: 'agency',
      status: 'approved',
      confirmed_at: Time.current,
      owner: @agency_user
    )
    clean_agency.account_users.create!(user: @agency_user, role: 'admin')

    sign_in @agency_user
    session[:account_id] = clean_agency.id

    get :index
    assert_response :success
    
    stats = assigns(:interest_stats)
    assert_equal 0, stats[:total]
    assert_equal 0, stats[:pending]
    assert_equal 0, stats[:contacted]
    assert_equal 0, stats[:qualified]
    
    assert_equal 0, assigns(:recent_interests).count
  end

  test "should display only approved vendor companies in featured" do
    # Create unapproved vendor
    unapproved_vendor = Account.create!(
      name: "Unapproved Vendor",
      account_type: 'vendor',
      status: 'pending',
      owner: @vendor_user
    )

    sign_in @agency_user
    session[:account_id] = @agency_account.id

    get :index
    companies = assigns(:featured_companies)
    
    # Should not include unapproved vendors
    companies.each do |company|
      assert_not_equal unapproved_vendor.id, company.id
      assert_equal 'approved', company.status
    end
  end

  test "should display only published products in featured" do
    # Create unpublished product
    unpublished_product = Product.create!(
      account: @vendor_account,
      name: "Unpublished Product",
      description: "This product is not published",
      published: false
    )

    sign_in @agency_user
    session[:account_id] = @agency_account.id

    get :index
    products = assigns(:featured_products)
    
    # Should not include unpublished products
    products.each do |product|
      assert_not_equal unpublished_product.id, product.id
      assert product.published?
    end
  end

  test "should set correct layout" do
    sign_in @agency_user
    session[:account_id] = @agency_account.id

    get :index
    # Test that dashboard layout is used (implicitly tested by successful response)
    assert_response :success
  end

  test "should use agency base controller authentication" do
    # Test that it inherits from Agencies::BaseController
    assert_equal Agencies::BaseController, Agencies::DashboardController.superclass
  end

  test "should handle multiple agency accounts correctly" do
    # Create second agency account for same user
    second_agency = Account.create!(
      name: "Second Agency",
      account_type: 'agency',
      status: 'approved',
      confirmed_at: Time.current,
      owner: @agency_user
    )
    second_agency.account_users.create!(user: @agency_user, role: 'admin')

    # Create lead for second agency
    Lead.create!(
      product: @product1,
      account: @vendor_account,
      agency_account: second_agency,
      user: @agency_user,
      status: :pending,
      message: "Lead for second agency",
      contact_name: "Second Contact",
      contact_email: "<EMAIL>",
      contact_company: "Second Agency"
    )

    sign_in @agency_user
    session[:account_id] = @agency_account.id

    get :index
    interests = assigns(:recent_interests)
    stats = assigns(:interest_stats)
    
    # Should only show leads for current agency (not second agency)
    assert_equal 3, stats[:total]
    interests.each do |interest|
      assert_equal @agency_account, interest.agency_account
    end
  end

  private

  def sign_in(user)
    session_record = user.sessions.create!(
      user_agent: 'Test User Agent',
      ip_address: '127.0.0.1'
    )
    Current.session = session_record
    cookies.signed[:session_id] = session_record.id
  end
end