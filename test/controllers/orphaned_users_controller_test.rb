require "test_helper"

class OrphanedUsersControllerTest < ActionController::TestCase
  def setup
    @user_with_accounts = users(:one)
    @user_without_accounts = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Orphaned",
      last_name: "User"
    )
    
    @vendor_account = accounts(:one)
    @agency_account = accounts(:two)
    
    # Ensure accounts have proper setup
    @vendor_account.update!(
      account_type: 'vendor', 
      status: 'approved', 
      confirmed_at: Time.current,
      owner: @user_with_accounts
    )
    
    @agency_account.update!(
      account_type: 'agency', 
      status: 'approved', 
      confirmed_at: Time.current
    )
    
    # Create user with pending account
    @user_with_pending_account = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Pending",
      last_name: "User"
    )
    @pending_account = Account.create!(
      name: "Pending Company",
      account_type: "vendor",
      status: "pending",
      owner: @user_with_pending_account
    )
    AccountUser.create!(
      account: @pending_account,
      user: @user_with_pending_account,
      role: 'admin'
    )
    
    # Create user with confirmed but not approved account
    @user_with_confirmed_account = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Confirmed",
      last_name: "User"
    )
    @confirmed_account = Account.create!(
      name: "Confirmed Company",
      account_type: "vendor",
      status: "pending",
      confirmed_at: Time.current,
      owner: @user_with_confirmed_account
    )
    AccountUser.create!(
      account: @confirmed_account,
      user: @user_with_confirmed_account,
      role: 'admin'
    )
    
    # Create user with approved but not confirmed account
    @user_with_approved_account = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Approved",
      last_name: "User"
    )
    @approved_account = Account.create!(
      name: "Approved Company",
      account_type: "vendor",
      status: "approved",
      confirmed_at: nil,
      owner: @user_with_approved_account
    )
    AccountUser.create!(
      account: @approved_account,
      user: @user_with_approved_account,
      role: 'admin'
    )
    
    # Create user with both approved and confirmed account
    @user_with_working_account = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Working",
      last_name: "User"
    )
    @working_account = Account.create!(
      name: "Working Company",
      account_type: "vendor",
      status: "approved",
      confirmed_at: Time.current,
      owner: @user_with_working_account
    )
    AccountUser.create!(
      account: @working_account,
      user: @user_with_working_account,
      role: 'admin'
    )
  end

  # ===============================
  # AUTHENTICATION TESTS
  # ===============================

  test "should require authentication" do
    get :show
    assert_redirected_to new_session_path
  end

  test "should not allow unauthenticated access" do
    get :show
    assert_response :redirect
    assert_not_nil session[:return_to_after_authenticating]
  end

  # ===============================
  # SHOW ACTION TESTS - ORPHANED USER
  # ===============================

  test "should show page for truly orphaned user" do
    sign_in_as @user_without_accounts
    
    get :show
    assert_response :success
    assert_template :show
  end

  test "orphaned user should stay on orphaned page" do
    sign_in_as @user_without_accounts
    
    # Debug: check if session cookie is set
    assert_not_nil cookies.signed[:session_id], "Session cookie should be set"
    
    get :show
    assert_response :success
    
    # If we get success, authentication worked - the user was not redirected to login
    # This means the before_action :require_authentication passed
    # and the before_action :ensure_orphaned_user allowed the user to stay
  end

  # ===============================
  # SHOW ACTION TESTS - REDIRECTS FOR WORKING ACCOUNTS
  # ===============================

  test "should redirect user with approved and confirmed account to dashboard" do
    sign_in_as @user_with_working_account
    
    get :show
    # Should redirect to wherever after_authentication_url points
    assert_response :redirect
  end

  test "should redirect user with vendor account to vendor dashboard" do
    sign_in_as @user_with_accounts
    
    get :show
    # Should redirect to wherever after_authentication_url points
    assert_response :redirect
  end

  # ===============================
  # SHOW ACTION TESTS - REDIRECTS FOR PENDING ACCOUNTS
  # ===============================

  test "should redirect user with approved account to pending page" do
    sign_in_as @user_with_approved_account
    
    get :show
    assert_redirected_to pending_path
  end

  test "should redirect user with confirmed account to pending page" do
    sign_in_as @user_with_confirmed_account
    
    get :show
    assert_redirected_to pending_path
  end

  test "should allow user with pending account to stay" do
    sign_in_as @user_with_pending_account
    
    get :show
    assert_response :success
    assert_template :show
  end

  # ===============================
  # EDGE CASE TESTS
  # ===============================

  test "should handle user with multiple accounts - some working" do
    # Add a working account to a user who also has a pending account
    working_account2 = Account.create!(
      name: "Second Working Company",
      account_type: "agency",
      status: "approved",
      confirmed_at: Time.current,
      owner: @user_with_pending_account
    )
    AccountUser.create!(
      account: working_account2,
      user: @user_with_pending_account,
      role: 'admin'
    )
    
    sign_in_as @user_with_pending_account
    
    get :show
    # Should redirect to wherever after_authentication_url points
    assert_response :redirect
  end

  test "should handle user with multiple pending accounts" do
    # Add another pending account
    pending_account2 = Account.create!(
      name: "Second Pending Company",
      account_type: "agency",
      status: "pending",
      owner: @user_with_pending_account
    )
    
    sign_in_as @user_with_pending_account
    
    get :show
    assert_response :success
    assert_template :show
  end

  test "should handle user with mix of approved and confirmed accounts" do
    # User has both an approved (not confirmed) and confirmed (not approved) account
    user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Mixed",
      last_name: "User"
    )
    
    # Approved but not confirmed
    approved_account = Account.create!(
      name: "Approved Company",
      account_type: "vendor",
      status: "approved",
      confirmed_at: nil,
      owner: user
    )
    AccountUser.create!(
      account: approved_account,
      user: user,
      role: 'admin'
    )
    
    # Confirmed but not approved
    confirmed_account = Account.create!(
      name: "Confirmed Company",
      account_type: "vendor",
      status: "pending",
      confirmed_at: Time.current,
      owner: user
    )
    AccountUser.create!(
      account: confirmed_account,
      user: user,
      role: 'admin'
    )
    
    sign_in_as user
    
    get :show
    assert_redirected_to pending_path
  end

  # ===============================
  # ACCOUNT ASSOCIATION TESTS
  # ===============================

  test "should properly evaluate user.accounts association" do
    sign_in_as @user_without_accounts
    
    # Verify the user has no accounts
    assert_equal 0, @user_without_accounts.accounts.count
    
    get :show
    assert_response :success
  end

  test "should properly evaluate approved accounts" do
    sign_in_as @user_with_approved_account
    
    # Check if account is approved using the account status
    approved_accounts = @user_with_approved_account.accounts.select { |account| account.status == 'approved' }
    assert_equal 1, approved_accounts.count
    
    get :show
    assert_redirected_to pending_path
  end

  test "should properly evaluate confirmed accounts" do
    sign_in_as @user_with_confirmed_account
    
    confirmed_accounts = @user_with_confirmed_account.accounts.select { |account| account.confirmed_at.present? }
    assert_equal 1, confirmed_accounts.count
    
    get :show
    assert_redirected_to pending_path
  end

  # ===============================
  # SECURITY TESTS
  # ===============================

  test "should use current_user from session" do
    sign_in_as @user_without_accounts
    
    get :show
    # If authentication works, we should get success not redirect to login
    assert_response :success
    # This proves that Current.user was properly set during the request
  end

  test "should not allow access without valid session" do
    # Don't sign in
    get :show
    assert_redirected_to new_session_path
  end

  # ===============================
  # PRIVATE METHOD TESTS (THROUGH BEHAVIOR)
  # ===============================

  test "ensure_orphaned_user should allow truly orphaned users" do
    sign_in_as @user_without_accounts
    
    get :show
    assert_response :success
  end

  test "ensure_orphaned_user should redirect users with working accounts" do
    sign_in_as @user_with_working_account
    
    get :show
    # Should redirect to wherever after_authentication_url points
    assert_response :redirect
  end

  test "ensure_orphaned_user should redirect users with partial accounts to pending" do
    sign_in_as @user_with_approved_account
    
    get :show
    assert_redirected_to pending_path
  end

  # ===============================
  # INTEGRATION TESTS
  # ===============================

  test "should handle full flow for orphaned user" do
    # Start without authentication
    get :show
    assert_redirected_to new_session_path
    
    # Sign in as orphaned user
    sign_in_as @user_without_accounts
    
    # Should now show orphaned page
    get :show
    assert_response :success
    assert_template :show
  end

  test "should handle account status changes during session" do
    sign_in_as @user_with_pending_account
    
    # Initially should show orphaned page
    get :show
    assert_response :success
    
    # Approve and confirm account
    @pending_account.update!(status: "approved", confirmed_at: Time.current)
    
    # Should now redirect to dashboard
    get :show
    # Should redirect to wherever after_authentication_url points
    assert_response :redirect
  end

  private

  def sign_in_as(user)
    session = Session.create!(
      user: user,
      user_agent: "Test User Agent",
      ip_address: "127.0.0.1"
    )
    cookies.signed[:session_id] = session.id
    Current.session = session
  end
end