require "test_helper"

module Vendors
  class ProductVideosControllerTest < ActionController::TestCase
    def setup
      # Create test users
      @vendor_user = users(:one)
      @other_vendor_user = users(:two)
      @admin_user = users(:admin)
      @admin_user.update!(super_admin: true)
      
      # Create test accounts
      @vendor_account = accounts(:one)
      @other_vendor_account = accounts(:two)
      
      @vendor_account.update!(
        account_type: 'vendor', 
        status: 'approved', 
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      @other_vendor_account.update!(
        account_type: 'vendor',
        status: 'approved',
        confirmed_at: Time.current,
        owner: @other_vendor_user
      )
      
      # Clear existing account users to avoid duplicates
      @vendor_account.account_users.destroy_all
      @vendor_account.account_users.create!(user: @vendor_user, role: 'admin')
      
      @other_vendor_account.account_users.destroy_all
      @other_vendor_account.account_users.create!(user: @other_vendor_user, role: 'admin')
      
      # Create test product
      @product = @vendor_account.products.create!(
        name: "Test Product",
        description: "A test product description",
        website: "https://test.com",
        published: false
      )
      
      @other_vendor_product = @other_vendor_account.products.create!(
        name: "Other Vendor Product",
        description: "Product from another vendor",
        website: "https://other.com",
        published: true
      )
      
      # Create test product videos
      @video1 = @product.product_videos.build(
        title: "Demo Video",
        description: "A demo video description",
        position: 1,
        published: true
      )
      @video1.video_file.attach(
        io: StringIO.new("fake video data"),
        filename: "demo.mp4",
        content_type: "video/mp4"
      )
      @video1.save!
      
      @video2 = @product.product_videos.build(
        title: "Tutorial Video",
        description: "A tutorial video description",
        position: 2,
        published: false
      )
      @video2.video_file.attach(
        io: StringIO.new("fake video data 2"),
        filename: "tutorial.mp4",
        content_type: "video/mp4"
      )
      @video2.save!
    end

    # ===============================
    # AUTHENTICATION & AUTHORIZATION TESTS
    # ===============================

    test "should require vendor authentication for all actions" do
      get :index, params: { product_id: @product.slug }
      assert_redirected_to new_session_path
      
      get :show, params: { product_id: @product.slug, id: @video1.id }
      assert_redirected_to new_session_path
      
      get :new, params: { product_id: @product.slug }
      assert_redirected_to new_session_path
      
      post :create, params: { product_id: @product.slug, product_video: { title: "Test" } }
      assert_redirected_to new_session_path
    end

    test "should require vendor account type" do
      agency_user = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "Agency",
        last_name: "User"
      )
      
      agency_account = Account.create!(
        name: "Agency Account",
        account_type: "agency",
        status: "approved",
        confirmed_at: Time.current,
        owner: agency_user
      )
      AccountUser.create!(account: agency_account, user: agency_user, role: 'admin')
      
      sign_in agency_user
      
      get :index, params: { product_id: @product.slug }
      assert_redirected_to root_path
    end

    test "should allow vendor users" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
    end

    test "should ensure product ownership" do
      sign_in @other_vendor_user
      
      # Should not be able to access another vendor's product
      get :index, params: { product_id: @product.slug }
      assert_response :redirect  # Should redirect due to ensure_owner
    end

    # ===============================
    # INDEX ACTION TESTS
    # ===============================

    test "should show product videos index" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      assert_not_nil assigns(:product_videos)
      
      videos = assigns(:product_videos)
      assert videos.include?(@video1)
      assert videos.include?(@video2)
    end

    test "should order videos by position" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      videos = assigns(:product_videos).to_a
      assert_equal @video1, videos.first
      assert_equal @video2, videos.second
    end

    test "should include video file attachments" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      videos = assigns(:product_videos)
      videos.each do |video|
        assert video.video_file.attached?, "Video should have attached file"
      end
    end

    test "should scope videos to current vendor account" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      videos = assigns(:product_videos)
      videos.each do |video|
        assert_equal @vendor_account, video.product.account
      end
    end

    # ===============================
    # SHOW ACTION TESTS
    # ===============================

    test "should show product video details" do
      sign_in @vendor_user
      
      get :show, params: { product_id: @product.slug, id: @video1.id }
      assert_response :success
      assert_equal @video1, assigns(:product_video)
      assert_equal @product, assigns(:product)
    end

    test "should not show other vendor's video" do
      sign_in @vendor_user
      
      # Create video for other vendor's product
      other_video = @other_vendor_product.product_videos.build(
        title: "Other Video",
        description: "Video for other vendor",
        position: 1
      )
      other_video.video_file.attach(
        io: StringIO.new("other video data"),
        filename: "other.mp4",
        content_type: "video/mp4"
      )
      other_video.save!
      
      # Should not be able to access through this vendor's product context
      assert_raises(ActiveRecord::RecordNotFound) do
        get :show, params: { product_id: @product.slug, id: other_video.id }
      end
    end

    # ===============================
    # NEW ACTION TESTS
    # ===============================

    test "should show new product video form" do
      sign_in @vendor_user
      
      get :new, params: { product_id: @product.slug }
      assert_response :success
      assert_instance_of ProductVideo, assigns(:product_video)
      assert assigns(:product_video).new_record?
      assert_equal @product, assigns(:product_video).product
    end

    test "should auto-assign next position for new video" do
      sign_in @vendor_user
      
      get :new, params: { product_id: @product.slug }
      assert_response :success
      
      video = assigns(:product_video)
      assert_equal 3, video.position  # Should be after existing videos (1, 2)
    end

    # ===============================
    # CREATE ACTION TESTS
    # ===============================

    test "should create product video with valid data" do
      sign_in @vendor_user
      
      file = fixture_file_upload('test/fixtures/files/test_video.mp4', 'video/mp4')
      video_params = {
        title: "New Video",
        description: "A new video description",
        video_file: file,
        published: true
      }
      
      assert_difference '@product.product_videos.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_video: video_params
        }
      end
      
      assert_redirected_to vendors_product_product_videos_path(@product)
      assert_match /Video was successfully added/, flash[:notice]
      
      video = ProductVideo.last
      assert_equal "New Video", video.title
      assert_equal "A new video description", video.description
      assert_equal @product, video.product
      assert video.published?
      assert video.video_file.attached?
    end

    test "should auto-assign position when creating without position" do
      sign_in @vendor_user
      
      file = fixture_file_upload('test/fixtures/files/test_video.mp4', 'video/mp4')
      video_params = {
        title: "Auto Position Video",
        description: "Should get next available position",
        video_file: file
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_video: video_params
      }
      
      video = ProductVideo.last
      assert_equal 3, video.position  # Should be after existing videos (1, 2)
    end

    test "should not create product video with invalid data" do
      sign_in @vendor_user
      
      invalid_params = {
        title: "",  # Required field
        description: ""
      }
      
      assert_no_difference 'ProductVideo.count' do
        post :create, params: { 
          product_id: @product.slug,
          product_video: invalid_params
        }
      end
      
      assert_response :unprocessable_entity
      assert_not_nil assigns(:product_video)
      assert assigns(:product_video).errors.any?
    end

    test "should create product video for own product only" do
      sign_in @vendor_user
      
      file = fixture_file_upload('test/fixtures/files/test_video.mp4', 'video/mp4')
      video_params = {
        title: "My Video",
        description: "Video for my product",
        video_file: file
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_video: video_params
      }
      
      video = ProductVideo.last
      assert_equal @vendor_account, video.product.account
    end

    # ===============================
    # EDIT ACTION TESTS
    # ===============================

    test "should show edit product video form" do
      sign_in @vendor_user
      
      get :edit, params: { product_id: @product.slug, id: @video1.id }
      assert_response :success
      assert_equal @video1, assigns(:product_video)
      assert_equal @product, assigns(:product)
    end

    test "should not edit other vendor's video" do
      sign_in @other_vendor_user
      
      # Should not be able to access this vendor's video for editing
      get :edit, params: { product_id: @product.slug, id: @video1.id }
      assert_response :redirect  # Should redirect due to ensure_owner
    end

    # ===============================
    # UPDATE ACTION TESTS
    # ===============================

    test "should update product video with valid data" do
      sign_in @vendor_user
      
      update_params = {
        title: "Updated Video Title",
        description: "Updated video description",
        published: false
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @video1.id,
        product_video: update_params
      }
      
      assert_redirected_to vendors_product_product_videos_path(@product)
      assert_match /Video was successfully updated/, flash[:notice]
      
      @video1.reload
      assert_equal "Updated Video Title", @video1.title
      assert_equal "Updated video description", @video1.description
      assert_not @video1.published?
    end

    test "should update product video file" do
      sign_in @vendor_user
      
      new_file = fixture_file_upload('test/fixtures/files/test_video.mp4', 'video/mp4')
      
      update_params = {
        title: @video1.title,
        video_file: new_file
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @video1.id,
        product_video: update_params
      }
      
      assert_redirected_to vendors_product_product_videos_path(@product)
      
      @video1.reload
      assert @video1.video_file.attached?
      assert_equal "test_video.mp4", @video1.video_file.filename.to_s
    end

    test "should not update product video with invalid data" do
      sign_in @vendor_user
      
      original_title = @video1.title
      
      invalid_params = {
        title: "",  # Invalid
        description: "Valid description"
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @video1.id,
        product_video: invalid_params
      }
      
      assert_response :unprocessable_entity
      assert_not_nil assigns(:product_video)
      assert assigns(:product_video).errors.any?
      
      @video1.reload
      assert_equal original_title, @video1.title
    end

    # ===============================
    # DESTROY ACTION TESTS
    # ===============================

    test "should delete product video" do
      sign_in @vendor_user
      
      assert_difference '@product.product_videos.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: @video1.id }
      end
      
      assert_redirected_to vendors_product_path(@product)
      assert_match /Video was successfully deleted/, flash[:notice]
    end

    test "should not delete other vendor's video" do
      sign_in @other_vendor_user
      
      # Should not be able to delete this vendor's video
      delete :destroy, params: { product_id: @product.slug, id: @video1.id }
      assert_response :redirect  # Should redirect due to ensure_owner
    end

    # ===============================
    # UPDATE POSITION ACTION TESTS
    # ===============================

    test "should update video position" do
      sign_in @vendor_user
      
      patch :update_position, params: { 
        product_id: @product.slug,
        id: @video1.id,
        position: 2
      }
      
      assert_response :success
      response_data = JSON.parse(response.body)
      assert_equal 'success', response_data['status']
      
      @video1.reload
      assert_equal 2, @video1.position
    end

    test "should reorder other videos when updating position" do
      sign_in @vendor_user
      
      # Create third video
      video3 = @product.product_videos.build(
        title: "Third Video",
        description: "Third video description",
        position: 3
      )
      video3.video_file.attach(
        io: StringIO.new("fake video data 3"),
        filename: "third.mp4",
        content_type: "video/mp4"
      )
      video3.save!
      
      # Move video3 to position 1
      patch :update_position, params: { 
        product_id: @product.slug,
        id: video3.id,
        position: 1
      }
      
      assert_response :success
      
      # Reload all videos and check new positions
      @video1.reload
      @video2.reload
      video3.reload
      
      # video3 should now be at position 1
      assert_equal 1, video3.position
      # Other videos should have shifted
      assert_equal 2, @video1.position
      assert_equal 3, @video2.position
    end

    test "should handle AJAX position updates" do
      sign_in @vendor_user
      
      patch :update_position, params: { 
        product_id: @product.slug,
        id: @video2.id,
        position: 1
      }, xhr: true
      
      assert_response :success
      response_data = JSON.parse(response.body)
      assert_equal 'success', response_data['status']
      
      @video2.reload
      assert_equal 1, @video2.position
    end

    test "should handle position update errors gracefully" do
      sign_in @vendor_user
      
      # Mock an error in the transaction
      ProductVideo.stub(:transaction, -> { raise StandardError.new("Position update failed") }) do
        patch :update_position, params: { 
          product_id: @product.slug,
          id: @video1.id,
          position: 2
        }
        
        assert_response :success
        response_data = JSON.parse(response.body)
        assert_equal 'error', response_data['status']
        assert_includes response_data['errors'], "Position update failed"
      end
    end

    # ===============================
    # PARAMETER SECURITY TESTS
    # ===============================

    test "should filter unpermitted parameters" do
      sign_in @vendor_user
      
      file = fixture_file_upload('test/fixtures/files/test_video.mp4', 'video/mp4')
      malicious_params = {
        title: "Valid Title",
        description: "Valid description",
        published: true,
        video_file: file,
        product_id: @other_vendor_product.id,  # Should be filtered/ignored
        position: 999,  # Should be auto-assigned
        created_at: 1.year.ago  # Should be filtered
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_video: malicious_params
      }
      
      video = ProductVideo.last
      assert_equal "Valid Title", video.title
      assert_equal @product, video.product  # Should use product from URL, not params
      assert_equal 3, video.position  # Should auto-assign, not use 999
      assert_not_equal 1.year.ago.to_date, video.created_at.to_date
    end

    test "should allow only permitted parameters" do
      sign_in @vendor_user
      
      file = fixture_file_upload('test/fixtures/files/test_video.mp4', 'video/mp4')
      permitted_params = {
        title: "Permitted Video",
        description: "Permitted description",
        published: false,
        video_file: file
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_video: permitted_params
      }
      
      video = ProductVideo.last
      assert_equal "Permitted Video", video.title
      assert_equal "Permitted description", video.description
      assert_not video.published?
      assert video.video_file.attached?
    end

    # ===============================
    # INTEGRATION TESTS
    # ===============================

    test "should complete full video management workflow" do
      sign_in @vendor_user
      
      # Step 1: View videos index
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      # Step 2: Visit new video page
      get :new, params: { product_id: @product.slug }
      assert_response :success
      
      # Step 3: Create video
      file = fixture_file_upload('test/fixtures/files/test_video.mp4', 'video/mp4')
      video_params = {
        title: "Workflow Video",
        description: "Video created through workflow",
        video_file: file,
        published: false
      }
      
      assert_difference '@product.product_videos.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_video: video_params
        }
      end
      
      video = ProductVideo.last
      assert_redirected_to vendors_product_product_videos_path(@product)
      
      # Step 4: View created video
      get :show, params: { product_id: @product.slug, id: video.id }
      assert_response :success
      assert_equal video, assigns(:product_video)
      
      # Step 5: Edit video
      get :edit, params: { product_id: @product.slug, id: video.id }
      assert_response :success
      
      # Step 6: Update video to published
      patch :update, params: { 
        product_id: @product.slug,
        id: video.id,
        product_video: { published: true }
      }
      
      assert_redirected_to vendors_product_product_videos_path(@product)
      
      video.reload
      assert video.published?
      
      # Step 7: Update position
      patch :update_position, params: { 
        product_id: @product.slug,
        id: video.id,
        position: 1
      }
      
      assert_response :success
      video.reload
      assert_equal 1, video.position
      
      # Step 8: Delete video
      assert_difference '@product.product_videos.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: video.id }
      end
      
      assert_redirected_to vendors_product_path(@product)
    end

    test "should maintain vendor account isolation" do
      sign_in @vendor_user
      
      # Should only see own products and videos
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      videos = assigns(:product_videos)
      videos.each do |video|
        assert_equal @vendor_account, video.product.account
      end
      
      # Should not be able to access other vendor's product
      assert_raises(ActiveRecord::RecordNotFound) do
        get :index, params: { product_id: @other_vendor_product.slug }
      end
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
    end
  end
end