require "test_helper"

module Vendors
  class BaseControllerTest < ActionController::TestCase
    # Since BaseController is abstract, we'll test through a concrete controller
    class TestController < BaseController
      def index
        render plain: "vendor index"
      end
      
      def show
        render plain: "vendor show"
      end
    end
    
    def setup
      @controller = TestController.new
      
      # Set up routes for our test controller
      Rails.application.routes.draw do
        namespace :vendors do
          resources :test, only: [:index, :show]
        end
      end
      
      # Create test users and accounts
      @vendor_user = users(:one)
      @agency_user = users(:two)
      @admin_user = users(:admin)
      
      @vendor_account = accounts(:one)
      @agency_account = accounts(:two)
      @pending_vendor_account = accounts(:pending_vendor)
      
      # Ensure accounts have proper setup
      @vendor_account.update!(account_type: 'vendor', status: 'approved', confirmed_at: Time.current)
      @agency_account.update!(account_type: 'agency', status: 'approved', confirmed_at: Time.current)
      @pending_vendor_account.update!(account_type: 'vendor', status: 'pending', confirmed_at: Time.current)
      
      # Create unapproved user
      @unapproved_user = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "Unapproved",
        last_name: "User"
      )
      @unapproved_account = Account.create!(
        name: "Unapproved Vendor",
        account_type: "vendor",
        status: "pending",
        confirmed_at: Time.current,
        owner: @unapproved_user
      )
      
      # Create user without accounts
      @user_without_accounts = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "No",
        last_name: "Accounts"
      )
    end
    
    def teardown
      # Restore original routes
      Rails.application.reload_routes!
    end

    # ===============================
    # AUTHENTICATION TESTS
    # ===============================

    test "should require authentication" do
      get :index
      assert_redirected_to new_session_path
    end

    test "should allow authenticated vendor users" do
      sign_in @vendor_user
      get :index
      assert_response :success
      assert_equal "vendor index", response.body
    end

    test "should store return_to path when requiring authentication" do
      get :index
      assert_equal vendors_test_index_url, session[:return_to_after_authenticating]
    end

    # ===============================
    # VENDOR ACCOUNT REQUIREMENT TESTS
    # ===============================

    test "should redirect non-vendor users" do
      sign_in @agency_user
      get :index
      assert_redirected_to root_path
    end

    test "should redirect users without accounts" do
      sign_in @user_without_accounts
      get :index
      assert_redirected_to orphaned_users_path
    end

    test "should allow admin users with vendor accounts" do
      # Give admin user a vendor account
      vendor_account_for_admin = Account.create!(
        name: "Admin's Vendor Account",
        account_type: "vendor",
        status: "approved",
        confirmed_at: Time.current,
        owner: @admin_user
      )
      
      sign_in @admin_user
      get :index
      assert_response :success
    end

    # ===============================
    # ACCOUNT APPROVAL REQUIREMENT TESTS
    # ===============================

    test "should redirect users with pending vendor accounts" do
      sign_in @unapproved_user
      get :index
      assert_redirected_to root_path
    end

    test "should allow users with approved vendor accounts" do
      sign_in @vendor_user
      get :index
      assert_response :success
      assert_equal "vendor index", response.body
    end

    test "should redirect when vendor account is not approved" do
      # Change account status to pending
      @vendor_account.update!(status: 'pending')
      
      sign_in @vendor_user
      get :index
      assert_redirected_to root_path
    end

    # ===============================
    # CURRENT ACCOUNT TESTS
    # ===============================

    test "should return vendor account from session when available" do
      sign_in @vendor_user
      session[:account_id] = @vendor_account.id
      
      get :index
      assert_equal @vendor_account, @controller.send(:current_account)
    end

    test "should return first vendor account when no session account_id" do
      sign_in @vendor_user
      session.delete(:account_id)
      
      get :index
      assert_equal @vendor_account, @controller.send(:current_account)
    end

    test "should return nil when user has no vendor accounts" do
      sign_in @agency_user
      
      # This will redirect, but we can still test the current_account method
      assert_nil @controller.send(:current_account)
    end

    test "should ignore non-vendor accounts in current_account" do
      # Create user with both vendor and agency accounts
      mixed_user = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "Mixed",
        last_name: "User"
      )
      
      vendor_acct = Account.create!(
        name: "Mixed Vendor",
        account_type: "vendor",
        status: "approved",
        confirmed_at: Time.current,
        owner: mixed_user
      )
      
      agency_acct = Account.create!(
        name: "Mixed Agency",
        account_type: "agency",
        status: "approved",
        confirmed_at: Time.current,
        owner: mixed_user
      )
      
      # Associate user with both accounts
      vendor_acct.account_users.create!(user: mixed_user, role: 'admin')
      agency_acct.account_users.create!(user: mixed_user, role: 'admin')
      
      sign_in mixed_user
      session[:account_id] = agency_acct.id  # Try to set agency account
      
      get :index
      # Should return vendor account, not agency account
      assert_equal vendor_acct, @controller.send(:current_account)
    end

    test "should handle invalid account_id in session" do
      sign_in @vendor_user
      session[:account_id] = 99999  # Non-existent account
      
      get :index
      # Should fall back to first vendor account
      assert_equal @vendor_account, @controller.send(:current_account)
    end

    test "should handle account user doesn't belong to" do
      other_user = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "Other",
        last_name: "User"
      )
      other_account = Account.create!(
        name: "Other Vendor",
        account_type: "vendor",
        status: "approved",
        confirmed_at: Time.current,
        owner: other_user
      )
      
      sign_in @vendor_user
      session[:account_id] = other_account.id  # Account user doesn't belong to
      
      get :index
      # Should fall back to user's own vendor account
      assert_equal @vendor_account, @controller.send(:current_account)
    end

    # ===============================
    # LAYOUT TESTS
    # ===============================

    test "should use dashboard layout" do
      sign_in @vendor_user
      get :index
    end

    # ===============================
    # FILTER CHAIN TESTS
    # ===============================

    test "should run all required filters in correct order" do
      # Test that all before_actions are executed
      
      # Without authentication - should stop at require_authentication
      get :index
      assert_redirected_to new_session_path
      
      # With agency user - should stop at require_vendor_account
      sign_in @agency_user
      get :index
      assert_redirected_to root_path
      
      # With unapproved vendor - should stop at require_approved_account
      sign_in @unapproved_user
      get :index
      assert_redirected_to root_path
      
      # With approved vendor - should succeed
      sign_in @vendor_user
      get :index
      assert_response :success
    end

    # ===============================
    # EDGE CASE TESTS
    # ===============================

    test "should handle user with multiple vendor accounts" do
      # Create second vendor account for user
      second_vendor = Account.create!(
        name: "Second Vendor",
        account_type: "vendor",
        status: "approved",
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      sign_in @vendor_user
      
      # Should return first vendor account by default
      get :index
      assert_equal @vendor_account, @controller.send(:current_account)
      
      # Should return specific account when set in session
      session[:account_id] = second_vendor.id
      get :show, params: { id: 1 }
      assert_equal second_vendor, @controller.send(:current_account)
    end

    test "should handle user with only unapproved vendor accounts" do
      all_pending_user = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "All Pending",
        last_name: "User"
      )
      
      Account.create!(
        name: "Pending Vendor 1",
        account_type: "vendor",
        status: "pending",
        confirmed_at: Time.current,
        owner: all_pending_user
      )
      
      Account.create!(
        name: "Pending Vendor 2",
        account_type: "vendor",
        status: "pending",
        confirmed_at: Time.current,
        owner: all_pending_user
      )
      
      sign_in all_pending_user
      get :index
      assert_redirected_to root_path
    end

    test "should handle user switching between vendor accounts" do
      # Create second approved vendor account
      second_vendor = Account.create!(
        name: "Second Vendor",
        account_type: "vendor",
        status: "approved",
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      sign_in @vendor_user
      
      # Start with first account
      session[:account_id] = @vendor_account.id
      get :index
      assert_equal @vendor_account, @controller.send(:current_account)
      
      # Switch to second account
      session[:account_id] = second_vendor.id
      get :show, params: { id: 1 }
      assert_equal second_vendor, @controller.send(:current_account)
    end

    # ===============================
    # SECURITY TESTS
    # ===============================

    test "should not allow bypassing authentication" do
      # Try various ways to bypass authentication
      
      # Direct controller instantiation shouldn't work in real app
      get :index
      assert_redirected_to new_session_path
      
      # Setting session manually shouldn't work without proper session
      session[:user_id] = @vendor_user.id
      get :index
      assert_redirected_to new_session_path
    end

    test "should not allow bypassing vendor requirement" do
      sign_in @agency_user
      
      # Try to force vendor account type
      @agency_account.update!(account_type: 'vendor')
      get :index
      # Should still redirect because require_vendor_account checks user's accounts
      assert_redirected_to root_path
    end

    test "should not allow bypassing approval requirement" do
      # Create user with vendor account but not approved
      pending_user = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "Still Pending",
        last_name: "User"
      )
      
      pending_account = Account.create!(
        name: "Still Pending Vendor",
        account_type: "vendor",
        status: "pending",
        confirmed_at: Time.current,
        owner: pending_user
      )
      
      sign_in pending_user
      get :index
      assert_redirected_to root_path
    end

    # ===============================
    # INTEGRATION TESTS
    # ===============================

    test "should work end-to-end for valid vendor user" do
      sign_in @vendor_user
      
      # Should be able to access index
      get :index
      assert_response :success
      assert_equal "vendor index", response.body
      
      # Should be able to access show
      get :show, params: { id: 1 }
      assert_response :success
      assert_equal "vendor show", response.body
      
      # Should have correct current_account
      assert_equal @vendor_account, @controller.send(:current_account)
    end

    test "should maintain session across requests" do
      sign_in @vendor_user
      
      get :index
      assert_response :success
      first_account = @controller.send(:current_account)
      
      get :show, params: { id: 1 }
      assert_response :success
      second_account = @controller.send(:current_account)
      
      assert_equal first_account, second_account
    end

    private

    def sign_in(user)
      # Simulate signing in by creating a session
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      @request.cookies.signed[:session_id] = session_record.id
    end
  end
end