require "test_helper"

module Vendors
  class ProductsControllerTest < ActionController::TestCase
    def setup
      # Create test users and accounts
      @vendor_user = users(:one)
      @other_vendor_user = users(:two)
      @admin_user = users(:admin)
      
      @vendor_account = accounts(:one)
      @vendor_account.update!(
        account_type: 'vendor', 
        status: 'approved', 
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      @other_vendor_account = accounts(:two)
      @other_vendor_account.update!(
        account_type: 'vendor',
        status: 'approved',
        confirmed_at: Time.current,
        owner: @other_vendor_user
      )
      
      # Clear existing account users to avoid duplicates
      @vendor_account.account_users.destroy_all
      @vendor_account.account_users.create!(user: @vendor_user, role: 'admin')
      
      @other_vendor_account.account_users.destroy_all
      @other_vendor_account.account_users.create!(user: @other_vendor_user, role: 'admin')
      
      @admin_user.update!(super_admin: true)
      
      # Create test products
      @product = @vendor_account.products.create!(
        name: "Test Product",
        description: "A test product description",
        website: "https://test.com",
        published: false
      )
      
      @published_product = @vendor_account.products.create!(
        name: "Published Product",
        description: "A published test product",
        website: "https://published.com",
        published: true
      )
      
      @other_vendor_product = @other_vendor_account.products.create!(
        name: "Other Vendor Product",
        description: "Product from another vendor",
        website: "https://other.com",
        published: true
      )
      
      # Create test categories
      @category1 = Category.create!(name: "Software", description: "Software products")
      @category2 = Category.create!(name: "Productivity", description: "Productivity tools")
      
      # Create test certifications
      @certification1 = Certification.create!(name: "SOC 2", description: "Security certification")
      @certification2 = Certification.create!(name: "FedRAMP", description: "Federal certification")
    end

    # ===============================
    # AUTHENTICATION & AUTHORIZATION TESTS
    # ===============================

    test "should require vendor authentication for all actions" do
      get :index
      assert_redirected_to new_session_path
      
      get :show, params: { id: @product.id }
      assert_redirected_to new_session_path
      
      get :new
      assert_redirected_to new_session_path
      
      post :create, params: { product: { name: "Test" } }
      assert_redirected_to new_session_path
    end

    test "should require vendor account type" do
      agency_user = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "Agency",
        last_name: "User"
      )
      
      agency_account = Account.create!(
        name: "Agency Account",
        account_type: "agency",
        status: "approved",
        confirmed_at: Time.current,
        owner: agency_user
      )
      AccountUser.create!(account: agency_account, user: agency_user, role: 'admin')
      
      sign_in agency_user
      
      get :index
      assert_redirected_to root_path
    end

    test "should require approved account" do
      pending_user = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "Pending",
        last_name: "User"
      )
      
      pending_account = Account.create!(
        name: "Pending Account",
        account_type: "vendor",
        status: "pending",
        confirmed_at: Time.current,
        owner: pending_user
      )
      AccountUser.create!(account: pending_account, user: pending_user, role: 'admin')
      
      sign_in pending_user
      
      get :index
      assert_redirected_to root_path
    end

    test "should use dashboard layout" do
      sign_in @vendor_user
      get :index
      assert_response :success
    end

    # ===============================
    # INDEX ACTION TESTS
    # ===============================

    test "should show products index" do
      sign_in @vendor_user
      
      get :index
      assert_response :success
      assert_not_nil assigns(:products)
      
      products = assigns(:products)
      assert products.include?(@product)
      assert products.include?(@published_product)
      assert_not products.include?(@other_vendor_product)  # Should not see other vendor's products
    end

    test "should show products ordered by creation date" do
      sign_in @vendor_user
      
      # Create another product with a different timestamp
      newer_product = @vendor_account.products.create!(
        name: "Newer Product",
        description: "A newer product",
        website: "https://newer.com",
        published: false
      )
      
      get :index
      assert_response :success
      
      products = assigns(:products).to_a
      assert_equal newer_product, products.first  # Should be first (newest)
    end

    test "should include categories with products" do
      sign_in @vendor_user
      
      @product.categories << @category1
      
      get :index
      assert_response :success
      
      products = assigns(:products)
      product_with_category = products.find { |p| p.id == @product.id }
      assert product_with_category.categories.loaded?  # Should be included/preloaded
    end

    # ===============================
    # SHOW ACTION TESTS
    # ===============================

    test "should show product details" do
      sign_in @vendor_user
      
      get :show, params: { id: @product.id }
      assert_response :success
      assert_equal @product, assigns(:product)
      assert_not_nil assigns(:product_videos)
      assert_not_nil assigns(:case_studies)
      assert_not_nil assigns(:product_content)
    end

    test "should not show other vendor's product" do
      sign_in @vendor_user
      
      # Should not be able to access other vendor's product (scoped by current_account)
      get :show, params: { id: @other_vendor_product.id }
      assert_response :redirect  # Should redirect to error page
    end

    test "should scope products to current vendor account" do
      sign_in @admin_user
      
      # Create admin vendor account
      admin_account = Account.create!(
        name: "Admin Account",
        account_type: "vendor",
        status: "approved",
        confirmed_at: Time.current,
        owner: @admin_user
      )
      AccountUser.create!(account: admin_account, user: @admin_user, role: 'admin')
      
      # Admin can only see their own vendor account products
      get :show, params: { id: @other_vendor_product.id }
      assert_response :redirect  # Should redirect to error page
    end

    test "should load associated content in show" do
      sign_in @vendor_user
      
      # Create associated content
      video = @product.product_videos.build(
        title: "Demo Video",
        description: "A demo video description",
        position: 1
      )
      video.video_file.attach(
        io: StringIO.new("fake video data"),
        filename: "demo.mp4",
        content_type: "video/mp4"
      )
      video.save!
      
      case_study = @product.case_studies.build(
        title: "Success Story",
        description: "A detailed case study about customer success",
        position: 1
      )
      case_study.upload.attach(
        io: StringIO.new("fake pdf data"),
        filename: "case_study.pdf",
        content_type: "application/pdf"
      )
      case_study.save!
      
      get :show, params: { id: @product.id }
      assert_response :success
      
      assert assigns(:product_videos).include?(video)
      assert assigns(:case_studies).include?(case_study)
    end

    # ===============================
    # NEW ACTION TESTS
    # ===============================

    test "should show new product form" do
      sign_in @vendor_user
      
      get :new
      assert_response :success
      assert_instance_of Product, assigns(:product)
      assert assigns(:product).new_record?
      assert_not_nil assigns(:categories)
      assert_not_nil assigns(:certifications)
    end

    test "should load categories and certifications for new form" do
      sign_in @vendor_user
      
      get :new
      assert_response :success
      
      categories = assigns(:categories)
      certifications = assigns(:certifications)
      
      assert categories.include?(@category1)
      assert categories.include?(@category2)
      assert certifications.include?(@certification1)
      assert certifications.include?(@certification2)
    end

    # ===============================
    # CREATE ACTION TESTS
    # ===============================

    test "should create product with valid data" do
      sign_in @vendor_user
      
      product_params = {
        name: "New Product",
        description: "A new product description",
        website: "https://newproduct.com",
        published: true,
        category_ids: [@category1.id, @category2.id],
        certification_ids: [@certification1.id]
      }
      
      assert_difference '@vendor_account.products.count', 1 do
        post :create, params: { product: product_params }
      end
      
      assert_redirected_to vendors_product_path(assigns(:product))
      assert_match /Product was successfully created/, flash[:notice]
      
      product = Product.last
      assert_equal "New Product", product.name
      assert_equal "A new product description", product.description.to_plain_text
      assert_equal "https://newproduct.com", product.website
      assert product.published?
      assert_equal @vendor_account, product.account
      
      assert product.categories.include?(@category1)
      assert product.categories.include?(@category2)
      assert product.certifications.include?(@certification1)
    end

    test "should not create product with invalid data" do
      sign_in @vendor_user
      
      invalid_params = {
        name: "",  # Required field
        description: "",
        website: "invalid-url"
      }
      
      assert_no_difference 'Product.count' do
        post :create, params: { product: invalid_params }
      end
      
      assert_response :unprocessable_entity
      assert_not_nil assigns(:product)
      assert assigns(:product).errors.any?
      assert_not_nil assigns(:categories)
      assert_not_nil assigns(:certifications)
    end

    test "should create unpublished product by default" do
      sign_in @vendor_user
      
      product_params = {
        name: "Draft Product",
        description: "A draft product",
        website: "https://draft.com"
        # published not specified
      }
      
      post :create, params: { product: product_params }
      
      product = Product.last
      assert_not product.published?
    end

    # ===============================
    # EDIT ACTION TESTS
    # ===============================

    test "should show edit product form" do
      sign_in @vendor_user
      
      get :edit, params: { id: @product.id }
      assert_response :success
      assert_equal @product, assigns(:product)
      assert_not_nil assigns(:categories)
      assert_not_nil assigns(:certifications)
    end

    test "should not edit other vendor's product" do
      sign_in @vendor_user
      
      get :edit, params: { id: @other_vendor_product.id }
      assert_response :redirect  # Should redirect to error page
    end

    test "should load current categories and certifications for edit" do
      sign_in @vendor_user
      
      @product.categories << @category1
      @product.certifications << @certification1
      
      get :edit, params: { id: @product.id }
      assert_response :success
      
      product = assigns(:product)
      assert product.categories.include?(@category1)
      assert product.certifications.include?(@certification1)
    end

    # ===============================
    # UPDATE ACTION TESTS
    # ===============================

    test "should update product with valid data" do
      sign_in @vendor_user
      
      update_params = {
        name: "Updated Product Name",
        description: "Updated description",
        website: "https://updated.com",
        published: true,
        category_ids: [@category2.id],
        certification_ids: [@certification2.id]
      }
      
      patch :update, params: { id: @product.id, product: update_params }
      
      assert_redirected_to vendors_product_path(@product)
      assert_match /Product was successfully updated/, flash[:notice]
      
      @product.reload
      assert_equal "Updated Product Name", @product.name
      assert_equal "Updated description", @product.description.to_plain_text
      assert_equal "https://updated.com", @product.website
      assert @product.published?
      
      assert @product.categories.include?(@category2)
      assert_not @product.categories.include?(@category1)
      assert @product.certifications.include?(@certification2)
    end

    test "should not update product with invalid data" do
      sign_in @vendor_user
      
      original_name = @product.name
      
      invalid_params = {
        name: "",  # Invalid
        description: "Valid description"
      }
      
      patch :update, params: { id: @product.id, product: invalid_params }
      
      assert_response :unprocessable_entity
      assert_not_nil assigns(:product)
      assert assigns(:product).errors.any?
      
      @product.reload
      assert_equal original_name, @product.name  # Should not change
    end

    test "should not update other vendor's product" do
      sign_in @vendor_user
      
      patch :update, params: { 
        id: @other_vendor_product.id, 
        product: { name: "Hacked Name" } 
      }
      assert_response :redirect  # Should redirect to error page
    end

    # ===============================
    # DESTROY ACTION TESTS
    # ===============================

    test "should delete product" do
      sign_in @vendor_user
      
      assert_difference '@vendor_account.products.count', -1 do
        delete :destroy, params: { id: @product.id }
      end
      
      assert_redirected_to vendors_products_path
      assert_match /Product was successfully deleted/, flash[:notice]
    end

    test "should not delete other vendor's product" do
      sign_in @vendor_user
      
      delete :destroy, params: { id: @other_vendor_product.id }
      assert_response :redirect  # Should redirect to error page
    end

    test "should handle delete of product with associated content" do
      sign_in @vendor_user
      
      # Create associated content
      video = @product.product_videos.build(
        title: "Demo Video",
        description: "A demo video description",
        position: 1
      )
      video.video_file.attach(
        io: StringIO.new("fake video data"),
        filename: "demo.mp4",
        content_type: "video/mp4"
      )
      video.save!
      
      case_study = @product.case_studies.build(
        title: "Success Story",
        description: "A detailed case study about customer success",
        position: 1
      )
      case_study.upload.attach(
        io: StringIO.new("fake pdf data"),
        filename: "case_study.pdf",
        content_type: "application/pdf"
      )
      case_study.save!
      
      assert_difference '@vendor_account.products.count', -1 do
        delete :destroy, params: { id: @product.id }
      end
      
      assert_redirected_to vendors_products_path
    end

    # ===============================
    # PARAMETER SECURITY TESTS
    # ===============================

    test "should filter unpermitted parameters" do
      sign_in @vendor_user
      
      malicious_params = {
        name: "Valid Name",
        description: "Valid description",
        account_id: @other_vendor_account.id,  # Should be filtered
        created_at: 1.year.ago,  # Should be filtered
        id: @other_vendor_product.id  # Should be filtered
      }
      
      post :create, params: { product: malicious_params }
      
      product = Product.last
      assert_equal @vendor_account, product.account  # Should use current account
      assert_equal "Valid Name", product.name
      assert_not_equal 1.year.ago.to_date, product.created_at.to_date
    end

    test "should allow only permitted parameters" do
      sign_in @vendor_user
      
      permitted_params = {
        name: "Permitted Product",
        description: "Permitted description",
        website: "https://permitted.com",
        published: true,
        category_ids: [@category1.id],
        certification_ids: [@certification1.id]
      }
      
      post :create, params: { product: permitted_params }
      
      product = Product.last
      assert_equal "Permitted Product", product.name
      assert_equal "Permitted description", product.description.to_plain_text
      assert_equal "https://permitted.com", product.website
      assert product.published?
      assert product.categories.include?(@category1)
      assert product.certifications.include?(@certification1)
    end

    # ===============================
    # CATEGORY & CERTIFICATION TESTS
    # ===============================

    test "should handle multiple categories" do
      sign_in @vendor_user
      
      product_params = {
        name: "Multi-Category Product",
        description: "Product with multiple categories",
        category_ids: [@category1.id, @category2.id]
      }
      
      post :create, params: { product: product_params }
      
      product = Product.last
      assert_equal 2, product.categories.count
      assert product.categories.include?(@category1)
      assert product.categories.include?(@category2)
    end

    test "should handle multiple certifications" do
      sign_in @vendor_user
      
      product_params = {
        name: "Multi-Cert Product",
        description: "Product with multiple certifications",
        certification_ids: [@certification1.id, @certification2.id]
      }
      
      post :create, params: { product: product_params }
      
      product = Product.last
      assert_equal 2, product.certifications.count
      assert product.certifications.include?(@certification1)
      assert product.certifications.include?(@certification2)
    end

    test "should remove categories when updating" do
      sign_in @vendor_user
      
      @product.categories << [@category1, @category2]
      assert_equal 2, @product.categories.count
      
      update_params = {
        name: @product.name,
        category_ids: [@category1.id]  # Only one category now
      }
      
      patch :update, params: { id: @product.id, product: update_params }
      
      @product.reload
      assert_equal 1, @product.categories.count
      assert @product.categories.include?(@category1)
      assert_not @product.categories.include?(@category2)
    end

    # ===============================
    # INTEGRATION TESTS
    # ===============================

    test "should complete full product creation workflow" do
      sign_in @vendor_user
      
      # Step 1: Visit new product page
      get :new
      assert_response :success
      
      # Step 2: Create product
      product_params = {
        name: "Workflow Product",
        description: "Product created through workflow",
        website: "https://workflow.com",
        published: false,
        category_ids: [@category1.id]
      }
      
      assert_difference 'Product.count', 1 do
        post :create, params: { product: product_params }
      end
      
      product = Product.last
      assert_redirected_to vendors_product_path(product)
      
      # Step 3: View created product
      get :show, params: { id: product.id }
      assert_response :success
      assert_equal product, assigns(:product)
      
      # Step 4: Edit product
      get :edit, params: { id: product.id }
      assert_response :success
      
      # Step 5: Update product to published
      patch :update, params: { 
        id: product.id, 
        product: { published: true } 
      }
      
      assert_redirected_to vendors_product_path(product)
      
      product.reload
      assert product.published?
    end

    test "should handle product management with validation errors" do
      sign_in @vendor_user
      
      # Step 1: Try to create invalid product
      invalid_params = {
        name: "",  # Invalid
        description: "Valid description"
      }
      
      assert_no_difference 'Product.count' do
        post :create, params: { product: invalid_params }
      end
      
      assert_response :unprocessable_entity
      assert assigns(:product).errors.any?
      
      # Step 2: Fix errors and create successfully
      valid_params = {
        name: "Fixed Product Name",
        description: "Valid description",
        website: "https://fixed.com"
      }
      
      assert_difference 'Product.count', 1 do
        post :create, params: { product: valid_params }
      end
      
      assert_redirected_to vendors_product_path(assigns(:product))
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
    end
  end
end