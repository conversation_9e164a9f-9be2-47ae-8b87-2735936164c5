require "test_helper"

class Vendors::CaseStudiesControllerTest < ActionController::TestCase
  setup do
    @vendor_user = users(:one)
    @vendor_account = accounts(:one)
    @agency_user = users(:two) 
    @agency_account = accounts(:two)

    # Configure vendor account properly
    @vendor_account.update!(
      account_type: 'vendor',
      status: 'approved',
      confirmed_at: Time.current,
      owner: @vendor_user
    )

    # Configure agency account
    @agency_account.update!(
      account_type: 'agency',
      status: 'approved',
      confirmed_at: Time.current,
      owner: @agency_user
    )

    # Clear existing account users to avoid duplicates
    @vendor_account.account_users.destroy_all
    @vendor_account.account_users.create!(user: @vendor_user, role: 'admin')

    # Clear existing data to ensure clean test state
    @vendor_account.products.destroy_all

    # Create test product
    @product = Product.create!(
      account: @vendor_account,
      name: "Test Product",
      description: "A test product",
      published: true
    )

    # Create test case studies with uploads
    @case_study1 = CaseStudy.new(
      product: @product,
      title: "First Case Study",
      description: "Description of first case study",
      position: 1,
      published: true
    )
    @case_study1.upload.attach(
      io: StringIO.new("%PDF-1.4 test content"),
      filename: "case_study1.pdf",
      content_type: "application/pdf"
    )
    @case_study1.save!
    
    @case_study2 = CaseStudy.new(
      product: @product,
      title: "Second Case Study",
      description: "Description of second case study",
      position: 2,
      published: false
    )
    @case_study2.upload.attach(
      io: StringIO.new("%PDF-1.4 test content"),
      filename: "case_study2.pdf",
      content_type: "application/pdf"
    )
    @case_study2.save!
  end

  # Authentication Tests
  test "should require authentication" do
    get :index, params: { product_id: @product.id }
    assert_response :redirect
    assert_redirected_to new_session_path
  end

  test "should require vendor account" do
    sign_in @agency_user
    session[:account_id] = @agency_account.id

    get :index, params: { product_id: @product.id }
    assert_response :redirect
  end

  test "should require product ownership" do
    other_vendor = Account.create!(
      name: "Other Vendor",
      account_type: 'vendor',
      status: 'approved',
      owner: @vendor_user
    )
    
    other_product = Product.create!(
      account: other_vendor,
      name: "Other Product",
      description: "Product from other vendor"
    )

    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index, params: { product_id: other_product.id }
    assert_response :redirect
  end

  # Index Tests
  test "should get index with valid product" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index, params: { product_id: @product.id }
    assert_response :success
    assert_not_nil assigns(:case_studies)
    assert_not_nil assigns(:product)
    assert_equal @product, assigns(:product)
  end

  test "should display all case studies for product ordered by position" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index, params: { product_id: @product.id }
    case_studies = assigns(:case_studies)
    
    assert_equal 2, case_studies.count
    assert_equal @case_study1, case_studies.first
    assert_equal @case_study2, case_studies.last
  end

  test "should include attached uploads to avoid N+1 queries" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index, params: { product_id: @product.id }
    case_studies = assigns(:case_studies)
    
    # Test that upload associations are loaded
    case_studies.each do |case_study|
      # This shouldn't trigger additional queries
      assert_not_nil case_study.upload.attached?
    end
    
    assert case_studies.any?, "Should have case studies"
  end

  # Show Tests
  test "should show case study" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :show, params: { product_id: @product.id, id: @case_study1.id }
    assert_response :success
    assert_equal @case_study1, assigns(:case_study)
    assert_equal @product, assigns(:product)
  end

  test "should not show case study from different product" do
    other_product = Product.create!(
      account: @vendor_account,
      name: "Other Product",
      description: "Another product from same vendor"
    )
    
    other_case_study = CaseStudy.new(
      product: other_product,
      title: "Other Case Study",
      description: "Description of other case study",
      position: 1
    )
    other_case_study.upload.attach(
      io: StringIO.new("%PDF-1.4 test content"),
      filename: "other_case_study.pdf",
      content_type: "application/pdf"
    )
    other_case_study.save!

    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    # Case studies are scoped to the product, so accessing with wrong product should fail
    get :show, params: { product_id: @product.id, id: other_case_study.id }
    assert_response :redirect
  end

  # New Tests
  test "should get new case study form" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :new, params: { product_id: @product.id }
    assert_response :success
    assert_not_nil assigns(:case_study)
    assert assigns(:case_study).new_record?
    assert_equal @product, assigns(:case_study).product
    assert_not_nil assigns(:case_study).position
  end

  test "should set next position for new case study" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :new, params: { product_id: @product.id }
    case_study = assigns(:case_study)
    
    # Should be positioned after existing case studies
    assert_equal 3, case_study.position
  end

  # Create Tests
  test "should create case study with valid params" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    file = fixture_file_upload(Rails.root.join('test', 'fixtures', 'files', 'test.pdf'), 'application/pdf')

    case_study_params = {
      title: "New Case Study",
      description: "Description of new case study",
      published: true,
      upload: file
    }

    assert_difference('CaseStudy.count', 1) do
      post :create, params: { 
        product_id: @product.id,
        case_study: case_study_params
      }
    end

    assert_redirected_to vendors_product_case_studies_path(@product)
    assert_equal 'Case study was successfully added.', flash[:notice]
    
    case_study = CaseStudy.last
    assert_equal "New Case Study", case_study.title
    assert_equal "Description of new case study", case_study.description
    assert_equal true, case_study.published
    assert_equal @product, case_study.product
    assert_equal 3, case_study.position
    assert case_study.upload.attached?
  end

  test "should create case study with file upload" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    file = fixture_file_upload(Rails.root.join('test', 'fixtures', 'files', 'test.pdf'), 'application/pdf')

    case_study_params = {
      title: "Case Study with Upload",
      description: "Description with upload",
      upload: file
    }

    assert_difference('CaseStudy.count', 1) do
      post :create, params: { 
        product_id: @product.id,
        case_study: case_study_params
      }
    end

    case_study = CaseStudy.last
    assert case_study.upload.attached?
  end

  test "should not create case study with invalid params" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    case_study_params = {
      title: "", # Invalid: title can't be blank
      description: "Description"
      # Missing upload: also invalid
    }

    assert_no_difference('CaseStudy.count') do
      post :create, params: { 
        product_id: @product.id,
        case_study: case_study_params
      }
    end

    assert_response :unprocessable_entity
    assert_template :new
    assert_not_nil assigns(:case_study)
    assert assigns(:case_study).errors.any?
  end

  # Edit Tests
  test "should get edit case study form" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :edit, params: { product_id: @product.id, id: @case_study1.id }
    assert_response :success
    assert_equal @case_study1, assigns(:case_study)
    assert_equal @product, assigns(:product)
  end

  # Update Tests
  test "should update case study with valid params" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    patch :update, params: { 
      product_id: @product.id,
      id: @case_study1.id,
      case_study: { 
        title: "Updated Title",
        description: "Updated description",
        published: false
      }
    }
    
    assert_redirected_to vendors_product_case_studies_path(@product)
    assert_equal 'Case study was successfully updated.', flash[:notice]
    
    @case_study1.reload
    assert_equal "Updated Title", @case_study1.title
    assert_equal "Updated description", @case_study1.description
    assert_equal false, @case_study1.published
  end

  test "should not update case study with invalid params" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    patch :update, params: { 
      product_id: @product.id,
      id: @case_study1.id,
      case_study: { title: "" } # Invalid: title can't be blank
    }
    
    assert_response :unprocessable_entity
    assert_template :edit
    assert_not_nil assigns(:case_study)
    assert assigns(:case_study).errors.any?
    
    @case_study1.reload
    assert_equal "First Case Study", @case_study1.title # Should remain unchanged
  end

  # Destroy Tests
  test "should destroy case study" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    assert_difference('CaseStudy.count', -1) do
      delete :destroy, params: { product_id: @product.id, id: @case_study1.id }
    end

    assert_redirected_to vendors_product_path(@product)
    assert_equal 'Case study was successfully deleted.', flash[:notice]
  end

  # Position Update Tests
  test "should update case study position via JSON" do
    # Create third case study for better position testing
    case_study3 = CaseStudy.new(
      product: @product,
      title: "Third Case Study",
      description: "Description of third case study",
      position: 3
    )
    case_study3.upload.attach(
      io: StringIO.new("%PDF-1.4 test content"),
      filename: "case_study3.pdf",
      content_type: "application/pdf"
    )
    case_study3.save!

    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    # Move case_study3 (position 3) to position 1
    patch :update_position, params: { 
      product_id: @product.id,
      id: case_study3.id,
      position: 1
    }, format: :json
    
    assert_response :success
    
    response_json = JSON.parse(response.body)
    assert_equal 'success', response_json['status']
    
    # Verify positions were updated correctly
    @case_study1.reload
    @case_study2.reload
    case_study3.reload
    
    assert_equal 1, case_study3.position
    assert_equal 2, @case_study1.position
    assert_equal 3, @case_study2.position
  end

  test "should handle position update errors gracefully" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    # Test with invalid position (negative number)
    patch :update_position, params: { 
      product_id: @product.id,
      id: @case_study1.id,
      position: -1
    }, format: :json
    
    # Should still succeed but position will be corrected
    assert_response :success
    
    response_json = JSON.parse(response.body)
    assert_equal 'success', response_json['status']
  end

  # Security Tests
  test "should only permit allowed parameters" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    patch :update, params: { 
      product_id: @product.id,
      id: @case_study1.id,
      case_study: { 
        title: "Updated Title",
        description: "Updated description",
        position: 999, # Should not be permitted for update
        product_id: @product.id + 1 # Should not be permitted
      }
    }
    
    @case_study1.reload
    assert_equal "Updated Title", @case_study1.title
    assert_equal "Updated description", @case_study1.description
    assert_equal 1, @case_study1.position # Should remain unchanged
    assert_equal @product.id, @case_study1.product_id # Should remain unchanged
  end

  test "should scope case studies to current product" do
    other_product = Product.create!(
      account: @vendor_account,
      name: "Other Product",
      description: "Another product from same vendor"
    )
    
    other_case_study = CaseStudy.new(
      product: other_product,
      title: "Other Case Study",
      description: "Description of other case study",
      position: 1
    )
    other_case_study.upload.attach(
      io: StringIO.new("%PDF-1.4 test content"),
      filename: "other_case_study.pdf",
      content_type: "application/pdf"
    )
    other_case_study.save!

    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index, params: { product_id: @product.id }
    case_studies = assigns(:case_studies)
    
    # Should only show case studies for current product
    assert_equal 2, case_studies.count
    assert_includes case_studies, @case_study1
    assert_includes case_studies, @case_study2
    assert_not_includes case_studies, other_case_study
  end

  test "should handle missing product gracefully" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    # Should redirect when product doesn't exist or doesn't belong to account
    get :index, params: { product_id: 'nonexistent' }
    assert_response :redirect
  end

  private

  def sign_in(user)
    session_record = user.sessions.create!(
      user_agent: 'Test User Agent',
      ip_address: '127.0.0.1'
    )
    Current.session = session_record
    cookies.signed[:session_id] = session_record.id
  end
end