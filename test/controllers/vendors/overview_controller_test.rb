require "test_helper"

class Vendors::OverviewControllerTest < ActionController::TestCase
  setup do
    @vendor_user = users(:one)
    @vendor_account = accounts(:one)
    @agency_user = users(:two) 
    @agency_account = accounts(:two)

    # Configure vendor account properly
    @vendor_account.update!(
      account_type: 'vendor',
      status: 'approved',
      confirmed_at: Time.current,
      owner: @vendor_user
    )

    # Configure agency account
    @agency_account.update!(
      account_type: 'agency',
      status: 'approved',
      confirmed_at: Time.current,
      owner: @agency_user
    )

    # Clear existing account users to avoid duplicates
    @vendor_account.account_users.destroy_all
    @vendor_account.account_users.create!(user: @vendor_user, role: 'admin')

    # Clear existing data to ensure clean test state
    @vendor_account.products.destroy_all

    # Create some test data
    @category = Category.create!(name: "Software", description: "Software products")
    
    @product1 = Product.create!(
      account: @vendor_account,
      name: "Test Product 1",
      description: "A test product",
      published: true
    )
    
    @product2 = Product.create!(
      account: @vendor_account,
      name: "Test Product 2", 
      description: "Another test product",
      published: false
    )

    @lead1 = Lead.create!(
      product: @product1,
      user: @agency_user,
      status: :pending,
      message: "Interested in this product",
      contact_name: "<PERSON>",
      contact_email: "<EMAIL>",
      contact_company: "City Government"
    )
    
    @lead2 = Lead.create!(
      product: @product1,
      user: @agency_user,
      status: :qualified,
      message: "Very interested",
      contact_name: "Jane Smith",
      contact_email: "<EMAIL>",
      contact_company: "City Government"
    )
  end

  # Authentication Tests
  test "should require authentication" do
    get :index
    assert_response :redirect
    assert_redirected_to new_session_path
  end

  test "should require vendor account" do
    sign_in @agency_user
    session[:account_id] = @agency_account.id

    get :index
    assert_response :redirect
  end

  # Successful Access Tests
  test "should get index with vendor user and account" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index
    assert_response :success
    assert_not_nil assigns(:products)
    assert_not_nil assigns(:recent_leads)
    assert_not_nil assigns(:stats)
  end

  test "should show products limited to 5" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    # Create 7 products
    5.times do |i|
      Product.create!(
        account: @vendor_account,
        name: "Extra Product #{i}",
        description: "Extra product #{i}"
      )
    end

    get :index
    products = assigns(:products)
    assert_equal 5, products.count
  end

  test "should show recent leads limited to 10" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    # Create 12 additional leads
    12.times do |i|
      Lead.create!(
        product: @product1,
        user: @agency_user,
        status: :pending,
        message: "Lead #{i}",
        contact_name: "Contact #{i}",
        contact_email: "contact#{i}@example.com",
        contact_company: "Company #{i}"
      )
    end

    get :index
    leads = assigns(:recent_leads)
    assert leads.count <= 10
  end

  test "should only show leads for current account products" do
    # Create another vendor with products and leads
    other_vendor = Account.create!(
      name: "Other Vendor",
      account_type: :vendor,
      status: :approved,
      owner: @vendor_user
    )
    
    other_product = Product.create!(
      account: other_vendor,
      name: "Other Product",
      description: "Product from other vendor"
    )
    
    Lead.create!(
      product: other_product,
      user: @agency_user,
      status: :pending,
      message: "Lead for other vendor",
      contact_name: "Other Contact",
      contact_email: "<EMAIL>",
      contact_company: "Other Company"
    )

    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index
    leads = assigns(:recent_leads)
    
    # Should only show leads for current vendor's products
    leads.each do |lead|
      assert_equal @vendor_account, lead.product.account
    end
  end

  test "should calculate correct stats" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index
    stats = assigns(:stats)

    assert_equal 2, stats[:products_count]
    assert_equal 1, stats[:published_products]
    assert_equal 2, stats[:total_leads]
    assert_equal 1, stats[:new_leads] # Only pending leads
  end

  test "should include associated data in recent leads" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index
    leads = assigns(:recent_leads)
    
    # Test that associations are loaded (no N+1 queries)
    assert leads.any?, "Should have some leads"
    leads.each do |lead|
      assert_not_nil lead.user, "Lead user should not be nil"
      assert_not_nil lead.product, "Lead product should not be nil"
    end
  end

  test "should scope products to current account only" do
    # Create product for different vendor
    other_vendor = Account.create!(
      name: "Other Vendor",
      account_type: :vendor,
      status: :approved,
      owner: @vendor_user
    )
    
    Product.create!(
      account: other_vendor,
      name: "Other Vendor Product",
      description: "Should not appear"
    )

    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index
    products = assigns(:products)
    
    products.each do |product|
      assert_equal @vendor_account, product.account
    end
  end

  test "should handle empty data gracefully" do
    # Create a clean vendor account with no products
    clean_vendor = Account.create!(
      name: "Clean Vendor",
      account_type: 'vendor',
      status: 'approved',
      confirmed_at: Time.current,
      owner: @vendor_user
    )
    clean_vendor.account_users.create!(user: @vendor_user, role: 'admin')

    sign_in @vendor_user
    session[:account_id] = clean_vendor.id

    get :index
    assert_response :success
    
    stats = assigns(:stats)
    assert_equal 0, stats[:products_count]
    assert_equal 0, stats[:published_products]
    assert_equal 0, stats[:total_leads]
    assert_equal 0, stats[:new_leads]
    
    assert_equal 0, assigns(:products).count
    assert_equal 0, assigns(:recent_leads).count
  end

  test "should order recent leads correctly" do
    # Create leads with different timestamps
    old_lead = Lead.create!(
      product: @product1,
      user: @agency_user,
      status: :pending,
      message: "Old lead",
      contact_name: "Old Contact",
      contact_email: "<EMAIL>",
      contact_company: "Old Company",
      created_at: 2.days.ago
    )
    
    new_lead = Lead.create!(
      product: @product1,
      user: @agency_user,
      status: :pending,
      message: "New lead",
      contact_name: "New Contact",
      contact_email: "<EMAIL>",
      contact_company: "New Company",
      created_at: 1.hour.ago
    )

    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index
    leads = assigns(:recent_leads)
    
    # Should be ordered by recent (assuming Lead.recent scope orders by created_at desc)
    assert leads.first.created_at >= leads.last.created_at
  end

  private

  def sign_in(user)
    session_record = user.sessions.create!(
      user_agent: 'Test User Agent',
      ip_address: '127.0.0.1'
    )
    Current.session = session_record
    cookies.signed[:session_id] = session_record.id
  end
end