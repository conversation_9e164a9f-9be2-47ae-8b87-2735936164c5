require "test_helper"

class Vendors::LeadsControllerTest < ActionController::TestCase
  setup do
    @vendor_user = users(:one)
    @vendor_account = accounts(:one)
    @agency_user = users(:two) 
    @agency_account = accounts(:two)

    # Configure vendor account properly
    @vendor_account.update!(
      account_type: 'vendor',
      status: 'approved',
      confirmed_at: Time.current,
      owner: @vendor_user
    )

    # Configure agency account
    @agency_account.update!(
      account_type: 'agency',
      status: 'approved',
      confirmed_at: Time.current,
      owner: @agency_user
    )

    # Clear existing account users to avoid duplicates
    @vendor_account.account_users.destroy_all
    @vendor_account.account_users.create!(user: @vendor_user, role: 'admin')

    # Clear existing data to ensure clean test state
    @vendor_account.products.destroy_all

    # Create test products
    @product1 = Product.create!(
      account: @vendor_account,
      name: "Test Product 1",
      description: "A test product",
      published: true
    )
    
    @product2 = Product.create!(
      account: @vendor_account,
      name: "Test Product 2", 
      description: "Another test product",
      published: false
    )

    # Create test leads
    @lead1 = Lead.create!(
      product: @product1,
      account: @vendor_account,
      user: @agency_user,
      status: :pending,
      message: "Interested in this product",
      contact_name: "<PERSON>",
      contact_email: "<EMAIL>",
      contact_company: "City Government"
    )
    
    @lead2 = Lead.create!(
      product: @product1,
      account: @vendor_account,
      user: @agency_user,
      status: :contacted,
      message: "Follow up needed",
      contact_name: "John Doe",
      contact_email: "<EMAIL>",
      contact_company: "City Government"
    )

    @lead3 = Lead.create!(
      product: @product2,
      account: @vendor_account,
      user: @agency_user,
      status: :qualified,
      message: "Ready to purchase",
      contact_name: "Bob Wilson",
      contact_email: "<EMAIL>",
      contact_company: "City Government"
    )
  end

  # Authentication Tests
  test "should require authentication" do
    get :index
    assert_response :redirect
    assert_redirected_to new_session_path
  end

  test "should require vendor account" do
    sign_in @agency_user
    session[:account_id] = @agency_account.id

    get :index
    assert_response :redirect
  end

  # Index Tests
  test "should get index with vendor user and account" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index
    assert_response :success
    assert_not_nil assigns(:leads)
    assert_not_nil assigns(:products)
    assert_not_nil assigns(:lead_stats)
  end

  test "should display all leads for current account" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index
    leads = assigns(:leads)
    
    assert_equal 3, leads.count
    assert_includes leads, @lead1
    assert_includes leads, @lead2
    assert_includes leads, @lead3
  end

  test "should order leads by created_at desc" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index
    leads = assigns(:leads)
    
    # Should be ordered by newest first
    assert leads.first.created_at >= leads.last.created_at
  end

  test "should include associated data to avoid N+1 queries" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index
    leads = assigns(:leads)
    
    # Test that associations are loaded
    leads.each do |lead|
      assert_not_nil lead.product
      assert_not_nil lead.user
    end
  end

  test "should calculate correct lead stats" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index
    stats = assigns(:lead_stats)
    
    assert_equal 3, stats[:total]
    assert_equal 1, stats[:pending]
    assert_equal 1, stats[:contacted]
    assert_equal 1, stats[:qualified]
    assert_equal 0, stats[:closed]
  end

  test "should scope leads to current account only" do
    # Create lead for different vendor
    other_vendor = Account.create!(
      name: "Other Vendor",
      account_type: 'vendor',
      status: 'approved',
      owner: @vendor_user
    )
    
    other_product = Product.create!(
      account: other_vendor,
      name: "Other Product",
      description: "Product from other vendor"
    )
    
    Lead.create!(
      product: other_product,
      account: other_vendor,
      user: @agency_user,
      status: :pending,
      message: "Lead for other vendor",
      contact_name: "Other Contact",
      contact_email: "<EMAIL>",
      contact_company: "Other Company"
    )

    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index
    leads = assigns(:leads)
    
    # Should only show leads for current vendor's products
    assert_equal 3, leads.count
    leads.each do |lead|
      assert_equal @vendor_account, lead.product.account
    end
  end

  # Filter Tests
  test "should filter leads by status" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index, params: { status: 'pending' }
    leads = assigns(:leads)
    
    assert_equal 1, leads.count
    assert_equal @lead1, leads.first
    assert_equal 'pending', leads.first.status
  end

  test "should filter leads by product" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index, params: { product_id: @product1.id }
    leads = assigns(:leads)
    
    assert_equal 2, leads.count
    leads.each do |lead|
      assert_equal @product1, lead.product
    end
  end

  test "should search leads by contact name" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index, params: { search: 'Jane' }
    leads = assigns(:leads)
    
    assert_equal 1, leads.count
    assert_equal @lead1, leads.first
  end

  test "should search leads by contact email" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index, params: { search: 'john@city' }
    leads = assigns(:leads)
    
    assert_equal 1, leads.count
    assert_equal @lead2, leads.first
  end

  test "should search leads by message" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index, params: { search: 'purchase' }
    leads = assigns(:leads)
    
    assert_equal 1, leads.count
    assert_equal @lead3, leads.first
  end

  test "should handle case insensitive search" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index, params: { search: 'JANE' }
    leads = assigns(:leads)
    
    assert_equal 1, leads.count
    assert_equal @lead1, leads.first
  end

  test "should combine multiple filters" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index, params: { 
      product_id: @product1.id,
      status: 'contacted'
    }
    leads = assigns(:leads)
    
    assert_equal 1, leads.count
    assert_equal @lead2, leads.first
  end

  # Show Tests
  test "should show lead" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :show, params: { id: @lead1.id }
    assert_response :success
    assert_equal @lead1, assigns(:lead)
  end

  test "should not show lead from different account" do
    other_vendor = Account.create!(
      name: "Other Vendor",
      account_type: 'vendor',
      status: 'approved',
      owner: @vendor_user
    )
    
    other_product = Product.create!(
      account: other_vendor,
      name: "Other Product",
      description: "Product from other vendor"
    )
    
    other_lead = Lead.create!(
      product: other_product,
      account: other_vendor,
      user: @agency_user,
      status: :pending,
      message: "Lead for other vendor",
      contact_name: "Other Contact",
      contact_email: "<EMAIL>",
      contact_company: "Other Company"
    )

    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    # Debug: Check that the leads are properly scoped
    assert_equal @vendor_account.id, session[:account_id]
    assert_equal other_vendor.id, other_lead.account_id
    assert_not_equal @vendor_account.id, other_lead.account_id

    assert_raises(ActiveRecord::RecordNotFound) do
      get :show, params: { id: other_lead.id }
    end
  end

  # Update Tests
  test "should update lead status via HTML" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    patch :update, params: { 
      id: @lead1.id,
      lead: { status: 'contacted', notes: 'Called customer' }
    }
    
    assert_redirected_to vendors_lead_path(@lead1)
    assert_equal 'Lead updated successfully.', flash[:notice]
    
    @lead1.reload
    assert_equal 'contacted', @lead1.status
    assert_equal 'Called customer', @lead1.notes
  end

  test "should update lead status via JSON" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    patch :update, params: { 
      id: @lead1.id,
      lead: { status: 'qualified' }
    }, format: :json
    
    assert_response :success
    
    response_json = JSON.parse(response.body)
    assert_equal 'success', response_json['status']
    assert_equal 'qualified', response_json['new_status']
    assert_equal 'Qualified', response_json['new_status_humanized']
    
    @lead1.reload
    assert_equal 'qualified', @lead1.status
  end

  test "should handle invalid lead update via HTML" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    assert_raises(ArgumentError) do
      patch :update, params: { 
        id: @lead1.id,
        lead: { status: 'invalid_status' }
      }
    end
  end

  test "should handle invalid lead update via JSON" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    assert_raises(ArgumentError) do
      patch :update, params: { 
        id: @lead1.id,
        lead: { status: 'invalid_status' }
      }, format: :json
    end
  end

  test "should not update lead from different account" do
    other_vendor = Account.create!(
      name: "Other Vendor",
      account_type: 'vendor',
      status: 'approved',
      owner: @vendor_user
    )
    
    other_product = Product.create!(
      account: other_vendor,
      name: "Other Product",
      description: "Product from other vendor"
    )
    
    other_lead = Lead.create!(
      product: other_product,
      account: other_vendor,
      user: @agency_user,
      status: :pending,
      message: "Lead for other vendor",
      contact_name: "Other Contact",
      contact_email: "<EMAIL>",
      contact_company: "Other Company"
    )

    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    assert_raises(ActiveRecord::RecordNotFound) do
      patch :update, params: { 
        id: other_lead.id,
        lead: { status: 'contacted' }
      }
    end
  end

  test "should only permit allowed parameters" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    patch :update, params: { 
      id: @lead1.id,
      lead: { 
        status: 'contacted',
        notes: 'Updated notes',
        contact_name: 'Hacked Name',  # Should not be permitted
        contact_email: '<EMAIL>'  # Should not be permitted
      }
    }
    
    @lead1.reload
    assert_equal 'contacted', @lead1.status
    assert_equal 'Updated notes', @lead1.notes
    assert_equal 'Jane Smith', @lead1.contact_name  # Should remain unchanged
    assert_equal '<EMAIL>', @lead1.contact_email  # Should remain unchanged
  end

  test "should handle empty filters gracefully" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index, params: { status: '', product_id: '', search: '' }
    assert_response :success
    
    leads = assigns(:leads)
    assert_equal 3, leads.count  # Should show all leads
  end

  private

  def sign_in(user)
    session_record = user.sessions.create!(
      user_agent: 'Test User Agent',
      ip_address: '127.0.0.1'
    )
    Current.session = session_record
    cookies.signed[:session_id] = session_record.id
  end
end