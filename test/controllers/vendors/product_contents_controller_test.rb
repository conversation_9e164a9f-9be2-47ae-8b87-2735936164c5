require "test_helper"

class Vendors::ProductContentsControllerTest < ActionController::TestCase
  setup do
    @vendor_user = users(:one)
    @vendor_account = accounts(:one)
    @agency_user = users(:two) 
    @agency_account = accounts(:two)

    # Configure vendor account properly
    @vendor_account.update!(
      account_type: 'vendor',
      status: 'approved',
      confirmed_at: Time.current,
      owner: @vendor_user
    )

    # Clear existing account users to avoid duplicates
    @vendor_account.account_users.destroy_all
    @vendor_account.account_users.create!(user: @vendor_user, role: 'admin')

    # Clear existing data to ensure clean test state
    @vendor_account.products.destroy_all

    # Create test product
    @product = Product.create!(
      account: @vendor_account,
      name: "Test Product",
      description: "A test product",
      published: true
    )

    # Create test product contents
    @content1 = ProductContent.new(
      product: @product,
      title: "First Content",
      description: "Description of first content",
      position: 1,
      published: true
    )
    @content1.file.attach(
      io: StringIO.new("test file content"),
      filename: "content1.pdf",
      content_type: "application/pdf"
    )
    @content1.save!
    
    @content2 = ProductContent.new(
      product: @product,
      title: "Second Content",
      description: "Description of second content",
      position: 2,
      published: false
    )
    @content2.file.attach(
      io: StringIO.new("test file content"),
      filename: "content2.pdf",
      content_type: "application/pdf"
    )
    @content2.save!
  end

  # Authentication Tests
  test "should require authentication" do
    get :index, params: { product_id: @product.id }
    assert_response :redirect
    assert_redirected_to new_session_path
  end

  test "should require vendor account" do
    sign_in @agency_user
    session[:account_id] = @agency_account.id

    get :index, params: { product_id: @product.id }
    assert_response :redirect
  end

  test "should require product ownership" do
    other_vendor = Account.create!(
      name: "Other Vendor",
      account_type: 'vendor',
      status: 'approved',
      owner: @vendor_user
    )
    
    other_product = Product.create!(
      account: other_vendor,
      name: "Other Product",
      description: "Product from other vendor"
    )

    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index, params: { product_id: other_product.id }
    assert_response :redirect
  end

  # Index Tests
  test "should get index with valid product" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index, params: { product_id: @product.id }
    assert_response :success
    assert_not_nil assigns(:product_contents)
    assert_not_nil assigns(:product)
    assert_equal @product, assigns(:product)
  end

  test "should display all product contents ordered by position" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index, params: { product_id: @product.id }
    contents = assigns(:product_contents)
    
    assert_equal 2, contents.count
    assert_equal @content1, contents.first
    assert_equal @content2, contents.last
  end

  test "should include attached files to avoid N+1 queries" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index, params: { product_id: @product.id }
    contents = assigns(:product_contents)
    
    # Test that file associations are loaded
    contents.each do |content|
      assert_not_nil content.file.attached?
    end
    
    assert contents.any?, "Should have contents"
  end

  # Show Tests
  test "should show product content" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :show, params: { product_id: @product.id, id: @content1.id }
    assert_response :success
    assert_equal @content1, assigns(:product_content)
    assert_equal @product, assigns(:product)
  end

  # New Tests
  test "should get new product content form" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :new, params: { product_id: @product.id }
    assert_response :success
    assert_not_nil assigns(:product_content)
    assert assigns(:product_content).new_record?
    assert_equal @product, assigns(:product_content).product
    assert_not_nil assigns(:product_content).position
  end

  test "should set next position for new product content" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :new, params: { product_id: @product.id }
    content = assigns(:product_content)
    
    # Should be positioned after existing contents
    assert_equal 3, content.position
  end

  # Create Tests
  test "should create product content with valid params" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    file = fixture_file_upload(Rails.root.join('test', 'fixtures', 'files', 'test.pdf'), 'application/pdf')

    content_params = {
      title: "New Content",
      description: "Description of new content",
      published: true,
      file: file
    }

    assert_difference('ProductContent.count', 1) do
      post :create, params: { 
        product_id: @product.id,
        product_content: content_params
      }
    end

    assert_redirected_to vendors_product_product_contents_path(@product)
    assert_equal 'Content was successfully added.', flash[:notice]
    
    content = ProductContent.last
    assert_equal "New Content", content.title
    assert_equal "Description of new content", content.description
    assert_equal true, content.published
    assert_equal @product, content.product
    assert_equal 3, content.position
    assert content.file.attached?
  end

  test "should not create product content with invalid params" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    content_params = {
      title: "", # Invalid: title can't be blank
      description: "Description"
    }

    assert_no_difference('ProductContent.count') do
      post :create, params: { 
        product_id: @product.id,
        product_content: content_params
      }
    end

    assert_response :unprocessable_entity
    assert_template :new
    assert_not_nil assigns(:product_content)
    assert assigns(:product_content).errors.any?
  end

  # Edit Tests
  test "should get edit product content form" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :edit, params: { product_id: @product.id, id: @content1.id }
    assert_response :success
    assert_equal @content1, assigns(:product_content)
    assert_equal @product, assigns(:product)
  end

  # Update Tests
  test "should update product content with valid params" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    patch :update, params: { 
      product_id: @product.id,
      id: @content1.id,
      product_content: { 
        title: "Updated Title",
        description: "Updated description",
        published: false
      }
    }
    
    assert_redirected_to vendors_product_product_contents_path(@product)
    assert_equal 'Content was successfully updated.', flash[:notice]
    
    @content1.reload
    assert_equal "Updated Title", @content1.title
    assert_equal "Updated description", @content1.description
    assert_equal false, @content1.published
  end

  test "should not update product content with invalid params" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    patch :update, params: { 
      product_id: @product.id,
      id: @content1.id,
      product_content: { title: "" } # Invalid: title can't be blank
    }
    
    assert_response :unprocessable_entity
    assert_template :edit
    assert_not_nil assigns(:product_content)
    assert assigns(:product_content).errors.any?
    
    @content1.reload
    assert_equal "First Content", @content1.title # Should remain unchanged
  end

  # Destroy Tests
  test "should destroy product content" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    assert_difference('ProductContent.count', -1) do
      delete :destroy, params: { product_id: @product.id, id: @content1.id }
    end

    assert_redirected_to vendors_product_path(@product)
    assert_equal 'Content was successfully deleted.', flash[:notice]
  end

  # Position Update Tests
  test "should update content position via JSON" do
    # Create third content for better position testing
    content3 = ProductContent.new(
      product: @product,
      title: "Third Content",
      description: "Description of third content",
      position: 3
    )
    content3.file.attach(
      io: StringIO.new("test file content"),
      filename: "content3.pdf",
      content_type: "application/pdf"
    )
    content3.save!

    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    # Move content3 (position 3) to position 1
    patch :update_position, params: { 
      product_id: @product.id,
      id: content3.id,
      position: 1
    }, format: :json
    
    assert_response :success
    
    response_json = JSON.parse(response.body)
    assert_equal 'success', response_json['status']
    
    # Verify positions were updated correctly
    @content1.reload
    @content2.reload
    content3.reload
    
    assert_equal 1, content3.position
    assert_equal 2, @content1.position
    assert_equal 3, @content2.position
  end

  test "should handle position update errors gracefully" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    # Test with invalid position (negative number)
    patch :update_position, params: { 
      product_id: @product.id,
      id: @content1.id,
      position: -1
    }, format: :json
    
    # Should still succeed but position will be corrected
    assert_response :success
    
    response_json = JSON.parse(response.body)
    assert_equal 'success', response_json['status']
  end

  # Security Tests
  test "should only permit allowed parameters" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    patch :update, params: { 
      product_id: @product.id,
      id: @content1.id,
      product_content: { 
        title: "Updated Title",
        description: "Updated description",
        position: 999, # Should not be permitted for update
        product_id: @product.id + 1 # Should not be permitted
      }
    }
    
    @content1.reload
    assert_equal "Updated Title", @content1.title
    assert_equal "Updated description", @content1.description
    assert_equal 1, @content1.position # Should remain unchanged
    assert_equal @product.id, @content1.product_id # Should remain unchanged
  end

  test "should scope contents to current product" do
    other_product = Product.create!(
      account: @vendor_account,
      name: "Other Product",
      description: "Another product from same vendor"
    )
    
    other_content = ProductContent.new(
      product: other_product,
      title: "Other Content",
      description: "Description of other content",
      position: 1
    )
    other_content.file.attach(
      io: StringIO.new("test file content"),
      filename: "other_content.pdf",
      content_type: "application/pdf"
    )
    other_content.save!

    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    get :index, params: { product_id: @product.id }
    contents = assigns(:product_contents)
    
    # Should only show contents for current product
    assert_equal 2, contents.count
    assert_includes contents, @content1
    assert_includes contents, @content2
    assert_not_includes contents, other_content
  end

  test "should handle missing product gracefully" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id

    # Should redirect when product doesn't exist or doesn't belong to account
    get :index, params: { product_id: 'nonexistent' }
    assert_response :redirect
  end

  private

  def sign_in(user)
    session_record = user.sessions.create!(
      user_agent: 'Test User Agent',
      ip_address: '127.0.0.1'
    )
    Current.session = session_record
    cookies.signed[:session_id] = session_record.id
  end
end