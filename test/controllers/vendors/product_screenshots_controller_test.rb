require "test_helper"

module Vendors
  class ProductScreenshotsControllerTest < ActionController::TestCase
    def setup
      # Create test users
      @vendor_user = users(:one)
      @other_vendor_user = users(:two)
      @admin_user = users(:admin)
      @admin_user.update!(super_admin: true)
      
      # Create test accounts
      @vendor_account = accounts(:one)
      @other_vendor_account = accounts(:two)
      
      @vendor_account.update!(
        account_type: 'vendor', 
        status: 'approved', 
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      @other_vendor_account.update!(
        account_type: 'vendor',
        status: 'approved',
        confirmed_at: Time.current,
        owner: @other_vendor_user
      )
      
      # Clear existing account users to avoid duplicates
      @vendor_account.account_users.destroy_all
      @vendor_account.account_users.create!(user: @vendor_user, role: 'admin')
      
      @other_vendor_account.account_users.destroy_all
      @other_vendor_account.account_users.create!(user: @other_vendor_user, role: 'admin')
      
      # Create test product
      @product = @vendor_account.products.create!(
        name: "Test Product",
        description: "A test product description",
        website: "https://test.com",
        published: false
      )
      
      @other_vendor_product = @other_vendor_account.products.create!(
        name: "Other Vendor Product",
        description: "Product from another vendor",
        website: "https://other.com",
        published: true
      )
      
      # Create test product screenshots
      @screenshot1 = @product.product_screenshots.build(
        title: "Dashboard Screenshot",
        description: "Main dashboard view",
        position: 1
      )
      @screenshot1.image.attach(
        io: StringIO.new("fake image data"),
        filename: "dashboard.png",
        content_type: "image/png"
      )
      @screenshot1.save!
      
      @screenshot2 = @product.product_screenshots.build(
        title: "Settings Screenshot",
        description: "Settings page view",
        position: 2
      )
      @screenshot2.image.attach(
        io: StringIO.new("fake image data 2"),
        filename: "settings.jpg",
        content_type: "image/jpeg"
      )
      @screenshot2.save!
    end

    # ===============================
    # AUTHENTICATION & AUTHORIZATION TESTS
    # ===============================

    test "should require vendor authentication for all actions" do
      get :index, params: { product_id: @product.slug }
      assert_redirected_to new_session_path
      
      get :show, params: { product_id: @product.slug, id: @screenshot1.id }
      assert_redirected_to new_session_path
      
      get :new, params: { product_id: @product.slug }
      assert_redirected_to new_session_path
      
      post :create, params: { product_id: @product.slug, product_screenshot: { title: "Test" } }
      assert_redirected_to new_session_path
    end

    test "should require vendor account type" do
      agency_user = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "Agency",
        last_name: "User"
      )
      
      agency_account = Account.create!(
        name: "Agency Account",
        account_type: "agency",
        status: "approved",
        confirmed_at: Time.current,
        owner: agency_user
      )
      AccountUser.create!(account: agency_account, user: agency_user, role: 'admin')
      
      sign_in agency_user
      
      get :index, params: { product_id: @product.slug }
      assert_redirected_to root_path
    end

    test "should allow vendor users" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
    end

    test "should scope products to current vendor account" do
      sign_in @vendor_user
      
      # Should not be able to access another vendor's product
      assert_raises(ActiveRecord::RecordNotFound) do
        get :index, params: { product_id: @other_vendor_product.slug }
      end
    end

    # ===============================
    # INDEX ACTION TESTS
    # ===============================

    test "should show product screenshots index" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      assert_not_nil assigns(:product_screenshots)
      
      screenshots = assigns(:product_screenshots)
      assert screenshots.include?(@screenshot1)
      assert screenshots.include?(@screenshot2)
    end

    test "should order screenshots by position" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      screenshots = assigns(:product_screenshots).to_a
      assert_equal @screenshot1, screenshots.first
      assert_equal @screenshot2, screenshots.second
    end

    test "should scope screenshots to current vendor account" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      screenshots = assigns(:product_screenshots)
      screenshots.each do |screenshot|
        assert_equal @vendor_account, screenshot.product.account
      end
    end

    # ===============================
    # SHOW ACTION TESTS
    # ===============================

    test "should show product screenshot details" do
      sign_in @vendor_user
      
      get :show, params: { product_id: @product.slug, id: @screenshot1.id }
      assert_response :success
      assert_equal @screenshot1, assigns(:product_screenshot)
      assert_equal @product, assigns(:product)
    end

    test "should not show other vendor's screenshot" do
      sign_in @vendor_user
      
      # Create screenshot for other vendor's product
      other_screenshot = @other_vendor_product.product_screenshots.build(
        title: "Other Screenshot",
        description: "Screenshot for other vendor",
        position: 1
      )
      other_screenshot.image.attach(
        io: StringIO.new("other image data"),
        filename: "other.png",
        content_type: "image/png"
      )
      other_screenshot.save!
      
      # Should not be able to access through this vendor's product context
      assert_raises(ActiveRecord::RecordNotFound) do
        get :show, params: { product_id: @product.slug, id: other_screenshot.id }
      end
    end

    # ===============================
    # NEW ACTION TESTS
    # ===============================

    test "should show new product screenshot form" do
      sign_in @vendor_user
      
      get :new, params: { product_id: @product.slug }
      assert_response :success
      assert_instance_of ProductScreenshot, assigns(:product_screenshot)
      assert assigns(:product_screenshot).new_record?
      assert_equal @product, assigns(:product_screenshot).product
    end

    # ===============================
    # CREATE ACTION TESTS
    # ===============================

    test "should create product screenshot with valid data" do
      sign_in @vendor_user
      
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      screenshot_params = {
        title: "New Screenshot",
        description: "A new screenshot description",
        image: file,
        position: 3
      }
      
      assert_difference '@product.product_screenshots.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_screenshot: screenshot_params
        }
      end
      
      assert_redirected_to vendors_product_product_screenshots_path(@product)
      assert_match /Screenshot was successfully created/, flash[:notice]
      
      screenshot = ProductScreenshot.last
      assert_equal "New Screenshot", screenshot.title
      assert_equal "A new screenshot description", screenshot.description
      assert_equal @product, screenshot.product
      assert_equal 3, screenshot.position
      assert screenshot.image.attached?
    end

    test "should auto-assign position when not provided" do
      sign_in @vendor_user
      
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      screenshot_params = {
        title: "Auto Position Screenshot",
        description: "Should get auto-assigned position",
        image: file
        # position not specified
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_screenshot: screenshot_params
      }
      
      screenshot = ProductScreenshot.last
      assert_equal 3, screenshot.position  # Should be after existing screenshots (1, 2)
    end

    test "should not create product screenshot with invalid data" do
      sign_in @vendor_user
      
      invalid_params = {
        title: "",  # Required field
        description: ""
        # missing image
      }
      
      assert_no_difference 'ProductScreenshot.count' do
        post :create, params: { 
          product_id: @product.slug,
          product_screenshot: invalid_params
        }
      end
      
      assert_response :unprocessable_entity
      assert_not_nil assigns(:product_screenshot)
      assert assigns(:product_screenshot).errors.any?
    end

    test "should create screenshot for own product only" do
      sign_in @vendor_user
      
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      screenshot_params = {
        title: "My Screenshot",
        description: "Screenshot for my product",
        image: file
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_screenshot: screenshot_params
      }
      
      screenshot = ProductScreenshot.last
      assert_equal @vendor_account, screenshot.product.account
    end

    # ===============================
    # EDIT ACTION TESTS
    # ===============================

    test "should show edit product screenshot form" do
      sign_in @vendor_user
      
      get :edit, params: { product_id: @product.slug, id: @screenshot1.id }
      assert_response :success
      assert_equal @screenshot1, assigns(:product_screenshot)
      assert_equal @product, assigns(:product)
    end

    test "should not edit other vendor's screenshot" do
      sign_in @other_vendor_user
      
      # Should not be able to access this vendor's screenshot for editing
      assert_raises(ActiveRecord::RecordNotFound) do
        get :edit, params: { product_id: @product.slug, id: @screenshot1.id }
      end
    end

    # ===============================
    # UPDATE ACTION TESTS
    # ===============================

    test "should update product screenshot with valid data" do
      sign_in @vendor_user
      
      update_params = {
        title: "Updated Screenshot Title",
        description: "Updated screenshot description",
        position: 5
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @screenshot1.id,
        product_screenshot: update_params
      }
      
      assert_redirected_to vendors_product_product_screenshots_path(@product)
      assert_match /Screenshot was successfully updated/, flash[:notice]
      
      @screenshot1.reload
      assert_equal "Updated Screenshot Title", @screenshot1.title
      assert_equal "Updated screenshot description", @screenshot1.description
      assert_equal 5, @screenshot1.position
    end

    test "should update product screenshot image" do
      sign_in @vendor_user
      
      new_file = fixture_file_upload('test/fixtures/files/test_image.jpg', 'image/jpeg')
      
      update_params = {
        title: @screenshot1.title,
        image: new_file
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @screenshot1.id,
        product_screenshot: update_params
      }
      
      assert_redirected_to vendors_product_product_screenshots_path(@product)
      
      @screenshot1.reload
      assert @screenshot1.image.attached?
      assert_equal "test_image.jpg", @screenshot1.image.filename.to_s
    end

    test "should not update product screenshot with invalid data" do
      sign_in @vendor_user
      
      original_title = @screenshot1.title
      
      invalid_params = {
        title: "",  # Invalid
        description: "Valid description"
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @screenshot1.id,
        product_screenshot: invalid_params
      }
      
      assert_response :unprocessable_entity
      assert_not_nil assigns(:product_screenshot)
      assert assigns(:product_screenshot).errors.any?
      
      @screenshot1.reload
      assert_equal original_title, @screenshot1.title
    end

    # ===============================
    # DESTROY ACTION TESTS
    # ===============================

    test "should delete product screenshot" do
      sign_in @vendor_user
      
      assert_difference '@product.product_screenshots.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: @screenshot1.id }
      end
      
      assert_redirected_to vendors_product_product_screenshots_path(@product)
      assert_match /Screenshot was successfully deleted/, flash[:notice]
    end

    test "should not delete other vendor's screenshot" do
      sign_in @other_vendor_user
      
      # Should not be able to delete this vendor's screenshot
      assert_raises(ActiveRecord::RecordNotFound) do
        delete :destroy, params: { product_id: @product.slug, id: @screenshot1.id }
      end
    end

    # ===============================
    # UPDATE POSITION ACTION TESTS
    # ===============================

    test "should update screenshot position" do
      sign_in @vendor_user
      
      patch :update_position, params: { 
        product_id: @product.slug,
        id: @screenshot1.id,
        position: 5
      }
      
      assert_response :ok
      
      @screenshot1.reload
      assert_equal 5, @screenshot1.position
    end

    test "should handle AJAX position updates" do
      sign_in @vendor_user
      
      patch :update_position, params: { 
        product_id: @product.slug,
        id: @screenshot2.id,
        position: 1
      }, xhr: true
      
      assert_response :ok
      
      @screenshot2.reload
      assert_equal 1, @screenshot2.position
    end

    # ===============================
    # PARAMETER SECURITY TESTS
    # ===============================

    test "should filter unpermitted parameters" do
      sign_in @vendor_user
      
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      malicious_params = {
        title: "Valid Title",
        description: "Valid description",
        image: file,
        position: 1,
        product_id: @other_vendor_product.id,  # Should be filtered/ignored
        created_at: 1.year.ago,  # Should be filtered
        published: true  # Not in permit list
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_screenshot: malicious_params
      }
      
      screenshot = ProductScreenshot.last
      assert_equal "Valid Title", screenshot.title
      assert_equal @product, screenshot.product  # Should use product from URL, not params
      assert_not_equal 1.year.ago.to_date, screenshot.created_at.to_date
      # published should remain default (false) since it's not permitted
      assert_not screenshot.published?
    end

    test "should allow only permitted parameters" do
      sign_in @vendor_user
      
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      permitted_params = {
        title: "Permitted Screenshot",
        description: "Permitted description",
        position: 1,
        image: file
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_screenshot: permitted_params
      }
      
      screenshot = ProductScreenshot.last
      assert_equal "Permitted Screenshot", screenshot.title
      assert_equal "Permitted description", screenshot.description
      assert_equal 1, screenshot.position
      assert screenshot.image.attached?
    end

    # ===============================
    # VENDOR ISOLATION TESTS
    # ===============================

    test "should maintain vendor account isolation" do
      sign_in @vendor_user
      
      # Should only see own products and screenshots
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      screenshots = assigns(:product_screenshots)
      screenshots.each do |screenshot|
        assert_equal @vendor_account, screenshot.product.account
      end
      
      # Should not be able to access other vendor's product
      assert_raises(ActiveRecord::RecordNotFound) do
        get :index, params: { product_id: @other_vendor_product.slug }
      end
    end

    test "should not access other vendor screenshots through different actions" do
      sign_in @other_vendor_user
      
      # Should not be able to access this vendor's screenshots through any action
      assert_raises(ActiveRecord::RecordNotFound) do
        get :show, params: { product_id: @product.slug, id: @screenshot1.id }
      end
      
      assert_raises(ActiveRecord::RecordNotFound) do
        get :edit, params: { product_id: @product.slug, id: @screenshot1.id }
      end
      
      assert_raises(ActiveRecord::RecordNotFound) do
        patch :update, params: { 
          product_id: @product.slug, 
          id: @screenshot1.id,
          product_screenshot: { title: "Hacked" }
        }
      end
      
      assert_raises(ActiveRecord::RecordNotFound) do
        delete :destroy, params: { product_id: @product.slug, id: @screenshot1.id }
      end
    end

    # ===============================
    # INTEGRATION TESTS
    # ===============================

    test "should complete full screenshot management workflow" do
      sign_in @vendor_user
      
      # Step 1: View screenshots index
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      # Step 2: Visit new screenshot page
      get :new, params: { product_id: @product.slug }
      assert_response :success
      
      # Step 3: Create screenshot
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      screenshot_params = {
        title: "Workflow Screenshot",
        description: "Screenshot created through workflow",
        image: file,
        position: 3
      }
      
      assert_difference '@product.product_screenshots.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_screenshot: screenshot_params
        }
      end
      
      screenshot = ProductScreenshot.last
      assert_redirected_to vendors_product_product_screenshots_path(@product)
      
      # Step 4: View created screenshot
      get :show, params: { product_id: @product.slug, id: screenshot.id }
      assert_response :success
      assert_equal screenshot, assigns(:product_screenshot)
      
      # Step 5: Edit screenshot
      get :edit, params: { product_id: @product.slug, id: screenshot.id }
      assert_response :success
      
      # Step 6: Update screenshot
      patch :update, params: { 
        product_id: @product.slug,
        id: screenshot.id,
        product_screenshot: { title: "Updated Workflow Screenshot" }
      }
      
      assert_redirected_to vendors_product_product_screenshots_path(@product)
      screenshot.reload
      assert_equal "Updated Workflow Screenshot", screenshot.title
      
      # Step 7: Update position
      patch :update_position, params: { 
        product_id: @product.slug,
        id: screenshot.id,
        position: 1
      }
      
      assert_response :ok
      screenshot.reload
      assert_equal 1, screenshot.position
      
      # Step 8: Delete screenshot
      assert_difference '@product.product_screenshots.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: screenshot.id }
      end
      
      assert_redirected_to vendors_product_product_screenshots_path(@product)
    end

    test "should handle screenshot management with validation errors" do
      sign_in @vendor_user
      
      # Step 1: Try to create invalid screenshot
      invalid_params = {
        title: "",  # Invalid
        description: "Valid description"
        # missing image
      }
      
      assert_no_difference 'ProductScreenshot.count' do
        post :create, params: { 
          product_id: @product.slug,
          product_screenshot: invalid_params
        }
      end
      
      assert_response :unprocessable_entity
      assert assigns(:product_screenshot).errors.any?
      
      # Step 2: Fix errors and create successfully
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      valid_params = {
        title: "Fixed Screenshot Title",
        description: "Valid description",
        image: file
      }
      
      assert_difference 'ProductScreenshot.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_screenshot: valid_params
        }
      end
      
      assert_redirected_to vendors_product_product_screenshots_path(@product)
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
    end
  end
end