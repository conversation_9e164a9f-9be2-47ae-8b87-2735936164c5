require "test_helper"

module Vendors
  class ProductFeaturesControllerTest < ActionController::TestCase
    def setup
      # Create test users
      @vendor_user = users(:one)
      @other_vendor_user = users(:two)
      
      # Create test accounts
      @vendor_account = accounts(:one)
      @other_vendor_account = accounts(:two)
      
      @vendor_account.update!(
        account_type: 'vendor', 
        status: 'approved', 
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      @other_vendor_account.update!(
        account_type: 'vendor',
        status: 'approved',
        confirmed_at: Time.current,
        owner: @other_vendor_user
      )
      
      # Clear existing account users to avoid duplicates
      @vendor_account.account_users.destroy_all
      @vendor_account.account_users.create!(user: @vendor_user, role: 'admin')
      
      @other_vendor_account.account_users.destroy_all
      @other_vendor_account.account_users.create!(user: @other_vendor_user, role: 'admin')
      
      # Create test product
      @product = @vendor_account.products.create!(
        name: "Test Product",
        description: "A test product description",
        website: "https://test.com",
        published: false
      )
      
      @other_vendor_product = @other_vendor_account.products.create!(
        name: "Other Vendor Product",
        description: "Product from another vendor",
        website: "https://other.com",
        published: true
      )
      
      # Create test product features
      @feature1 = @product.product_features.create!(
        name: "Advanced Analytics",
        description: "Comprehensive analytics dashboard",
        position: 1,
        published: true
      )
      
      @feature2 = @product.product_features.create!(
        name: "Real-time Collaboration",
        description: "Work together in real-time",
        position: 2,
        published: false
      )
    end

    # ===============================
    # AUTHENTICATION & AUTHORIZATION TESTS
    # ===============================

    test "should require vendor authentication for all actions" do
      get :index, params: { product_id: @product.slug }
      assert_redirected_to new_session_path
      
      post :create, params: { product_id: @product.slug, product_feature: { name: "Test" } }
      assert_redirected_to new_session_path
    end

    test "should require vendor account type" do
      agency_user = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "Agency",
        last_name: "User"
      )
      
      agency_account = Account.create!(
        name: "Agency Account",
        account_type: "agency",
        status: "approved",
        confirmed_at: Time.current,
        owner: agency_user
      )
      AccountUser.create!(account: agency_account, user: agency_user, role: 'admin')
      
      sign_in agency_user
      get :index, params: { product_id: @product.slug }
      assert_redirected_to root_path
    end

    test "should allow vendor users" do
      sign_in @vendor_user
      get :index, params: { product_id: @product.slug }
      assert_response :success
    end

    test "should scope products to current vendor account" do
      sign_in @vendor_user
      
      # Should not be able to access another vendor's product
      assert_raises(ActiveRecord::RecordNotFound) do
        get :index, params: { product_id: @other_vendor_product.slug }
      end
    end

    # ===============================
    # INDEX ACTION TESTS
    # ===============================

    test "should show product features index" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      assert_not_nil assigns(:product_features)
      
      features = assigns(:product_features)
      assert features.include?(@feature1)
      assert features.include?(@feature2)
    end

    test "should order features by position" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      features = assigns(:product_features).to_a
      assert_equal @feature1, features.first
      assert_equal @feature2, features.second
    end

    # ===============================
    # CREATE ACTION TESTS
    # ===============================

    test "should create product feature with valid data" do
      sign_in @vendor_user
      
      feature_params = {
        name: "New Feature",
        description: "A new feature description",
        published: true
      }
      
      assert_difference '@product.product_features.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_feature: feature_params
        }
      end
      
      assert_redirected_to vendors_product_product_features_path(@product)
      assert_match /Product feature was successfully created/, flash[:notice]
      
      feature = ProductFeature.last
      assert_equal "New Feature", feature.name
      assert_equal @product, feature.product
      assert_equal @vendor_account, feature.product.account
    end

    test "should not create product feature with invalid data" do
      sign_in @vendor_user
      
      invalid_params = {
        name: "",  # Required field
        description: "Valid description"
      }
      
      assert_no_difference 'ProductFeature.count' do
        post :create, params: { 
          product_id: @product.slug,
          product_feature: invalid_params
        }
      end
      
      assert_response :unprocessable_entity
      assert assigns(:product_feature).errors.any?
    end

    # ===============================
    # UPDATE ACTION TESTS
    # ===============================

    test "should update product feature with valid data" do
      sign_in @vendor_user
      
      update_params = {
        name: "Updated Feature Name",
        description: "Updated feature description",
        published: false
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @feature1.id,
        product_feature: update_params
      }
      
      assert_redirected_to vendors_product_product_features_path(@product)
      assert_match /Product feature was successfully updated/, flash[:notice]
      
      @feature1.reload
      assert_equal "Updated Feature Name", @feature1.name
      assert_not @feature1.published?
    end

    # ===============================
    # DESTROY ACTION TESTS
    # ===============================

    test "should delete product feature" do
      sign_in @vendor_user
      
      assert_difference '@product.product_features.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: @feature1.id }
      end
      
      assert_redirected_to vendors_product_product_features_path(@product)
      assert_match /Product feature was successfully deleted/, flash[:notice]
    end

    test "should not delete other vendor's feature" do
      sign_in @other_vendor_user
      
      # Should not be able to delete this vendor's feature
      assert_raises(ActiveRecord::RecordNotFound) do
        delete :destroy, params: { product_id: @product.slug, id: @feature1.id }
      end
    end

    # ===============================
    # VENDOR ISOLATION TESTS
    # ===============================

    test "should maintain vendor account isolation" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      features = assigns(:product_features)
      features.each do |feature|
        assert_equal @vendor_account, feature.product.account
      end
    end

    # ===============================
    # INTEGRATION TESTS
    # ===============================

    test "should complete vendor feature workflow" do
      sign_in @vendor_user
      
      # Create feature
      feature_params = {
        name: "Vendor Feature",
        description: "Feature created by vendor",
        published: false
      }
      
      assert_difference '@product.product_features.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_feature: feature_params
        }
      end
      
      feature = ProductFeature.last
      
      # Update feature
      patch :update, params: { 
        product_id: @product.slug,
        id: feature.id,
        product_feature: { published: true }
      }
      
      feature.reload
      assert feature.published?
      
      # Delete feature
      assert_difference '@product.product_features.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: feature.id }
      end
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
    end
  end
end