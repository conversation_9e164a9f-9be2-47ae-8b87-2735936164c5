require "test_helper"

module Vendors
  class ProductCustomersControllerTest < ActionController::TestCase
    def setup
      # Create test users
      @vendor_user = users(:one)
      @other_vendor_user = users(:two)
      
      # Create test accounts
      @vendor_account = accounts(:one)
      @other_vendor_account = accounts(:two)
      
      @vendor_account.update!(
        account_type: 'vendor', 
        status: 'approved', 
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      @other_vendor_account.update!(
        account_type: 'vendor',
        status: 'approved',
        confirmed_at: Time.current,
        owner: @other_vendor_user
      )
      
      # Clear existing account users to avoid duplicates
      @vendor_account.account_users.destroy_all
      @vendor_account.account_users.create!(user: @vendor_user, role: 'admin')
      
      @other_vendor_account.account_users.destroy_all
      @other_vendor_account.account_users.create!(user: @other_vendor_user, role: 'admin')
      
      # Create test product
      @product = @vendor_account.products.create!(
        name: "Test Product",
        description: "A test product description",
        website: "https://test.com",
        published: false
      )
      
      @other_vendor_product = @other_vendor_account.products.create!(
        name: "Other Vendor Product",
        description: "Product from another vendor",
        website: "https://other.com",
        published: true
      )
      
      # Create test product customers
      @customer1 = @product.product_customers.build(
        name: "Acme Corporation",
        description: "Fortune 500 company using our solution",
        position: 1
      )
      @customer1.logo.attach(
        io: StringIO.new("fake logo data"),
        filename: "acme_logo.png",
        content_type: "image/png"
      )
      @customer1.save!
      
      @customer2 = @product.product_customers.create!(
        name: "TechStart Inc",
        description: "Growing startup leveraging our platform",
        position: 2
      )
    end

    # ===============================
    # AUTHENTICATION & AUTHORIZATION TESTS
    # ===============================

    test "should require vendor authentication for all actions" do
      get :index, params: { product_id: @product.slug }
      assert_redirected_to new_session_path
      
      post :create, params: { product_id: @product.slug, product_customer: { name: "Test" } }
      assert_redirected_to new_session_path
    end

    test "should require vendor account type" do
      agency_user = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "Agency",
        last_name: "User"
      )
      
      agency_account = Account.create!(
        name: "Agency Account",
        account_type: "agency",
        status: "approved",
        confirmed_at: Time.current,
        owner: agency_user
      )
      AccountUser.create!(account: agency_account, user: agency_user, role: 'admin')
      
      sign_in agency_user
      get :index, params: { product_id: @product.slug }
      assert_redirected_to root_path
    end

    test "should allow vendor users" do
      sign_in @vendor_user
      get :index, params: { product_id: @product.slug }
      assert_response :success
    end

    test "should scope products to current vendor account" do
      sign_in @vendor_user
      
      # Should not be able to access another vendor's product
      get :index, params: { product_id: @other_vendor_product.slug }
      assert_redirected_to errors_not_found_path
    end

    # ===============================
    # INDEX ACTION TESTS
    # ===============================

    test "should show product customers index" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      assert_not_nil assigns(:product_customers)
      
      customers = assigns(:product_customers)
      assert customers.include?(@customer1)
      assert customers.include?(@customer2)
    end

    test "should order customers by position" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      customers = assigns(:product_customers).to_a
      assert_equal @customer1, customers.first
      assert_equal @customer2, customers.second
    end

    test "should only show customers for vendor's product" do
      sign_in @vendor_user
      
      # Create customer for other vendor's product
      other_customer = @other_vendor_product.product_customers.create!(
        name: "Other Vendor Customer",
        description: "Customer for other vendor's product",
        position: 1
      )
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      customers = assigns(:product_customers)
      assert customers.include?(@customer1)
      assert customers.include?(@customer2)
      assert_not customers.include?(other_customer)
    end

    # ===============================
    # CREATE ACTION TESTS
    # ===============================

    test "should create product customer with valid data" do
      sign_in @vendor_user
      
      customer_params = {
        name: "New Customer Corp",
        description: "A new customer description",
        position: 3
      }
      
      assert_difference '@product.product_customers.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_customer: customer_params
        }
      end
      
      assert_redirected_to vendors_product_product_customers_path(@product)
      assert_match /Product customer was successfully created/, flash[:notice]
      
      customer = ProductCustomer.last
      assert_equal "New Customer Corp", customer.name
      assert_equal "A new customer description", customer.description
      assert_equal @product, customer.product
      assert_equal @vendor_account, customer.product.account
      assert_equal 3, customer.position
    end

    test "should create customer with logo attachment" do
      sign_in @vendor_user
      
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      customer_params = {
        name: "Customer with Logo",
        description: "Customer with attached logo",
        logo: file
      }
      
      assert_difference '@product.product_customers.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_customer: customer_params
        }
      end
      
      customer = ProductCustomer.last
      assert customer.logo.attached?
      assert_equal "test_image.png", customer.logo.filename.to_s
      assert customer.image?
      assert_equal @vendor_account, customer.product.account
    end

    test "should auto-assign position when not provided" do
      sign_in @vendor_user
      
      customer_params = {
        name: "Auto Position Customer",
        description: "Should get auto-assigned position"
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_customer: customer_params
      }
      
      customer = ProductCustomer.last
      # Position should be auto-assigned (either 3 or the next available position)
      assert customer.position > 0, "Position should be auto-assigned to a positive value"
    end

    test "should not create product customer with invalid data" do
      sign_in @vendor_user
      
      invalid_params = {
        name: "",  # Required field
        description: "Valid description"
      }
      
      assert_no_difference 'ProductCustomer.count' do
        post :create, params: { 
          product_id: @product.slug,
          product_customer: invalid_params
        }
      end
      
      assert_response :success  # Renders :new template
      assert assigns(:product_customer).errors.any?
    end

    test "should not create customer for other vendor's product" do
      sign_in @vendor_user
      
      customer_params = {
        name: "Unauthorized Customer",
        description: "Trying to create for another vendor's product"
      }
      
      # Should not be able to create customer for other vendor's product
      post :create, params: { 
        product_id: @other_vendor_product.slug,
        product_customer: customer_params
      }
      assert_redirected_to errors_not_found_path
    end

    # ===============================
    # UPDATE ACTION TESTS
    # ===============================

    test "should update product customer with valid data" do
      sign_in @vendor_user
      
      update_params = {
        name: "Updated Customer Name",
        description: "Updated customer description",
        position: 5
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @customer1.id,
        product_customer: update_params
      }
      
      assert_redirected_to vendors_product_product_customers_path(@product)
      assert_match /Product customer was successfully updated/, flash[:notice]
      
      @customer1.reload
      assert_equal "Updated Customer Name", @customer1.name
      assert_equal "Updated customer description", @customer1.description
      assert_equal 5, @customer1.position
    end

    test "should not update other vendor's customer" do
      sign_in @other_vendor_user
      
      update_params = {
        name: "Unauthorized Update",
        description: "Should not be allowed"
      }
      
      # Should not be able to access this vendor's customer
      patch :update, params: { 
        product_id: @product.slug,
        id: @customer1.id,
        product_customer: update_params
      }
      assert_redirected_to errors_not_found_path
    end

    # ===============================
    # DESTROY ACTION TESTS
    # ===============================

    test "should delete product customer" do
      sign_in @vendor_user
      
      assert_difference '@product.product_customers.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: @customer1.id }
      end
      
      assert_redirected_to vendors_product_product_customers_path(@product)
      assert_match /Product customer was successfully deleted/, flash[:notice]
    end

    test "should not delete other vendor's customer" do
      sign_in @other_vendor_user
      
      # Should not be able to delete this vendor's customer
      delete :destroy, params: { product_id: @product.slug, id: @customer1.id }
      assert_redirected_to errors_not_found_path
    end

    test "should delete attached logo when destroying customer" do
      sign_in @vendor_user
      
      customer_with_logo = @product.product_customers.build(
        name: "Customer to Delete",
        description: "This customer will be deleted",
        position: 3
      )
      customer_with_logo.logo.attach(
        io: StringIO.new("logo to delete"),
        filename: "delete_me.png",
        content_type: "image/png"
      )
      customer_with_logo.save!
      
      assert customer_with_logo.logo.attached?
      
      delete :destroy, params: { product_id: @product.slug, id: customer_with_logo.id }
      
      assert_redirected_to vendors_product_product_customers_path(@product)
    end

    # ===============================
    # UPDATE POSITION ACTION TESTS
    # ===============================

    test "should update customer position" do
      sign_in @vendor_user
      
      patch :update_position, params: { 
        product_id: @product.slug,
        id: @customer1.id,
        position: 5
      }
      
      assert_response :ok
      
      @customer1.reload
      assert_equal 5, @customer1.position
    end

    test "should handle AJAX position updates" do
      sign_in @vendor_user
      
      patch :update_position, params: { 
        product_id: @product.slug,
        id: @customer2.id,
        position: 1
      }, xhr: true
      
      assert_response :ok
      
      @customer2.reload
      assert_equal 1, @customer2.position
    end

    # ===============================
    # VENDOR ISOLATION TESTS
    # ===============================

    test "should maintain vendor account isolation" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      customers = assigns(:product_customers)
      customers.each do |customer|
        assert_equal @vendor_account, customer.product.account
      end
    end

    test "should enforce vendor isolation for position updates" do
      sign_in @other_vendor_user
      
      # Should not be able to update position for this vendor's customer
      patch :update_position, params: { 
        product_id: @product.slug,
        id: @customer1.id,
        position: 10
      }
      assert_redirected_to errors_not_found_path
    end

    # ===============================
    # PARAMETER SECURITY TESTS
    # ===============================

    test "should filter unpermitted parameters" do
      sign_in @vendor_user
      
      malicious_params = {
        name: "Valid Name",
        description: "Valid description",
        position: 1,
        product_id: @product.id + 1,  # Should be filtered/ignored
        created_at: 1.year.ago,  # Should be filtered
        published: false  # Not in permit list
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_customer: malicious_params
      }
      
      customer = ProductCustomer.last
      assert_equal "Valid Name", customer.name
      assert_equal @product, customer.product  # Should use product from URL, not params
      assert_not_equal 1.year.ago.to_date, customer.created_at.to_date
      # published should remain default (true) since it's not permitted
      assert customer.published?
    end

    # ===============================
    # ERROR HANDLING TESTS
    # ===============================

    test "should handle invalid product id" do
      sign_in @vendor_user
      
      get :index, params: { product_id: 'invalid-slug' }
      assert_redirected_to errors_not_found_path
    end

    test "should handle invalid customer id" do
      sign_in @vendor_user
      
      patch :update, params: { 
        product_id: @product.slug, 
        id: 99999,
        product_customer: { name: "Test" }
      }
      assert_redirected_to errors_not_found_path
    end

    # ===============================
    # INTEGRATION TESTS
    # ===============================

    test "should complete vendor customer workflow" do
      sign_in @vendor_user
      
      # Step 1: View customers index
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      # Step 2: Create customer
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      customer_params = {
        name: "Workflow Customer",
        description: "Customer created through workflow",
        logo: file,
        position: 3
      }
      
      assert_difference '@product.product_customers.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_customer: customer_params
        }
      end
      
      customer = ProductCustomer.last
      assert_redirected_to vendors_product_product_customers_path(@product)
      assert_equal @vendor_account, customer.product.account
      
      # Step 3: Update customer
      patch :update, params: { 
        product_id: @product.slug,
        id: customer.id,
        product_customer: { name: "Updated Workflow Customer" }
      }
      
      assert_redirected_to vendors_product_product_customers_path(@product)
      customer.reload
      assert_equal "Updated Workflow Customer", customer.name
      
      # Step 4: Update position
      patch :update_position, params: { 
        product_id: @product.slug,
        id: customer.id,
        position: 1
      }
      
      assert_response :ok
      customer.reload
      assert_equal 1, customer.position
      
      # Step 5: Delete customer
      assert_difference '@product.product_customers.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: customer.id }
      end
      
      assert_redirected_to vendors_product_product_customers_path(@product)
    end

    test "should handle customer management with validation errors" do
      sign_in @vendor_user
      
      # Step 1: Try to create invalid customer
      invalid_params = {
        name: "",  # Invalid
        description: "Valid description"
      }
      
      assert_no_difference 'ProductCustomer.count' do
        post :create, params: { 
          product_id: @product.slug,
          product_customer: invalid_params
        }
      end
      
      assert_response :success  # Renders :new template
      assert assigns(:product_customer).errors.any?
      
      # Step 2: Fix errors and create successfully
      valid_params = {
        name: "Fixed Customer Name",
        description: "Valid description"
      }
      
      assert_difference 'ProductCustomer.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_customer: valid_params
        }
      end
      
      customer = ProductCustomer.last
      assert_equal @vendor_account, customer.product.account
      assert_redirected_to vendors_product_product_customers_path(@product)
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
      
      # Set account in session for vendor controllers
      if user == @vendor_user
        session[:account_id] = @vendor_account.id
      elsif user == @other_vendor_user
        session[:account_id] = @other_vendor_account.id
      end
    end
  end
end