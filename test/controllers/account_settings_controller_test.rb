require "test_helper"

class AccountSettingsControllerTest < ActionController::TestCase
  def setup
    # Create test users and accounts
    @owner_user = users(:one)
    @member_user = users(:two)
    @admin_user = users(:admin)
    @external_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "External",
      last_name: "User"
    )
    
    @account = accounts(:one)
    @account.update!(
      account_type: 'vendor', 
      status: 'approved', 
      confirmed_at: Time.current,
      owner: @owner_user
    )
    
    # Clear existing account users to avoid duplicates
    @account.account_users.destroy_all
    
    # Set up account users
    @account.account_users.create!(user: @owner_user, role: 'admin')
    @member_account_user = @account.account_users.create!(user: @member_user, role: 'member')
    
    @admin_user.update!(super_admin: true)
    
    # Create pending account for testing approval requirements
    @pending_account = Account.create!(
      name: "Pending Account",
      account_type: "vendor", 
      status: "pending",
      confirmed_at: Time.current,
      owner: @external_user
    )
    @pending_account.account_users.create!(user: @external_user, role: 'admin')
  end

  # ===============================
  # AUTHENTICATION TESTS
  # ===============================

  test "should require authentication for all actions" do
    get :show
    assert_redirected_to new_session_path
    
    get :edit
    assert_redirected_to new_session_path
    
    patch :update, params: { account: { name: "Test" } }
    assert_redirected_to new_session_path
  end

  test "should use dashboard layout" do
    sign_in @owner_user
    get :show
    assert_response :success
  end

  # ===============================
  # AUTHORIZATION TESTS
  # ===============================

  test "should require account admin access for all actions" do
    sign_in @member_user
    # Force session to try to access the owner's account (not their own)
    session[:account_id] = @account.id
    
    get :show
    # Member users should be redirected or denied access to admin-only functions
    assert_response :redirect
    
    get :edit
    assert_response :redirect
    
    patch :update, params: { account: { name: "Test" } }
    assert_response :redirect
  end

  test "should allow account owner access" do
    sign_in @owner_user
    
    get :show
    assert_response :success
    
    get :edit
    assert_response :success
  end

  test "should allow super admin access" do
    sign_in @admin_user
    
    # Create account for admin to avoid orphaned user redirect
    admin_account = Account.create!(
      name: "Admin Test Account",
      account_type: "vendor",
      status: "approved",
      confirmed_at: Time.current,
      owner: @admin_user
    )
    AccountUser.create!(account: admin_account, user: @admin_user, role: 'admin')
    
    get :show
    assert_response :success
  end

  test "should handle external user access" do
    sign_in @external_user
    
    get :show
    # External user will access their own account (pending account)
    assert_response :success
  end

  # ===============================
  # SHOW ACTION TESTS
  # ===============================

  test "should show account settings page" do
    sign_in @owner_user
    
    get :show
    assert_response :success
    assert_equal @account, assigns(:account)
  end

  test "should load current account for show" do
    sign_in @owner_user
    
    get :show
    assert_response :success
    assert_not_nil assigns(:account)
    assert_equal @account, assigns(:account)
  end

  # ===============================
  # EDIT ACTION TESTS
  # ===============================

  test "should show edit account settings page" do
    sign_in @owner_user
    
    get :edit
    assert_response :success
    assert_equal @account, assigns(:account)
  end

  test "should load account data for editing" do
    sign_in @owner_user
    
    get :edit
    assert_response :success
    assert_not_nil assigns(:account)
    assert_equal @account.name, assigns(:account).name
    assert_equal @account.headquarters_location, assigns(:account).headquarters_location
  end

  # ===============================
  # UPDATE ACTION TESTS
  # ===============================

  test "should update account information" do
    sign_in @owner_user
    
    new_name = "Updated Company Name"
    new_location = "San Francisco, CA"
    new_description = "Updated company description"
    new_linkedin = "https://linkedin.com/company/updated"
    
    patch :update, params: {
      account: {
        name: new_name,
        headquarters_location: new_location,
        description: new_description,
        linkedin_url: new_linkedin
      }
    }
    
    assert_redirected_to account_settings_path
    assert_match /Account information updated successfully/, flash[:notice]
    
    @account.reload
    assert_equal new_name, @account.name
    assert_equal new_location, @account.headquarters_location
    assert_equal new_description, @account.description.to_plain_text
    assert_equal new_linkedin, @account.linkedin_url
  end

  test "should not update account with invalid data" do
    sign_in @owner_user
    
    original_name = @account.name
    
    patch :update, params: {
      account: {
        name: "",  # Invalid - required field
        headquarters_location: "New Location"
      }
    }
    
    assert_response :unprocessable_entity
    assert_not_nil assigns(:account)
    
    @account.reload
    assert_equal original_name, @account.name  # Should not have changed
  end

  test "should handle update with blank optional fields" do
    sign_in @owner_user
    
    patch :update, params: {
      account: {
        name: "Valid Name",
        headquarters_location: "",
        description: "",
        linkedin_url: ""
      }
    }
    
    assert_redirected_to account_settings_path
    assert_match /Account information updated successfully/, flash[:notice]
    
    @account.reload
    assert_equal "Valid Name", @account.name
  end

  # ===============================
  # CURRENT ACCOUNT TESTS
  # ===============================

  test "should use session account_id when available" do
    sign_in @owner_user
    session[:account_id] = @account.id
    
    get :show
    assert_equal @account, assigns(:account)
  end

  test "should fall back to first account when no session" do
    sign_in @owner_user
    session.delete(:account_id)
    
    get :show
    assert_equal @owner_user.accounts.first, assigns(:account)
  end

  test "should handle user with invalid account_id in session" do
    sign_in @owner_user
    session[:account_id] = 99999  # Non-existent account
    
    get :show
    # Should fall back to first account
    assert_equal @owner_user.accounts.first, assigns(:account)
  end

  # ===============================
  # PARAMETER SECURITY TESTS
  # ===============================

  test "should filter unpermitted parameters" do
    sign_in @owner_user
    
    original_status = @account.status
    original_account_type = @account.account_type
    original_owner_id = @account.owner_id
    
    patch :update, params: {
      account: {
        name: "Updated Name",
        status: "rejected",  # Should be filtered
        account_type: "agency",  # Should be filtered
        owner_id: @external_user.id,  # Should be filtered
        confirmed_at: 1.year.ago  # Should be filtered
      }
    }
    
    @account.reload
    assert_equal "Updated Name", @account.name
    assert_equal original_status, @account.status  # Should not change
    assert_equal original_account_type, @account.account_type  # Should not change
    assert_equal original_owner_id, @account.owner_id  # Should not change
  end

  test "should allow only permitted parameters" do
    sign_in @owner_user
    
    patch :update, params: {
      account: {
        name: "New Name",
        headquarters_location: "New Location",
        linkedin_url: "https://linkedin.com/company/new",
        description: "New description"
      }
    }
    
    assert_redirected_to account_settings_path
    
    @account.reload
    assert_equal "New Name", @account.name
    assert_equal "New Location", @account.headquarters_location
    assert_equal "https://linkedin.com/company/new", @account.linkedin_url
    assert_equal "New description", @account.description.to_plain_text
  end

  # ===============================
  # ERROR HANDLING TESTS
  # ===============================

  test "should handle missing current account gracefully" do
    user_without_accounts = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "No",
      last_name: "Accounts"
    )
    sign_in user_without_accounts
    
    get :show
    # Should handle gracefully (may redirect or show error)
    assert_response :redirect
  end

  test "should validate account name presence" do
    sign_in @owner_user
    
    patch :update, params: {
      account: {
        name: nil,
        headquarters_location: "Some Location"
      }
    }
    
    assert_response :unprocessable_entity
    assert_not_nil assigns(:account)
    assert assigns(:account).errors.any?
  end

  test "should handle very long field values" do
    sign_in @owner_user
    
    long_description = "a" * 2000  # Very long description
    
    patch :update, params: {
      account: {
        name: "Valid Name",
        description: long_description
      }
    }
    
    # Should either accept or gracefully handle based on validation rules
    assert_response :redirect  # or :unprocessable_entity based on validation
  end

  # ===============================
  # INTEGRATION TESTS
  # ===============================

  test "should complete full account settings update workflow" do
    sign_in @owner_user
    
    # Step 1: View current settings
    get :show
    assert_response :success
    
    # Step 2: Go to edit page
    get :edit
    assert_response :success
    
    # Step 3: Update settings
    patch :update, params: {
      account: {
        name: "Workflow Test Company",
        headquarters_location: "Portland, OR",
        description: "Test workflow description",
        linkedin_url: "https://linkedin.com/company/workflow-test"
      }
    }
    
    assert_redirected_to account_settings_path
    
    # Step 4: Verify changes were saved
    @account.reload
    assert_equal "Workflow Test Company", @account.name
    assert_equal "Portland, OR", @account.headquarters_location
    assert_equal "Test workflow description", @account.description.to_plain_text
    assert_equal "https://linkedin.com/company/workflow-test", @account.linkedin_url
    
    # Step 5: View updated settings
    get :show
    assert_response :success
  end

  test "should handle edit and update with validation errors" do
    sign_in @owner_user
    
    # Step 1: Go to edit page
    get :edit
    assert_response :success
    
    # Step 2: Submit invalid data
    patch :update, params: {
      account: {
        name: "",  # Invalid
        headquarters_location: "Valid Location"
      }
    }
    
    assert_response :unprocessable_entity
    assert_not_nil assigns(:account)
    
    # Step 3: Fix the error and resubmit
    patch :update, params: {
      account: {
        name: "Fixed Name",
        headquarters_location: "Valid Location"
      }
    }
    
    assert_redirected_to account_settings_path
    assert_match /Account information updated successfully/, flash[:notice]
  end

  # ===============================
  # ACCOUNT ACCESS TESTS
  # ===============================

  test "should check account user membership" do
    sign_in @member_user
    # Force session to try to access the owner's account (not their own)
    session[:account_id] = @account.id
    
    # Member should not have admin access to this account
    get :show
    assert_response :redirect
  end

  test "should allow account admin role access" do
    # Create a user with admin role (not owner)
    admin_member = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Account",
      last_name: "Admin"
    )
    @account.account_users.create!(user: admin_member, role: 'admin')
    sign_in admin_member
    
    get :show
    assert_response :success
  end

  test "should deny access to non-account members" do
    non_member = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Non",
      last_name: "Member"
    )
    
    # Create their own account so they're not orphaned
    their_account = Account.create!(
      name: "Their Account",
      account_type: "vendor",
      status: "approved",
      confirmed_at: Time.current,
      owner: non_member
    )
    AccountUser.create!(account: their_account, user: non_member, role: 'admin')
    
    sign_in non_member
    
    get :show
    # Should access their own account, not the test account
    assert_response :success
    assert_equal their_account, assigns(:account)
  end

  private

  def sign_in(user)
    session_record = user.sessions.create!(
      user_agent: 'Test User Agent',
      ip_address: '127.0.0.1'
    )
    Current.session = session_record
    cookies.signed[:session_id] = session_record.id
  end
end