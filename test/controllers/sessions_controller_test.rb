require "test_helper"

class SessionsControllerTest < ActionController::TestCase
  def setup
    @valid_user = users(:one)
    @admin_user = users(:admin)
    @vendor_account = accounts(:one)
    @agency_account = accounts(:two)
    
    # Ensure accounts have proper setup
    @vendor_account.update!(account_type: 'vendor', status: 'approved', confirmed_at: Time.current)
    @agency_account.update!(account_type: 'agency', status: 'approved', confirmed_at: Time.current)
    
    # Create a user without accounts for testing
    @orphaned_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Orphaned",
      last_name: "User"
    )
    
    # Create pending account for testing
    @pending_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Pending",
      last_name: "User"
    )
    @pending_account = Account.create!(
      name: "Pending Company",
      account_type: "vendor",
      status: "pending",
      owner: @pending_user
    )
    
    # Create confirmed but not approved account
    @confirmed_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Confirmed",
      last_name: "User"
    )
    @confirmed_account = Account.create!(
      name: "Confirmed Company",
      account_type: "vendor",
      status: "pending",
      confirmed_at: Time.current,
      owner: @confirmed_user
    )
    
    # Create approved but not confirmed account
    @approved_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Approved",
      last_name: "User"
    )
    @approved_account = Account.create!(
      name: "Approved Company",
      account_type: "vendor",
      status: "approved",
      confirmed_at: nil,
      owner: @approved_user
    )
  end

  # ===============================
  # NEW ACTION TESTS
  # ===============================

  test "should get new session page" do
    get :new
    assert_response :success
  end

  test "should allow unauthenticated access to new" do
    # This should not redirect to login
    get :new
    assert_response :success
  end

  test "should not require authentication for new action" do
    get :new
    assert_response :success
    assert_nil session[:return_to_after_authenticating]
  end

  # ===============================
  # CREATE ACTION TESTS - SUCCESS CASES
  # ===============================

  test "should login valid user with approved and confirmed account" do
    post :create, params: { 
      email_address: @valid_user.email_address, 
      password: "password" 
    }
    
    assert_redirected_to vendors_root_path  # Assuming user has vendor account
    assert Current.session.present?
    assert_equal @valid_user, Current.user
  end

  test "should login super admin user bypassing account checks" do
    @admin_user.update!(super_admin: true)
    
    post :create, params: { 
      email_address: @admin_user.email_address, 
      password: "password" 
    }
    
    assert_redirected_to admin_root_path
    assert Current.session.present?
    assert_equal @admin_user, Current.user
  end

  test "should redirect orphaned user to setup page" do
    post :create, params: { 
      email_address: @orphaned_user.email_address, 
      password: "password123" 
    }
    
    assert_redirected_to orphaned_users_path
    assert Current.session.present?
    assert_equal @orphaned_user, Current.user
  end

  test "should handle pending invitation acceptance on login" do
    # Create a team invitation
    invitation = TeamInvitation.create!(
      account: @vendor_account,
      email: @valid_user.email_address,
      token: SecureRandom.hex(32),
      expires_at: 1.week.from_now,
      invited_by: @vendor_account.owner,
      first_name: "Invited",
      last_name: "User",
      status: "pending"
    )
    
    # Set invitation token in session
    session[:invitation_token] = invitation.token
    
    post :create, params: { 
      email_address: @valid_user.email_address, 
      password: "password" 
    }
    
    assert_redirected_to team_members_account_path
    assert_match /Welcome to the team/, flash[:notice]
    assert_nil session[:invitation_token]
  end

  test "should handle expired invitation on login" do
    # Create an expired invitation
    invitation = TeamInvitation.create!(
      account: @vendor_account,
      email: @valid_user.email_address,
      token: SecureRandom.hex(32),
      expires_at: 1.day.ago,  # Expired
      invited_by: @vendor_account.owner,
      first_name: "Invited",
      last_name: "User",
      status: "pending"
    )
    
    session[:invitation_token] = invitation.token
    
    post :create, params: { 
      email_address: @valid_user.email_address, 
      password: "password" 
    }
    
    # Should clear expired invitation and proceed normally
    assert_nil session[:invitation_token]
    assert Current.session.present?
  end

  # ===============================
  # CREATE ACTION TESTS - PENDING CASES
  # ===============================

  test "should redirect pending account to pending page" do
    post :create, params: { 
      email_address: @pending_user.email_address, 
      password: "password123" 
    }
    
    assert_redirected_to pending_path
    assert_match /pending approval and confirmation/, flash[:notice]
  end

  test "should redirect confirmed but not approved account" do
    post :create, params: { 
      email_address: @confirmed_user.email_address, 
      password: "password123" 
    }
    
    assert_redirected_to pending_path
    assert_match /confirmed and waiting for admin approval/, flash[:notice]
  end

  test "should redirect approved but not confirmed account" do
    post :create, params: { 
      email_address: @approved_user.email_address, 
      password: "password123" 
    }
    
    assert_redirected_to pending_path
    assert_match /approved but needs to be confirmed/, flash[:alert]
  end

  # ===============================
  # CREATE ACTION TESTS - FAILURE CASES
  # ===============================

  test "should not login with invalid email" do
    post :create, params: { 
      email_address: "<EMAIL>", 
      password: "password" 
    }
    
    assert_redirected_to new_session_path
    assert_match /Try another email address or password/, flash[:alert]
    assert_nil Current.session
  end

  test "should not login with invalid password" do
    post :create, params: { 
      email_address: @valid_user.email_address, 
      password: "wrongpassword" 
    }
    
    assert_redirected_to new_session_path
    assert_match /Try another email address or password/, flash[:alert]
    assert_nil Current.session
  end

  test "should not login with empty credentials" do
    post :create, params: { 
      email_address: "", 
      password: "" 
    }
    
    assert_redirected_to new_session_path
    assert_match /Try another email address or password/, flash[:alert]
    assert_nil Current.session
  end

  test "should handle missing email parameter" do
    post :create, params: { 
      password: "password" 
    }
    
    assert_redirected_to new_session_path
    assert_match /Try another email address or password/, flash[:alert]
  end

  test "should handle missing password parameter" do
    post :create, params: { 
      email_address: @valid_user.email_address 
    }
    
    assert_redirected_to new_session_path
    assert_match /Try another email address or password/, flash[:alert]
  end

  # ===============================
  # RATE LIMITING TESTS
  # ===============================

  test "should handle rate limiting gracefully" do
    # The rate limit is 10 attempts within 3 minutes
    # We'll test that the rate limit is applied but won't actually hit it
    # since that would require 11 requests
    
    # Make one failed request
    post :create, params: { 
      email_address: "<EMAIL>", 
      password: "wrong" 
    }
    
    assert_redirected_to new_session_path
    assert_match /Try another email address or password/, flash[:alert]
  end

  # ===============================
  # DESTROY ACTION TESTS
  # ===============================

  test "should logout authenticated user" do
    # First log in
    post :create, params: { 
      email_address: @valid_user.email_address, 
      password: "password" 
    }
    assert Current.session.present?
    
    # Then log out
    delete :destroy
    assert_redirected_to new_session_path
    assert_nil Current.session
  end

  test "should handle logout when not authenticated" do
    # Try to logout without being logged in
    delete :destroy
    assert_redirected_to new_session_path
    assert_nil Current.session
  end

  test "should clear session cookie on logout" do
    # First log in
    post :create, params: { 
      email_address: @valid_user.email_address, 
      password: "password" 
    }
    assert_not_nil cookies.signed[:session_id]
    
    # Then log out
    delete :destroy
    assert_nil cookies[:session_id]
  end

  # ===============================
  # PARAMETER HANDLING TESTS
  # ===============================

  test "should permit email_address and password parameters" do
    post :create, params: { 
      email_address: @valid_user.email_address, 
      password: "password",
      extra_param: "should_be_ignored"
    }
    
    # Should succeed without parameter errors
    assert_response :redirect
  end

  test "should handle case insensitive email" do
    post :create, params: { 
      email_address: @valid_user.email_address.upcase, 
      password: "password" 
    }
    
    # This might fail depending on User.authenticate_by implementation
    # But should not cause errors
    assert_response :redirect
  end

  # ===============================
  # REDIRECT AFTER AUTHENTICATION TESTS
  # ===============================

  test "should redirect to return_to_after_authenticating if set" do
    # Simulate a redirect scenario
    session[:return_to_after_authenticating] = "/vendors/products"
    
    post :create, params: { 
      email_address: @valid_user.email_address, 
      password: "password" 
    }
    
    assert_redirected_to "/vendors/products"
    assert_nil session[:return_to_after_authenticating]
  end

  test "should redirect admin to admin dashboard" do
    @admin_user.update!(super_admin: true)
    
    post :create, params: { 
      email_address: @admin_user.email_address, 
      password: "password" 
    }
    
    assert_redirected_to admin_root_path
  end

  test "should redirect vendor to vendor dashboard" do
    post :create, params: { 
      email_address: @valid_user.email_address, 
      password: "password" 
    }
    
    # Assuming @valid_user has a vendor account
    assert_redirected_to vendors_root_path
  end

  # ===============================
  # SECURITY TESTS
  # ===============================

  test "should not expose user existence through different error messages" do
    # Both invalid email and invalid password should give same error message
    
    post :create, params: { 
      email_address: "<EMAIL>", 
      password: "password" 
    }
    invalid_email_message = flash[:alert]
    
    # Clear flash
    flash.clear
    
    post :create, params: { 
      email_address: @valid_user.email_address, 
      password: "wrongpassword" 
    }
    invalid_password_message = flash[:alert]
    
    assert_equal invalid_email_message, invalid_password_message
  end

  test "should allow unauthenticated access only to allowed actions" do
    # new and create should be accessible
    get :new
    assert_response :success
    
    post :create, params: { email_address: "", password: "" }
    assert_response :redirect  # Should redirect, not require auth
  end

  # ===============================
  # SESSION MANAGEMENT TESTS
  # ===============================

  test "should create new session record on successful login" do
    assert_difference 'Session.count', 1 do
      post :create, params: { 
        email_address: @valid_user.email_address, 
        password: "password" 
      }
    end
  end

  test "should not create session record on failed login" do
    assert_no_difference 'Session.count' do
      post :create, params: { 
        email_address: "<EMAIL>", 
        password: "wrong" 
      }
    end
  end

  test "should destroy session record on logout" do
    # First log in
    post :create, params: { 
      email_address: @valid_user.email_address, 
      password: "password" 
    }
    session_id = Current.session.id
    
    # Then log out
    assert_difference 'Session.count', -1 do
      delete :destroy
    end
    
    assert_raises(ActiveRecord::RecordNotFound) do
      Session.find(session_id)
    end
  end

  # ===============================
  # INTEGRATION TESTS
  # ===============================

  test "should handle full login and logout flow" do
    # Start unauthenticated
    assert_nil Current.session
    
    # Login
    post :create, params: { 
      email_address: @valid_user.email_address, 
      password: "password" 
    }
    assert Current.session.present?
    assert_redirected_to vendors_root_path
    
    # Verify session persists
    get :new  # Any action to check session
    assert Current.session.present?
    
    # Logout
    delete :destroy
    assert_nil Current.session
    assert_redirected_to new_session_path
  end
end