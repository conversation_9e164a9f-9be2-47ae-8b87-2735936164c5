require "test_helper"

module Admin
  class ProductsControllerTest < ActionController::TestCase
    def setup
      # Create test users
      @admin_user = users(:admin)
      @vendor_user = users(:one)
      @agency_user = users(:two)
      @admin_user.update!(super_admin: true)
      
      # Create test accounts
      @vendor_account = accounts(:one)
      @agency_account = accounts(:two)
      
      @vendor_account.update!(
        account_type: 'vendor', 
        status: 'approved', 
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      @agency_account.update!(
        account_type: 'agency',
        status: 'approved', 
        confirmed_at: Time.current,
        owner: @agency_user
      )
      
      # Clear existing account users to avoid duplicates
      @vendor_account.account_users.destroy_all
      @vendor_account.account_users.create!(user: @vendor_user, role: 'admin')
      
      # Create test products
      @product = @vendor_account.products.create!(
        name: "Test Product",
        description: "A test product description",
        website: "https://test.com",
        published: false
      )
      
      @published_product = @vendor_account.products.create!(
        name: "Published Product",
        description: "A published test product",
        website: "https://published.com",
        published: true
      )
      
      @other_account_product = @agency_account.products.create!(
        name: "Agency Product",
        description: "Product from agency account",
        website: "https://agency.com",
        published: true
      )
      
      # Create test categories
      @parent_category = Category.create!(name: "Software", description: "Software products")
      @category1 = Category.create!(name: "CRM", description: "Customer relationship management", parent: @parent_category)
      @category2 = Category.create!(name: "ERP", description: "Enterprise resource planning", parent: @parent_category)
      
      # Create test certifications
      @certification1 = Certification.create!(name: "SOC 2", description: "Security certification")
      @certification2 = Certification.create!(name: "FedRAMP", description: "Federal certification")
    end

    # ===============================
    # AUTHENTICATION & AUTHORIZATION TESTS
    # ===============================

    test "should require admin authentication for all actions" do
      get :index
      assert_redirected_to new_session_path
      
      get :show, params: { id: @product.slug }
      assert_redirected_to new_session_path
      
      get :new
      assert_redirected_to new_session_path
      
      post :create, params: { product: { name: "Test" } }
      assert_redirected_to new_session_path
    end

    test "should require admin privileges" do
      sign_in @vendor_user
      
      get :index
      assert_redirected_to root_path
      
      get :show, params: { id: @product.slug }
      assert_redirected_to root_path
    end

    test "should allow admin users" do
      sign_in @admin_user
      
      get :index
      assert_response :success
    end

    # ===============================
    # INDEX ACTION TESTS
    # ===============================

    test "should show products index" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      assert_not_nil assigns(:products)
      
      products = assigns(:products)
      assert products.include?(@product)
      assert products.include?(@published_product)
      assert products.include?(@other_account_product)
    end

    test "should order products by creation date descending" do
      sign_in @admin_user
      
      # Create a newer product
      newer_product = @vendor_account.products.create!(
        name: "Newer Product",
        description: "A newer product",
        website: "https://newer.com",
        published: false
      )
      
      get :index
      assert_response :success
      
      products = assigns(:products).to_a
      newer_index = products.index(newer_product)
      older_index = products.index(@product)
      
      assert newer_index < older_index, "Newer product should appear first"
    end

    test "should include account and categories associations" do
      sign_in @admin_user
      
      @product.categories << @category1
      
      get :index
      assert_response :success
      
      products = assigns(:products)
      product_with_associations = products.find { |p| p.id == @product.id }
      
      assert product_with_associations.account.loaded?
      assert product_with_associations.categories.loaded?
    end

    test "should filter by search term in product name" do
      sign_in @admin_user
      
      get :index, params: { search: "Test Product" }
      assert_response :success
      
      products = assigns(:products)
      assert products.include?(@product)
      assert_not products.include?(@published_product)
    end

    test "should filter by search term in product description" do
      sign_in @admin_user
      
      get :index, params: { search: "published test" }
      assert_response :success
      
      products = assigns(:products)
      assert products.include?(@published_product)
      assert_not products.include?(@product)
    end

    test "should filter by search term in account name" do
      sign_in @admin_user
      
      get :index, params: { search: @vendor_account.name }
      assert_response :success
      
      products = assigns(:products)
      assert products.include?(@product)
      assert products.include?(@published_product)
      assert_not products.include?(@other_account_product)
    end

    test "should filter by published status" do
      sign_in @admin_user
      
      get :index, params: { published: 'true' }
      assert_response :success
      
      products = assigns(:products)
      assert products.include?(@published_product)
      assert products.include?(@other_account_product)
      assert_not products.include?(@product)
    end

    test "should filter by unpublished status" do
      sign_in @admin_user
      
      get :index, params: { published: 'false' }
      assert_response :success
      
      products = assigns(:products)
      assert products.include?(@product)
      assert_not products.include?(@published_product)
      assert_not products.include?(@other_account_product)
    end

    test "should combine search and filters" do
      sign_in @admin_user
      
      get :index, params: { 
        search: @vendor_account.name,
        published: 'true'
      }
      assert_response :success
      
      products = assigns(:products)
      assert products.include?(@published_product)
      assert_not products.include?(@product)
      assert_not products.include?(@other_account_product)
    end

    # ===============================
    # SHOW ACTION TESTS
    # ===============================

    test "should show product details" do
      sign_in @admin_user
      
      get :show, params: { id: @product.slug }
      assert_response :success
      assert_equal @product, assigns(:product)
    end

    test "should find product by slug" do
      sign_in @admin_user
      
      get :show, params: { id: @product.slug }
      assert_response :success
      assert_equal @product, assigns(:product)
    end

    test "should show products from any account" do
      sign_in @admin_user
      
      get :show, params: { id: @other_account_product.slug }
      assert_response :success
      assert_equal @other_account_product, assigns(:product)
    end

    test "should handle invalid product id" do
      sign_in @admin_user
      
      assert_raises(ActiveRecord::RecordNotFound) do
        get :show, params: { id: 'invalid-slug' }
      end
    end

    # ===============================
    # NEW ACTION TESTS
    # ===============================

    test "should show new product form" do
      sign_in @admin_user
      
      get :new
      assert_response :success
      assert_instance_of Product, assigns(:product)
      assert assigns(:product).new_record?
      assert_not_nil assigns(:accounts)
      assert_not_nil assigns(:categories)
      assert_not_nil assigns(:certifications)
    end

    test "should load accounts ordered by name" do
      sign_in @admin_user
      
      get :new
      assert_response :success
      
      accounts = assigns(:accounts)
      assert accounts.include?(@vendor_account)
      assert accounts.include?(@agency_account)
    end

    test "should load leaf categories with parent associations" do
      sign_in @admin_user
      
      get :new
      assert_response :success
      
      categories = assigns(:categories)
      assert categories.include?(@category1)
      assert categories.include?(@category2)
      assert_not categories.include?(@parent_category)  # Parent category should not be included
      
      # Check that parent associations are loaded
      category_with_parent = categories.find { |c| c.id == @category1.id }
      assert category_with_parent.parent.loaded?
    end

    test "should load certifications alphabetically" do
      sign_in @admin_user
      
      get :new
      assert_response :success
      
      certifications = assigns(:certifications)
      assert certifications.include?(@certification1)
      assert certifications.include?(@certification2)
    end

    # ===============================
    # CREATE ACTION TESTS
    # ===============================

    test "should create product with valid data" do
      sign_in @admin_user
      
      product_params = {
        name: "New Product",
        description: "A new product description",
        website: "https://newproduct.com",
        account_id: @vendor_account.id,
        published: true,
        category_ids: [@category1.id, @category2.id],
        certification_ids: [@certification1.id]
      }
      
      assert_difference 'Product.count', 1 do
        post :create, params: { product: product_params }
      end
      
      assert_redirected_to admin_product_path(assigns(:product))
      assert_match /Product was successfully created/, flash[:notice]
      
      product = Product.last
      assert_equal "New Product", product.name
      assert_equal "A new product description", product.description.to_plain_text
      assert_equal "https://newproduct.com", product.website
      assert_equal @vendor_account, product.account
      assert product.published?
      
      assert product.categories.include?(@category1)
      assert product.categories.include?(@category2)
      assert product.certifications.include?(@certification1)
    end

    test "should not create product with invalid data" do
      sign_in @admin_user
      
      invalid_params = {
        name: "",  # Required field
        description: "",
        website: "invalid-url",
        account_id: nil  # Required
      }
      
      assert_no_difference 'Product.count' do
        post :create, params: { product: invalid_params }
      end
      
      assert_response :unprocessable_entity
      assert_not_nil assigns(:product)
      assert assigns(:product).errors.any?
      assert_not_nil assigns(:accounts)
      assert_not_nil assigns(:categories)
      assert_not_nil assigns(:certifications)
    end

    test "should create unpublished product by default" do
      sign_in @admin_user
      
      product_params = {
        name: "Draft Product",
        description: "A draft product",
        website: "https://draft.com",
        account_id: @vendor_account.id
        # published not specified
      }
      
      post :create, params: { product: product_params }
      
      product = Product.last
      assert_not product.published?
    end

    test "should assign product to specified account" do
      sign_in @admin_user
      
      product_params = {
        name: "Agency Product",
        description: "Product for agency",
        website: "https://agency-product.com",
        account_id: @agency_account.id
      }
      
      post :create, params: { product: product_params }
      
      product = Product.last
      assert_equal @agency_account, product.account
    end

    # ===============================
    # EDIT ACTION TESTS
    # ===============================

    test "should show edit product form" do
      sign_in @admin_user
      
      get :edit, params: { id: @product.slug }
      assert_response :success
      assert_equal @product, assigns(:product)
      assert_not_nil assigns(:accounts)
      assert_not_nil assigns(:categories)
      assert_not_nil assigns(:certifications)
    end

    test "should load current categories and certifications for edit" do
      sign_in @admin_user
      
      @product.categories << @category1
      @product.certifications << @certification1
      
      get :edit, params: { id: @product.slug }
      assert_response :success
      
      product = assigns(:product)
      assert product.categories.include?(@category1)
      assert product.certifications.include?(@certification1)
    end

    # ===============================
    # UPDATE ACTION TESTS
    # ===============================

    test "should update product with valid data" do
      sign_in @admin_user
      
      update_params = {
        name: "Updated Product Name",
        description: "Updated description",
        website: "https://updated.com",
        account_id: @agency_account.id,
        published: true,
        category_ids: [@category2.id],
        certification_ids: [@certification2.id]
      }
      
      patch :update, params: { id: @product.slug, product: update_params }
      
      assert_redirected_to admin_product_path(@product)
      assert_match /Product was successfully updated/, flash[:notice]
      
      @product.reload
      assert_equal "Updated Product Name", @product.name
      assert_equal "Updated description", @product.description.to_plain_text
      assert_equal "https://updated.com", @product.website
      assert_equal @agency_account, @product.account
      assert @product.published?
      
      assert @product.categories.include?(@category2)
      assert_not @product.categories.include?(@category1)
      assert @product.certifications.include?(@certification2)
    end

    test "should not update product with invalid data" do
      sign_in @admin_user
      
      original_name = @product.name
      original_account = @product.account
      
      invalid_params = {
        name: "",  # Invalid
        account_id: nil,  # Invalid
        description: "Valid description"
      }
      
      patch :update, params: { id: @product.slug, product: invalid_params }
      
      assert_response :unprocessable_entity
      assert_not_nil assigns(:product)
      assert assigns(:product).errors.any?
      
      @product.reload
      assert_equal original_name, @product.name
      assert_equal original_account, @product.account
    end

    test "should change product account as admin" do
      sign_in @admin_user
      
      original_account = @product.account
      
      update_params = {
        name: @product.name,
        account_id: @agency_account.id
      }
      
      patch :update, params: { id: @product.slug, product: update_params }
      
      assert_redirected_to admin_product_path(@product)
      
      @product.reload
      assert_not_equal original_account, @product.account
      assert_equal @agency_account, @product.account
    end

    # ===============================
    # DESTROY ACTION TESTS
    # ===============================

    test "should delete product" do
      sign_in @admin_user
      
      assert_difference 'Product.count', -1 do
        delete :destroy, params: { id: @product.slug }
      end
      
      assert_redirected_to admin_products_path
      assert_match /Product was successfully deleted/, flash[:notice]
    end

    test "should delete product from any account" do
      sign_in @admin_user
      
      assert_difference 'Product.count', -1 do
        delete :destroy, params: { id: @other_account_product.slug }
      end
      
      assert_redirected_to admin_products_path
    end

    test "should handle delete of product with associated content" do
      sign_in @admin_user
      
      # Create associated content
      video = @product.product_videos.build(
        title: "Demo Video",
        description: "A demo video description",
        position: 1
      )
      video.video_file.attach(
        io: StringIO.new("fake video data"),
        filename: "demo.mp4",
        content_type: "video/mp4"
      )
      video.save!
      
      case_study = @product.case_studies.build(
        title: "Success Story",
        description: "A detailed case study about customer success",
        position: 1
      )
      case_study.upload.attach(
        io: StringIO.new("fake pdf data"),
        filename: "case_study.pdf",
        content_type: "application/pdf"
      )
      case_study.save!
      
      assert_difference 'Product.count', -1 do
        delete :destroy, params: { id: @product.slug }
      end
      
      assert_redirected_to admin_products_path
    end

    # ===============================
    # PARAMETER SECURITY TESTS
    # ===============================

    test "should allow all permitted parameters for admin" do
      sign_in @admin_user
      
      permitted_params = {
        name: "Permitted Product",
        description: "Permitted description",
        website: "https://permitted.com",
        account_id: @agency_account.id,
        published: true,
        category_ids: [@category1.id],
        certification_ids: [@certification1.id]
      }
      
      post :create, params: { product: permitted_params }
      
      product = Product.last
      assert_equal "Permitted Product", product.name
      assert_equal "Permitted description", product.description.to_plain_text
      assert_equal "https://permitted.com", product.website
      assert_equal @agency_account, product.account
      assert product.published?
      assert product.categories.include?(@category1)
      assert product.certifications.include?(@certification1)
    end

    test "should filter unpermitted parameters" do
      sign_in @admin_user
      
      malicious_params = {
        name: "Valid Name",
        description: "Valid description",
        account_id: @vendor_account.id,
        created_at: 1.year.ago,  # Should be filtered
        id: 999,  # Should be filtered
        slug: "malicious-slug"  # Should be filtered
      }
      
      post :create, params: { product: malicious_params }
      
      product = Product.last
      assert_equal "Valid Name", product.name
      assert_equal @vendor_account, product.account
      assert_not_equal 1.year.ago.to_date, product.created_at.to_date
      assert_not_equal "malicious-slug", product.slug
    end

    # ===============================
    # CATEGORY & CERTIFICATION TESTS
    # ===============================

    test "should handle multiple categories" do
      sign_in @admin_user
      
      product_params = {
        name: "Multi-Category Product",
        description: "Product with multiple categories",
        account_id: @vendor_account.id,
        category_ids: [@category1.id, @category2.id]
      }
      
      post :create, params: { product: product_params }
      
      product = Product.last
      assert_equal 2, product.categories.count
      assert product.categories.include?(@category1)
      assert product.categories.include?(@category2)
    end

    test "should handle multiple certifications" do
      sign_in @admin_user
      
      product_params = {
        name: "Multi-Cert Product",
        description: "Product with multiple certifications",
        account_id: @vendor_account.id,
        certification_ids: [@certification1.id, @certification2.id]
      }
      
      post :create, params: { product: product_params }
      
      product = Product.last
      assert_equal 2, product.certifications.count
      assert product.certifications.include?(@certification1)
      assert product.certifications.include?(@certification2)
    end

    test "should remove categories when updating" do
      sign_in @admin_user
      
      @product.categories << [@category1, @category2]
      assert_equal 2, @product.categories.count
      
      update_params = {
        name: @product.name,
        account_id: @product.account_id,
        category_ids: [@category1.id]  # Only one category now
      }
      
      patch :update, params: { id: @product.slug, product: update_params }
      
      @product.reload
      assert_equal 1, @product.categories.count
      assert @product.categories.include?(@category1)
      assert_not @product.categories.include?(@category2)
    end

    # ===============================
    # INTEGRATION TESTS
    # ===============================

    test "should complete full product management workflow" do
      sign_in @admin_user
      
      # Step 1: Visit products index
      get :index
      assert_response :success
      
      # Step 2: Visit new product page
      get :new
      assert_response :success
      
      # Step 3: Create product
      product_params = {
        name: "Workflow Product",
        description: "Product created through admin workflow",
        website: "https://admin-workflow.com",
        account_id: @vendor_account.id,
        published: false,
        category_ids: [@category1.id]
      }
      
      assert_difference 'Product.count', 1 do
        post :create, params: { product: product_params }
      end
      
      product = Product.last
      assert_redirected_to admin_product_path(product)
      
      # Step 4: View created product
      get :show, params: { id: product.slug }
      assert_response :success
      assert_equal product, assigns(:product)
      
      # Step 5: Edit product
      get :edit, params: { id: product.slug }
      assert_response :success
      
      # Step 6: Update product to published and change account
      patch :update, params: { 
        id: product.slug, 
        product: { 
          published: true,
          account_id: @agency_account.id
        } 
      }
      
      assert_redirected_to admin_product_path(product)
      
      product.reload
      assert product.published?
      assert_equal @agency_account, product.account
      
      # Step 7: Delete product
      assert_difference 'Product.count', -1 do
        delete :destroy, params: { id: product.slug }
      end
      
      assert_redirected_to admin_products_path
    end

    test "should handle product management with validation errors" do
      sign_in @admin_user
      
      # Step 1: Try to create invalid product
      invalid_params = {
        name: "",  # Invalid
        description: "Valid description",
        account_id: nil  # Invalid
      }
      
      assert_no_difference 'Product.count' do
        post :create, params: { product: invalid_params }
      end
      
      assert_response :unprocessable_entity
      assert assigns(:product).errors.any?
      
      # Step 2: Fix errors and create successfully
      valid_params = {
        name: "Fixed Product Name",
        description: "Valid description",
        website: "https://fixed.com",
        account_id: @vendor_account.id
      }
      
      assert_difference 'Product.count', 1 do
        post :create, params: { product: valid_params }
      end
      
      assert_redirected_to admin_product_path(assigns(:product))
    end

    test "should manage products across different accounts" do
      sign_in @admin_user
      
      # Create products for different accounts
      vendor_product_params = {
        name: "Vendor Product",
        description: "Product for vendor account",
        website: "https://vendor.com",
        account_id: @vendor_account.id
      }
      
      agency_product_params = {
        name: "Agency Product",
        description: "Product for agency account",
        website: "https://agency.com",
        account_id: @agency_account.id
      }
      
      # Create vendor product
      post :create, params: { product: vendor_product_params }
      vendor_product = Product.last
      assert_equal @vendor_account, vendor_product.account
      
      # Create agency product
      post :create, params: { product: agency_product_params }
      agency_product = Product.last
      assert_equal @agency_account, agency_product.account
      
      # Admin should be able to view and manage both
      get :show, params: { id: vendor_product.slug }
      assert_response :success
      
      get :show, params: { id: agency_product.slug }
      assert_response :success
      
      # Should be able to transfer products between accounts
      patch :update, params: { 
        id: vendor_product.slug,
        product: { account_id: @agency_account.id }
      }
      
      vendor_product.reload
      assert_equal @agency_account, vendor_product.account
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
    end
  end
end