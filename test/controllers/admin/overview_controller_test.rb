require "test_helper"

module Admin
  class OverviewControllerTest < ActionController::TestCase
    def setup
      # Create test users
      @admin_user = users(:admin)
      @vendor_user = users(:one)
      @agency_user = users(:two)
      @admin_user.update!(super_admin: true)
      
      # Create another admin for testing
      @second_admin = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "Second",
        last_name: "Admin",
        super_admin: true
      )
      
      # Create test accounts
      @vendor_account = accounts(:one)
      @agency_account = accounts(:two)
      @pending_account = accounts(:pending_vendor)
      
      @vendor_account.update!(
        account_type: 'vendor', 
        status: 'approved', 
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      @agency_account.update!(
        account_type: 'agency',
        status: 'approved', 
        confirmed_at: Time.current,
        owner: @agency_user
      )
      
      @pending_account.update!(
        account_type: 'vendor',
        status: 'pending',
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      # Create additional test account for rejected status
      @rejected_account = Account.create!(
        name: "Rejected Account",
        account_type: "vendor",
        status: "rejected",
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      # Clear existing account users to avoid duplicates
      @vendor_account.account_users.destroy_all
      @vendor_account.account_users.create!(user: @vendor_user, role: 'admin')
      
      @agency_account.account_users.destroy_all
      @agency_account.account_users.create!(user: @agency_user, role: 'admin')
      
      # Create test products
      @published_product = @vendor_account.products.create!(
        name: "Published Product",
        description: "A published product",
        website: "https://published.com",
        published: true
      )
      
      @draft_product = @vendor_account.products.create!(
        name: "Draft Product",
        description: "A draft product",
        website: "https://draft.com",
        published: false
      )
      
      # Create test case studies
      @published_case_study = @published_product.case_studies.build(
        title: "Published Case Study",
        description: "A published case study",
        position: 1,
        published: true
      )
      @published_case_study.upload.attach(
        io: StringIO.new("fake pdf data"),
        filename: "published_case_study.pdf",
        content_type: "application/pdf"
      )
      @published_case_study.save!
      
      @draft_case_study = @published_product.case_studies.build(
        title: "Draft Case Study",
        description: "A draft case study",
        position: 2,
        published: false
      )
      @draft_case_study.upload.attach(
        io: StringIO.new("fake pdf data"),
        filename: "draft_case_study.pdf",
        content_type: "application/pdf"
      )
      @draft_case_study.save!
      
      # Create test leads
      @pending_lead = Lead.create!(
        product: @published_product,
        account: @vendor_account,
        agency_account: @agency_account,
        contact_name: "John Doe",
        contact_email: "<EMAIL>",
        contact_phone: "555-1234",
        contact_company: "Test Agency",
        message: "Interested in your product",
        status: "pending"
      )
      
      @contacted_lead = Lead.create!(
        product: @published_product,
        account: @vendor_account,
        agency_account: @agency_account,
        contact_name: "Jane Smith",
        contact_email: "<EMAIL>",
        contact_phone: "555-5678",
        contact_company: "Another Agency",
        message: "Want to learn more",
        status: "contacted"
      )
      
      # Create sessions for active session count
      @admin_user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
    end

    # ===============================
    # AUTHENTICATION & AUTHORIZATION TESTS
    # ===============================

    test "should require admin authentication" do
      get :index
      assert_redirected_to new_session_path
    end

    test "should require admin privileges" do
      sign_in @vendor_user
      
      get :index
      assert_redirected_to root_path
    end

    test "should allow admin users" do
      sign_in @admin_user
      
      get :index
      assert_response :success
    end

    # ===============================
    # INDEX ACTION TESTS
    # ===============================

    test "should show admin overview dashboard" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      
      # Check that all required instance variables are set
      assert_not_nil assigns(:user_stats)
      assert_not_nil assigns(:account_stats)
      assert_not_nil assigns(:content_stats)
      assert_not_nil assigns(:engagement_stats)
      assert_not_nil assigns(:recent_users)
      assert_not_nil assigns(:recent_accounts)
      assert_not_nil assigns(:recent_products)
      assert_not_nil assigns(:recent_leads)
      assert_not_nil assigns(:growth_data)
      assert_not_nil assigns(:system_health)
    end

    # ===============================
    # USER STATISTICS TESTS
    # ===============================

    test "should calculate user statistics correctly" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      
      user_stats = assigns(:user_stats)
      
      # Total users should include all created users
      assert user_stats[:total_users] >= 3  # At least admin, vendor, agency users
      
      # Super admins should include at least the test admin users
      assert user_stats[:super_admins] >= 2  # @admin_user and @second_admin
      
      # Check that all expected keys are present
      expected_keys = [:total_users, :pending_approval, :approved_users, :recent_signups, :super_admins]
      expected_keys.each do |key|
        assert user_stats.key?(key), "Missing user stat key: #{key}"
        assert user_stats[key].is_a?(Integer), "User stat #{key} should be an integer"
      end
    end

    test "should calculate recent signups correctly" do
      sign_in @admin_user
      
      # Create a user from 8 days ago (should not be included)
      old_user = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "Old",
        last_name: "User",
        created_at: 8.days.ago
      )
      
      # Create a recent user (should be included)
      recent_user = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "Recent",
        last_name: "User",
        created_at: 3.days.ago
      )
      
      get :index
      assert_response :success
      
      user_stats = assigns(:user_stats)
      # Should include recent_user but not old_user
      assert user_stats[:recent_signups] >= 1
    end

    # ===============================
    # ACCOUNT STATISTICS TESTS
    # ===============================

    test "should calculate account statistics correctly" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      
      account_stats = assigns(:account_stats)
      
      # Should count all account types correctly
      assert account_stats[:vendor_accounts] >= 2  # @vendor_account, @pending_account, @rejected_account
      assert account_stats[:agency_accounts] >= 1  # @agency_account
      
      # Should count all statuses correctly
      assert account_stats[:approved_accounts] >= 2  # @vendor_account, @agency_account
      assert account_stats[:pending_accounts] >= 1   # @pending_account
      assert account_stats[:rejected_accounts] >= 1  # @rejected_account
      
      # Check that all expected keys are present
      expected_keys = [:total_accounts, :vendor_accounts, :agency_accounts, :approved_accounts, :pending_accounts, :rejected_accounts]
      expected_keys.each do |key|
        assert account_stats.key?(key), "Missing account stat key: #{key}"
        assert account_stats[key].is_a?(Integer), "Account stat #{key} should be an integer"
      end
    end

    # ===============================
    # CONTENT STATISTICS TESTS
    # ===============================

    test "should calculate content statistics correctly" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      
      content_stats = assigns(:content_stats)
      
      # Should count products correctly
      assert content_stats[:total_products] >= 2     # @published_product, @draft_product
      assert content_stats[:published_products] >= 1 # @published_product
      assert content_stats[:draft_products] >= 1     # @draft_product
      
      # Should count case studies correctly
      assert content_stats[:total_case_studies] >= 2         # @published_case_study, @draft_case_study
      assert content_stats[:published_case_studies] >= 1     # @published_case_study
      
      # Should count companies (vendor accounts) correctly
      assert content_stats[:total_companies] >= 2            # vendor accounts
      assert content_stats[:approved_companies] >= 1         # approved vendor accounts
      
      # Check that all expected keys are present
      expected_keys = [:total_products, :published_products, :draft_products, :total_companies, :approved_companies, :total_case_studies, :published_case_studies]
      expected_keys.each do |key|
        assert content_stats.key?(key), "Missing content stat key: #{key}"
        assert content_stats[key].is_a?(Integer), "Content stat #{key} should be an integer"
      end
    end

    # ===============================
    # ENGAGEMENT STATISTICS TESTS
    # ===============================

    test "should calculate engagement statistics correctly" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      
      engagement_stats = assigns(:engagement_stats)
      
      # Should count leads correctly
      assert engagement_stats[:total_leads] >= 2      # @pending_lead, @contacted_lead
      assert engagement_stats[:pending_leads] >= 1    # @pending_lead
      assert engagement_stats[:contacted_leads] >= 1  # @contacted_lead
      assert engagement_stats[:qualified_leads] >= 0  # None created in setup
      
      # Recent leads (last 7 days) should include our test leads
      assert engagement_stats[:recent_leads] >= 2
      
      # Check that all expected keys are present
      expected_keys = [:total_leads, :pending_leads, :contacted_leads, :qualified_leads, :recent_leads]
      expected_keys.each do |key|
        assert engagement_stats.key?(key), "Missing engagement stat key: #{key}"
        assert engagement_stats[key].is_a?(Integer), "Engagement stat #{key} should be an integer"
      end
    end

    # ===============================
    # RECENT ACTIVITY TESTS
    # ===============================

    test "should load recent activity data" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      
      # Check recent users
      recent_users = assigns(:recent_users)
      assert recent_users.count <= 5
      assert recent_users.include?(@second_admin)  # Most recently created
      
      # Check recent accounts
      recent_accounts = assigns(:recent_accounts)
      assert recent_accounts.count <= 5
      assert recent_accounts.include?(@rejected_account)  # Most recently created
      
      # Check recent products
      recent_products = assigns(:recent_products)
      assert recent_products.count <= 5
      assert recent_products.any? { |p| [@published_product.id, @draft_product.id].include?(p.id) }
      
      # Check that products include account association
      recent_products.each do |product|
        assert_not_nil product.account, "Product should have account association"
      end
      
      # Check recent leads
      recent_leads = assigns(:recent_leads)
      assert recent_leads.count <= 5
      assert recent_leads.any? { |l| [@pending_lead.id, @contacted_lead.id].include?(l.id) }
      
      # Check that leads include necessary associations
      recent_leads.each do |lead|
        assert_not_nil lead.product, "Lead should have product association"
        assert_not_nil lead.account, "Lead should have account association"
        # agency_account can be nil for some leads, so just check it exists as an attribute
        assert_respond_to lead, :agency_account, "Lead should respond to agency_account"
      end
    end

    # ===============================
    # GROWTH DATA TESTS
    # ===============================

    test "should calculate growth data for last 30 days" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      
      growth_data = assigns(:growth_data)
      
      # Should have data for all model types
      [:users, :accounts, :products, :leads].each do |model_type|
        assert growth_data.key?(model_type), "Missing growth data for #{model_type}"
        
        model_data = growth_data[model_type]
        assert model_data.is_a?(Array), "Growth data for #{model_type} should be an array"
        assert_equal 30, model_data.length, "Should have 30 days of data for #{model_type}"
        
        # Each day should have date and count
        model_data.each do |day_data|
          assert day_data.key?(:date), "Missing date in growth data"
          assert day_data.key?(:count), "Missing count in growth data"
          assert day_data[:date].is_a?(Date), "Date should be a Date object"
          assert day_data[:count].is_a?(Integer), "Count should be an integer"
        end
        
        # Data should be in chronological order (oldest first)
        dates = model_data.map { |d| d[:date] }
        assert_equal dates.sort, dates, "Growth data should be sorted by date"
      end
    end

    # ===============================
    # SYSTEM HEALTH TESTS
    # ===============================

    test "should calculate system health metrics" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      
      system_health = assigns(:system_health)
      
      # Check that all expected keys are present
      expected_keys = [:database_size, :uptime, :last_backup, :active_sessions, :failed_jobs]
      expected_keys.each do |key|
        assert system_health.key?(key), "Missing system health key: #{key}"
      end
      
      # Database size should be a string with size information
      assert system_health[:database_size].is_a?(String)
      
      # Uptime should be a formatted string
      assert system_health[:uptime].is_a?(String)
      
      # Active sessions should include at least our test session
      assert system_health[:active_sessions] >= 1
      assert system_health[:active_sessions].is_a?(Integer)
      
      # Failed jobs should be an integer (placeholder value is 0)
      assert system_health[:failed_jobs].is_a?(Integer)
    end

    # ===============================
    # HELPER METHOD TESTS
    # ===============================

    test "should format bytes correctly" do
      sign_in @admin_user
      controller = @controller
      
      # Test various byte sizes
      assert_equal "0 B", controller.send(:format_bytes, 0)
      assert_equal "1.0 B", controller.send(:format_bytes, 1)
      assert_equal "1.0 KB", controller.send(:format_bytes, 1024)
      assert_equal "1.0 MB", controller.send(:format_bytes, 1024 * 1024)
      assert_equal "1.5 KB", controller.send(:format_bytes, 1536)
    end

    test "should format duration correctly" do
      sign_in @admin_user
      controller = @controller
      
      # Test various durations
      assert_equal "5m", controller.send(:format_duration, 300)      # 5 minutes
      assert_equal "1h 30m", controller.send(:format_duration, 5400) # 1.5 hours
      assert_equal "2d 3h", controller.send(:format_duration, 183600) # 2+ days
    end

    test "should handle database size calculation errors gracefully" do
      sign_in @admin_user
      
      # Test that the controller handles database errors gracefully
      get :index
      assert_response :success
      
      system_health = assigns(:system_health)
      # Database size should be a string (either calculated or "Unknown")
      assert system_health[:database_size].is_a?(String)
    end

    # ===============================
    # PERFORMANCE TESTS
    # ===============================

    test "should efficiently load dashboard data" do
      sign_in @admin_user
      
      # Create additional test data to ensure queries are efficient
      10.times do |i|
        User.create!(
          email_address: "user#{i}@example.com",
          password: "password123",
          first_name: "User",
          last_name: "#{i}"
        )
      end
      
      # Dashboard should load without excessive queries
      assert_nothing_raised do
        get :index
        assert_response :success
      end
      
      # Verify all stats are still calculated correctly
      user_stats = assigns(:user_stats)
      assert user_stats[:total_users] >= 13  # Original users + 10 new ones
    end

    # ===============================
    # INTEGRATION TESTS
    # ===============================

    test "should show complete admin dashboard with all sections" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      
      # Verify the page loads with all required data
      assert_not_nil assigns(:user_stats)
      assert_not_nil assigns(:account_stats)
      assert_not_nil assigns(:content_stats)
      assert_not_nil assigns(:engagement_stats)
      assert_not_nil assigns(:recent_users)
      assert_not_nil assigns(:recent_accounts)
      assert_not_nil assigns(:recent_products)
      assert_not_nil assigns(:recent_leads)
      assert_not_nil assigns(:growth_data)
      assert_not_nil assigns(:system_health)
      
      # Verify statistics make sense
      user_stats = assigns(:user_stats)
      account_stats = assigns(:account_stats)
      content_stats = assigns(:content_stats)
      engagement_stats = assigns(:engagement_stats)
      
      # Basic sanity checks
      assert user_stats[:total_users] > 0
      assert account_stats[:total_accounts] > 0
      assert content_stats[:total_products] > 0
      assert engagement_stats[:total_leads] > 0
      
      # Approved should be subset of total
      assert user_stats[:approved_users] <= user_stats[:total_users]
      assert account_stats[:approved_accounts] <= account_stats[:total_accounts]
      assert content_stats[:published_products] <= content_stats[:total_products]
    end

    test "should handle empty data gracefully" do
      # Clear all data
      Lead.destroy_all
      CaseStudy.destroy_all
      ProductVideo.destroy_all
      Product.destroy_all
      AccountUser.destroy_all
      Account.where.not(id: [@admin_user.id]).destroy_all
      Session.destroy_all
      
      sign_in @admin_user
      
      get :index
      assert_response :success
      
      # Should not raise errors even with no data
      user_stats = assigns(:user_stats)
      account_stats = assigns(:account_stats)
      content_stats = assigns(:content_stats)
      engagement_stats = assigns(:engagement_stats)
      
      # Counts should be 0 or minimal
      assert_equal 0, engagement_stats[:total_leads]
      assert_equal 0, content_stats[:total_products]
      assert_equal 0, content_stats[:total_case_studies]
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
    end
  end
end