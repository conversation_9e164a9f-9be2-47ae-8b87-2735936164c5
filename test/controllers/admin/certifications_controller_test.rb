require "test_helper"

module Admin
  class CertificationsControllerTest < ActionController::TestCase
    setup do
      @admin_user = users(:one)
      @admin_user.update!(super_admin: true)
      
      @vendor_user = users(:two)
      @vendor_account = accounts(:one)
      @vendor_account.update!(
        account_type: 'vendor',
        status: 'approved',
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      @vendor_account.account_users.find_or_create_by(user: @vendor_user, role: 'admin')

      # Clean up existing certifications to ensure clean test state
      Certification.destroy_all
      
      # Create test certifications
      @certification1 = Certification.create!(
        name: "ISO 27001",
        description: "Information security management system certification"
      )
      
      @certification2 = Certification.create!(
        name: "FedRAMP",
        description: "Federal Risk and Authorization Management Program"
      )
      
      @certification3 = Certification.create!(
        name: "SOC 2",
        description: "Service Organization Control 2 certification"
      )
    end

    # Authentication Tests
    test "should require authentication for index" do
      get :index
      assert_response :redirect
      assert_redirected_to new_session_path
    end

    test "should require super admin access for index" do
      sign_in @vendor_user
      session[:account_id] = @vendor_account.id
      
      get :index
      assert_response :redirect
      assert_redirected_to root_path
    end

    test "should allow super admin access to index" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      assert_not_nil assigns(:certifications)
    end

    # Index Action Tests
    test "should get index" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      assert_not_nil assigns(:certifications)
      assert_not_nil assigns(:pagy)
    end

    test "should display certifications in alphabetical order" do
      sign_in @admin_user
      
      get :index
      certifications = assigns(:certifications)
      
      # Should be ordered alphabetically by name
      certification_names = certifications.map(&:name)
      assert_equal certification_names.sort, certification_names
    end

    test "should include product associations" do
      sign_in @admin_user
      
      get :index
      certifications = assigns(:certifications)
      
      # Test that products association is eager loaded
      certifications.each do |cert|
        # This shouldn't trigger additional queries
        products = cert.products.to_a
        assert products.is_a?(Array)
      end
    end

    test "should search certifications by name" do
      sign_in @admin_user
      
      get :index, params: { search: 'ISO' }
      certifications = assigns(:certifications)
      
      assert certifications.any?
      certifications.each do |cert|
        assert cert.name.downcase.include?('iso') || cert.description.downcase.include?('iso')
      end
    end

    test "should search certifications by description" do
      sign_in @admin_user
      
      get :index, params: { search: 'security' }
      certifications = assigns(:certifications)
      
      assert certifications.any?
      certifications.each do |cert|
        description_match = cert.description.downcase.include?('security')
        name_match = cert.name.downcase.include?('security')
        assert description_match || name_match
      end
    end

    test "should handle empty search results" do
      sign_in @admin_user
      
      get :index, params: { search: 'nonexistent' }
      certifications = assigns(:certifications)
      
      assert_equal 0, certifications.count
    end

    test "should paginate certifications" do
      sign_in @admin_user
      
      get :index
      pagy = assigns(:pagy)
      
      assert_not_nil pagy
      assert pagy.respond_to?(:pages)
    end

    # Show Action Tests
    test "should show certification" do
      sign_in @admin_user
      
      get :show, params: { id: @certification1.id }
      assert_response :success
      assert_equal @certification1, assigns(:certification)
    end

    test "should require authentication for show" do
      get :show, params: { id: @certification1.id }
      assert_response :redirect
      assert_redirected_to new_session_path
    end

    test "should require super admin access for show" do
      sign_in @vendor_user
      session[:account_id] = @vendor_account.id
      
      get :show, params: { id: @certification1.id }
      assert_response :redirect
      assert_redirected_to root_path
    end

    test "should handle nonexistent certification in show" do
      sign_in @admin_user
      
      get :show, params: { id: 99999 }
      assert_response 302  # Should redirect to error page
    end

    # New Action Tests
    test "should get new" do
      sign_in @admin_user
      
      get :new
      assert_response :success
      assert_not_nil assigns(:certification)
      assert assigns(:certification).new_record?
    end

    test "should require authentication for new" do
      get :new
      assert_response :redirect
      assert_redirected_to new_session_path
    end

    test "should require super admin access for new" do
      sign_in @vendor_user
      session[:account_id] = @vendor_account.id
      
      get :new
      assert_response :redirect
      assert_redirected_to root_path
    end

    # Create Action Tests
    test "should create certification" do
      sign_in @admin_user
      
      certification_params = {
        name: "NIST Cybersecurity Framework",
        description: "National Institute of Standards and Technology cybersecurity framework"
      }

      assert_difference('Certification.count') do
        post :create, params: { certification: certification_params }
      end

      certification = Certification.last
      assert_equal certification_params[:name], certification.name
      assert_equal certification_params[:description], certification.description
      assert_redirected_to admin_certification_path(certification)
      assert_equal 'Certification was successfully created.', flash[:notice]
    end

    test "should not create certification with invalid data" do
      sign_in @admin_user
      
      certification_params = {
        name: "",  # Required field
        description: ""
      }

      assert_no_difference('Certification.count') do
        post :create, params: { certification: certification_params }
      end

      assert_response :unprocessable_entity
      assert_not_nil assigns(:certification)
      assert assigns(:certification).errors.any?
    end

    test "should not create duplicate certification names" do
      sign_in @admin_user
      
      certification_params = {
        name: @certification1.name,  # Duplicate name
        description: "Different description"
      }

      assert_no_difference('Certification.count') do
        post :create, params: { certification: certification_params }
      end

      assert_response :unprocessable_entity
    end

    test "should require authentication for create" do
      certification_params = {
        name: "Test Certification",
        description: "Test description"
      }

      post :create, params: { certification: certification_params }
      assert_response :redirect
      assert_redirected_to new_session_path
    end

    # Edit Action Tests
    test "should get edit" do
      sign_in @admin_user
      
      get :edit, params: { id: @certification1.id }
      assert_response :success
      assert_equal @certification1, assigns(:certification)
    end

    test "should require authentication for edit" do
      get :edit, params: { id: @certification1.id }
      assert_response :redirect
      assert_redirected_to new_session_path
    end

    test "should require super admin access for edit" do
      sign_in @vendor_user
      session[:account_id] = @vendor_account.id
      
      get :edit, params: { id: @certification1.id }
      assert_response :redirect
      assert_redirected_to root_path
    end

    # Update Action Tests
    test "should update certification" do
      sign_in @admin_user
      
      new_attributes = {
        name: "Updated ISO 27001",
        description: "Updated description for ISO 27001"
      }

      patch :update, params: { id: @certification1.id, certification: new_attributes }
      
      @certification1.reload
      assert_equal new_attributes[:name], @certification1.name
      assert_equal new_attributes[:description], @certification1.description
      assert_redirected_to admin_certification_path(@certification1)
      assert_equal 'Certification was successfully updated.', flash[:notice]
    end

    test "should not update certification with invalid data" do
      sign_in @admin_user
      
      invalid_attributes = {
        name: "",  # Required field
        description: "Valid description"
      }

      patch :update, params: { id: @certification1.id, certification: invalid_attributes }
      
      assert_response :unprocessable_entity
      assert_not_nil assigns(:certification)
      assert assigns(:certification).errors.any?
      
      @certification1.reload
      assert_not_equal invalid_attributes[:name], @certification1.name
    end

    test "should not update to duplicate certification name" do
      sign_in @admin_user
      
      duplicate_attributes = {
        name: @certification2.name,  # Already exists
        description: "Updated description"
      }

      patch :update, params: { id: @certification1.id, certification: duplicate_attributes }
      
      assert_response :unprocessable_entity
      @certification1.reload
      assert_not_equal @certification2.name, @certification1.name
    end

    test "should require authentication for update" do
      patch :update, params: { id: @certification1.id, certification: { name: "Updated" } }
      assert_response :redirect
      assert_redirected_to new_session_path
    end

    # Destroy Action Tests
    test "should destroy certification when no products associated" do
      sign_in @admin_user
      
      # Ensure no products are associated
      assert_equal 0, @certification3.products.count
      
      assert_difference('Certification.count', -1) do
        delete :destroy, params: { id: @certification3.id }
      end

      assert_redirected_to admin_certifications_path
      assert_equal 'Certification was successfully deleted.', flash[:notice]
    end

    test "should not destroy certification with associated products" do
      sign_in @admin_user
      
      # Create a product and associate it with the certification
      product = Product.create!(
        name: "Test Product",
        description: "Test description",
        account: @vendor_account,
        published: true
      )
      product.certifications << @certification1
      
      assert_no_difference('Certification.count') do
        delete :destroy, params: { id: @certification1.id }
      end

      assert_redirected_to admin_certifications_path
      assert_equal 'Cannot delete certification with associated products.', flash[:alert]
    end

    test "should require authentication for destroy" do
      delete :destroy, params: { id: @certification1.id }
      assert_response :redirect
      assert_redirected_to new_session_path
    end

    test "should require super admin access for destroy" do
      sign_in @vendor_user
      session[:account_id] = @vendor_account.id
      
      delete :destroy, params: { id: @certification1.id }
      assert_response :redirect
      assert_redirected_to root_path
    end

    test "should handle nonexistent certification in destroy" do
      sign_in @admin_user
      
      delete :destroy, params: { id: 99999 }
      assert_response 302  # Should redirect to error page
    end

    # Parameter Security Tests
    test "should only permit allowed parameters" do
      sign_in @admin_user
      
      params_with_extra = {
        name: "Test Certification",
        description: "Test description",
        id: 999,  # Should be ignored
        created_at: Time.current,  # Should be ignored
        updated_at: Time.current   # Should be ignored
      }

      post :create, params: { certification: params_with_extra }
      
      certification = Certification.last
      assert_equal params_with_extra[:name], certification.name
      assert_equal params_with_extra[:description], certification.description
      # ID should be auto-generated, not from params
      assert_not_equal 999, certification.id
    end

    # Edge Cases
    test "should handle long certification names" do
      sign_in @admin_user
      
      long_name = "A" * 255  # Assuming reasonable name length limit
      certification_params = {
        name: long_name,
        description: "Test description"
      }

      post :create, params: { certification: certification_params }
      
      if Certification.last&.name == long_name
        assert_redirected_to admin_certification_path(Certification.last)
      else
        assert_response :unprocessable_entity
      end
    end

    test "should handle special characters in search" do
      sign_in @admin_user
      
      # Test search with special characters
      get :index, params: { search: "ISO & SOC" }
      assert_response :success
      
      get :index, params: { search: "27001/2" }
      assert_response :success
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
    end
  end
end