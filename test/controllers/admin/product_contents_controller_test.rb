require "test_helper"

module Admin
  class ProductContentsControllerTest < ActionController::TestCase
    def setup
      # Create test users
      @admin_user = users(:admin)
      @vendor_user = users(:one)
      @admin_user.update!(super_admin: true)
      
      # Create test account and product
      @vendor_account = accounts(:one)
      @vendor_account.update!(
        account_type: 'vendor', 
        status: 'approved', 
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      @vendor_account.account_users.destroy_all
      @vendor_account.account_users.create!(user: @vendor_user, role: 'admin')
      
      @product = @vendor_account.products.create!(
        name: "Test Product",
        description: "A test product description",
        website: "https://test.com",
        published: false
      )
      
      # Create test product content
      @content1 = @product.product_content.build(
        title: "User Manual",
        description: "Comprehensive user manual for the product",
        position: 1,
        published: true
      )
      @content1.file.attach(
        io: StringIO.new("fake pdf data"),
        filename: "user_manual.pdf",
        content_type: "application/pdf"
      )
      @content1.save!
      
      @content2 = @product.product_content.build(
        title: "Product Demo Video",
        description: "Video demonstration of key features",
        position: 2,
        published: false
      )
      @content2.file.attach(
        io: StringIO.new("fake video data"),
        filename: "demo_video.mp4",
        content_type: "video/mp4"
      )
      @content2.save!
    end

    # ===============================
    # AUTHENTICATION & AUTHORIZATION TESTS
    # ===============================

    test "should require admin authentication for all actions" do
      get :index, params: { product_id: @product.slug }
      assert_redirected_to new_session_path
      
      get :show, params: { product_id: @product.slug, id: @content1.id }
      assert_redirected_to new_session_path
      
      get :new, params: { product_id: @product.slug }
      assert_redirected_to new_session_path
      
      post :create, params: { product_id: @product.slug, product_content: { title: "Test" } }
      assert_redirected_to new_session_path
    end

    test "should require admin privileges" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_redirected_to root_path
      
      get :show, params: { product_id: @product.slug, id: @content1.id }
      assert_redirected_to root_path
    end

    test "should allow admin users" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
    end

    # ===============================
    # INDEX ACTION TESTS
    # ===============================

    test "should show product contents index" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      assert_not_nil assigns(:product_contents)
      
      contents = assigns(:product_contents)
      assert contents.include?(@content1)
      assert contents.include?(@content2)
    end

    test "should order contents by position" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      contents = assigns(:product_contents).to_a
      assert_equal @content1, contents.first
      assert_equal @content2, contents.second
    end

    test "should set product from slug" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      assert_equal @product, assigns(:product)
    end

    # ===============================
    # SHOW ACTION TESTS
    # ===============================

    test "should show product content details" do
      sign_in @admin_user
      
      get :show, params: { product_id: @product.slug, id: @content1.id }
      assert_response :success
      assert_equal @content1, assigns(:product_content)
      assert_equal @product, assigns(:product)
    end

    test "should only show content for specified product" do
      sign_in @admin_user
      
      # Create another product with content
      other_product = @vendor_account.products.create!(
        name: "Other Product",
        description: "Another product",
        website: "https://other.com"
      )
      
      other_content = other_product.product_content.build(
        title: "Other Content",
        description: "Content for other product",
        position: 1
      )
      other_content.file.attach(
        io: StringIO.new("other content data"),
        filename: "other.pdf",
        content_type: "application/pdf"
      )
      other_content.save!
      
      # Should not be able to access other product's content through this product
      get :show, params: { product_id: @product.slug, id: other_content.id }
      assert_redirected_to errors_not_found_path
    end

    # ===============================
    # NEW ACTION TESTS
    # ===============================

    test "should show new product content form" do
      sign_in @admin_user
      
      get :new, params: { product_id: @product.slug }
      assert_response :success
      assert_instance_of ProductContent, assigns(:product_content)
      assert assigns(:product_content).new_record?
      assert_equal @product, assigns(:product_content).product
    end

    # ===============================
    # CREATE ACTION TESTS
    # ===============================

    test "should create product content with PDF file" do
      sign_in @admin_user
      
      file = fixture_file_upload('test/fixtures/files/test_document.pdf', 'application/pdf')
      content_params = {
        title: "New Document",
        description: "A new document for the product",
        file: file,
        published: true
      }
      
      assert_difference '@product.product_content.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_content: content_params
        }
      end
      
      assert_redirected_to admin_product_product_contents_path(@product)
      assert_match /Product content was successfully created/, flash[:notice]
      
      content = ProductContent.last
      assert_equal "New Document", content.title
      assert_equal "A new document for the product", content.description
      assert_equal @product, content.product
      assert content.file.attached?
      assert_equal "test_document.pdf", content.file.filename.to_s
      assert content.published?
    end

    test "should create product content with video file" do
      sign_in @admin_user
      
      file = fixture_file_upload('test/fixtures/files/test_video.mp4', 'video/mp4')
      content_params = {
        title: "Product Video",
        description: "Video showcasing the product",
        file: file,
        published: false
      }
      
      assert_difference '@product.product_content.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_content: content_params
        }
      end
      
      content = ProductContent.last
      assert_equal "Product Video", content.title
      assert content.file.attached?
      assert_equal "test_video.mp4", content.file.filename.to_s
      assert_equal 'video', content.file_type
      assert_not content.published?
    end

    test "should create product content with image file" do
      sign_in @admin_user
      
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      content_params = {
        title: "Product Image",
        description: "Image showing the product interface",
        file: file
      }
      
      assert_difference '@product.product_content.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_content: content_params
        }
      end
      
      content = ProductContent.last
      assert_equal "Product Image", content.title
      assert content.file.attached?
      assert_equal "test_image.png", content.file.filename.to_s
      assert_equal 'image', content.file_type
    end

    test "should not create product content with invalid data" do
      sign_in @admin_user
      
      invalid_params = {
        title: "",  # Required field
        description: "Valid description"
        # Missing required file
      }
      
      assert_no_difference 'ProductContent.count' do
        post :create, params: { 
          product_id: @product.slug,
          product_content: invalid_params
        }
      end
      
      assert_response :unprocessable_entity
      assert_not_nil assigns(:product_content)
      assert assigns(:product_content).errors.any?
    end

    test "should not create content without required file" do
      sign_in @admin_user
      
      content_params = {
        title: "Content Without File",
        description: "This should fail validation"
      }
      
      assert_no_difference 'ProductContent.count' do
        post :create, params: { 
          product_id: @product.slug,
          product_content: content_params
        }
      end
      
      assert_response :unprocessable_entity
      content = assigns(:product_content)
      assert content.errors[:file].any?
    end

    # ===============================
    # EDIT ACTION TESTS
    # ===============================

    test "should show edit product content form" do
      sign_in @admin_user
      
      get :edit, params: { product_id: @product.slug, id: @content1.id }
      assert_response :success
      assert_equal @content1, assigns(:product_content)
      assert_equal @product, assigns(:product)
    end

    # ===============================
    # UPDATE ACTION TESTS
    # ===============================

    test "should update product content with valid data" do
      sign_in @admin_user
      
      update_params = {
        title: "Updated Content Title",
        description: "Updated content description",
        published: false
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @content1.id,
        product_content: update_params
      }
      
      assert_redirected_to admin_product_product_contents_path(@product)
      assert_match /Product content was successfully updated/, flash[:notice]
      
      @content1.reload
      assert_equal "Updated Content Title", @content1.title
      assert_equal "Updated content description", @content1.description
      assert_not @content1.published?
    end

    test "should update product content file" do
      sign_in @admin_user
      
      new_file = fixture_file_upload('test/fixtures/files/test_image.jpg', 'image/jpeg')
      
      update_params = {
        title: @content1.title,
        description: @content1.description,
        file: new_file
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @content1.id,
        product_content: update_params
      }
      
      assert_redirected_to admin_product_product_contents_path(@product)
      
      @content1.reload
      assert @content1.file.attached?
      assert_equal "test_image.jpg", @content1.file.filename.to_s
      assert_equal 'image', @content1.file_type
    end

    test "should not update product content with invalid data" do
      sign_in @admin_user
      
      original_title = @content1.title
      
      invalid_params = {
        title: "",  # Invalid
        description: "Valid description"
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @content1.id,
        product_content: invalid_params
      }
      
      assert_response :unprocessable_entity
      assert_not_nil assigns(:product_content)
      assert assigns(:product_content).errors.any?
      
      @content1.reload
      assert_equal original_title, @content1.title
    end

    # ===============================
    # DESTROY ACTION TESTS
    # ===============================

    test "should delete product content" do
      sign_in @admin_user
      
      assert_difference '@product.product_content.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: @content1.id }
      end
      
      assert_redirected_to admin_product_product_contents_path(@product)
      assert_match /Product content was successfully deleted/, flash[:notice]
    end

    test "should delete attached file when destroying content" do
      sign_in @admin_user
      
      content_with_file = @product.product_content.build(
        title: "Content to Delete",
        description: "This content will be deleted",
        position: 3
      )
      content_with_file.file.attach(
        io: StringIO.new("file to delete"),
        filename: "delete_me.pdf",
        content_type: "application/pdf"
      )
      content_with_file.save!
      
      assert content_with_file.file.attached?
      
      delete :destroy, params: { product_id: @product.slug, id: content_with_file.id }
      
      assert_redirected_to admin_product_product_contents_path(@product)
    end

    # ===============================
    # UPDATE POSITION ACTION TESTS
    # ===============================

    test "should update content position" do
      sign_in @admin_user
      
      patch :update_position, params: { 
        product_id: @product.slug,
        id: @content1.id,
        position: 5
      }
      
      assert_response :ok
      
      @content1.reload
      assert_equal 5, @content1.position
    end

    test "should handle AJAX position updates" do
      sign_in @admin_user
      
      patch :update_position, params: { 
        product_id: @product.slug,
        id: @content2.id,
        position: 1
      }, xhr: true
      
      assert_response :ok
      
      @content2.reload
      assert_equal 1, @content2.position
    end

    # ===============================
    # PARAMETER SECURITY TESTS
    # ===============================

    test "should filter unpermitted parameters" do
      sign_in @admin_user
      
      file = fixture_file_upload('test/fixtures/files/test_document.pdf', 'application/pdf')
      malicious_params = {
        title: "Valid Title",
        description: "Valid description",
        file: file,
        published: true,
        product_id: @product.id + 1,  # Should be filtered/ignored
        created_at: 1.year.ago,  # Should be filtered
        position: 999  # Not in permit list
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_content: malicious_params
      }
      
      content = ProductContent.last
      assert_equal "Valid Title", content.title
      assert_equal @product, content.product  # Should use product from URL, not params
      assert_not_equal 1.year.ago.to_date, content.created_at.to_date
      # position should be auto-assigned, not from params
      assert_not_equal 999, content.position
    end

    test "should allow only permitted parameters" do
      sign_in @admin_user
      
      file = fixture_file_upload('test/fixtures/files/test_document.pdf', 'application/pdf')
      permitted_params = {
        title: "Permitted Content",
        description: "Permitted description",
        file: file,
        published: false
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_content: permitted_params
      }
      
      content = ProductContent.last
      assert_equal "Permitted Content", content.title
      assert_equal "Permitted description", content.description
      assert content.file.attached?
      assert_not content.published?
    end

    # ===============================
    # FILE VALIDATION TESTS
    # ===============================

    test "should reject invalid file types" do
      sign_in @admin_user
      
      # Create a fake file with invalid content type
      file = fixture_file_upload('test/fixtures/files/test_document.txt', 'text/plain')
      content_params = {
        title: "Invalid File Type",
        description: "This should fail validation",
        file: file
      }
      
      assert_no_difference 'ProductContent.count' do
        post :create, params: { 
          product_id: @product.slug,
          product_content: content_params
        }
      end
      
      assert_response :unprocessable_entity
      content = assigns(:product_content)
      assert content.errors[:file].any?
    end

    # ===============================
    # ERROR HANDLING TESTS
    # ===============================

    test "should handle invalid product id" do
      sign_in @admin_user
      
      get :index, params: { product_id: 'invalid-slug' }
      assert_redirected_to errors_not_found_path
    end

    test "should handle invalid content id" do
      sign_in @admin_user
      
      get :show, params: { product_id: @product.slug, id: 99999 }
      assert_redirected_to errors_not_found_path
    end

    test "should scope content to correct product" do
      sign_in @admin_user
      
      # Create another product
      other_product = @vendor_account.products.create!(
        name: "Other Product",
        description: "Another product",
        website: "https://other.com"
      )
      
      # Try to access @content1 through other_product (should fail)
      get :show, params: { product_id: other_product.slug, id: @content1.id }
      assert_redirected_to errors_not_found_path
    end

    # ===============================
    # INTEGRATION TESTS
    # ===============================

    test "should complete full content management workflow" do
      sign_in @admin_user
      
      # Step 1: View contents index
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      # Step 2: Visit new content page
      get :new, params: { product_id: @product.slug }
      assert_response :success
      
      # Step 3: Create content  
      file = fixture_file_upload('test/fixtures/files/test_document.pdf', 'application/pdf')
      content_params = {
        title: "Workflow Content",
        description: "Content created through workflow",
        file: file,
        published: false
      }
      
      assert_difference '@product.product_content.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_content: content_params
        }
      end
      
      content = ProductContent.last
      assert_redirected_to admin_product_product_contents_path(@product)
      
      # Step 4: View created content
      get :show, params: { product_id: @product.slug, id: content.id }
      assert_response :success
      assert_equal content, assigns(:product_content)
      
      # Step 5: Edit content
      get :edit, params: { product_id: @product.slug, id: content.id }
      assert_response :success
      
      # Step 6: Update content
      patch :update, params: { 
        product_id: @product.slug,
        id: content.id,
        product_content: { title: "Updated Workflow Content", published: true }
      }
      
      assert_redirected_to admin_product_product_contents_path(@product)
      content.reload
      assert_equal "Updated Workflow Content", content.title
      assert content.published?
      
      # Step 7: Update position
      patch :update_position, params: { 
        product_id: @product.slug,
        id: content.id,
        position: 1
      }
      
      assert_response :ok
      content.reload
      assert_equal 1, content.position
      
      # Step 8: Delete content
      assert_difference '@product.product_content.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: content.id }
      end
      
      assert_redirected_to admin_product_product_contents_path(@product)
    end

    test "should handle content management with validation errors" do
      sign_in @admin_user
      
      # Step 1: Try to create invalid content
      invalid_params = {
        title: "",  # Invalid
        description: "Valid description"
        # Missing required file
      }
      
      assert_no_difference 'ProductContent.count' do
        post :create, params: { 
          product_id: @product.slug,
          product_content: invalid_params
        }
      end
      
      assert_response :unprocessable_entity
      assert assigns(:product_content).errors.any?
      
      # Step 2: Fix errors and create successfully
      file = fixture_file_upload('test/fixtures/files/test_document.pdf', 'application/pdf')
      valid_params = {
        title: "Fixed Content Title",
        description: "Valid description",
        file: file
      }
      
      assert_difference 'ProductContent.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_content: valid_params
        }
      end
      
      assert_redirected_to admin_product_product_contents_path(@product)
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
    end
  end
end