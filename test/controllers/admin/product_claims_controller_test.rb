require "test_helper"

module Admin
  class ProductClaimsControllerTest < ActionController::TestCase
    def setup
      # Create test users
      @admin_user = users(:admin)
      @vendor_user = users(:one)
      @admin_user.update!(super_admin: true)
      
      # Create test account and product
      @vendor_account = accounts(:one)
      @vendor_account.update!(
        account_type: 'vendor', 
        status: 'approved', 
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      @vendor_account.account_users.destroy_all
      @vendor_account.account_users.create!(user: @vendor_user, role: 'admin')
      
      @product = @vendor_account.products.create!(
        name: "Test Product",
        description: "A test product description",
        website: "https://test.com",
        published: true
      )
      
      
      # Create test product claims
      @claim1 = @product.product_claims.create!(
        name: "<PERSON>",
        email: "<EMAIL>",
        company: "Acme Corporation",
        title: "CTO",
        message: "I would like to claim this product for our company.",
        status: "pending"
      )
      
      @claim2 = @product.product_claims.create!(
        name: "<PERSON>",
        email: "<EMAIL>",
        company: "TechStart Inc",
        title: "CEO",
        message: "This is our product, please transfer ownership.",
        status: "reviewed"
      )
      
      @claim3 = @product.product_claims.create!(
        name: "<PERSON>",
        email: "<EMAIL>",
        company: "Enterprise Corp",
        title: "Product Manager",
        message: "We own this product and need administrative access.",
        status: "approved"
      )
    end

    # ===============================
    # AUTHENTICATION & AUTHORIZATION TESTS
    # ===============================

    test "should require admin authentication for all actions" do
      get :index
      assert_redirected_to new_session_path
      
      get :show, params: { id: @claim1.id }
      assert_redirected_to new_session_path
    end

    test "should require admin privileges" do
      sign_in @vendor_user
      
      get :index
      assert_redirected_to root_path
      
      get :show, params: { id: @claim1.id }
      assert_redirected_to root_path
    end

    test "should allow admin users" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      
      get :show, params: { id: @claim1.id }
      assert_response :success
    end

    # ===============================
    # INDEX ACTION TESTS
    # ===============================

    test "should show product claims index" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      assert_not_nil assigns(:product_claims)
      
      claims = assigns(:product_claims)
      assert claims.include?(@claim1)
      assert claims.include?(@claim2)
      assert claims.include?(@claim3)
    end

    test "should order claims by created_at desc" do
      sign_in @admin_user
      
      # Create a newer claim
      newer_claim = @product.product_claims.create!(
        name: "New Claimant",
        email: "<EMAIL>",
        company: "New Company",
        title: "Manager",
        message: "Recent claim request",
        status: "pending"
      )
      
      get :index
      assert_response :success
      
      claims = assigns(:product_claims).to_a
      assert_equal newer_claim, claims.first
    end

    test "should include product information" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      
      claims = assigns(:product_claims)
      claims.each do |claim|
        assert_not_nil claim.product
        assert_not_nil claim.product.name
      end
    end

    test "should filter by status when provided" do
      sign_in @admin_user
      
      # Test filtering by pending status
      get :index, params: { status: 'pending' }
      assert_response :success
      
      claims = assigns(:product_claims).to_a
      assert claims.include?(@claim1)
      assert_not claims.include?(@claim2)  # reviewed status
      assert_not claims.include?(@claim3)  # approved status
      
      # Test filtering by reviewed status
      get :index, params: { status: 'reviewed' }
      assert_response :success
      
      claims = assigns(:product_claims).to_a
      assert_not claims.include?(@claim1)  # pending status
      assert claims.include?(@claim2)
      assert_not claims.include?(@claim3)  # approved status
    end

    test "should ignore invalid status filter" do
      sign_in @admin_user
      
      get :index, params: { status: 'invalid_status' }
      assert_response :success
      
      # Should show all claims when invalid status is provided
      claims = assigns(:product_claims)
      assert claims.include?(@claim1)
      assert claims.include?(@claim2)
      assert claims.include?(@claim3)
    end

    test "should handle pagination" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      assert_not_nil assigns(:pagy)
      assert_not_nil assigns(:product_claims)
    end

    # ===============================
    # SHOW ACTION TESTS
    # ===============================

    test "should show product claim details" do
      sign_in @admin_user
      
      get :show, params: { id: @claim1.id }
      assert_response :success
      assert_equal @claim1, assigns(:product_claim)
    end

    test "should show claim with all required fields" do
      sign_in @admin_user
      
      get :show, params: { id: @claim1.id }
      assert_response :success
      
      claim = assigns(:product_claim)
      assert_equal "John Doe", claim.name
      assert_equal "<EMAIL>", claim.email
      assert_equal "Acme Corporation", claim.company
      assert_equal "CTO", claim.title
      assert_equal "I would like to claim this product for our company.", claim.message
      assert_equal "pending", claim.status
      assert_equal @product, claim.product
    end

    test "should show claims for different products" do
      sign_in @admin_user
      
      # Create another product with a claim
      other_product = @vendor_account.products.create!(
        name: "Other Product",
        description: "Another product",
        website: "https://other.com",
        published: true
      )
      
      other_claim = other_product.product_claims.create!(
        name: "Other Claimant",
        email: "<EMAIL>",
        company: "Other Company",
        title: "Director",
        message: "Claim for other product",
        status: "pending"
      )
      
      get :show, params: { id: other_claim.id }
      assert_response :success
      assert_equal other_claim, assigns(:product_claim)
      assert_equal other_product, assigns(:product_claim).product
    end

    # ===============================
    # ERROR HANDLING TESTS
    # ===============================

    test "should handle invalid claim id" do
      sign_in @admin_user
      
      get :show, params: { id: 99999 }
      assert_redirected_to errors_not_found_path
    end

    test "should handle non-numeric claim id" do
      sign_in @admin_user
      
      get :show, params: { id: 'invalid' }
      assert_redirected_to errors_not_found_path
    end

    # ===============================
    # STATUS FILTERING TESTS
    # ===============================

    test "should show only pending claims when filtered" do
      sign_in @admin_user
      
      get :index, params: { status: 'pending' }
      assert_response :success
      
      claims = assigns(:product_claims).to_a
      claims.each do |claim|
        assert_equal 'pending', claim.status
      end
    end

    test "should show only reviewed claims when filtered" do
      sign_in @admin_user
      
      get :index, params: { status: 'reviewed' }
      assert_response :success
      
      claims = assigns(:product_claims).to_a
      claims.each do |claim|
        assert_equal 'reviewed', claim.status
      end
    end

    test "should show only approved claims when filtered" do
      sign_in @admin_user
      
      get :index, params: { status: 'approved' }
      assert_response :success
      
      claims = assigns(:product_claims).to_a
      claims.each do |claim|
        assert_equal 'approved', claim.status
      end
    end

    test "should show only rejected claims when filtered" do
      sign_in @admin_user
      
      # Create a rejected claim
      rejected_claim = @product.product_claims.create!(
        name: "Rejected User",
        email: "<EMAIL>",
        company: "Rejected Company",
        title: "Manager",
        message: "This will be rejected",
        status: "rejected"
      )
      
      get :index, params: { status: 'rejected' }
      assert_response :success
      
      claims = assigns(:product_claims).to_a
      assert claims.include?(rejected_claim)
      claims.each do |claim|
        assert_equal 'rejected', claim.status
      end
    end

    # ===============================
    # CROSS-ACCOUNT ACCESS TESTS
    # ===============================

    test "should show claims from all vendor accounts" do
      sign_in @admin_user
      
      # Create another vendor account with product and claim
      other_vendor_account = accounts(:two)
      other_vendor_user = users(:two)
      
      other_vendor_account.update!(
        account_type: 'vendor',
        status: 'approved',
        confirmed_at: Time.current,
        owner: other_vendor_user
      )
      
      other_product = other_vendor_account.products.create!(
        name: "Other Vendor Product",
        description: "Product from different vendor",
        website: "https://othervendor.com",
        published: true
      )
      
      other_claim = other_product.product_claims.create!(
        name: "Cross Account Claimant",
        email: "<EMAIL>",
        company: "Cross Company",
        title: "Executive",
        message: "Claim from different vendor's product",
        status: "pending"
      )
      
      get :index
      assert_response :success
      
      claims = assigns(:product_claims)
      assert claims.include?(@claim1)
      assert claims.include?(other_claim)
    end

    # ===============================
    # INTEGRATION TESTS
    # ===============================

    test "should complete admin claim review workflow" do
      sign_in @admin_user
      
      # Step 1: View all claims
      get :index
      assert_response :success
      
      claims = assigns(:product_claims)
      assert claims.include?(@claim1)
      assert claims.include?(@claim2)
      assert claims.include?(@claim3)
      
      # Step 2: Filter to see only pending claims
      get :index, params: { status: 'pending' }
      assert_response :success
      
      pending_claims = assigns(:product_claims).to_a
      assert pending_claims.include?(@claim1)
      assert_not pending_claims.include?(@claim2)  # reviewed
      assert_not pending_claims.include?(@claim3)  # approved
      
      # Step 3: View specific claim details
      get :show, params: { id: @claim1.id }
      assert_response :success
      
      claim = assigns(:product_claim)
      assert_equal @claim1, claim
      assert_equal "pending", claim.status
      assert_not_nil claim.product
      assert_not_nil claim.name
      assert_not_nil claim.email
      assert_not_nil claim.company
      assert_not_nil claim.message
    end

    test "should handle empty claims list" do
      sign_in @admin_user
      
      # Delete all claims
      ProductClaim.destroy_all
      
      get :index
      assert_response :success
      assert_not_nil assigns(:product_claims)
      assert_empty assigns(:product_claims)
    end

    test "should handle claims with different statuses in filter workflow" do
      sign_in @admin_user
      
      # Test each status filter
      %w[pending reviewed approved rejected].each do |status|
        get :index, params: { status: status }
        assert_response :success
        
        claims = assigns(:product_claims).to_a
        claims.each do |claim|
          assert_equal status, claim.status
        end
      end
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
    end
  end
end