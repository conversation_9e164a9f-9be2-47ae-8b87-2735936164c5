require "test_helper"

module Admin
  class AccountInvitationsControllerTest < ActionController::TestCase
    include ActionMailer::<PERSON><PERSON><PERSON><PERSON>
    def setup
      # Clear existing account invitations to avoid conflicts
      AccountInvitation.destroy_all
      
      # Create test users
      @admin_user = users(:admin)
      @vendor_user = users(:one)
      @admin_user.update!(super_admin: true)
      
      # Create test account invitations
      @invitation1 = AccountInvitation.create!(
        email: "<EMAIL>",
        account_type: "vendor",
        account_name: "Acme Corporation",
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON>",
        invited_by: @admin_user,
        status: "pending",
        expires_at: 5.days.from_now
      )
      
      @invitation2 = AccountInvitation.new(
        email: "<EMAIL>",
        account_type: "agency",
        account_name: "Government Agency",
        first_name: "<PERSON>",
        last_name: "<PERSON>",
        invited_by: @admin_user,
        expires_at: 3.days.from_now
      )
      @invitation2.save!
      @invitation2.update!(status: "accepted")  # Update after creation to override default
      
      @expired_invitation = AccountInvitation.new(
        email: "<EMAIL>",
        account_type: "vendor",
        account_name: "Expired Corp",
        first_name: "Expired",
        last_name: "User",
        invited_by: @admin_user,
        expires_at: 1.day.ago
      )
      @expired_invitation.save!
      @expired_invitation.update!(status: "expired")  # Update after creation to override default
    end

    # ===============================
    # AUTHENTICATION & AUTHORIZATION TESTS
    # ===============================

    test "should require admin authentication for all actions" do
      get :index
      assert_redirected_to new_session_path
      
      get :new
      assert_redirected_to new_session_path
      
      post :create, params: { account_invitation: { email: "<EMAIL>" } }
      assert_redirected_to new_session_path
    end

    test "should require admin privileges" do
      sign_in @vendor_user
      
      get :index
      assert_redirected_to root_path
      
      get :new
      assert_redirected_to root_path
    end

    test "should allow admin users" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      
      get :new
      assert_response :success
    end

    # ===============================
    # INDEX ACTION TESTS
    # ===============================

    test "should show account invitations index" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      assert_not_nil assigns(:invitations)
      
      invitations = assigns(:invitations)
      assert invitations.include?(@invitation1)
      assert invitations.include?(@invitation2)
      assert invitations.include?(@expired_invitation)
    end

    test "should order invitations by created_at desc" do
      sign_in @admin_user
      
      # Create a newer invitation
      newer_invitation = AccountInvitation.create!(
        email: "<EMAIL>",
        account_type: "vendor",
        account_name: "New Company",
        first_name: "New",
        last_name: "User",
        invited_by: @admin_user
      )
      
      get :index
      assert_response :success
      
      invitations = assigns(:invitations).to_a
      assert_equal newer_invitation, invitations.first
    end

    test "should include invited_by information" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      
      invitations = assigns(:invitations)
      invitations.each do |invitation|
        assert_not_nil invitation.invited_by
        assert_not_nil invitation.invited_by.email_address
      end
    end

    test "should filter by status when provided" do
      sign_in @admin_user
      
      # Test filtering by pending status
      get :index, params: { status: 'pending' }
      assert_response :success
      
      invitations = assigns(:invitations).to_a
      assert invitations.include?(@invitation1), "Should include pending invitation"
      refute invitations.include?(@invitation2), "Should not include accepted invitation"  
      refute invitations.include?(@expired_invitation), "Should not include expired invitation"
      
      # Test filtering by accepted status
      get :index, params: { status: 'accepted' }
      assert_response :success
      
      invitations = assigns(:invitations).to_a
      refute invitations.include?(@invitation1)  # pending status
      assert invitations.include?(@invitation2)
      refute invitations.include?(@expired_invitation)  # expired status
    end

    test "should ignore invalid status filter" do
      sign_in @admin_user
      
      get :index, params: { status: 'invalid_status' }
      assert_response :success
      
      # Should show all invitations when invalid status is provided
      invitations = assigns(:invitations)
      assert invitations.include?(@invitation1)
      assert invitations.include?(@invitation2)
      assert invitations.include?(@expired_invitation)
    end

    test "should search invitations by email" do
      sign_in @admin_user
      
      get :index, params: { search: 'john.doe' }
      assert_response :success
      
      invitations = assigns(:invitations).to_a
      assert invitations.include?(@invitation1)
      assert_not invitations.include?(@invitation2)
      assert_not invitations.include?(@expired_invitation)
    end

    test "should search invitations by account name" do
      sign_in @admin_user
      
      get :index, params: { search: 'Acme' }
      assert_response :success
      
      invitations = assigns(:invitations).to_a
      assert invitations.include?(@invitation1)
      assert_not invitations.include?(@invitation2)
      assert_not invitations.include?(@expired_invitation)
    end

    test "should search invitations by first or last name" do
      sign_in @admin_user
      
      get :index, params: { search: 'Jane' }
      assert_response :success
      
      invitations = assigns(:invitations).to_a
      assert_not invitations.include?(@invitation1)
      assert invitations.include?(@invitation2)
      assert_not invitations.include?(@expired_invitation)
    end


    # ===============================
    # NEW ACTION TESTS
    # ===============================

    test "should show new account invitation form" do
      sign_in @admin_user
      
      get :new
      assert_response :success
      assert_instance_of AccountInvitation, assigns(:invitation)
      assert assigns(:invitation).new_record?
    end

    # ===============================
    # CREATE ACTION TESTS
    # ===============================

    test "should create account invitation with valid data" do
      sign_in @admin_user
      
      invitation_params = {
        email: "<EMAIL>",
        account_type: "vendor",
        account_name: "New Vendor Corp",
        first_name: "New",
        last_name: "User"
      }
      
      assert_difference 'AccountInvitation.count', 1 do
        assert_emails 1 do
          post :create, params: { account_invitation: invitation_params }
        end
      end
      
      assert_redirected_to admin_account_invitations_path
      assert_match /Account invitation sent successfully/, flash[:notice]
      
      invitation = AccountInvitation.last
      assert_equal "<EMAIL>", invitation.email
      assert_equal "vendor", invitation.account_type
      assert_equal "New Vendor Corp", invitation.account_name
      assert_equal "New", invitation.first_name
      assert_equal "User", invitation.last_name
      assert_equal @admin_user, invitation.invited_by
      assert_equal "pending", invitation.status
      assert invitation.expires_at > Time.current
      assert_not_nil invitation.token
    end

    test "should create agency invitation" do
      sign_in @admin_user
      
      invitation_params = {
        email: "<EMAIL>",
        account_type: "agency",
        account_name: "Federal Agency",
        first_name: "Agency",
        last_name: "Admin"
      }
      
      assert_difference 'AccountInvitation.count', 1 do
        post :create, params: { account_invitation: invitation_params }
      end
      
      invitation = AccountInvitation.last
      assert_equal "agency", invitation.account_type
      assert_equal "Federal Agency", invitation.account_name
    end

    test "should not create invitation with invalid data" do
      sign_in @admin_user
      
      invalid_params = {
        email: "",  # Required field
        account_type: "vendor",
        account_name: "Valid Company",
        first_name: "Valid",
        last_name: "User"
      }
      
      assert_no_difference 'AccountInvitation.count' do
        assert_no_emails do
          post :create, params: { account_invitation: invalid_params }
        end
      end
      
      assert_response :unprocessable_entity
      assert_not_nil assigns(:invitation)
      assert assigns(:invitation).errors.any?
    end

    test "should not create invitation with invalid account type" do
      sign_in @admin_user
      
      invalid_params = {
        email: "<EMAIL>",
        account_type: "invalid_type",
        account_name: "Valid Company",
        first_name: "Valid",
        last_name: "User"
      }
      
      assert_no_difference 'AccountInvitation.count' do
        post :create, params: { account_invitation: invalid_params }
      end
      
      assert_response :unprocessable_entity
      invitation = assigns(:invitation)
      assert invitation.errors[:account_type].any?
    end

    test "should not create invitation with duplicate email" do
      sign_in @admin_user
      
      duplicate_params = {
        email: @invitation1.email,  # Already exists
        account_type: "vendor",
        account_name: "Another Company",
        first_name: "Another",
        last_name: "User"
      }
      
      assert_no_difference 'AccountInvitation.count' do
        post :create, params: { account_invitation: duplicate_params }
      end
      
      assert_response :unprocessable_entity
      invitation = assigns(:invitation)
      assert invitation.errors[:email].any?
    end

    # ===============================
    # DESTROY ACTION TESTS
    # ===============================

    test "should delete account invitation" do
      sign_in @admin_user
      
      assert_difference 'AccountInvitation.count', -1 do
        delete :destroy, params: { id: @invitation1.id }
      end
      
      assert_redirected_to admin_account_invitations_path
      assert_match /Invitation deleted successfully/, flash[:notice]
    end

    test "should delete any status invitation" do
      sign_in @admin_user
      
      # Delete accepted invitation
      assert_difference 'AccountInvitation.count', -1 do
        delete :destroy, params: { id: @invitation2.id }
      end
      
      assert_redirected_to admin_account_invitations_path
      
      # Delete expired invitation
      assert_difference 'AccountInvitation.count', -1 do
        delete :destroy, params: { id: @expired_invitation.id }
      end
      
      assert_redirected_to admin_account_invitations_path
    end

    # ===============================
    # RESEND ACTION TESTS
    # ===============================

    test "should resend pending non-expired invitation" do
      sign_in @admin_user
      
      assert_emails 1 do
        patch :resend, params: { id: @invitation1.id }
      end
      
      assert_redirected_to admin_account_invitations_path
      assert_match /Invitation resent successfully/, flash[:notice]
    end

    test "should not resend accepted invitation" do
      sign_in @admin_user
      
      # Ensure invitation is accepted
      @invitation2.update!(status: 'accepted')
      
      assert_no_emails do
        patch :resend, params: { id: @invitation2.id }
      end
      
      assert_redirected_to admin_account_invitations_path
      assert_match /Cannot resend expired or accepted invitation/, flash[:alert]
    end

    test "should not resend expired invitation" do
      sign_in @admin_user
      
      # Ensure invitation is expired 
      @expired_invitation.update!(status: 'expired', expires_at: 1.day.ago)
      
      assert_no_emails do
        patch :resend, params: { id: @expired_invitation.id }
      end
      
      assert_redirected_to admin_account_invitations_path
      assert_match /Cannot resend expired or accepted invitation/, flash[:alert]
    end

    # ===============================
    # PARAMETER SECURITY TESTS
    # ===============================

    test "should filter unpermitted parameters" do
      sign_in @admin_user
      
      malicious_params = {
        email: "<EMAIL>",
        account_type: "vendor",
        account_name: "Secure Company",
        first_name: "Secure",
        last_name: "User",
        status: "accepted",  # Should be filtered
        token: "malicious_token",  # Should be filtered
        invited_by_id: @vendor_user.id,  # Should be filtered
        expires_at: 1.year.from_now  # Should be filtered
      }
      
      post :create, params: { account_invitation: malicious_params }
      
      invitation = AccountInvitation.last
      assert_equal "<EMAIL>", invitation.email
      assert_equal @admin_user, invitation.invited_by  # Should use current_user
      assert_equal "pending", invitation.status  # Should use default
      assert_not_equal "malicious_token", invitation.token
      assert_not_equal 1.year.from_now.to_date, invitation.expires_at.to_date
    end

    test "should allow only permitted parameters" do
      sign_in @admin_user
      
      permitted_params = {
        email: "<EMAIL>",
        account_type: "agency",
        account_name: "Permitted Agency",
        first_name: "Permitted",
        last_name: "User"
      }
      
      post :create, params: { account_invitation: permitted_params }
      
      invitation = AccountInvitation.last
      assert_equal "<EMAIL>", invitation.email
      assert_equal "agency", invitation.account_type
      assert_equal "Permitted Agency", invitation.account_name
      assert_equal "Permitted", invitation.first_name
      assert_equal "User", invitation.last_name
    end

    # ===============================
    # ERROR HANDLING TESTS
    # ===============================

    test "should handle invalid invitation id for destroy" do
      sign_in @admin_user
      
      delete :destroy, params: { id: 99999 }
      assert_redirected_to errors_not_found_path
    end

    test "should handle non-numeric invitation id for resend" do
      sign_in @admin_user
      
      patch :resend, params: { id: 'invalid' }
      assert_redirected_to errors_not_found_path
    end

    # ===============================
    # COMBINED FILTER TESTS
    # ===============================

    test "should apply both status filter and search" do
      sign_in @admin_user
      
      get :index, params: { status: 'pending', search: 'john' }
      assert_response :success
      
      invitations = assigns(:invitations).to_a
      assert invitations.include?(@invitation1)  # pending and matches john
      assert_not invitations.include?(@invitation2)  # accepted, not pending
      assert_not invitations.include?(@expired_invitation)  # expired, not pending
    end

    # ===============================
    # INTEGRATION TESTS
    # ===============================

    test "should complete full invitation management workflow" do
      sign_in @admin_user
      
      # Step 1: View invitations index
      get :index
      assert_response :success
      
      # Step 2: Visit new invitation page
      get :new
      assert_response :success
      
      # Step 3: Create invitation
      invitation_params = {
        email: "<EMAIL>",
        account_type: "vendor",
        account_name: "Workflow Company",
        first_name: "Workflow",
        last_name: "User"
      }
      
      assert_difference 'AccountInvitation.count', 1 do
        assert_emails 1 do
          post :create, params: { account_invitation: invitation_params }
        end
      end
      
      invitation = AccountInvitation.last
      assert_redirected_to admin_account_invitations_path
      
      # Step 4: Resend invitation
      assert_emails 1 do
        patch :resend, params: { id: invitation.id }
      end
      
      assert_redirected_to admin_account_invitations_path
      
      # Step 5: Delete invitation
      assert_difference 'AccountInvitation.count', -1 do
        delete :destroy, params: { id: invitation.id }
      end
      
      assert_redirected_to admin_account_invitations_path
    end

    test "should handle invitation management with validation errors" do
      sign_in @admin_user
      
      # Step 1: Try to create invalid invitation
      invalid_params = {
        email: "",  # Invalid
        account_type: "vendor",
        account_name: "Valid Company",
        first_name: "Valid",
        last_name: "User"
      }
      
      assert_no_difference 'AccountInvitation.count' do
        post :create, params: { account_invitation: invalid_params }
      end
      
      assert_response :unprocessable_entity
      assert assigns(:invitation).errors.any?
      
      # Step 2: Fix errors and create successfully
      valid_params = {
        email: "<EMAIL>",
        account_type: "vendor",
        account_name: "Valid Company",
        first_name: "Valid",
        last_name: "User"
      }
      
      assert_difference 'AccountInvitation.count', 1 do
        post :create, params: { account_invitation: valid_params }
      end
      
      assert_redirected_to admin_account_invitations_path
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
    end
  end
end