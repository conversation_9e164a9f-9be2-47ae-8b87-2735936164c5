require "test_helper"

module Admin
  class ProductFeaturesControllerTest < ActionController::TestCase
    def setup
      # Create test users
      @admin_user = users(:admin)
      @vendor_user = users(:one)
      @admin_user.update!(super_admin: true)
      
      # Create test account and product
      @vendor_account = accounts(:one)
      @vendor_account.update!(
        account_type: 'vendor', 
        status: 'approved', 
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      @vendor_account.account_users.destroy_all
      @vendor_account.account_users.create!(user: @vendor_user, role: 'admin')
      
      @product = @vendor_account.products.create!(
        name: "Test Product",
        description: "A test product description",
        website: "https://test.com",
        published: false
      )
      
      # Create test product features
      @feature1 = @product.product_features.create!(
        name: "Advanced Analytics",
        description: "Comprehensive analytics dashboard",
        position: 1,
        published: true
      )
      
      @feature2 = @product.product_features.create!(
        name: "Real-time Collaboration",
        description: "Work together in real-time",
        position: 2,
        published: false
      )
    end

    # ===============================
    # AUTHENTICATION & AUTHORIZATION TESTS
    # ===============================

    test "should require admin authentication for all actions" do
      get :index, params: { product_id: @product.slug }
      assert_redirected_to new_session_path
      
      get :show, params: { product_id: @product.slug, id: @feature1.id }
      assert_redirected_to new_session_path
      
      post :create, params: { product_id: @product.slug, product_feature: { name: "Test" } }
      assert_redirected_to new_session_path
    end

    test "should require admin privileges" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_redirected_to root_path
    end

    test "should allow admin users" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
    end

    # ===============================
    # INDEX ACTION TESTS
    # ===============================

    test "should show product features index" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      assert_not_nil assigns(:product_features)
      
      features = assigns(:product_features)
      assert features.include?(@feature1)
      assert features.include?(@feature2)
    end

    test "should order features by position" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      features = assigns(:product_features).to_a
      assert_equal @feature1, features.first
      assert_equal @feature2, features.second
    end

    # ===============================
    # SHOW ACTION TESTS
    # ===============================

    test "should show product feature details" do
      sign_in @admin_user
      
      get :show, params: { product_id: @product.slug, id: @feature1.id }
      assert_response :success
      assert_equal @feature1, assigns(:product_feature)
      assert_equal @product, assigns(:product)
    end

    # ===============================
    # NEW ACTION TESTS
    # ===============================

    test "should show new product feature form" do
      sign_in @admin_user
      
      get :new, params: { product_id: @product.slug }
      assert_response :success
      assert_instance_of ProductFeature, assigns(:product_feature)
      assert assigns(:product_feature).new_record?
    end

    # ===============================
    # CREATE ACTION TESTS
    # ===============================

    test "should create product feature with valid data" do
      sign_in @admin_user
      
      feature_params = {
        name: "New Feature",
        description: "A new feature description",
        published: true,
        position: 3
      }
      
      assert_difference '@product.product_features.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_feature: feature_params
        }
      end
      
      assert_redirected_to admin_product_product_features_path(@product)
      assert_match /Product feature was successfully created/, flash[:notice]
      
      feature = ProductFeature.last
      assert_equal "New Feature", feature.name
      assert_equal "A new feature description", feature.description
      assert_equal @product, feature.product
      assert feature.published?
      assert_equal 3, feature.position
    end

    test "should create feature with attachment" do
      sign_in @admin_user
      
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      feature_params = {
        name: "Feature with Image",
        description: "Feature with attached image",
        attachment: file
      }
      
      assert_difference '@product.product_features.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_feature: feature_params
        }
      end
      
      feature = ProductFeature.last
      assert feature.attachment.attached?
      assert_equal "test_image.png", feature.attachment.filename.to_s
      assert feature.image?
    end

    test "should auto-assign position when not provided" do
      sign_in @admin_user
      
      feature_params = {
        name: "Auto Position Feature",
        description: "Should get auto-assigned position"
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_feature: feature_params
      }
      
      feature = ProductFeature.last
      assert_equal 3, feature.position  # Should be after existing features (1, 2)
    end

    test "should not create product feature with invalid data" do
      sign_in @admin_user
      
      invalid_params = {
        name: "",  # Required field
        description: "Valid description"
      }
      
      assert_no_difference 'ProductFeature.count' do
        post :create, params: { 
          product_id: @product.slug,
          product_feature: invalid_params
        }
      end
      
      assert_response :unprocessable_entity
      assert assigns(:product_feature).errors.any?
    end

    # ===============================
    # UPDATE ACTION TESTS
    # ===============================

    test "should update product feature with valid data" do
      sign_in @admin_user
      
      update_params = {
        name: "Updated Feature Name",
        description: "Updated feature description",
        published: false,
        position: 5
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @feature1.id,
        product_feature: update_params
      }
      
      assert_redirected_to admin_product_product_features_path(@product)
      assert_match /Product feature was successfully updated/, flash[:notice]
      
      @feature1.reload
      assert_equal "Updated Feature Name", @feature1.name
      assert_equal "Updated feature description", @feature1.description
      assert_not @feature1.published?
      assert_equal 5, @feature1.position
    end

    # ===============================
    # DESTROY ACTION TESTS
    # ===============================

    test "should delete product feature" do
      sign_in @admin_user
      
      assert_difference '@product.product_features.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: @feature1.id }
      end
      
      assert_redirected_to admin_product_product_features_path(@product)
      assert_match /Product feature was successfully deleted/, flash[:notice]
    end

    # ===============================
    # UPDATE POSITION ACTION TESTS
    # ===============================

    test "should update feature position" do
      sign_in @admin_user
      
      patch :update_position, params: { 
        product_id: @product.slug,
        id: @feature1.id,
        position: 5
      }
      
      assert_response :ok
      
      @feature1.reload
      assert_equal 5, @feature1.position
    end

    # ===============================
    # PARAMETER SECURITY TESTS
    # ===============================

    test "should filter unpermitted parameters" do
      sign_in @admin_user
      
      malicious_params = {
        name: "Valid Name",
        description: "Valid description",
        published: true,
        product_id: @product.id + 1,  # Should be filtered/ignored
        created_at: 1.year.ago  # Should be filtered
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_feature: malicious_params
      }
      
      feature = ProductFeature.last
      assert_equal "Valid Name", feature.name
      assert_equal @product, feature.product  # Should use product from URL
      assert_not_equal 1.year.ago.to_date, feature.created_at.to_date
    end

    # ===============================
    # INTEGRATION TESTS
    # ===============================

    test "should complete full feature management workflow" do
      sign_in @admin_user
      
      # Step 1: View features index
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      # Step 2: Create feature
      feature_params = {
        name: "Workflow Feature",
        description: "Feature created through workflow",
        published: false
      }
      
      assert_difference '@product.product_features.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_feature: feature_params
        }
      end
      
      feature = ProductFeature.last
      assert_redirected_to admin_product_product_features_path(@product)
      
      # Step 3: Update feature to published
      patch :update, params: { 
        product_id: @product.slug,
        id: feature.id,
        product_feature: { published: true }
      }
      
      feature.reload
      assert feature.published?
      
      # Step 4: Update position
      patch :update_position, params: { 
        product_id: @product.slug,
        id: feature.id,
        position: 1
      }
      
      feature.reload
      assert_equal 1, feature.position
      
      # Step 5: Delete feature
      assert_difference '@product.product_features.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: feature.id }
      end
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
    end
  end
end