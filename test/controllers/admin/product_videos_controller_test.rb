require "test_helper"

module Admin
  class ProductVideosControllerTest < ActionController::TestCase
    def setup
      # Create test users
      @admin_user = users(:admin)
      @vendor_user = users(:one)
      @admin_user.update!(super_admin: true)
      
      # Create test accounts
      @vendor_account = accounts(:one)
      @vendor_account.update!(
        account_type: 'vendor', 
        status: 'approved', 
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      @vendor_account.account_users.destroy_all
      @vendor_account.account_users.create!(user: @vendor_user, role: 'admin')
      
      # Create test product
      @product = @vendor_account.products.create!(
        name: "Test Product",
        description: "A test product description",
        website: "https://test.com",
        published: false
      )
      
      # Create test product videos
      @video1 = @product.product_videos.build(
        title: "Demo Video",
        description: "A demo video description",
        position: 1,
        published: true
      )
      @video1.video_file.attach(
        io: StringIO.new("fake video data"),
        filename: "demo.mp4",
        content_type: "video/mp4"
      )
      @video1.save!
      
      @video2 = @product.product_videos.build(
        title: "Tutorial Video",
        description: "A tutorial video description",
        position: 2,
        published: false
      )
      @video2.video_file.attach(
        io: StringIO.new("fake video data 2"),
        filename: "tutorial.mp4",
        content_type: "video/mp4"
      )
      @video2.save!
    end

    # ===============================
    # AUTHENTICATION & AUTHORIZATION TESTS
    # ===============================

    test "should require admin authentication for all actions" do
      get :index, params: { product_id: @product.slug }
      assert_redirected_to new_session_path
      
      get :show, params: { product_id: @product.slug, id: @video1.id }
      assert_redirected_to new_session_path
      
      get :new, params: { product_id: @product.slug }
      assert_redirected_to new_session_path
      
      post :create, params: { product_id: @product.slug, product_video: { title: "Test" } }
      assert_redirected_to new_session_path
    end

    test "should require admin privileges" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_redirected_to root_path
      
      get :show, params: { product_id: @product.slug, id: @video1.id }
      assert_redirected_to root_path
    end

    test "should allow admin users" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
    end

    # ===============================
    # INDEX ACTION TESTS
    # ===============================

    test "should show product videos index" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      assert_not_nil assigns(:product_videos)
      
      videos = assigns(:product_videos)
      assert videos.include?(@video1)
      assert videos.include?(@video2)
    end

    test "should order videos by position" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      videos = assigns(:product_videos).to_a
      assert_equal @video1, videos.first
      assert_equal @video2, videos.second
    end

    test "should set product from slug" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      assert_equal @product, assigns(:product)
    end

    # ===============================
    # SHOW ACTION TESTS
    # ===============================

    test "should show product video details" do
      sign_in @admin_user
      
      get :show, params: { product_id: @product.slug, id: @video1.id }
      assert_response :success
      assert_equal @video1, assigns(:product_video)
      assert_equal @product, assigns(:product)
    end

    test "should only show videos for specified product" do
      sign_in @admin_user
      
      # Create another product with videos
      other_product = @vendor_account.products.create!(
        name: "Other Product",
        description: "Another product",
        website: "https://other.com"
      )
      
      other_video = other_product.product_videos.build(
        title: "Other Video",
        description: "Video for other product",
        position: 1
      )
      other_video.video_file.attach(
        io: StringIO.new("other video data"),
        filename: "other.mp4",
        content_type: "video/mp4"
      )
      other_video.save!
      
      # Should not be able to access other product's video through this product
      assert_raises(ActiveRecord::RecordNotFound) do
        get :show, params: { product_id: @product.slug, id: other_video.id }
      end
    end

    # ===============================
    # NEW ACTION TESTS
    # ===============================

    test "should show new product video form" do
      sign_in @admin_user
      
      get :new, params: { product_id: @product.slug }
      assert_response :success
      assert_instance_of ProductVideo, assigns(:product_video)
      assert assigns(:product_video).new_record?
      assert_equal @product, assigns(:product_video).product
    end

    # ===============================
    # CREATE ACTION TESTS
    # ===============================

    test "should create product video with valid data" do
      sign_in @admin_user
      
      file = fixture_file_upload('test/fixtures/files/test_video.mp4', 'video/mp4')
      video_params = {
        title: "New Video",
        description: "A new video description",
        video_file: file,
        published: true
      }
      
      assert_difference '@product.product_videos.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_video: video_params
        }
      end
      
      assert_redirected_to admin_product_product_videos_path(@product)
      assert_match /Product video was successfully created/, flash[:notice]
      
      video = ProductVideo.last
      assert_equal "New Video", video.title
      assert_equal "A new video description", video.description
      assert_equal @product, video.product
      assert video.published?
    end

    test "should create product video with file upload" do
      sign_in @admin_user
      
      file = fixture_file_upload('test/fixtures/files/test_video.mp4', 'video/mp4')
      
      video_params = {
        title: "Video with File",
        description: "Video with uploaded file",
        video_file: file,
        published: false
      }
      
      assert_difference '@product.product_videos.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_video: video_params
        }
      end
      
      video = ProductVideo.last
      assert video.video_file.attached?
      assert_equal "test_video.mp4", video.video_file.filename.to_s
    end

    test "should not create product video with invalid data" do
      sign_in @admin_user
      
      invalid_params = {
        title: "",  # Required field
        description: ""
      }
      
      assert_no_difference 'ProductVideo.count' do
        post :create, params: { 
          product_id: @product.slug,
          product_video: invalid_params
        }
      end
      
      assert_response :unprocessable_entity
      assert_not_nil assigns(:product_video)
      assert assigns(:product_video).errors.any?
    end

    test "should auto-assign position when creating" do
      sign_in @admin_user
      
      file = fixture_file_upload('test/fixtures/files/test_video.mp4', 'video/mp4')
      video_params = {
        title: "Auto Position Video",
        description: "Should get next available position",
        video_file: file
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_video: video_params
      }
      
      video = ProductVideo.last
      assert_equal 3, video.position  # Should be after existing videos (1, 2)
    end

    # ===============================
    # EDIT ACTION TESTS
    # ===============================

    test "should show edit product video form" do
      sign_in @admin_user
      
      get :edit, params: { product_id: @product.slug, id: @video1.id }
      assert_response :success
      assert_equal @video1, assigns(:product_video)
      assert_equal @product, assigns(:product)
    end

    # ===============================
    # UPDATE ACTION TESTS
    # ===============================

    test "should update product video with valid data" do
      sign_in @admin_user
      
      update_params = {
        title: "Updated Video Title",
        description: "Updated video description",
        published: false
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @video1.id,
        product_video: update_params
      }
      
      assert_redirected_to admin_product_product_videos_path(@product)
      assert_match /Product video was successfully updated/, flash[:notice]
      
      @video1.reload
      assert_equal "Updated Video Title", @video1.title
      assert_equal "Updated video description", @video1.description
      assert_not @video1.published?
    end

    test "should update product video file" do
      sign_in @admin_user
      
      new_file = fixture_file_upload('test/fixtures/files/test_video.mp4', 'video/mp4')
      
      update_params = {
        title: @video1.title,
        video_file: new_file
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @video1.id,
        product_video: update_params
      }
      
      assert_redirected_to admin_product_product_videos_path(@product)
      
      @video1.reload
      assert @video1.video_file.attached?
      assert_equal "test_video.mp4", @video1.video_file.filename.to_s
    end

    test "should not update product video with invalid data" do
      sign_in @admin_user
      
      original_title = @video1.title
      
      invalid_params = {
        title: "",  # Invalid
        description: "Valid description"
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @video1.id,
        product_video: invalid_params
      }
      
      assert_response :unprocessable_entity
      assert_not_nil assigns(:product_video)
      assert assigns(:product_video).errors.any?
      
      @video1.reload
      assert_equal original_title, @video1.title
    end

    # ===============================
    # DESTROY ACTION TESTS
    # ===============================

    test "should delete product video" do
      sign_in @admin_user
      
      assert_difference '@product.product_videos.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: @video1.id }
      end
      
      assert_redirected_to admin_product_product_videos_path(@product)
      assert_match /Product video was successfully deleted/, flash[:notice]
    end

    test "should delete attached file when destroying video" do
      sign_in @admin_user
      
      video_with_file = @product.product_videos.build(
        title: "Video to Delete",
        description: "This video will be deleted",
        position: 3
      )
      video_with_file.video_file.attach(
        io: StringIO.new("video to delete"),
        filename: "delete_me.mp4",
        content_type: "video/mp4"
      )
      video_with_file.save!
      
      assert video_with_file.video_file.attached?
      
      delete :destroy, params: { product_id: @product.slug, id: video_with_file.id }
      
      assert_redirected_to admin_product_product_videos_path(@product)
    end

    # ===============================
    # UPDATE POSITION ACTION TESTS
    # ===============================

    test "should update video position" do
      sign_in @admin_user
      
      patch :update_position, params: { 
        product_id: @product.slug,
        id: @video1.id,
        position: 5
      }
      
      assert_response :ok
      
      @video1.reload
      assert_equal 5, @video1.position
    end

    test "should handle AJAX position updates" do
      sign_in @admin_user
      
      patch :update_position, params: { 
        product_id: @product.slug,
        id: @video2.id,
        position: 1
      }, xhr: true
      
      assert_response :ok
      
      @video2.reload
      assert_equal 1, @video2.position
    end

    # ===============================
    # PARAMETER SECURITY TESTS
    # ===============================

    test "should filter unpermitted parameters" do
      sign_in @admin_user
      
      malicious_params = {
        title: "Valid Title",
        description: "Valid description",
        published: true,
        product_id: @product.id + 1,  # Should be filtered/ignored
        position: 999,  # Should be filtered (not in permit list)
        created_at: 1.year.ago  # Should be filtered
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_video: malicious_params
      }
      
      video = ProductVideo.last
      assert_equal "Valid Title", video.title
      assert_equal @product, video.product  # Should use product from URL, not params
      assert_not_equal 999, video.position  # Should auto-assign position
      assert_not_equal 1.year.ago.to_date, video.created_at.to_date
    end

    test "should allow only permitted parameters" do
      sign_in @admin_user
      
      permitted_params = {
        title: "Permitted Video",
        description: "Permitted description",
        published: false
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_video: permitted_params
      }
      
      video = ProductVideo.last
      assert_equal "Permitted Video", video.title
      assert_equal "Permitted description", video.description
      assert_not video.published?
    end

    # ===============================
    # ERROR HANDLING TESTS
    # ===============================

    test "should handle invalid product id" do
      sign_in @admin_user
      
      assert_raises(ActiveRecord::RecordNotFound) do
        get :index, params: { product_id: 'invalid-slug' }
      end
    end

    test "should handle invalid video id" do
      sign_in @admin_user
      
      assert_raises(ActiveRecord::RecordNotFound) do
        get :show, params: { product_id: @product.slug, id: 99999 }
      end
    end

    test "should scope videos to correct product" do
      sign_in @admin_user
      
      # Create another product
      other_product = @vendor_account.products.create!(
        name: "Other Product",
        description: "Another product",
        website: "https://other.com"
      )
      
      # Try to access @video1 through other_product (should fail)
      assert_raises(ActiveRecord::RecordNotFound) do
        get :show, params: { product_id: other_product.slug, id: @video1.id }
      end
    end

    # ===============================
    # INTEGRATION TESTS
    # ===============================

    test "should complete full video management workflow" do
      sign_in @admin_user
      
      # Step 1: View videos index
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      # Step 2: Visit new video page
      get :new, params: { product_id: @product.slug }
      assert_response :success
      
      # Step 3: Create video
      file = fixture_file_upload('test/fixtures/files/test_video.mp4', 'video/mp4')
      video_params = {
        title: "Workflow Video",
        description: "Video created through workflow",
        video_file: file,
        published: false
      }
      
      assert_difference '@product.product_videos.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_video: video_params
        }
      end
      
      video = ProductVideo.last
      assert_redirected_to admin_product_product_videos_path(@product)
      
      # Step 4: View created video
      get :show, params: { product_id: @product.slug, id: video.id }
      assert_response :success
      assert_equal video, assigns(:product_video)
      
      # Step 5: Edit video
      get :edit, params: { product_id: @product.slug, id: video.id }
      assert_response :success
      
      # Step 6: Update video to published
      patch :update, params: { 
        product_id: @product.slug,
        id: video.id,
        product_video: { published: true }
      }
      
      assert_redirected_to admin_product_product_videos_path(@product)
      
      video.reload
      assert video.published?
      
      # Step 7: Update position
      patch :update_position, params: { 
        product_id: @product.slug,
        id: video.id,
        position: 1
      }
      
      assert_response :ok
      video.reload
      assert_equal 1, video.position
      
      # Step 8: Delete video
      assert_difference '@product.product_videos.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: video.id }
      end
      
      assert_redirected_to admin_product_product_videos_path(@product)
    end

    test "should handle video management with validation errors" do
      sign_in @admin_user
      
      # Step 1: Try to create invalid video
      invalid_params = {
        title: "",  # Invalid
        description: "Valid description"
      }
      
      assert_no_difference 'ProductVideo.count' do
        post :create, params: { 
          product_id: @product.slug,
          product_video: invalid_params
        }
      end
      
      assert_response :unprocessable_entity
      assert assigns(:product_video).errors.any?
      
      # Step 2: Fix errors and create successfully
      file = fixture_file_upload('test/fixtures/files/test_video.mp4', 'video/mp4')
      valid_params = {
        title: "Fixed Video Title",
        description: "Valid description",
        video_file: file
      }
      
      assert_difference 'ProductVideo.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_video: valid_params
        }
      end
      
      assert_redirected_to admin_product_product_videos_path(@product)
    end

    test "should maintain position ordering when managing videos" do
      sign_in @admin_user
      
      # Initial positions: video1(1), video2(2)
      assert_equal 1, @video1.position
      assert_equal 2, @video2.position
      
      # Create new video (should get position 3)
      file = fixture_file_upload('test/fixtures/files/test_video.mp4', 'video/mp4')
      video_params = {
        title: "Third Video",
        description: "Should get position 3",
        video_file: file
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_video: video_params
      }
      
      third_video = ProductVideo.last
      assert_equal 3, third_video.position
      
      # Update position of third video to 1
      patch :update_position, params: { 
        product_id: @product.slug,
        id: third_video.id,
        position: 1
      }
      
      third_video.reload
      assert_equal 1, third_video.position
      
      # Delete middle video
      delete :destroy, params: { product_id: @product.slug, id: @video2.id }
      
      # Should still be able to manage remaining videos
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      videos = assigns(:product_videos)
      assert_equal 2, videos.count
      assert videos.include?(@video1)
      assert videos.include?(third_video)
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
    end
  end
end