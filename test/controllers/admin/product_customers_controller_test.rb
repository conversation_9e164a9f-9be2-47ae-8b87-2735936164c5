require "test_helper"

module Admin
  class ProductCustomersControllerTest < ActionController::TestCase
    def setup
      # Create test users
      @admin_user = users(:admin)
      @vendor_user = users(:one)
      @admin_user.update!(super_admin: true)
      
      # Create test account and product
      @vendor_account = accounts(:one)
      @vendor_account.update!(
        account_type: 'vendor', 
        status: 'approved', 
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      @vendor_account.account_users.destroy_all
      @vendor_account.account_users.create!(user: @vendor_user, role: 'admin')
      
      @product = @vendor_account.products.create!(
        name: "Test Product",
        description: "A test product description",
        website: "https://test.com",
        published: false
      )
      
      # Create test product customers
      @customer1 = @product.product_customers.build(
        name: "Acme Corporation",
        description: "Fortune 500 company using our solution",
        position: 1
      )
      @customer1.logo.attach(
        io: StringIO.new("fake logo data"),
        filename: "acme_logo.png",
        content_type: "image/png"
      )
      @customer1.save!
      
      @customer2 = @product.product_customers.create!(
        name: "TechStart Inc",
        description: "Growing startup leveraging our platform",
        position: 2
      )
    end

    # ===============================
    # AUTHENTICATION & AUTHORIZATION TESTS
    # ===============================

    test "should require admin authentication for all actions" do
      get :index, params: { product_id: @product.slug }
      assert_redirected_to new_session_path
      
      get :show, params: { product_id: @product.slug, id: @customer1.id }
      assert_redirected_to new_session_path
      
      get :new, params: { product_id: @product.slug }
      assert_redirected_to new_session_path
      
      post :create, params: { product_id: @product.slug, product_customer: { name: "Test" } }
      assert_redirected_to new_session_path
    end

    test "should require admin privileges" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_redirected_to root_path
      
      get :show, params: { product_id: @product.slug, id: @customer1.id }
      assert_redirected_to root_path
    end

    test "should allow admin users" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
    end

    # ===============================
    # INDEX ACTION TESTS
    # ===============================

    test "should show product customers index" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      assert_not_nil assigns(:product_customers)
      
      customers = assigns(:product_customers)
      assert customers.include?(@customer1)
      assert customers.include?(@customer2)
    end

    test "should order customers by position" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      customers = assigns(:product_customers).to_a
      assert_equal @customer1, customers.first
      assert_equal @customer2, customers.second
    end

    test "should set product from slug" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      assert_equal @product, assigns(:product)
    end

    # ===============================
    # SHOW ACTION TESTS
    # ===============================

    test "should show product customer details" do
      sign_in @admin_user
      
      get :show, params: { product_id: @product.slug, id: @customer1.id }
      assert_response :success
      assert_equal @customer1, assigns(:product_customer)
      assert_equal @product, assigns(:product)
    end

    test "should only show customers for specified product" do
      sign_in @admin_user
      
      # Create another product with customers
      other_product = @vendor_account.products.create!(
        name: "Other Product",
        description: "Another product",
        website: "https://other.com"
      )
      
      other_customer = other_product.product_customers.create!(
        name: "Other Customer",
        description: "Customer for other product",
        position: 1
      )
      
      # Should not be able to access other product's customer through this product
      assert_raises(ActiveRecord::RecordNotFound) do
        get :show, params: { product_id: @product.slug, id: other_customer.id }
      end
    end

    # ===============================
    # NEW ACTION TESTS
    # ===============================

    test "should show new product customer form" do
      sign_in @admin_user
      
      get :new, params: { product_id: @product.slug }
      assert_response :success
      assert_instance_of ProductCustomer, assigns(:product_customer)
      assert assigns(:product_customer).new_record?
      assert_equal @product, assigns(:product_customer).product
    end

    # ===============================
    # CREATE ACTION TESTS
    # ===============================

    test "should create product customer with valid data" do
      sign_in @admin_user
      
      customer_params = {
        name: "New Customer Corp",
        description: "A new customer description",
        position: 3
      }
      
      assert_difference '@product.product_customers.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_customer: customer_params
        }
      end
      
      assert_redirected_to admin_product_product_customers_path(@product)
      assert_match /Product customer was successfully created/, flash[:notice]
      
      customer = ProductCustomer.last
      assert_equal "New Customer Corp", customer.name
      assert_equal "A new customer description", customer.description
      assert_equal @product, customer.product
      assert_equal 3, customer.position
    end

    test "should create customer with logo attachment" do
      sign_in @admin_user
      
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      customer_params = {
        name: "Customer with Logo",
        description: "Customer with attached logo",
        logo: file
      }
      
      assert_difference '@product.product_customers.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_customer: customer_params
        }
      end
      
      customer = ProductCustomer.last
      assert customer.logo.attached?
      assert_equal "test_image.png", customer.logo.filename.to_s
      assert customer.image?
    end

    test "should auto-assign position when not provided" do
      sign_in @admin_user
      
      customer_params = {
        name: "Auto Position Customer",
        description: "Should get auto-assigned position"
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_customer: customer_params
      }
      
      customer = ProductCustomer.last
      assert_equal 3, customer.position  # Should be after existing customers (1, 2)
    end

    test "should not create product customer with invalid data" do
      sign_in @admin_user
      
      invalid_params = {
        name: "",  # Required field
        description: "Valid description"
      }
      
      assert_no_difference 'ProductCustomer.count' do
        post :create, params: { 
          product_id: @product.slug,
          product_customer: invalid_params
        }
      end
      
      assert_response :success  # Renders :new template
      assert_not_nil assigns(:product_customer)
      assert assigns(:product_customer).errors.any?
    end

    # ===============================
    # EDIT ACTION TESTS
    # ===============================

    test "should show edit product customer form" do
      sign_in @admin_user
      
      get :edit, params: { product_id: @product.slug, id: @customer1.id }
      assert_response :success
      assert_equal @customer1, assigns(:product_customer)
      assert_equal @product, assigns(:product)
    end

    # ===============================
    # UPDATE ACTION TESTS
    # ===============================

    test "should update product customer with valid data" do
      sign_in @admin_user
      
      update_params = {
        name: "Updated Customer Name",
        description: "Updated customer description",
        position: 5
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @customer1.id,
        product_customer: update_params
      }
      
      assert_redirected_to admin_product_product_customers_path(@product)
      assert_match /Product customer was successfully updated/, flash[:notice]
      
      @customer1.reload
      assert_equal "Updated Customer Name", @customer1.name
      assert_equal "Updated customer description", @customer1.description
      assert_equal 5, @customer1.position
    end

    test "should update product customer logo" do
      sign_in @admin_user
      
      new_file = fixture_file_upload('test/fixtures/files/test_image.jpg', 'image/jpeg')
      
      update_params = {
        name: @customer1.name,
        logo: new_file
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @customer1.id,
        product_customer: update_params
      }
      
      assert_redirected_to admin_product_product_customers_path(@product)
      
      @customer1.reload
      assert @customer1.logo.attached?
      assert_equal "test_image.jpg", @customer1.logo.filename.to_s
    end

    test "should not update product customer with invalid data" do
      sign_in @admin_user
      
      original_name = @customer1.name
      
      invalid_params = {
        name: "",  # Invalid
        description: "Valid description"
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @customer1.id,
        product_customer: invalid_params
      }
      
      assert_response :success  # Renders :edit template
      assert_not_nil assigns(:product_customer)
      assert assigns(:product_customer).errors.any?
      
      @customer1.reload
      assert_equal original_name, @customer1.name
    end

    # ===============================
    # DESTROY ACTION TESTS
    # ===============================

    test "should delete product customer" do
      sign_in @admin_user
      
      assert_difference '@product.product_customers.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: @customer1.id }
      end
      
      assert_redirected_to admin_product_product_customers_path(@product)
      assert_match /Product customer was successfully deleted/, flash[:notice]
    end

    test "should delete attached logo when destroying customer" do
      sign_in @admin_user
      
      customer_with_logo = @product.product_customers.build(
        name: "Customer to Delete",
        description: "This customer will be deleted",
        position: 3
      )
      customer_with_logo.logo.attach(
        io: StringIO.new("logo to delete"),
        filename: "delete_me.png",
        content_type: "image/png"
      )
      customer_with_logo.save!
      
      assert customer_with_logo.logo.attached?
      
      delete :destroy, params: { product_id: @product.slug, id: customer_with_logo.id }
      
      assert_redirected_to admin_product_product_customers_path(@product)
    end

    # ===============================
    # UPDATE POSITION ACTION TESTS
    # ===============================

    test "should update customer position" do
      sign_in @admin_user
      
      patch :update_position, params: { 
        product_id: @product.slug,
        id: @customer1.id,
        position: 5
      }
      
      assert_response :ok
      
      @customer1.reload
      assert_equal 5, @customer1.position
    end

    test "should handle AJAX position updates" do
      sign_in @admin_user
      
      patch :update_position, params: { 
        product_id: @product.slug,
        id: @customer2.id,
        position: 1
      }, xhr: true
      
      assert_response :ok
      
      @customer2.reload
      assert_equal 1, @customer2.position
    end

    # ===============================
    # PARAMETER SECURITY TESTS
    # ===============================

    test "should filter unpermitted parameters" do
      sign_in @admin_user
      
      malicious_params = {
        name: "Valid Name",
        description: "Valid description",
        position: 1,
        product_id: @product.id + 1,  # Should be filtered/ignored
        created_at: 1.year.ago,  # Should be filtered
        published: false  # Not in permit list
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_customer: malicious_params
      }
      
      customer = ProductCustomer.last
      assert_equal "Valid Name", customer.name
      assert_equal @product, customer.product  # Should use product from URL, not params
      assert_not_equal 1.year.ago.to_date, customer.created_at.to_date
      # published should remain default (true) since it's not permitted
      assert customer.published?
    end

    test "should allow only permitted parameters" do
      sign_in @admin_user
      
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      permitted_params = {
        name: "Permitted Customer",
        description: "Permitted description",
        position: 1,
        logo: file
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_customer: permitted_params
      }
      
      customer = ProductCustomer.last
      assert_equal "Permitted Customer", customer.name
      assert_equal "Permitted description", customer.description
      assert_equal 1, customer.position
      assert customer.logo.attached?
    end

    # ===============================
    # ERROR HANDLING TESTS
    # ===============================

    test "should handle invalid product id" do
      sign_in @admin_user
      
      assert_raises(ActiveRecord::RecordNotFound) do
        get :index, params: { product_id: 'invalid-slug' }
      end
    end

    test "should handle invalid customer id" do
      sign_in @admin_user
      
      assert_raises(ActiveRecord::RecordNotFound) do
        get :show, params: { product_id: @product.slug, id: 99999 }
      end
    end

    test "should scope customers to correct product" do
      sign_in @admin_user
      
      # Create another product
      other_product = @vendor_account.products.create!(
        name: "Other Product",
        description: "Another product",
        website: "https://other.com"
      )
      
      # Try to access @customer1 through other_product (should fail)
      assert_raises(ActiveRecord::RecordNotFound) do
        get :show, params: { product_id: other_product.slug, id: @customer1.id }
      end
    end

    # ===============================
    # INTEGRATION TESTS
    # ===============================

    test "should complete full customer management workflow" do
      sign_in @admin_user
      
      # Step 1: View customers index
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      # Step 2: Visit new customer page
      get :new, params: { product_id: @product.slug }
      assert_response :success
      
      # Step 3: Create customer
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      customer_params = {
        name: "Workflow Customer",
        description: "Customer created through workflow",
        logo: file,
        position: 3
      }
      
      assert_difference '@product.product_customers.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_customer: customer_params
        }
      end
      
      customer = ProductCustomer.last
      assert_redirected_to admin_product_product_customers_path(@product)
      
      # Step 4: View created customer
      get :show, params: { product_id: @product.slug, id: customer.id }
      assert_response :success
      assert_equal customer, assigns(:product_customer)
      
      # Step 5: Edit customer
      get :edit, params: { product_id: @product.slug, id: customer.id }
      assert_response :success
      
      # Step 6: Update customer
      patch :update, params: { 
        product_id: @product.slug,
        id: customer.id,
        product_customer: { name: "Updated Workflow Customer" }
      }
      
      assert_redirected_to admin_product_product_customers_path(@product)
      customer.reload
      assert_equal "Updated Workflow Customer", customer.name
      
      # Step 7: Update position
      patch :update_position, params: { 
        product_id: @product.slug,
        id: customer.id,
        position: 1
      }
      
      assert_response :ok
      customer.reload
      assert_equal 1, customer.position
      
      # Step 8: Delete customer
      assert_difference '@product.product_customers.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: customer.id }
      end
      
      assert_redirected_to admin_product_product_customers_path(@product)
    end

    test "should handle customer management with validation errors" do
      sign_in @admin_user
      
      # Step 1: Try to create invalid customer
      invalid_params = {
        name: "",  # Invalid
        description: "Valid description"
      }
      
      assert_no_difference 'ProductCustomer.count' do
        post :create, params: { 
          product_id: @product.slug,
          product_customer: invalid_params
        }
      end
      
      assert_response :success  # Renders :new
      assert assigns(:product_customer).errors.any?
      
      # Step 2: Fix errors and create successfully
      valid_params = {
        name: "Fixed Customer Name",
        description: "Valid description"
      }
      
      assert_difference 'ProductCustomer.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_customer: valid_params
        }
      end
      
      assert_redirected_to admin_product_product_customers_path(@product)
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
    end
  end
end