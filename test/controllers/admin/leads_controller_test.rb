require "test_helper"

class Admin::LeadsControllerTest < ActionController::TestCase
  setup do
    @admin_user = users(:one)
    @admin_user.update!(super_admin: true)
    
    @vendor_user = users(:two)
    @vendor_account = accounts(:one)
    @vendor_account.update!(
      account_type: 'vendor',
      status: 'approved',
      confirmed_at: Time.current,
      owner: @vendor_user
    )
    @vendor_account.account_users.find_or_create_by(user: @vendor_user, role: 'admin')

    @agency_user = User.create!(
      first_name: "Agency",
      last_name: "User",
      email_address: "<EMAIL>",
      password: "password123"
    )
    
    @agency_account = Account.create!(
      name: "Test Agency",
      account_type: 'agency',
      status: 'approved',
      confirmed_at: Time.current,
      owner: @agency_user
    )
    @agency_account.account_users.create!(user: @agency_user, role: 'admin')

    # Clean up existing data to ensure clean test state
    Lead.destroy_all
    Product.destroy_all
    
    # Create test product
    @product = Product.create!(
      account: @vendor_account,
      name: "Test Product",
      description: "A test product for leads",
      published: true
    )
    
    # Create test leads
    @lead1 = Lead.create!(
      product: @product,
      account: @vendor_account,
      agency_account: @agency_account,
      user: @agency_user,
      status: :pending,
      message: "Interested in this product",
      contact_name: "<PERSON>",
      contact_email: "<EMAIL>",
      contact_company: "Test Agency",
      created_at: 2.days.ago
    )
    
    @lead2 = Lead.create!(
      product: @product,
      account: @vendor_account,
      agency_account: @agency_account,
      user: @agency_user,
      status: :contacted,
      message: "Follow up needed",
      contact_name: "Jane Smith",
      contact_email: "<EMAIL>",
      contact_company: "Test Agency",
      created_at: 1.day.ago
    )
    
    @lead3 = Lead.create!(
      product: @product,
      account: @vendor_account,
      agency_account: @agency_account,
      user: @agency_user,
      status: :qualified,
      message: "Very interested",
      contact_name: "Bob Wilson",
      contact_email: "<EMAIL>",
      contact_company: "Test Agency",
      created_at: Time.current
    )
  end

  # Authentication Tests
  test "should require authentication for index" do
    get :index, params: { product_id: @product.friendly_id }
    assert_response :redirect
    assert_redirected_to new_session_path
  end

  test "should require super admin access for index" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id
    
    get :index, params: { product_id: @product.friendly_id }
    assert_response :redirect
    assert_redirected_to root_path
  end

  test "should allow super admin access to index" do
    sign_in @admin_user
    
    get :index, params: { product_id: @product.friendly_id }
    assert_response :success
    assert_not_nil assigns(:leads)
    assert_not_nil assigns(:product)
  end

  test "should require authentication for show" do
    get :show, params: { product_id: @product.friendly_id, id: @lead1.id }
    assert_response :redirect
    assert_redirected_to new_session_path
  end

  test "should require super admin access for show" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id
    
    get :show, params: { product_id: @product.friendly_id, id: @lead1.id }
    assert_response :redirect
    assert_redirected_to root_path
  end

  test "should allow super admin access to show" do
    sign_in @admin_user
    
    get :show, params: { product_id: @product.friendly_id, id: @lead1.id }
    assert_response :success
    assert_not_nil assigns(:lead)
    assert_not_nil assigns(:product)
  end

  # Index Action Tests
  test "should get index" do
    sign_in @admin_user
    
    get :index, params: { product_id: @product.friendly_id }
    assert_response :success
    assert_not_nil assigns(:leads)
    assert_not_nil assigns(:product)
    assert_equal @product, assigns(:product)
  end

  test "should display leads for specific product only" do
    sign_in @admin_user
    
    # Create another product with leads
    other_product = Product.create!(
      account: @vendor_account,
      name: "Other Product",
      description: "Another test product",
      published: true
    )
    
    other_lead = Lead.create!(
      product: other_product,
      account: @vendor_account,
      agency_account: @agency_account,
      user: @agency_user,
      status: :pending,
      message: "Lead for other product",
      contact_name: "Other Contact",
      contact_email: "<EMAIL>",
      contact_company: "Test Agency"
    )
    
    get :index, params: { product_id: @product.friendly_id }
    leads = assigns(:leads)
    
    # Should only include leads for the specified product
    assert_equal 3, leads.count
    leads.each do |lead|
      assert_equal @product, lead.product
    end
    
    # Should not include the other product's lead
    assert_not_includes leads, other_lead
  end

  test "should order leads by created_at desc" do
    sign_in @admin_user
    
    get :index, params: { product_id: @product.friendly_id }
    leads = assigns(:leads)
    
    # Should be ordered with newest first
    assert_equal [@lead3, @lead2, @lead1], leads.to_a
    
    # Verify timestamps are in descending order
    leads.each_cons(2) do |current, previous|
      assert current.created_at >= previous.created_at
    end
  end

  test "should include user and account associations" do
    sign_in @admin_user
    
    get :index, params: { product_id: @product.friendly_id }
    leads = assigns(:leads)
    
    # Test that associations are eager loaded
    leads.each do |lead|
      assert_not_nil lead.user
      assert_not_nil lead.account
      # This shouldn't trigger additional queries
      assert_equal @agency_user, lead.user
      assert_equal @vendor_account, lead.account
    end
  end

  test "should handle product with no leads" do
    sign_in @admin_user
    
    # Create product with no leads
    empty_product = Product.create!(
      account: @vendor_account,
      name: "Empty Product",
      description: "Product with no leads",
      published: true
    )
    
    get :index, params: { product_id: empty_product.friendly_id }
    assert_response :success
    
    leads = assigns(:leads)
    assert_equal 0, leads.count
  end

  test "should handle nonexistent product in index" do
    sign_in @admin_user
    
    get :index, params: { product_id: 'nonexistent' }
    assert_response 302  # Should redirect to error page
  end

  # Show Action Tests
  test "should show lead" do
    sign_in @admin_user
    
    get :show, params: { product_id: @product.friendly_id, id: @lead1.id }
    assert_response :success
    assert_equal @lead1, assigns(:lead)
    assert_equal @product, assigns(:product)
  end

  test "should show lead with all attributes" do
    sign_in @admin_user
    
    get :show, params: { product_id: @product.friendly_id, id: @lead1.id }
    lead = assigns(:lead)
    
    # Verify all lead attributes are accessible
    assert_equal "pending", lead.status
    assert_equal "Interested in this product", lead.message
    assert_equal "John Doe", lead.contact_name
    assert_equal "<EMAIL>", lead.contact_email
    assert_equal "Test Agency", lead.contact_company
    assert_equal @agency_user, lead.user
    assert_equal @vendor_account, lead.account
    assert_equal @agency_account, lead.agency_account
  end

  test "should handle nonexistent lead in show" do
    sign_in @admin_user
    
    get :show, params: { product_id: @product.friendly_id, id: 99999 }
    assert_response 302  # Should redirect to error page
  end

  test "should handle lead from different product in show" do
    sign_in @admin_user
    
    # Create lead for different product
    other_product = Product.create!(
      account: @vendor_account,
      name: "Other Product",
      description: "Another test product",
      published: true
    )
    
    other_lead = Lead.create!(
      product: other_product,
      account: @vendor_account,
      agency_account: @agency_account,
      user: @agency_user,
      status: :pending,
      message: "Lead for other product",
      contact_name: "Other Contact",
      contact_email: "<EMAIL>",
      contact_company: "Test Agency"
    )
    
    # Try to access other_lead through our product's path
    get :show, params: { product_id: @product.friendly_id, id: other_lead.id }
    assert_response 302  # Should redirect to error page (lead not found for this product)
  end

  test "should handle nonexistent product in show" do
    sign_in @admin_user
    
    get :show, params: { product_id: 'nonexistent', id: @lead1.id }
    assert_response 302  # Should redirect to error page
  end

  # Lead Status Tests
  test "should display leads with different statuses" do
    sign_in @admin_user
    
    get :index, params: { product_id: @product.friendly_id }
    leads = assigns(:leads)
    
    statuses = leads.map(&:status)
    assert_includes statuses, 'pending'
    assert_includes statuses, 'contacted'
    assert_includes statuses, 'qualified'
  end

  test "should handle leads with all possible statuses" do
    sign_in @admin_user
    
    # Create leads with all valid statuses (based on enum: pending, contacted, qualified, closed)
    Lead.create!(
      product: @product,
      account: @vendor_account,
      agency_account: @agency_account,
      user: @agency_user,
      status: :closed,
      message: "Closed lead",
      contact_name: "Closed Contact",
      contact_email: "<EMAIL>",
      contact_company: "Test Agency"
    )
    
    get :index, params: { product_id: @product.friendly_id }
    leads = assigns(:leads)
    
    # Should include all leads regardless of status
    assert leads.count >= 4
    statuses = leads.map(&:status).uniq.sort
    expected_statuses = ['closed', 'contacted', 'pending', 'qualified']
    assert_equal expected_statuses, statuses
  end

  # Security Tests
  test "should scope leads to specified product only" do
    sign_in @admin_user
    
    # Create leads for different products
    other_product = Product.create!(
      account: @vendor_account,
      name: "Other Product",
      description: "Another test product",
      published: true
    )
    
    other_lead = Lead.create!(
      product: other_product,
      account: @vendor_account,
      agency_account: @agency_account,
      user: @agency_user,
      status: :pending,
      message: "Lead for other product",
      contact_name: "Other Contact",
      contact_email: "<EMAIL>",
      contact_company: "Test Agency"
    )
    
    get :index, params: { product_id: @product.friendly_id }
    leads = assigns(:leads)
    
    # Should not include leads from other products
    leads.each do |lead|
      assert_equal @product.id, lead.product_id
    end
    
    assert_not_includes leads.map(&:id), other_lead.id
  end

  test "should not expose sensitive lead information inappropriately" do
    sign_in @admin_user
    
    get :index, params: { product_id: @product.friendly_id }
    assert_response :success
    
    # This test ensures the controller itself doesn't leak sensitive info
    # (actual security depends on view implementation)
    leads = assigns(:leads)
    leads.each do |lead|
      # Ensure basic data integrity
      assert_not_nil lead.contact_email
      assert_not_nil lead.contact_name
    end
  end

  # Performance Tests
  test "should not trigger N+1 queries for associations" do
    sign_in @admin_user
    
    # Add more leads to test query performance
    10.times do |i|
      Lead.create!(
        product: @product,
        account: @vendor_account,
        agency_account: @agency_account,
        user: @agency_user,
        status: :pending,
        message: "Performance test lead #{i}",
        contact_name: "Test Contact #{i}",
        contact_email: "test#{i}@agency.gov",
        contact_company: "Test Agency"
      )
    end
    
    get :index, params: { product_id: @product.friendly_id }
    leads = assigns(:leads)
    
    # Access associated objects - this shouldn't trigger additional queries
    leads.each do |lead|
      lead.user.email_address if lead.user
      lead.account.name if lead.account
    end
    
    assert leads.count >= 10
  end

  # Edge Cases
  test "should handle leads with minimal required fields" do
    sign_in @admin_user
    
    # Create lead with all required fields (contact_company is required)
    minimal_lead = Lead.create!(
      product: @product,
      account: @vendor_account,
      status: :pending,
      message: "Minimal lead",
      contact_name: "Minimal Contact",
      contact_email: "<EMAIL>",
      contact_company: "Minimal Company"
      # Missing optional fields like agency_account, user, etc.
    )
    
    get :index, params: { product_id: @product.friendly_id }
    leads = assigns(:leads)
    
    assert_includes leads.map(&:id), minimal_lead.id
    
    get :show, params: { product_id: @product.friendly_id, id: minimal_lead.id }
    assert_response :success
  end

  test "should handle products with friendly_id" do
    sign_in @admin_user
    
    # Test that friendly_id routing works correctly
    get :index, params: { product_id: @product.slug }
    assert_response :success
    assert_equal @product, assigns(:product)
  end

  private

  def sign_in(user)
    session_record = user.sessions.create!(
      user_agent: 'Test User Agent',
      ip_address: '127.0.0.1'
    )
    Current.session = session_record
    cookies.signed[:session_id] = session_record.id
  end
end