require "test_helper"

module Admin
  class EmailPreviewsControllerTest < ActionController::TestCase
    setup do
      @admin_user = users(:one)
      @admin_user.update!(super_admin: true)
      
      @vendor_user = users(:two)
      @vendor_account = accounts(:one)
      @vendor_account.update!(
        account_type: 'vendor',
        status: 'approved',
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      @vendor_account.account_users.find_or_create_by(user: @vendor_user, role: 'admin')
    end

    # Authentication Tests
    test "should require authentication for index" do
      get :index
      assert_response :redirect
      assert_redirected_to new_session_path
    end

    test "should require super admin access for index" do
      sign_in @vendor_user
      session[:account_id] = @vendor_account.id
      
      get :index
      assert_response :redirect
      assert_redirected_to root_path
    end

    test "should allow super admin access to index" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      assert_not_nil assigns(:email_previews)
    end

    test "should require authentication for show" do
      get :show, params: { id: 'user', email_method: 'welcome' }
      assert_response :redirect
      assert_redirected_to new_session_path
    end

    test "should require super admin access for show" do
      sign_in @vendor_user
      session[:account_id] = @vendor_account.id
      
      get :show, params: { id: 'user', email_method: 'welcome' }
      assert_response :redirect
      assert_redirected_to root_path
    end

    # Index Action Tests
    test "should display available email previews" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      
      email_previews = assigns(:email_previews)
      assert_not_nil email_previews
      assert email_previews.is_a?(Array)
      
      # Should contain at least the sample previews when no real previews exist
      assert email_previews.any?
      
      # Check structure of preview objects
      email_previews.each do |preview|
        assert preview.key?(:name)
        assert preview.key?(:class_name)
        assert preview.key?(:emails)
        assert preview.key?(:description)
        assert preview[:emails].is_a?(Array)
      end
    end

    test "should include default sample previews when no real previews exist" do
      sign_in @admin_user
      
      get :index
      email_previews = assigns(:email_previews)
      
      # Should include default sample previews
      preview_names = email_previews.map { |p| p[:name] }
      assert_includes preview_names, 'User'
      assert_includes preview_names, 'Lead'
      assert_includes preview_names, 'Admin'
      assert_includes preview_names, 'PasswordsMailer'
    end

    test "should sort email previews by name" do
      sign_in @admin_user
      
      get :index
      email_previews = assigns(:email_previews)
      
      # Should be sorted alphabetically by name
      names = email_previews.map { |p| p[:name] }
      assert_equal names.sort, names
    end

    # Show Action Tests - Error Handling
    test "should handle non-existent preview class" do
      sign_in @admin_user
      
      get :show, params: { id: 'nonexistent', email_method: 'test' }
      assert_redirected_to admin_email_previews_path
      assert_equal "Email preview 'NonexistentPreview' not found", flash[:alert]
    end

    test "should handle non-existent email method" do
      sign_in @admin_user
      
      # Use a preview that exists but with non-existent method
      get :show, params: { id: 'user', email_method: 'nonexistent_method' }
      assert_redirected_to admin_email_previews_path
      assert_equal "Email method 'nonexistent_method' not found", flash[:alert]
    end

    test "should handle preview loading errors gracefully" do
      sign_in @admin_user
      
      # Test with malformed preview class name
      get :show, params: { id: 'invalid-class-name!', email_method: 'test' }
      assert_redirected_to admin_email_previews_path
      assert_not_nil flash[:alert]
      assert flash[:alert].include?('not found')
    end

    # Show Action Tests - HTML Format
    test "should render email preview in HTML format" do
      sign_in @admin_user
      
      # This test assumes LeadPreview exists and has new_lead_notification method
      # If it doesn't exist, the controller will create sample previews
      get :show, params: { id: 'lead', email_method: 'new_lead_notification' }, format: :html
      
      # Should either show the preview or redirect with error message
      assert_includes [200, 302], response.status
    end

    test "should render email preview text part when requested" do
      sign_in @admin_user
      
      get :show, params: { id: 'user', email_method: 'welcome', part: 'text' }, format: :html
      
      # Should either show the text preview or redirect with error message
      assert_includes [200, 302], response.status
      
      if response.status == 200
        assert_equal 'text/plain', response.content_type
      end
    end

    # Show Action Tests - JSON Format
    test "should render email preview in JSON format" do
      sign_in @admin_user
      
      get :show, params: { id: 'admin', email_method: 'new_user_signup' }, format: :json
      
      # Should either return JSON or redirect with error
      assert_includes [200, 302], response.status
      
      if response.status == 200
        assert_equal 'application/json', response.content_type
        json_response = JSON.parse(response.body)
        
        # Should have expected email fields
        assert json_response.key?('subject')
        assert json_response.key?('to')
        assert json_response.key?('from')
        assert json_response.key?('html')
        assert json_response.key?('text')
      end
    end

    test "should use first email method when not specified" do
      sign_in @admin_user
      
      # Test without specifying email_method - should use first available
      get :show, params: { id: 'user' }
      
      # Should either render successfully or redirect with error
      assert_includes [200, 302], response.status
    end

    # Security Tests
    test "should not expose sensitive information in previews" do
      sign_in @admin_user
      
      get :index
      email_previews = assigns(:email_previews)
      
      # Ensure no actual sensitive data is exposed (exclude legitimate references like "password reset")
      email_previews.each do |preview|
        description = preview[:description].downcase
        assert_not description.include?('secret')
        assert_not description.include?('token')
        assert_not description.include?('api_key')
        assert_not description.include?('private_key')
        # Password reset emails are legitimate and expected
      end
    end

    test "should sanitize preview class names" do
      sign_in @admin_user
      
      # Test with potentially dangerous class name
      get :show, params: { id: '../../../etc/passwd', email_method: 'test' }
      assert_redirected_to admin_email_previews_path
      assert_not_nil flash[:alert]
    end

    # File System Tests
    test "should handle missing preview directories gracefully" do
      sign_in @admin_user
      
      # Controller should handle missing preview directories without errors
      get :index
      assert_response :success
      
      # Should fallback to sample previews
      email_previews = assigns(:email_previews)
      assert email_previews.any?
    end

    test "should scan test and spec directories for previews" do
      sign_in @admin_user
      
      # The controller scans both test/mailers/previews and spec/mailers/previews
      # This test ensures it doesn't crash when directories don't exist
      get :index
      assert_response :success
      
      email_previews = assigns(:email_previews)
      assert_not_nil email_previews
    end

    # Preview Structure Tests
    test "should include account confirmation mailer in previews" do
      sign_in @admin_user
      
      get :index
      email_previews = assigns(:email_previews)
      
      # Should include AccountConfirmationMailer if not already present
      preview_names = email_previews.map { |p| p[:class_name] }
      assert_includes preview_names, 'AccountConfirmationMailerPreview'
    end

    test "should provide email methods array for each preview" do
      sign_in @admin_user
      
      get :index
      email_previews = assigns(:email_previews)
      
      email_previews.each do |preview|
        assert preview[:emails].is_a?(Array)
        # Note: Some previews may have empty email arrays if the class doesn't exist
        # This is handled gracefully by the controller
      end
    end

    test "should provide descriptions for all previews" do
      sign_in @admin_user
      
      get :index
      email_previews = assigns(:email_previews)
      
      email_previews.each do |preview|
        assert preview[:description].present?, "Preview #{preview[:name]} should have a description"
        assert preview[:description].is_a?(String)
      end
    end

    # Edge Cases
    test "should handle empty email methods array" do
      sign_in @admin_user
      
      # This tests the controller's robustness when a preview has no email methods
      get :index
      assert_response :success
    end

    test "should handle class loading errors gracefully" do
      sign_in @admin_user
      
      # The controller should handle errors when loading preview classes
      get :index
      assert_response :success
      
      # Should still provide sample previews as fallback
      email_previews = assigns(:email_previews)
      assert email_previews.any?
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
    end
  end
end