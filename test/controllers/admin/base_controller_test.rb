require "test_helper"

module Admin
  class BaseControllerTest < ActionController::TestCase
    # Test through an existing concrete controller instead of creating test routes
    tests Admin::OverviewController
    
    def setup
      # Create test users and accounts
      @vendor_user = users(:one)
      @agency_user = users(:two)
      @admin_user = users(:admin)
      
      # Ensure admin user has super_admin flag
      @admin_user.update!(super_admin: true)
      
      # Create regular user without admin privileges
      @regular_user = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "Regular",
        last_name: "User",
        super_admin: false
      )
    end

    # ===============================
    # AUTHENTICATION TESTS
    # ===============================

    test "should require authentication" do
      get :index
      assert_redirected_to new_session_path
    end

    test "should allow authenticated admin users" do
      sign_in @admin_user
      get :index
      assert_response :success
    end

    # ===============================
    # ADMIN REQUIREMENT TESTS
    # ===============================

    test "should redirect non-admin users" do
      sign_in @vendor_user
      get :index
      assert_redirected_to root_path
    end

    test "should redirect agency users" do
      sign_in @agency_user
      get :index
      assert_redirected_to root_path
    end

    test "should redirect regular users" do
      sign_in @regular_user
      get :index
      assert_redirected_to root_path
    end

    test "should allow super_admin users" do
      sign_in @admin_user
      get :index
      assert_response :success
    end

    test "should check super_admin flag specifically" do
      # Create user with super_admin: false
      non_admin = User.create!(
        email_address: "<EMAIL>",
        password: "password123",
        first_name: "Not",
        last_name: "Admin",
        super_admin: false
      )
      
      sign_in non_admin
      get :index
      assert_redirected_to root_path
    end

    # ===============================
    # SECURITY TESTS
    # ===============================

    test "should not allow bypassing authentication" do
      get :index
      assert_redirected_to new_session_path
      
      # Setting session manually shouldn't work without proper session
      session[:user_id] = @admin_user.id
      get :index
      assert_redirected_to new_session_path
    end

    test "should not allow bypassing admin requirement" do
      sign_in @vendor_user
      
      # Try to manually set admin flag in session (shouldn't work)
      session[:is_admin] = true
      get :index
      assert_redirected_to root_path
    end

    # ===============================
    # INTEGRATION TESTS
    # ===============================

    test "should work end-to-end for valid admin user" do
      sign_in @admin_user
      
      # Should be able to access admin overview
      get :index
      assert_response :success
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
    end
  end
end