require "test_helper"

module Admin
  class ProductScreenshotsControllerTest < ActionController::TestCase
    def setup
      # Create test users
      @admin_user = users(:admin)
      @vendor_user = users(:one)
      @admin_user.update!(super_admin: true)
      
      # Create test accounts
      @vendor_account = accounts(:one)
      @vendor_account.update!(
        account_type: 'vendor', 
        status: 'approved', 
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      @vendor_account.account_users.destroy_all
      @vendor_account.account_users.create!(user: @vendor_user, role: 'admin')
      
      # Create test product
      @product = @vendor_account.products.create!(
        name: "Test Product",
        description: "A test product description",
        website: "https://test.com",
        published: false
      )
      
      # Create test product screenshots
      @screenshot1 = @product.product_screenshots.build(
        title: "Dashboard Screenshot",
        description: "Main dashboard view",
        position: 1
      )
      @screenshot1.image.attach(
        io: StringIO.new("fake image data"),
        filename: "dashboard.png",
        content_type: "image/png"
      )
      @screenshot1.save!
      
      @screenshot2 = @product.product_screenshots.build(
        title: "Settings Screenshot",
        description: "Settings page view",
        position: 2
      )
      @screenshot2.image.attach(
        io: StringIO.new("fake image data 2"),
        filename: "settings.jpg",
        content_type: "image/jpeg"
      )
      @screenshot2.save!
    end

    # ===============================
    # AUTHENTICATION & AUTHORIZATION TESTS
    # ===============================

    test "should require admin authentication for all actions" do
      get :index, params: { product_id: @product.slug }
      assert_redirected_to new_session_path
      
      get :show, params: { product_id: @product.slug, id: @screenshot1.id }
      assert_redirected_to new_session_path
      
      get :new, params: { product_id: @product.slug }
      assert_redirected_to new_session_path
      
      post :create, params: { product_id: @product.slug, product_screenshot: { title: "Test" } }
      assert_redirected_to new_session_path
    end

    test "should require admin privileges" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_redirected_to root_path
      
      get :show, params: { product_id: @product.slug, id: @screenshot1.id }
      assert_redirected_to root_path
    end

    test "should allow admin users" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
    end

    # ===============================
    # INDEX ACTION TESTS
    # ===============================

    test "should show product screenshots index" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      assert_not_nil assigns(:product_screenshots)
      
      screenshots = assigns(:product_screenshots)
      assert screenshots.include?(@screenshot1)
      assert screenshots.include?(@screenshot2)
    end

    test "should order screenshots by position" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      screenshots = assigns(:product_screenshots).to_a
      assert_equal @screenshot1, screenshots.first
      assert_equal @screenshot2, screenshots.second
    end

    test "should set product from slug" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      assert_equal @product, assigns(:product)
    end

    # ===============================
    # SHOW ACTION TESTS
    # ===============================

    test "should show product screenshot details" do
      sign_in @admin_user
      
      get :show, params: { product_id: @product.slug, id: @screenshot1.id }
      assert_response :success
      assert_equal @screenshot1, assigns(:product_screenshot)
      assert_equal @product, assigns(:product)
    end

    test "should only show screenshots for specified product" do
      sign_in @admin_user
      
      # Create another product with screenshots
      other_product = @vendor_account.products.create!(
        name: "Other Product",
        description: "Another product",
        website: "https://other.com"
      )
      
      other_screenshot = other_product.product_screenshots.build(
        title: "Other Screenshot",
        description: "Screenshot for other product",
        position: 1
      )
      other_screenshot.image.attach(
        io: StringIO.new("other image data"),
        filename: "other.png",
        content_type: "image/png"
      )
      other_screenshot.save!
      
      # Should not be able to access other product's screenshot through this product
      assert_raises(ActiveRecord::RecordNotFound) do
        get :show, params: { product_id: @product.slug, id: other_screenshot.id }
      end
    end

    # ===============================
    # NEW ACTION TESTS
    # ===============================

    test "should show new product screenshot form" do
      sign_in @admin_user
      
      get :new, params: { product_id: @product.slug }
      assert_response :success
      assert_instance_of ProductScreenshot, assigns(:product_screenshot)
      assert assigns(:product_screenshot).new_record?
      assert_equal @product, assigns(:product_screenshot).product
    end

    # ===============================
    # CREATE ACTION TESTS
    # ===============================

    test "should create product screenshot with valid data" do
      sign_in @admin_user
      
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      screenshot_params = {
        title: "New Screenshot",
        description: "A new screenshot description",
        image: file,
        position: 3
      }
      
      assert_difference '@product.product_screenshots.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_screenshot: screenshot_params
        }
      end
      
      assert_redirected_to admin_product_product_screenshots_path(@product)
      assert_match /Screenshot was successfully created/, flash[:notice]
      
      screenshot = ProductScreenshot.last
      assert_equal "New Screenshot", screenshot.title
      assert_equal "A new screenshot description", screenshot.description
      assert_equal @product, screenshot.product
      assert_equal 3, screenshot.position
      assert screenshot.image.attached?
    end

    test "should create screenshot with different image formats" do
      sign_in @admin_user
      
      file = fixture_file_upload('test/fixtures/files/test_image.jpg', 'image/jpeg')
      screenshot_params = {
        title: "JPEG Screenshot",
        description: "Screenshot in JPEG format",
        image: file
      }
      
      assert_difference '@product.product_screenshots.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_screenshot: screenshot_params
        }
      end
      
      screenshot = ProductScreenshot.last
      assert screenshot.image.attached?
      assert_equal "test_image.jpg", screenshot.image.filename.to_s
    end

    test "should auto-assign position when not provided" do
      sign_in @admin_user
      
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      screenshot_params = {
        title: "Auto Position Screenshot",
        description: "Should get auto-assigned position",
        image: file
        # position not specified
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_screenshot: screenshot_params
      }
      
      screenshot = ProductScreenshot.last
      assert_equal 3, screenshot.position  # Should be after existing screenshots (1, 2)
    end

    test "should not create product screenshot with invalid data" do
      sign_in @admin_user
      
      invalid_params = {
        title: "",  # Required field
        description: ""
        # missing image
      }
      
      assert_no_difference 'ProductScreenshot.count' do
        post :create, params: { 
          product_id: @product.slug,
          product_screenshot: invalid_params
        }
      end
      
      assert_response :unprocessable_entity
      assert_not_nil assigns(:product_screenshot)
      assert assigns(:product_screenshot).errors.any?
    end

    test "should validate image presence" do
      sign_in @admin_user
      
      params_without_image = {
        title: "Valid Title",
        description: "Valid description",
        position: 1
      }
      
      assert_no_difference 'ProductScreenshot.count' do
        post :create, params: { 
          product_id: @product.slug,
          product_screenshot: params_without_image
        }
      end
      
      assert_response :unprocessable_entity
      screenshot = assigns(:product_screenshot)
      assert screenshot.errors[:image].any?
    end

    # ===============================
    # EDIT ACTION TESTS
    # ===============================

    test "should show edit product screenshot form" do
      sign_in @admin_user
      
      get :edit, params: { product_id: @product.slug, id: @screenshot1.id }
      assert_response :success
      assert_equal @screenshot1, assigns(:product_screenshot)
      assert_equal @product, assigns(:product)
    end

    # ===============================
    # UPDATE ACTION TESTS
    # ===============================

    test "should update product screenshot with valid data" do
      sign_in @admin_user
      
      update_params = {
        title: "Updated Screenshot Title",
        description: "Updated screenshot description",
        position: 5
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @screenshot1.id,
        product_screenshot: update_params
      }
      
      assert_redirected_to admin_product_product_screenshots_path(@product)
      assert_match /Screenshot was successfully updated/, flash[:notice]
      
      @screenshot1.reload
      assert_equal "Updated Screenshot Title", @screenshot1.title
      assert_equal "Updated screenshot description", @screenshot1.description
      assert_equal 5, @screenshot1.position
    end

    test "should update product screenshot image" do
      sign_in @admin_user
      
      new_file = fixture_file_upload('test/fixtures/files/test_image.jpg', 'image/jpeg')
      
      update_params = {
        title: @screenshot1.title,
        image: new_file
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @screenshot1.id,
        product_screenshot: update_params
      }
      
      assert_redirected_to admin_product_product_screenshots_path(@product)
      
      @screenshot1.reload
      assert @screenshot1.image.attached?
      assert_equal "test_image.jpg", @screenshot1.image.filename.to_s
    end

    test "should not update product screenshot with invalid data" do
      sign_in @admin_user
      
      original_title = @screenshot1.title
      
      invalid_params = {
        title: "",  # Invalid
        description: "Valid description"
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @screenshot1.id,
        product_screenshot: invalid_params
      }
      
      assert_response :unprocessable_entity
      assert_not_nil assigns(:product_screenshot)
      assert assigns(:product_screenshot).errors.any?
      
      @screenshot1.reload
      assert_equal original_title, @screenshot1.title
    end

    # ===============================
    # DESTROY ACTION TESTS
    # ===============================

    test "should delete product screenshot" do
      sign_in @admin_user
      
      assert_difference '@product.product_screenshots.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: @screenshot1.id }
      end
      
      assert_redirected_to admin_product_product_screenshots_path(@product)
      assert_match /Screenshot was successfully deleted/, flash[:notice]
    end

    test "should delete attached image when destroying screenshot" do
      sign_in @admin_user
      
      screenshot_with_image = @product.product_screenshots.build(
        title: "Screenshot to Delete",
        description: "This screenshot will be deleted",
        position: 3
      )
      screenshot_with_image.image.attach(
        io: StringIO.new("image to delete"),
        filename: "delete_me.png",
        content_type: "image/png"
      )
      screenshot_with_image.save!
      
      assert screenshot_with_image.image.attached?
      
      delete :destroy, params: { product_id: @product.slug, id: screenshot_with_image.id }
      
      assert_redirected_to admin_product_product_screenshots_path(@product)
    end

    # ===============================
    # UPDATE POSITION ACTION TESTS
    # ===============================

    test "should update screenshot position" do
      sign_in @admin_user
      
      patch :update_position, params: { 
        product_id: @product.slug,
        id: @screenshot1.id,
        position: 5
      }
      
      assert_response :ok
      
      @screenshot1.reload
      assert_equal 5, @screenshot1.position
    end

    test "should handle AJAX position updates" do
      sign_in @admin_user
      
      patch :update_position, params: { 
        product_id: @product.slug,
        id: @screenshot2.id,
        position: 1
      }, xhr: true
      
      assert_response :ok
      
      @screenshot2.reload
      assert_equal 1, @screenshot2.position
    end

    # ===============================
    # PARAMETER SECURITY TESTS
    # ===============================

    test "should filter unpermitted parameters" do
      sign_in @admin_user
      
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      malicious_params = {
        title: "Valid Title",
        description: "Valid description",
        image: file,
        position: 1,
        product_id: @product.id + 1,  # Should be filtered/ignored
        created_at: 1.year.ago,  # Should be filtered
        published: true  # Not in permit list
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_screenshot: malicious_params
      }
      
      screenshot = ProductScreenshot.last
      assert_equal "Valid Title", screenshot.title
      assert_equal @product, screenshot.product  # Should use product from URL, not params
      assert_not_equal 1.year.ago.to_date, screenshot.created_at.to_date
      # published should remain default (false) since it's not permitted
      assert_not screenshot.published?
    end

    test "should allow only permitted parameters" do
      sign_in @admin_user
      
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      permitted_params = {
        title: "Permitted Screenshot",
        description: "Permitted description",
        position: 1,
        image: file
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_screenshot: permitted_params
      }
      
      screenshot = ProductScreenshot.last
      assert_equal "Permitted Screenshot", screenshot.title
      assert_equal "Permitted description", screenshot.description
      assert_equal 1, screenshot.position
      assert screenshot.image.attached?
    end

    # ===============================
    # ERROR HANDLING TESTS
    # ===============================

    test "should handle invalid product id" do
      sign_in @admin_user
      
      assert_raises(ActiveRecord::RecordNotFound) do
        get :index, params: { product_id: 'invalid-slug' }
      end
    end

    test "should handle invalid screenshot id" do
      sign_in @admin_user
      
      assert_raises(ActiveRecord::RecordNotFound) do
        get :show, params: { product_id: @product.slug, id: 99999 }
      end
    end

    test "should scope screenshots to correct product" do
      sign_in @admin_user
      
      # Create another product
      other_product = @vendor_account.products.create!(
        name: "Other Product",
        description: "Another product",
        website: "https://other.com"
      )
      
      # Try to access @screenshot1 through other_product (should fail)
      assert_raises(ActiveRecord::RecordNotFound) do
        get :show, params: { product_id: other_product.slug, id: @screenshot1.id }
      end
    end

    # ===============================
    # INTEGRATION TESTS
    # ===============================

    test "should complete full screenshot management workflow" do
      sign_in @admin_user
      
      # Step 1: View screenshots index
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      # Step 2: Visit new screenshot page
      get :new, params: { product_id: @product.slug }
      assert_response :success
      
      # Step 3: Create screenshot
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      screenshot_params = {
        title: "Workflow Screenshot",
        description: "Screenshot created through workflow",
        image: file,
        position: 3
      }
      
      assert_difference '@product.product_screenshots.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_screenshot: screenshot_params
        }
      end
      
      screenshot = ProductScreenshot.last
      assert_redirected_to admin_product_product_screenshots_path(@product)
      
      # Step 4: View created screenshot
      get :show, params: { product_id: @product.slug, id: screenshot.id }
      assert_response :success
      assert_equal screenshot, assigns(:product_screenshot)
      
      # Step 5: Edit screenshot
      get :edit, params: { product_id: @product.slug, id: screenshot.id }
      assert_response :success
      
      # Step 6: Update screenshot
      patch :update, params: { 
        product_id: @product.slug,
        id: screenshot.id,
        product_screenshot: { title: "Updated Workflow Screenshot" }
      }
      
      assert_redirected_to admin_product_product_screenshots_path(@product)
      screenshot.reload
      assert_equal "Updated Workflow Screenshot", screenshot.title
      
      # Step 7: Update position
      patch :update_position, params: { 
        product_id: @product.slug,
        id: screenshot.id,
        position: 1
      }
      
      assert_response :ok
      screenshot.reload
      assert_equal 1, screenshot.position
      
      # Step 8: Delete screenshot
      assert_difference '@product.product_screenshots.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: screenshot.id }
      end
      
      assert_redirected_to admin_product_product_screenshots_path(@product)
    end

    test "should handle screenshot management with validation errors" do
      sign_in @admin_user
      
      # Step 1: Try to create invalid screenshot
      invalid_params = {
        title: "",  # Invalid
        description: "Valid description"
        # missing image
      }
      
      assert_no_difference 'ProductScreenshot.count' do
        post :create, params: { 
          product_id: @product.slug,
          product_screenshot: invalid_params
        }
      end
      
      assert_response :unprocessable_entity
      assert assigns(:product_screenshot).errors.any?
      
      # Step 2: Fix errors and create successfully
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      valid_params = {
        title: "Fixed Screenshot Title",
        description: "Valid description",
        image: file
      }
      
      assert_difference 'ProductScreenshot.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          product_screenshot: valid_params
        }
      end
      
      assert_redirected_to admin_product_product_screenshots_path(@product)
    end

    test "should maintain position ordering when managing screenshots" do
      sign_in @admin_user
      
      # Initial positions: screenshot1(1), screenshot2(2)
      assert_equal 1, @screenshot1.position
      assert_equal 2, @screenshot2.position
      
      # Create new screenshot with position 3
      file = fixture_file_upload('test/fixtures/files/test_image.png', 'image/png')
      screenshot_params = {
        title: "Third Screenshot",
        description: "Should get position 3",
        image: file,
        position: 3
      }
      
      post :create, params: { 
        product_id: @product.slug,
        product_screenshot: screenshot_params
      }
      
      third_screenshot = ProductScreenshot.last
      assert_equal 3, third_screenshot.position
      
      # Update position of third screenshot to 1
      patch :update_position, params: { 
        product_id: @product.slug,
        id: third_screenshot.id,
        position: 1
      }
      
      third_screenshot.reload
      assert_equal 1, third_screenshot.position
      
      # Delete middle screenshot
      delete :destroy, params: { product_id: @product.slug, id: @screenshot2.id }
      
      # Should still be able to manage remaining screenshots
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      screenshots = assigns(:product_screenshots)
      assert_equal 2, screenshots.count
      assert screenshots.include?(@screenshot1)
      assert screenshots.include?(third_screenshot)
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
    end
  end
end