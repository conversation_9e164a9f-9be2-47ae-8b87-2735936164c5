require "test_helper"

module Admin
  class HealthControllerTest < ActionController::TestCase
    setup do
      @admin_user = users(:one)
      @admin_user.update!(super_admin: true)
      
      @vendor_user = users(:two)
      @vendor_account = accounts(:one)
      @vendor_account.update!(
        account_type: 'vendor',
        status: 'approved',
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      @vendor_account.account_users.find_or_create_by(user: @vendor_user, role: 'admin')
    end

    # Authentication Tests
    test "should require authentication" do
      get :show
      assert_response :redirect
      assert_redirected_to new_session_path
    end

    test "should require super admin access" do
      sign_in @vendor_user
      session[:account_id] = @vendor_account.id
      
      get :show
      assert_response :redirect
      assert_redirected_to root_path
    end

    test "should allow super admin access" do
      sign_in @admin_user
      
      get :show
      assert_response :success
      assert_equal 'application/json', response.content_type
    end

    # Health Check Response Tests
    test "should return health status in JSON format" do
      sign_in @admin_user
      
      get :show
      assert_response :success
      
      json_response = JSON.parse(response.body)
      
      # Test top-level structure
      assert_equal 'healthy', json_response['status']
      assert_not_nil json_response['timestamp']
      assert_not_nil json_response['version']
      assert_equal Rails.env, json_response['environment']
      assert json_response.key?('checks')
      assert json_response.key?('metrics')
    end

    test "should include database health check" do
      sign_in @admin_user
      
      get :show
      json_response = JSON.parse(response.body)
      
      database_check = json_response['checks']['database']
      assert_not_nil database_check
      assert database_check.key?('status')
      assert database_check.key?('response_time')
      assert_equal 'healthy', database_check['status']
      assert database_check['response_time'].is_a?(Numeric)
    end

    test "should include jobs health check" do
      sign_in @admin_user
      
      get :show
      json_response = JSON.parse(response.body)
      
      jobs_check = json_response['checks']['jobs']
      assert_not_nil jobs_check
      assert jobs_check.key?('status')
      
      # Check for SolidQueue configuration
      if defined?(SolidQueue)
        assert_equal 'healthy', jobs_check['status']
        assert jobs_check.key?('failed_jobs')
        assert jobs_check.key?('pending_jobs')
        assert jobs_check.key?('response_time')
      else
        assert_equal 'not_configured', jobs_check['status']
      end
    end

    test "should include storage health check" do
      sign_in @admin_user
      
      get :show
      json_response = JSON.parse(response.body)
      
      storage_check = json_response['checks']['storage']
      assert_not_nil storage_check
      assert storage_check.key?('status')
      assert_equal 'healthy', storage_check['status']
      assert storage_check.key?('response_time')
      assert storage_check['response_time'].is_a?(Numeric)
    end

    test "should include memory health check" do
      sign_in @admin_user
      
      get :show
      json_response = JSON.parse(response.body)
      
      memory_check = json_response['checks']['memory']
      assert_not_nil memory_check
      assert memory_check.key?('status')
      assert_equal 'healthy', memory_check['status']
      assert memory_check.key?('pid')
      assert memory_check.key?('memory_percent')
      assert memory_check.key?('memory_rss')
      assert_equal Process.pid, memory_check['pid']
    end

    test "should include application metrics" do
      sign_in @admin_user
      
      get :show
      json_response = JSON.parse(response.body)
      
      metrics = json_response['metrics']
      assert_not_nil metrics
      
      # Test that all expected metrics are present
      assert metrics.key?('uptime')
      assert metrics.key?('users')
      assert metrics.key?('accounts')
      assert metrics.key?('products')
      assert metrics.key?('leads')
      assert metrics.key?('sessions')
      
      # Test that metrics are numeric or string values
      assert metrics['users'].is_a?(Integer)
      assert metrics['accounts'].is_a?(Integer)
      assert metrics['products'].is_a?(Integer)
      assert metrics['leads'].is_a?(Integer)
      assert metrics['sessions'].is_a?(Integer)
      assert metrics['uptime'].is_a?(String)
    end

    test "should return current timestamp in ISO8601 format" do
      sign_in @admin_user
      
      freeze_time = Time.current
      
      travel_to freeze_time do
        get :show
        json_response = JSON.parse(response.body)
        
        timestamp = json_response['timestamp']
        assert_not_nil timestamp
        
        # Test that timestamp is in ISO8601 format and matches current time
        parsed_time = Time.parse(timestamp)
        assert_in_delta freeze_time.to_i, parsed_time.to_i, 2 # Allow 2 second difference
      end
    end

    test "should return application version" do
      sign_in @admin_user
      
      get :show
      json_response = JSON.parse(response.body)
      
      version = json_response['version']
      assert_not_nil version
      assert version.is_a?(String)
      assert_equal Rails.application.class.module_parent_name.downcase, version
    end

    test "should return current environment" do
      sign_in @admin_user
      
      get :show
      json_response = JSON.parse(response.body)
      
      assert_equal Rails.env, json_response['environment']
    end

    # Error Handling Tests
    test "should handle database connection errors gracefully" do
      sign_in @admin_user
      
      # Simulate database error by temporarily disabling connection
      original_connection = ActiveRecord::Base.connection_pool
      ActiveRecord::Base.connection_pool.disconnect!
      
      begin
        get :show
        json_response = JSON.parse(response.body)
        
        database_check = json_response['checks']['database']
        assert_equal 'unhealthy', database_check['status']
        assert_not_nil database_check['error']
      ensure
        # Restore connection
        ActiveRecord::Base.establish_connection(ActiveRecord::Base.connection_db_config)
      end
    end

    test "should handle storage errors gracefully" do
      sign_in @admin_user
      
      # Test by trying to write to non-existent directory
      original_root = Rails.root
      Rails.stub(:root, Pathname.new('/nonexistent/path')) do
        get :show
        json_response = JSON.parse(response.body)
        
        storage_check = json_response['checks']['storage']
        assert_equal 'unhealthy', storage_check['status']
        assert_not_nil storage_check['error']
      end
    end

    test "should handle memory check errors gracefully" do
      sign_in @admin_user
      
      # This test is harder to mock without complex setup, so let's just verify
      # that the memory check runs without throwing unhandled exceptions
      get :show
      json_response = JSON.parse(response.body)
      
      memory_check = json_response['checks']['memory']
      assert_not_nil memory_check
      assert_includes ['healthy', 'unhealthy'], memory_check['status']
    end

    # Performance Tests
    test "should respond within reasonable time" do
      sign_in @admin_user
      
      start_time = Time.current
      get :show
      end_time = Time.current
      
      assert_response :success
      assert (end_time - start_time) < 5.0 # Should respond within 5 seconds
    end

    test "should include response times in checks" do
      sign_in @admin_user
      
      get :show
      json_response = JSON.parse(response.body)
      
      # Database check should include response time
      database_check = json_response['checks']['database']
      assert database_check['response_time'].is_a?(Numeric)
      assert database_check['response_time'] >= 0
      
      # Storage check should include response time
      storage_check = json_response['checks']['storage']
      assert storage_check['response_time'].is_a?(Numeric)
      assert storage_check['response_time'] >= 0
    end

    # Format Tests
    test "should format uptime correctly" do
      sign_in @admin_user
      
      get :show
      json_response = JSON.parse(response.body)
      
      uptime = json_response['metrics']['uptime']
      assert_not_nil uptime
      assert uptime.is_a?(String)
      
      # Should match expected format patterns (Xd Xh Xm, Xh Xm, or Xm)
      assert_match(/^(\d+d \d+h \d+m|\d+h \d+m|\d+m)$/, uptime)
    end

    test "should format memory usage correctly" do
      sign_in @admin_user
      
      get :show
      json_response = JSON.parse(response.body)
      
      memory_check = json_response['checks']['memory']
      memory_rss = memory_check['memory_rss']
      
      assert_not_nil memory_rss
      assert_match(/^\d+ MB$/, memory_rss)
    end

    # Security Tests
    test "should not expose sensitive information" do
      sign_in @admin_user
      
      get :show
      json_response = JSON.parse(response.body)
      
      # Ensure no sensitive data is exposed
      assert_not json_response.to_s.include?('password')
      assert_not json_response.to_s.include?('secret')
      assert_not json_response.to_s.include?('token')
      assert_not json_response.to_s.include?('key')
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
    end
  end
end