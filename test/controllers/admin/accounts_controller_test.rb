require "test_helper"

module Admin
  class AccountsControllerTest < ActionController::TestCase
    def setup
      # Create test users
      @admin_user = users(:admin)
      @vendor_user = users(:one)
      @agency_user = users(:two)
      @admin_user.update!(super_admin: true)
      
      # Create test accounts
      @vendor_account = accounts(:one)
      @agency_account = accounts(:two)
      @pending_account = accounts(:pending_vendor)
      
      @vendor_account.update!(
        account_type: 'vendor', 
        status: 'approved', 
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      @agency_account.update!(
        account_type: 'agency',
        status: 'approved', 
        confirmed_at: Time.current,
        owner: @agency_user
      )
      
      @pending_account.update!(
        account_type: 'vendor',
        status: 'pending',
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      # Clear existing account users to avoid duplicates
      @vendor_account.account_users.destroy_all
      @vendor_account.account_users.create!(user: @vendor_user, role: 'admin')
      
      @agency_account.account_users.destroy_all
      @agency_account.account_users.create!(user: @agency_user, role: 'admin')
      
      @pending_account.account_users.destroy_all
      @pending_account.account_users.create!(user: @vendor_user, role: 'admin')
    end

    # ===============================
    # AUTHENTICATION & AUTHORIZATION TESTS
    # ===============================

    test "should require admin authentication for all actions" do
      get :index
      assert_redirected_to new_session_path
      
      get :show, params: { id: @vendor_account.slug }
      assert_redirected_to new_session_path
      
      get :edit, params: { id: @vendor_account.slug }
      assert_redirected_to new_session_path
      
      patch :update, params: { id: @vendor_account.slug, account: { name: "Test" } }
      assert_redirected_to new_session_path
    end

    test "should require admin privileges" do
      sign_in @vendor_user
      
      get :index
      assert_redirected_to root_path
      
      get :show, params: { id: @vendor_account.slug }
      assert_redirected_to root_path
    end

    test "should allow admin users" do
      sign_in @admin_user
      
      get :index
      assert_response :success
    end

    # ===============================
    # INDEX ACTION TESTS
    # ===============================

    test "should show accounts index" do
      sign_in @admin_user
      
      get :index
      assert_response :success
      assert_not_nil assigns(:accounts)
      
      accounts = assigns(:accounts)
      assert accounts.include?(@vendor_account)
      assert accounts.include?(@agency_account)
      assert accounts.include?(@pending_account)
    end

    test "should order accounts by creation date descending" do
      sign_in @admin_user
      
      # Create a newer account
      newer_account = Account.create!(
        name: "Newer Account",
        account_type: "vendor",
        status: "pending",
        owner: @vendor_user
      )
      
      get :index
      assert_response :success
      
      accounts = assigns(:accounts).to_a
      newer_index = accounts.index(newer_account)
      older_index = accounts.index(@vendor_account)
      
      assert newer_index < older_index, "Newer account should appear first"
    end

    test "should include owner and approved_by associations" do
      sign_in @admin_user
      
      # Approve an account to test approved_by association
      @pending_account.approve!(@admin_user)
      
      get :index
      assert_response :success
      
      accounts = assigns(:accounts)
      account_with_approver = accounts.find { |a| a.id == @pending_account.id }
      
      # Check that associations are included/preloaded
      assert_not_nil account_with_approver.owner
      assert_not_nil account_with_approver.approved_by if account_with_approver.approved_by
    end

    test "should filter by search term" do
      sign_in @admin_user
      
      get :index, params: { search: @vendor_account.name }
      assert_response :success
      
      accounts = assigns(:accounts)
      assert accounts.include?(@vendor_account)
      assert_not accounts.include?(@agency_account)
    end

    test "should filter by status" do
      sign_in @admin_user
      
      get :index, params: { status: 'pending' }
      assert_response :success
      
      accounts = assigns(:accounts)
      assert accounts.include?(@pending_account)
      assert_not accounts.include?(@vendor_account)
    end

    test "should filter by account type" do
      sign_in @admin_user
      
      get :index, params: { account_type: 'vendor' }
      assert_response :success
      
      accounts = assigns(:accounts)
      assert accounts.include?(@vendor_account)
      assert_not accounts.include?(@agency_account)
    end

    test "should combine search and filters" do
      sign_in @admin_user
      
      get :index, params: { 
        search: @vendor_account.name,
        status: 'approved',
        account_type: 'vendor'
      }
      assert_response :success
      
      accounts = assigns(:accounts)
      assert accounts.include?(@vendor_account)
      assert_not accounts.include?(@agency_account)
      assert_not accounts.include?(@pending_account)
    end

    # ===============================
    # SHOW ACTION TESTS
    # ===============================

    test "should show account details" do
      sign_in @admin_user
      
      get :show, params: { id: @vendor_account.slug }
      assert_response :success
      assert_equal @vendor_account, assigns(:account)
    end

    test "should find account by slug" do
      sign_in @admin_user
      
      get :show, params: { id: @vendor_account.slug }
      assert_response :success
      assert_equal @vendor_account, assigns(:account)
    end

    test "should handle invalid account id" do
      sign_in @admin_user
      
      assert_raises(ActiveRecord::RecordNotFound) do
        get :show, params: { id: 'invalid-slug' }
      end
    end

    # ===============================
    # EDIT ACTION TESTS
    # ===============================

    test "should show edit account form" do
      sign_in @admin_user
      
      get :edit, params: { id: @vendor_account.slug }
      assert_response :success
      assert_equal @vendor_account, assigns(:account)
    end

    # ===============================
    # UPDATE ACTION TESTS
    # ===============================

    test "should update account with valid data" do
      sign_in @admin_user
      
      update_params = {
        name: "Updated Account Name",
        account_type: "agency",
        status: "approved"
      }
      
      patch :update, params: { id: @vendor_account.slug, account: update_params }
      
      assert_redirected_to admin_account_path(@vendor_account)
      assert_match /Account was successfully updated/, flash[:notice]
      
      @vendor_account.reload
      assert_equal "Updated Account Name", @vendor_account.name
      assert_equal "agency", @vendor_account.account_type
      assert_equal "approved", @vendor_account.status
    end

    test "should not update account with invalid data" do
      sign_in @admin_user
      
      original_name = @vendor_account.name
      
      invalid_params = {
        name: "",  # Invalid - required field
        account_type: "vendor"
      }
      
      patch :update, params: { id: @vendor_account.slug, account: invalid_params }
      
      assert_response :unprocessable_entity
      assert_not_nil assigns(:account)
      assert assigns(:account).errors.any?
      
      @vendor_account.reload
      assert_equal original_name, @vendor_account.name
    end

    # ===============================
    # DESTROY ACTION TESTS
    # ===============================

    test "should delete account" do
      sign_in @admin_user
      
      assert_difference 'Account.count', -1 do
        delete :destroy, params: { id: @vendor_account.slug }
      end
      
      assert_redirected_to admin_accounts_path
      assert_match /Account was successfully deleted/, flash[:notice]
    end

    # ===============================
    # APPROVE ACTION TESTS
    # ===============================

    test "should approve pending account" do
      sign_in @admin_user
      
      patch :approve, params: { id: @pending_account.slug }
      
      assert_redirected_to admin_accounts_path
      assert_match /#{@pending_account.name} has been approved/, flash[:notice]
      
      @pending_account.reload
      assert_equal 'approved', @pending_account.status
      assert_equal @admin_user, @pending_account.approved_by
    end

    # ===============================
    # REJECT ACTION TESTS
    # ===============================

    test "should reject pending account" do
      sign_in @admin_user
      
      patch :reject, params: { id: @pending_account.slug }
      
      assert_redirected_to admin_accounts_path
      assert_match /#{@pending_account.name} has been rejected/, flash[:notice]
      
      @pending_account.reload
      assert_equal 'rejected', @pending_account.status
      assert_equal @admin_user, @pending_account.approved_by
    end

    # ===============================
    # TEAM MEMBERS ACTION TESTS
    # ===============================

    test "should show team members" do
      sign_in @admin_user
      
      get :team_members, params: { id: @vendor_account.slug }
      assert_response :success
      
      assert_not_nil assigns(:team_members)
      assert_not_nil assigns(:pending_invitations)
      assert_not_nil assigns(:users)
      
      team_members = assigns(:team_members)
      assert team_members.any? { |tm| tm.user == @vendor_user }
    end

    test "should include pending invitations" do
      sign_in @admin_user
      
      # Create a pending invitation with unique email
      unique_email = "invited-#{SecureRandom.hex(4)}@example.com"
      invitation = TeamInvitation.create!(
        account: @vendor_account,
        email: unique_email,
        role: "member",
        invited_by: @admin_user,
        token: SecureRandom.urlsafe_base64(32),
        expires_at: 7.days.from_now,
        status: "pending"
      )
      
      get :team_members, params: { id: @vendor_account.slug }
      assert_response :success
      
      pending_invitations = assigns(:pending_invitations)
      assert pending_invitations.include?(invitation)
    end

    # ===============================
    # NEW TEAM MEMBER ACTION TESTS
    # ===============================

    test "should show new team member form" do
      sign_in @admin_user
      
      get :new_team_member, params: { id: @vendor_account.slug }
      assert_response :success
      
      assert_instance_of User, assigns(:user)
      assert_instance_of AccountUser, assigns(:account_user)
      assert assigns(:user).new_record?
      assert assigns(:account_user).new_record?
    end

    # ===============================
    # CREATE TEAM MEMBER ACTION TESTS
    # ===============================

    test "should create team member with valid data" do
      sign_in @admin_user
      
      user_params = {
        first_name: "New",
        last_name: "Member",
        email_address: "<EMAIL>",
        phone: "555-1234",
        job_title: "Developer",
        password: "password123",
        password_confirmation: "password123"
      }
      
      account_user_params = {
        role: "member"
      }
      
      assert_difference ['User.count', 'AccountUser.count'], 1 do
        post :create_team_member, params: { 
          id: @vendor_account.slug,
          user: user_params,
          account_user: account_user_params
        }
      end
      
      assert_redirected_to team_members_admin_account_path(@vendor_account)
      assert_match /Team member .* created successfully/, flash[:notice]
      
      new_user = User.last
      assert_equal "New", new_user.first_name
      assert_equal "Member", new_user.last_name
      
      account_user = AccountUser.last
      assert_equal new_user, account_user.user
      assert_equal @vendor_account, account_user.account
      assert_equal "member", account_user.role
    end

    test "should generate password if not provided" do
      sign_in @admin_user
      
      user_params = {
        first_name: "Auto",
        last_name: "Password",
        email_address: "<EMAIL>"
      }
      
      account_user_params = {
        role: "member"
      }
      
      assert_difference 'User.count', 1 do
        post :create_team_member, params: { 
          id: @vendor_account.slug,
          user: user_params,
          account_user: account_user_params
        }
      end
      
      new_user = User.last
      assert new_user.authenticate("password123") == false  # Should have generated password
      assert new_user.password_digest.present?
    end

    test "should not create team member with invalid user data" do
      sign_in @admin_user
      
      invalid_user_params = {
        first_name: "",  # Required
        email_address: "invalid-email"  # Invalid format
      }
      
      account_user_params = {
        role: "member"
      }
      
      assert_no_difference ['User.count', 'AccountUser.count'] do
        post :create_team_member, params: { 
          id: @vendor_account.slug,
          user: invalid_user_params,
          account_user: account_user_params
        }
      end
      
      assert_response :unprocessable_entity
      assert assigns(:user).errors.any?
    end

    # ===============================
    # EDIT TEAM MEMBER ACTION TESTS
    # ===============================

    test "should show edit team member form" do
      sign_in @admin_user
      team_member = @vendor_account.account_users.first
      
      get :edit_team_member, params: { 
        id: @vendor_account.slug,
        team_member_id: team_member.id
      }
      assert_response :success
      
      assert_equal team_member, assigns(:team_member)
      assert_equal team_member.user, assigns(:user)
    end

    # ===============================
    # UPDATE TEAM MEMBER ACTION TESTS
    # ===============================

    test "should update team member with valid data" do
      sign_in @admin_user
      team_member = @vendor_account.account_users.first
      original_role = team_member.role
      
      user_params = {
        first_name: "Updated",
        last_name: "Name"
      }
      
      account_user_params = {
        role: "admin"
      }
      
      patch :update_team_member, params: { 
        id: @vendor_account.slug,
        team_member_id: team_member.id,
        user: user_params,
        account_user: account_user_params
      }
      
      assert_redirected_to team_members_admin_account_path(@vendor_account)
      assert_match /Team member .* updated successfully/, flash[:notice]
      
      team_member.reload
      assert_equal "Updated", team_member.user.first_name
      assert_equal "admin", team_member.role
    end

    # ===============================
    # DESTROY TEAM MEMBER ACTION TESTS
    # ===============================

    test "should remove team member" do
      sign_in @admin_user
      
      # Create additional team member (not the owner)
      new_user = User.create!(
        first_name: "Remove",
        last_name: "Me",
        email_address: "<EMAIL>",
        password: "password123"
      )
      
      team_member = @vendor_account.account_users.create!(
        user: new_user,
        role: "member",
        joined_at: Time.current
      )
      
      assert_difference '@vendor_account.account_users.count', -1 do
        delete :destroy_team_member, params: { 
          id: @vendor_account.slug,
          team_member_id: team_member.id
        }
      end
      
      assert_redirected_to team_members_admin_account_path(@vendor_account)
      assert_match /Team member removed successfully/, flash[:notice]
    end

    test "should not remove account owner" do
      sign_in @admin_user
      owner_team_member = @vendor_account.account_users.find_by(user: @vendor_account.owner)
      
      assert_no_difference '@vendor_account.account_users.count' do
        delete :destroy_team_member, params: { 
          id: @vendor_account.slug,
          team_member_id: owner_team_member.id
        }
      end
      
      assert_redirected_to team_members_admin_account_path(@vendor_account)
      assert_match /Cannot remove the account owner/, flash[:alert]
    end

    # ===============================
    # TEAM INVITATION TESTS
    # ===============================

    test "should show new team invitation form" do
      sign_in @admin_user
      
      get :new_team_invitation, params: { id: @vendor_account.slug }
      assert_response :success
      
      assert_instance_of TeamInvitation, assigns(:team_invitation)
      assert assigns(:team_invitation).new_record?
    end

    test "should create team invitation with valid data" do
      sign_in @admin_user
      
      invitation_params = {
        email: "<EMAIL>",
        role: "member",
        message: "Welcome to our team!"
      }
      
      assert_difference 'TeamInvitation.count', 1 do
        post :create_team_invitation, params: { 
          id: @vendor_account.slug,
          team_invitation: invitation_params
        }
      end
      
      assert_redirected_to team_members_admin_account_path(@vendor_account)
      assert_match /Team invitation <NAME_EMAIL>/, flash[:notice]
      
      invitation = TeamInvitation.last
      assert_equal "<EMAIL>", invitation.email
      assert_equal "member", invitation.role
      assert_equal @admin_user, invitation.invited_by
      assert_equal @vendor_account, invitation.account
      assert invitation.token.present?
      assert invitation.expires_at > Time.current
    end

    test "should destroy team invitation" do
      sign_in @admin_user
      
      invitation = TeamInvitation.create!(
        account: @vendor_account,
        email: "<EMAIL>",
        role: "member",
        invited_by: @admin_user,
        token: SecureRandom.urlsafe_base64(32),
        expires_at: 7.days.from_now,
        status: "pending"
      )
      
      assert_difference 'TeamInvitation.count', -1 do
        delete :destroy_team_invitation, params: { 
          id: @vendor_account.slug,
          invitation_id: invitation.id
        }
      end
      
      assert_redirected_to team_members_admin_account_path(@vendor_account)
      assert_match /Invitation cancelled successfully/, flash[:notice]
    end

    # ===============================
    # ADD EXISTING USER TESTS
    # ===============================

    test "should add existing user to account" do
      sign_in @admin_user
      
      existing_user = User.create!(
        first_name: "Existing",
        last_name: "User",
        email_address: "<EMAIL>",
        password: "password123"
      )
      
      assert_difference '@vendor_account.account_users.count', 1 do
        post :add_existing_user, params: { 
          id: @vendor_account.slug,
          user_id: existing_user.id,
          role: "member"
        }
      end
      
      assert_redirected_to team_members_admin_account_path(@vendor_account)
      assert_match /#{existing_user.full_name} added to team successfully/, flash[:notice]
      
      account_user = @vendor_account.account_users.find_by(user: existing_user)
      assert_not_nil account_user
      assert_equal "member", account_user.role
    end

    # ===============================
    # PARAMETER SECURITY TESTS
    # ===============================

    test "should filter unpermitted account parameters" do
      sign_in @admin_user
      
      malicious_params = {
        name: "Valid Name",
        account_type: "vendor",
        status: "approved",
        created_at: 1.year.ago,  # Should be filtered
        id: 999,  # Should be filtered
        owner_id: @agency_user.id  # Should be filtered
      }
      
      patch :update, params: { id: @vendor_account.slug, account: malicious_params }
      
      @vendor_account.reload
      assert_equal "Valid Name", @vendor_account.name
      assert_equal "vendor", @vendor_account.account_type
      assert_equal "approved", @vendor_account.status
      assert_not_equal 1.year.ago.to_date, @vendor_account.created_at.to_date
      assert_not_equal @agency_user, @vendor_account.owner
    end

    # ===============================
    # ERROR HANDLING TESTS
    # ===============================

    test "should handle team member not found" do
      sign_in @admin_user
      
      get :edit_team_member, params: { 
        id: @vendor_account.slug,
        team_member_id: 99999
      }
      
      assert_redirected_to team_members_admin_account_path(@vendor_account)
      assert_match /Team member not found/, flash[:alert]
    end

    test "should handle invitation not found" do
      sign_in @admin_user
      
      delete :destroy_team_invitation, params: { 
        id: @vendor_account.slug,
        invitation_id: 99999
      }
      
      assert_redirected_to team_members_admin_account_path(@vendor_account)
      assert_match /Invitation not found/, flash[:alert]
    end

    # ===============================
    # INTEGRATION TESTS
    # ===============================

    test "should complete full account management workflow" do
      sign_in @admin_user
      
      # Step 1: View accounts index
      get :index
      assert_response :success
      
      # Step 2: View pending account details
      get :show, params: { id: @pending_account.slug }
      assert_response :success
      
      # Step 3: Approve the account
      patch :approve, params: { id: @pending_account.slug }
      assert_redirected_to admin_accounts_path
      
      @pending_account.reload
      assert_equal 'approved', @pending_account.status
      
      # Step 4: Edit account details
      get :edit, params: { id: @pending_account.slug }
      assert_response :success
      
      # Step 5: Update account
      patch :update, params: { 
        id: @pending_account.slug,
        account: { name: "Updated Approved Account" }
      }
      
      assert_redirected_to admin_account_path(@pending_account)
      
      @pending_account.reload
      assert_equal "Updated Approved Account", @pending_account.name
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
    end
  end
end