require "test_helper"

module Admin
  class CaseStudiesControllerTest < ActionController::TestCase
    def setup
      # Create test users
      @admin_user = users(:admin)
      @vendor_user = users(:one)
      @admin_user.update!(super_admin: true)
      
      # Create test accounts
      @vendor_account = accounts(:one)
      @vendor_account.update!(
        account_type: 'vendor', 
        status: 'approved', 
        confirmed_at: Time.current,
        owner: @vendor_user
      )
      
      @vendor_account.account_users.destroy_all
      @vendor_account.account_users.create!(user: @vendor_user, role: 'admin')
      
      # Create test product
      @product = @vendor_account.products.create!(
        name: "Test Product",
        description: "A test product description",
        website: "https://test.com",
        published: false
      )
      
      # Create test case studies
      @case_study1 = @product.case_studies.build(
        title: "Success Story 1",
        description: "A detailed case study about customer success",
        position: 1
      )
      @case_study1.upload.attach(
        io: StringIO.new("fake pdf data"),
        filename: "case_study1.pdf",
        content_type: "application/pdf"
      )
      @case_study1.save!
      
      @case_study2 = @product.case_studies.build(
        title: "Success Story 2",
        description: "Another customer success story",
        position: 2
      )
      @case_study2.upload.attach(
        io: StringIO.new("fake pdf data 2"),
        filename: "case_study2.pdf",
        content_type: "application/pdf"
      )
      @case_study2.save!
    end

    # ===============================
    # AUTHENTICATION & AUTHORIZATION TESTS
    # ===============================

    test "should require admin authentication for all actions" do
      get :index, params: { product_id: @product.slug }
      assert_redirected_to new_session_path
      
      get :show, params: { product_id: @product.slug, id: @case_study1.id }
      assert_redirected_to new_session_path
      
      get :new, params: { product_id: @product.slug }
      assert_redirected_to new_session_path
      
      post :create, params: { product_id: @product.slug, case_study: { title: "Test" } }
      assert_redirected_to new_session_path
    end

    test "should require admin privileges" do
      sign_in @vendor_user
      
      get :index, params: { product_id: @product.slug }
      assert_redirected_to root_path
    end

    test "should allow admin users" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
    end

    # ===============================
    # INDEX ACTION TESTS
    # ===============================

    test "should show case studies index" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      assert_not_nil assigns(:case_studies)
      
      case_studies = assigns(:case_studies)
      assert case_studies.include?(@case_study1)
      assert case_studies.include?(@case_study2)
    end

    test "should order case studies by position" do
      sign_in @admin_user
      
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      case_studies = assigns(:case_studies).to_a
      assert_equal @case_study1, case_studies.first
      assert_equal @case_study2, case_studies.second
    end

    # ===============================
    # SHOW ACTION TESTS
    # ===============================

    test "should show case study details" do
      sign_in @admin_user
      
      get :show, params: { product_id: @product.slug, id: @case_study1.id }
      assert_response :success
      assert_equal @case_study1, assigns(:case_study)
      assert_equal @product, assigns(:product)
    end

    # ===============================
    # NEW ACTION TESTS
    # ===============================

    test "should show new case study form" do
      sign_in @admin_user
      
      get :new, params: { product_id: @product.slug }
      assert_response :success
      assert_instance_of CaseStudy, assigns(:case_study)
      assert assigns(:case_study).new_record?
      assert_equal @product, assigns(:case_study).product
    end

    # ===============================
    # CREATE ACTION TESTS
    # ===============================

    test "should create case study with valid data" do
      sign_in @admin_user
      
      case_study_params = {
        title: "New Case Study",
        description: "A new case study description"
      }
      
      assert_difference '@product.case_studies.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          case_study: case_study_params
        }
      end
      
      assert_redirected_to admin_product_case_studies_path(@product)
      assert_match /Case study was successfully created/, flash[:notice]
      
      case_study = CaseStudy.last
      assert_equal "New Case Study", case_study.title
      assert_equal "A new case study description", case_study.description
      assert_equal @product, case_study.product
    end

    test "should create case study with file upload" do
      sign_in @admin_user
      
      file = fixture_file_upload('test/fixtures/files/test_document.pdf', 'application/pdf')
      
      case_study_params = {
        title: "Case Study with File",
        description: "Case study with uploaded file",
        upload: file
      }
      
      assert_difference '@product.case_studies.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          case_study: case_study_params
        }
      end
      
      case_study = CaseStudy.last
      assert case_study.upload.attached?
      assert_equal "test_document.pdf", case_study.upload.filename.to_s
    end

    test "should not create case study with invalid data" do
      sign_in @admin_user
      
      invalid_params = {
        title: "",  # Required field
        description: ""
      }
      
      assert_no_difference 'CaseStudy.count' do
        post :create, params: { 
          product_id: @product.slug,
          case_study: invalid_params
        }
      end
      
      assert_response :unprocessable_entity
      assert_not_nil assigns(:case_study)
      assert assigns(:case_study).errors.any?
    end

    # ===============================
    # EDIT ACTION TESTS
    # ===============================

    test "should show edit case study form" do
      sign_in @admin_user
      
      get :edit, params: { product_id: @product.slug, id: @case_study1.id }
      assert_response :success
      assert_equal @case_study1, assigns(:case_study)
      assert_equal @product, assigns(:product)
    end

    # ===============================
    # UPDATE ACTION TESTS
    # ===============================

    test "should update case study with valid data" do
      sign_in @admin_user
      
      update_params = {
        title: "Updated Case Study Title",
        description: "Updated case study description"
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @case_study1.id,
        case_study: update_params
      }
      
      assert_redirected_to admin_product_case_studies_path(@product)
      assert_match /Case study was successfully updated/, flash[:notice]
      
      @case_study1.reload
      assert_equal "Updated Case Study Title", @case_study1.title
      assert_equal "Updated case study description", @case_study1.description
    end

    test "should not update case study with invalid data" do
      sign_in @admin_user
      
      original_title = @case_study1.title
      
      invalid_params = {
        title: "",  # Invalid
        description: "Valid description"
      }
      
      patch :update, params: { 
        product_id: @product.slug,
        id: @case_study1.id,
        case_study: invalid_params
      }
      
      assert_response :unprocessable_entity
      assert_not_nil assigns(:case_study)
      assert assigns(:case_study).errors.any?
      
      @case_study1.reload
      assert_equal original_title, @case_study1.title
    end

    # ===============================
    # DESTROY ACTION TESTS
    # ===============================

    test "should delete case study" do
      sign_in @admin_user
      
      assert_difference '@product.case_studies.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: @case_study1.id }
      end
      
      assert_redirected_to admin_product_case_studies_path(@product)
      assert_match /Case study was successfully deleted/, flash[:notice]
    end

    # ===============================
    # UPDATE POSITION ACTION TESTS
    # ===============================

    test "should update case study position" do
      sign_in @admin_user
      
      patch :update_position, params: { 
        product_id: @product.slug,
        id: @case_study1.id,
        position: 5
      }
      
      assert_response :ok
      
      @case_study1.reload
      assert_equal 5, @case_study1.position
    end

    # ===============================
    # INTEGRATION TESTS
    # ===============================

    test "should complete full case study management workflow" do
      sign_in @admin_user
      
      # Step 1: View case studies index
      get :index, params: { product_id: @product.slug }
      assert_response :success
      
      # Step 2: Visit new case study page
      get :new, params: { product_id: @product.slug }
      assert_response :success
      
      # Step 3: Create case study
      case_study_params = {
        title: "Workflow Case Study",
        description: "Case study created through workflow"
      }
      
      assert_difference '@product.case_studies.count', 1 do
        post :create, params: { 
          product_id: @product.slug,
          case_study: case_study_params
        }
      end
      
      case_study = CaseStudy.last
      assert_redirected_to admin_product_case_studies_path(@product)
      
      # Step 4: View created case study
      get :show, params: { product_id: @product.slug, id: case_study.id }
      assert_response :success
      
      # Step 5: Edit case study
      get :edit, params: { product_id: @product.slug, id: case_study.id }
      assert_response :success
      
      # Step 6: Update case study
      patch :update, params: { 
        product_id: @product.slug,
        id: case_study.id,
        case_study: { title: "Updated Workflow Case Study" }
      }
      
      assert_redirected_to admin_product_case_studies_path(@product)
      case_study.reload
      assert_equal "Updated Workflow Case Study", case_study.title
      
      # Step 7: Update position
      patch :update_position, params: { 
        product_id: @product.slug,
        id: case_study.id,
        position: 1
      }
      
      assert_response :ok
      case_study.reload
      assert_equal 1, case_study.position
      
      # Step 8: Delete case study
      assert_difference '@product.case_studies.count', -1 do
        delete :destroy, params: { product_id: @product.slug, id: case_study.id }
      end
      
      assert_redirected_to admin_product_case_studies_path(@product)
    end

    private

    def sign_in(user)
      session_record = user.sessions.create!(
        user_agent: 'Test User Agent',
        ip_address: '127.0.0.1'
      )
      Current.session = session_record
      cookies.signed[:session_id] = session_record.id
    end
  end
end