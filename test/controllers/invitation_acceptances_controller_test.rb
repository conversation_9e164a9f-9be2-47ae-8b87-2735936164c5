require "test_helper"

class InvitationAcceptancesControllerTest < ActionController::TestCase
  def setup
    @vendor_account = accounts(:one)
    @agency_account = accounts(:two)
    @admin_user = users(:admin)
    @vendor_user = users(:one)
    @agency_user = users(:two)
    
    # Create test invitations
    @pending_invitation = TeamInvitation.create!(
      account: @vendor_account,
      invited_by: @vendor_user,
      email: "<EMAIL>",
      role: "member",
      status: "pending",
      message: "Join our team!",
      token: "pending_token_123",
      expires_at: 7.days.from_now
    )
    
    @expired_invitation = TeamInvitation.create!(
      account: @vendor_account,
      invited_by: @vendor_user,
      email: "<EMAIL>",
      role: "member",
      status: "pending",
      message: "This invitation expired",
      token: "expired_token_456",
      expires_at: 1.day.ago
    )
    
    @accepted_invitation = TeamInvitation.create!(
      account: @vendor_account,
      invited_by: @vendor_user,
      email: "<EMAIL>",
      role: "member",
      status: "accepted",
      message: "Already accepted",
      token: "accepted_token_789",
      expires_at: 7.days.from_now
    )
    
    @declined_invitation = TeamInvitation.create!(
      account: @vendor_account,
      invited_by: @vendor_user,
      email: "<EMAIL>",
      role: "member",
      status: "declined",
      message: "Already declined",
      token: "declined_token_abc",
      expires_at: 7.days.from_now
    )
    
    # Create user that exists for testing
    @existing_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Existing",
      last_name: "User",
      job_title: "Developer",
      department: "Engineering",
      
    )
    
    @existing_user_invitation = TeamInvitation.create!(
      account: @vendor_account,
      invited_by: @vendor_user,
      email: @existing_user.email_address,
      role: "admin",
      status: "pending",
      message: "Join us!",
      token: "existing_user_token_def",
      expires_at: 7.days.from_now
    )
  end

  # ===============================
  # AUTHENTICATION TESTS
  # ===============================

  test "should allow unauthenticated access" do
    get :show, params: { token: @pending_invitation.token }
    assert_response :success
    assert_nil session[:return_to_after_authenticating]
  end

  test "should not require authentication for any action" do
    [:show, :accept, :decline].each do |action|
      if action == :show
        get action, params: { token: @pending_invitation.token }
      else
        post action, params: { token: @pending_invitation.token }
      end
      # Should respond successfully or redirect (not to login)
      assert_includes [200, 302], response.status
    end
  end

  # ===============================
  # SHOW ACTION TESTS - VALID INVITATIONS
  # ===============================

  test "should show invitation details" do
    get :show, params: { token: @pending_invitation.token }
    
    assert_response :success
    assert_template :show
    assert_equal @pending_invitation, assigns(:invitation)
    assert_equal 'show', assigns(:action)
  end

  test "should detect accept action from URL" do
    get :show, params: { token: @pending_invitation.token }
    @request.path = "/invitations/#{@pending_invitation.token}/accept"
    
    get :show, params: { token: @pending_invitation.token }
    assert_equal 'accept', assigns(:action)
  end

  test "should detect decline action from URL" do
    get :show, params: { token: @pending_invitation.token }
    @request.path = "/invitations/#{@pending_invitation.token}/decline"
    
    get :show, params: { token: @pending_invitation.token }
    assert_equal 'decline', assigns(:action)
  end

  test "should find existing user by email" do
    get :show, params: { token: @existing_user_invitation.token }
    
    assert_equal @existing_user, assigns(:existing_user)
    assert_nil assigns(:user)
  end

  test "should create new user instance for non-existing email" do
    get :show, params: { token: @pending_invitation.token }
    
    assert_nil assigns(:existing_user)
    user = assigns(:user)
    assert_not_nil user
    assert_equal @pending_invitation.email, user.email_address
  end

  # ===============================
  # SHOW ACTION TESTS - INVALID INVITATIONS
  # ===============================

  test "should redirect for invalid token" do
    get :show, params: { token: "invalid_token" }
    
    assert_redirected_to root_path
    assert_match /Invalid invitation link/, flash[:alert]
  end

  test "should redirect for expired invitation" do
    get :show, params: { token: @expired_invitation.token }
    
    assert_redirected_to root_path
    assert_match /expired/, flash[:alert]
  end

  test "should redirect for already accepted invitation" do
    get :show, params: { token: @accepted_invitation.token }
    
    assert_redirected_to root_path
    assert_match /already been accepted/, flash[:notice]
  end

  test "should redirect for already declined invitation" do
    get :show, params: { token: @declined_invitation.token }
    
    assert_redirected_to root_path
    assert_match /already been declined/, flash[:notice]
  end

  # ===============================
  # ACCEPT ACTION TESTS - AUTHENTICATED USER
  # ===============================

  test "should accept invitation for authenticated user" do
    sign_in_as @vendor_user
    
    post :accept, params: { token: @pending_invitation.token }
    
    # Should redirect to wherever after_authentication_url points
    assert_response :redirect
    assert_match /Welcome to the team/, flash[:notice]
    
    @pending_invitation.reload
    assert_equal "accepted", @pending_invitation.status
  end

  test "should handle invitation acceptance failure for authenticated user" do
    sign_in_as @vendor_user
    
    # Mock the accept! method to return false
    TeamInvitation.any_instance.expects(:accept!).returns(false)
    
    post :accept, params: { token: @pending_invitation.token }
    
    assert_redirected_to @pending_invitation
    assert_match /Unable to accept invitation/, flash[:alert]
  end

  # ===============================
  # ACCEPT ACTION TESTS - EXISTING USER NOT SIGNED IN
  # ===============================

  test "should redirect existing user to sign in" do
    post :accept, params: { token: @existing_user_invitation.token }
    
    assert_redirected_to new_session_path
    assert_match /Please sign in to accept/, flash[:notice]
    assert_equal @existing_user_invitation.token, session[:invitation_token]
  end

  # ===============================
  # ACCEPT ACTION TESTS - NEW USER CREATION
  # ===============================

  test "should create new user and accept invitation" do
    assert_difference ['User.count'], 1 do
      post :accept, params: {
        token: @pending_invitation.token,
        user: {
          first_name: "New",
          last_name: "User",
          job_title: "Developer",
          password: "password123",
          password_confirmation: "password123"
        }
      }
    end
    
    # Should redirect to wherever after_authentication_url points
    assert_response :redirect
    assert_match /Welcome! Your account has been created/, flash[:notice]
    
    @pending_invitation.reload
    assert_equal "accepted", @pending_invitation.status
    
    # Should be signed in
    assert_not_nil Current.session
    assert_equal User.last, Current.user
  end

  test "should handle user creation failure" do
    assert_no_difference 'User.count' do
      post :accept, params: {
        token: @pending_invitation.token,
        user: {
          first_name: "",  # Invalid
          last_name: "User",
          password: "123",  # Too short
          password_confirmation: "456"  # Doesn't match
        }
      }
    end
    
    assert_response :success
    assert_template :show
    assert_equal 'accept', assigns(:action)
  end

  test "should redirect if no user params provided for new user" do
    post :accept, params: { token: @pending_invitation.token }
    
    assert_redirected_to accept_invitation_path(@pending_invitation.token)
    assert_match /Please fill in your information/, flash[:alert]
  end

  # ===============================
  # ACCEPT ACTION TESTS - INVALID INVITATIONS
  # ===============================

  test "should not accept invalid invitation" do
    post :accept, params: { token: "invalid_token" }
    
    assert_redirected_to root_path
    assert_match /Invalid invitation link/, flash[:alert]
  end

  test "should not accept expired invitation" do
    post :accept, params: { token: @expired_invitation.token }
    
    assert_redirected_to root_path
    assert_match /expired/, flash[:alert]
  end

  # ===============================
  # DECLINE ACTION TESTS
  # ===============================

  test "should decline invitation" do
    post :decline, params: { token: @pending_invitation.token }
    
    assert_redirected_to root_path
    assert_match /You have declined the invitation/, flash[:notice]
    
    @pending_invitation.reload
    assert_equal "declined", @pending_invitation.status
  end

  test "should handle decline for invalid invitation" do
    post :decline, params: { token: "invalid_token" }
    
    assert_redirected_to root_path
    assert_match /Invalid invitation link/, flash[:alert]
  end

  test "should handle decline for expired invitation" do
    post :decline, params: { token: @expired_invitation.token }
    
    assert_redirected_to root_path
    assert_match /expired/, flash[:alert]
  end

  # ===============================
  # PARAMETER HANDLING TESTS
  # ===============================

  test "should permit user parameters" do
    post :accept, params: {
      token: @pending_invitation.token,
      user: {
        first_name: "Test",
        last_name: "User",
        job_title: "Manager",
        password: "password123",
        password_confirmation: "password123",
        malicious_param: "should_be_ignored"
      }
    }
    
    user = User.last
    assert_equal "Test", user.first_name
    assert_equal "User", user.last_name
    assert_equal "Manager", user.job_title
  end

  test "should force email from invitation" do
    post :accept, params: {
      token: @pending_invitation.token,
      user: {
        first_name: "Test",
        last_name: "User",
        email_address: "<EMAIL>",  # Should be ignored
        password: "password123",
        password_confirmation: "password123"
      }
    }
    
    user = User.last
    assert_equal @pending_invitation.email, user.email_address
  end

  # ===============================
  # EXPIRATION TESTS
  # ===============================

  test "should mark expired invitation as expired" do
    get :show, params: { token: @expired_invitation.token }
    
    @expired_invitation.reload
    assert_equal "expired", @expired_invitation.status
  end

  test "should check expiration on all actions" do
    [:show, :accept, :decline].each do |action|
      @expired_invitation.update!(status: "pending")  # Reset status
      
      if action == :show
        get action, params: { token: @expired_invitation.token }
      else
        post action, params: { token: @expired_invitation.token }
      end
      
      assert_redirected_to root_path
      assert_match /expired/, flash[:alert]
    end
  end

  # ===============================
  # SECURITY TESTS
  # ===============================

  test "should not expose invitation token in response" do
    get :show, params: { token: @pending_invitation.token }
    
    assert_response :success
    assert_not_includes response.body, @pending_invitation.token
  end

  test "should handle missing token parameter" do
    assert_raises(ActionController::ParameterMissing) do
      get :show
    end
  end

  # ===============================
  # ROUTING TESTS
  # ===============================

  test "should route to invitation acceptance" do
    assert_routing({ method: :get, path: "/invitations/abc123" }, 
                  { controller: "invitation_acceptances", action: "show", token: "abc123" })
  end

  test "should route to invitation accept" do
    assert_routing({ method: :post, path: "/invitations/abc123/accept" }, 
                  { controller: "invitation_acceptances", action: "accept", token: "abc123" })
  end

  test "should route to invitation decline" do
    assert_routing({ method: :post, path: "/invitations/abc123/decline" }, 
                  { controller: "invitation_acceptances", action: "decline", token: "abc123" })
  end

  # ===============================
  # INTEGRATION TESTS
  # ===============================

  test "should handle complete invitation flow for new user" do
    # Show invitation
    get :show, params: { token: @pending_invitation.token }
    assert_response :success
    
    # Accept invitation with user creation
    assert_difference 'User.count', 1 do
      post :accept, params: {
        token: @pending_invitation.token,
        user: {
          first_name: "Integration",
          last_name: "Test",
          job_title: "QA",
          password: "password123",
          password_confirmation: "password123"
        }
      }
    end
    
    # Should redirect to wherever after_authentication_url points
    assert_response :redirect
    assert_not_nil Current.session
    
    @pending_invitation.reload
    assert_equal "accepted", @pending_invitation.status
  end

  test "should handle complete invitation flow for existing user" do
    # Show invitation
    get :show, params: { token: @existing_user_invitation.token }
    assert_response :success
    assert_equal @existing_user, assigns(:existing_user)
    
    # Try to accept without being signed in
    post :accept, params: { token: @existing_user_invitation.token }
    assert_redirected_to new_session_path
    assert_equal @existing_user_invitation.token, session[:invitation_token]
    
    # Sign in and accept
    sign_in_as @existing_user
    post :accept, params: { token: @existing_user_invitation.token }
    
    # Should redirect to wherever after_authentication_url points
    assert_response :redirect
    assert_match /Welcome to the team/, flash[:notice]
  end

  private

  def sign_in_as(user)
    session = Session.create!(user: user)
    cookies.signed[:session_id] = session.id
    Current.session = session
  end

  def assert_success_or_redirect
    assert_includes [200, 302], response.status
  end
end