require "test_helper"

class ErrorsControllerTest < ActionController::TestCase
  # ===============================
  # NOT FOUND ACTION TESTS
  # ===============================

  test "should get not_found page" do
    get :not_found
    assert_response :not_found
  end

  test "not_found should set proper instance variables" do
    get :not_found
    assert_equal 404, assigns(:error_code)
    assert_equal "Page Not Found", assigns(:error_title)
    assert_equal "The page you're looking for doesn't exist or has been moved.", assigns(:error_message)
  end

  test "not_found should respond to HTML format" do
    get :not_found, format: :html
    assert_response :not_found
    assert_template :not_found
  end

  test "not_found should respond to JSON format" do
    get :not_found, format: :json
    assert_response :not_found
    
    json_response = JSON.parse(response.body)
    assert_equal "The page you're looking for doesn't exist or has been moved.", json_response["error"]
  end

  test "not_found should allow unauthenticated access" do
    # Should not redirect to login
    get :not_found
    assert_response :not_found
    assert_nil session[:return_to_after_authenticating]
  end

  # ===============================
  # UNPROCESSABLE ENTITY ACTION TESTS
  # ===============================

  test "should get unprocessable_entity page" do
    get :unprocessable_entity
    assert_response :unprocessable_entity
  end

  test "unprocessable_entity should set proper instance variables" do
    get :unprocessable_entity
    assert_equal 422, assigns(:error_code)
    assert_equal "Unable to Process Request", assigns(:error_title)
    assert_equal "The request could not be processed due to invalid data.", assigns(:error_message)
  end

  test "unprocessable_entity should respond to HTML format" do
    get :unprocessable_entity, format: :html
    assert_response :unprocessable_entity
    assert_template :unprocessable_entity
  end

  test "unprocessable_entity should respond to JSON format" do
    get :unprocessable_entity, format: :json
    assert_response :unprocessable_entity
    
    json_response = JSON.parse(response.body)
    assert_equal "The request could not be processed due to invalid data.", json_response["error"]
  end

  test "unprocessable_entity should allow unauthenticated access" do
    # Should not redirect to login
    get :unprocessable_entity
    assert_response :unprocessable_entity
    assert_nil session[:return_to_after_authenticating]
  end

  # ===============================
  # INTERNAL SERVER ERROR ACTION TESTS
  # ===============================

  test "should get internal_server_error page" do
    get :internal_server_error
    assert_response :internal_server_error
  end

  test "internal_server_error should set proper instance variables" do
    get :internal_server_error
    assert_equal 500, assigns(:error_code)
    assert_equal "Internal Server Error", assigns(:error_title)
    assert_equal "Something went wrong on our end. We've been notified and are working to fix it.", assigns(:error_message)
  end

  test "internal_server_error should respond to HTML format" do
    get :internal_server_error, format: :html
    assert_response :internal_server_error
    assert_template :internal_server_error
  end

  test "internal_server_error should respond to JSON format" do
    get :internal_server_error, format: :json
    assert_response :internal_server_error
    
    json_response = JSON.parse(response.body)
    assert_equal "Something went wrong on our end. We've been notified and are working to fix it.", json_response["error"]
  end

  test "internal_server_error should allow unauthenticated access" do
    # Should not redirect to login
    get :internal_server_error
    assert_response :internal_server_error
    assert_nil session[:return_to_after_authenticating]
  end

  # ===============================
  # STATUS CODE TESTS
  # ===============================

  test "not_found should return 404 status" do
    get :not_found
    assert_response 404
  end

  test "unprocessable_entity should return 422 status" do
    get :unprocessable_entity
    assert_response 422
  end

  test "internal_server_error should return 500 status" do
    get :internal_server_error
    assert_response 500
  end

  # ===============================
  # JSON RESPONSE STRUCTURE TESTS
  # ===============================

  test "JSON responses should have proper structure" do
    [:not_found, :unprocessable_entity, :internal_server_error].each do |action|
      get action, format: :json
      
      json_response = JSON.parse(response.body)
      assert json_response.key?("error"), "JSON response should have 'error' key for #{action}"
      assert json_response["error"].is_a?(String), "Error message should be a string for #{action}"
      assert json_response["error"].present?, "Error message should not be empty for #{action}"
    end
  end

  # ===============================
  # CONTENT TYPE TESTS
  # ===============================

  test "HTML responses should have correct content type" do
    [:not_found, :unprocessable_entity, :internal_server_error].each do |action|
      get action, format: :html
      assert_equal "text/html; charset=utf-8", response.content_type
    end
  end

  test "JSON responses should have correct content type" do
    [:not_found, :unprocessable_entity, :internal_server_error].each do |action|
      get action, format: :json
      assert_equal "application/json; charset=utf-8", response.content_type
    end
  end
end