require "test_helper"

class PasswordsControllerTest < ActionController::TestCase
  def setup
    @user = users(:one)
    @valid_token = @user.generate_token_for(:password_reset)
    @invalid_token = "invalid_token_12345"
    @expired_token = "expired_token"
  end

  # ===============================
  # NEW ACTION TESTS
  # ===============================

  test "should get new password reset page" do
    get :new
    assert_response :success
  end

  test "should allow unauthenticated access to new" do
    get :new
    assert_response :success
    assert_nil session[:return_to_after_authenticating]
  end

  test "should not require authentication for new action" do
    get :new
    assert_response :success
  end

  # ===============================
  # CREATE ACTION TESTS - SUCCESS CASES
  # ===============================

  test "should send password reset email for existing user" do
    assert_enqueued_email_with PasswordsMailer, :reset do
      post :create, params: { 
        email_address: @user.email_address 
      }
    end
    
    assert_redirected_to new_session_path
    assert_match /Password reset instructions sent/, flash[:notice]
  end

  test "should handle non-existent email gracefully" do
    assert_no_enqueued_emails do
      post :create, params: { 
        email_address: "<EMAIL>" 
      }
    end
    
    assert_redirected_to new_session_path
    # Should give same message for security (don't reveal if email exists)
    assert_match /Password reset instructions sent/, flash[:notice]
  end

  test "should handle empty email parameter" do
    assert_no_enqueued_emails do
      post :create, params: { 
        email_address: "" 
      }
    end
    
    assert_redirected_to new_session_path
    assert_match /Password reset instructions sent/, flash[:notice]
  end

  test "should handle missing email parameter" do
    assert_no_enqueued_emails do
      post :create, params: {}
    end
    
    assert_redirected_to new_session_path
    assert_match /Password reset instructions sent/, flash[:notice]
  end

  test "should handle case insensitive email lookup" do
    assert_enqueued_email_with PasswordsMailer, :reset do
      post :create, params: { 
        email_address: @user.email_address.upcase 
      }
    end
    
    assert_redirected_to new_session_path
    assert_match /Password reset instructions sent/, flash[:notice]
  end

  # ===============================
  # EDIT ACTION TESTS
  # ===============================

  test "should get edit password page with valid token" do
    get :edit, params: { token: @valid_token }
    assert_response :success
    assert_not_nil assigns(:user)
    assert_equal @user, assigns(:user)
  end

  test "should redirect with invalid token" do
    get :edit, params: { token: @invalid_token }
    assert_redirected_to new_password_path
    assert_match /Password reset link is invalid or has expired/, flash[:alert]
  end

  test "should redirect with expired token" do
    # Simulate expired token by manipulating time
    travel_to 25.hours.from_now do
      get :edit, params: { token: @valid_token }
      assert_redirected_to new_password_path
      assert_match /Password reset link is invalid or has expired/, flash[:alert]
    end
  end

  test "should redirect with missing token" do
    get :edit, params: {}
    assert_redirected_to new_password_path
    assert_match /Password reset link is invalid or has expired/, flash[:alert]
  end

  test "should redirect with empty token" do
    get :edit, params: { token: "" }
    assert_redirected_to new_password_path
    assert_match /Password reset link is invalid or has expired/, flash[:alert]
  end

  test "should allow unauthenticated access to edit with valid token" do
    get :edit, params: { token: @valid_token }
    assert_response :success
    assert_nil session[:return_to_after_authenticating]
  end

  # ===============================
  # UPDATE ACTION TESTS - SUCCESS CASES
  # ===============================

  test "should update password with valid token and matching passwords" do
    patch :update, params: { 
      token: @valid_token,
      password: "newpassword123",
      password_confirmation: "newpassword123"
    }
    
    assert_redirected_to new_session_path
    assert_match /Password has been reset/, flash[:notice]
    
    # Verify password was actually changed
    @user.reload
    assert @user.authenticate("newpassword123")
    assert_not @user.authenticate("password")  # Old password should not work
  end

  test "should handle strong password requirements" do
    strong_password = "StrongP@ssw0rd123!"
    
    patch :update, params: { 
      token: @valid_token,
      password: strong_password,
      password_confirmation: strong_password
    }
    
    assert_redirected_to new_session_path
    assert_match /Password has been reset/, flash[:notice]
    
    @user.reload
    assert @user.authenticate(strong_password)
  end

  # ===============================
  # UPDATE ACTION TESTS - VALIDATION FAILURES
  # ===============================

  test "should not update password with mismatched confirmation" do
    patch :update, params: { 
      token: @valid_token,
      password: "newpassword123",
      password_confirmation: "differentpassword"
    }
    
    assert_redirected_to edit_password_path(@valid_token)
    assert_match /Passwords did not match/, flash[:alert]
    
    # Verify password was not changed
    @user.reload
    assert @user.authenticate("password")  # Old password should still work
    assert_not @user.authenticate("newpassword123")
  end

  test "should not update password with weak password" do
    patch :update, params: { 
      token: @valid_token,
      password: "123",
      password_confirmation: "123"
    }
    
    assert_redirected_to edit_password_path(@valid_token)
    assert_match /Passwords did not match/, flash[:alert]
    
    # Verify password was not changed
    @user.reload
    assert @user.authenticate("password")
  end

  test "should not update password with empty password" do
    patch :update, params: { 
      token: @valid_token,
      password: "",
      password_confirmation: ""
    }
    
    assert_redirected_to edit_password_path(@valid_token)
    assert_match /Passwords did not match/, flash[:alert]
    
    # Verify password was not changed
    @user.reload
    assert @user.authenticate("password")
  end

  test "should not update password with missing password" do
    patch :update, params: { 
      token: @valid_token
    }
    
    assert_redirected_to edit_password_path(@valid_token)
    assert_match /Passwords did not match/, flash[:alert]
    
    # Verify password was not changed
    @user.reload
    assert @user.authenticate("password")
  end

  test "should not update password with invalid token" do
    patch :update, params: { 
      token: @invalid_token,
      password: "newpassword123",
      password_confirmation: "newpassword123"
    }
    
    assert_redirected_to new_password_path
    assert_match /Password reset link is invalid or has expired/, flash[:alert]
  end

  test "should not update password with expired token" do
    travel_to 25.hours.from_now do
      patch :update, params: { 
        token: @valid_token,
        password: "newpassword123",
        password_confirmation: "newpassword123"
      }
      
      assert_redirected_to new_password_path
      assert_match /Password reset link is invalid or has expired/, flash[:alert]
    end
  end

  # ===============================
  # SECURITY TESTS
  # ===============================

  test "should allow unauthenticated access to all actions" do
    get :new
    assert_response :success
    
    post :create, params: { email_address: @user.email_address }
    assert_response :redirect  # Not auth error
    
    get :edit, params: { token: @valid_token }
    assert_response :success
    
    patch :update, params: { 
      token: @valid_token,
      password: "test123",
      password_confirmation: "test123"
    }
    assert_response :redirect  # Not auth error
  end

  test "should not reveal user existence through different messages" do
    # Both existing and non-existing emails should get same message
    post :create, params: { email_address: @user.email_address }
    existing_user_message = flash[:notice]
    
    flash.clear
    
    post :create, params: { email_address: "<EMAIL>" }
    nonexistent_user_message = flash[:notice]
    
    assert_equal existing_user_message, nonexistent_user_message
  end

  test "should handle token timing attacks" do
    # Both invalid and valid tokens should take similar time to process
    # This is a basic test - real timing attack prevention would need more sophisticated testing
    
    start_time = Time.current
    get :edit, params: { token: @invalid_token }
    invalid_time = Time.current - start_time
    
    start_time = Time.current
    get :edit, params: { token: @valid_token }
    valid_time = Time.current - start_time
    
    # Times should be relatively close (this is a basic check)
    assert (invalid_time - valid_time).abs < 1.second
  end

  test "should filter password parameters from logs" do
    # This test ensures sensitive parameters are filtered
    # The actual filtering happens in Rails parameter filtering config
    
    patch :update, params: { 
      token: @valid_token,
      password: "secretpassword",
      password_confirmation: "secretpassword"
    }
    
    # Should succeed without exposing password
    assert_response :redirect
  end

  test "should handle SQL injection attempts" do
    malicious_email = "<EMAIL>'; DROP TABLE users; --"
    
    assert_no_enqueued_emails do
      post :create, params: { 
        email_address: malicious_email 
      }
    end
    
    assert_redirected_to new_session_path
    assert_match /Password reset instructions sent/, flash[:notice]
  end

  test "should handle XSS attempts in email parameter" do
    xss_email = "<script>alert('xss')</script>@example.com"
    
    post :create, params: { 
      email_address: xss_email 
    }
    
    assert_redirected_to new_session_path
    # Flash message should not contain the script
    assert_not flash[:notice].include?("<script>")
  end

  # ===============================
  # TOKEN HANDLING TESTS
  # ===============================

  test "should handle malformed tokens gracefully" do
    malformed_tokens = [
      "short",
      "a" * 1000,  # Very long token
      "token with spaces",
      "token/with/slashes",
      "token%20with%20encoding"
    ]
    
    malformed_tokens.each do |token|
      get :edit, params: { token: token }
      assert_redirected_to new_password_path
      assert_match /Password reset link is invalid or has expired/, flash[:alert]
    end
  end

  test "should handle token reuse" do
    # First use of token should work
    patch :update, params: { 
      token: @valid_token,
      password: "newpassword123",
      password_confirmation: "newpassword123"
    }
    
    assert_redirected_to new_session_path
    assert_match /Password has been reset/, flash[:notice]
    
    # Second use of same token should fail
    get :edit, params: { token: @valid_token }
    assert_redirected_to new_password_path
    assert_match /Password reset link is invalid or has expired/, flash[:alert]
  end

  # ===============================
  # PARAMETER HANDLING TESTS
  # ===============================

  test "should permit only allowed parameters in update" do
    patch :update, params: { 
      token: @valid_token,
      password: "newpassword123",
      password_confirmation: "newpassword123",
      email_address: "<EMAIL>",  # Should be ignored
      super_admin: true  # Should be ignored
    }
    
    assert_redirected_to new_session_path
    assert_match /Password has been reset/, flash[:notice]
    
    # Verify only password was updated, not other attributes
    @user.reload
    assert @user.authenticate("newpassword123")
    assert_not_equal "<EMAIL>", @user.email_address
    assert_not @user.super_admin
  end

  test "should handle unicode characters in password" do
    unicode_password = "pássw0rd123中文🔒"
    
    patch :update, params: { 
      token: @valid_token,
      password: unicode_password,
      password_confirmation: unicode_password
    }
    
    assert_redirected_to new_session_path
    assert_match /Password has been reset/, flash[:notice]
    
    @user.reload
    assert @user.authenticate(unicode_password)
  end

  # ===============================
  # EMAIL DELIVERY TESTS
  # ===============================

  test "should send email with valid reset token" do
    # Capture the delivered email
    assert_enqueued_email_with PasswordsMailer, :reset do
      post :create, params: { 
        email_address: @user.email_address 
      }
    end
  end

  test "should generate unique tokens for different requests" do
    token1 = @user.generate_token_for(:password_reset)
    token2 = @user.generate_token_for(:password_reset)
    
    assert_not_equal token1, token2
  end

  # ===============================
  # EDGE CASE TESTS
  # ===============================

  test "should handle very long email addresses" do
    long_email = "#{'a' * 240}@example.com"
    
    post :create, params: { 
      email_address: long_email 
    }
    
    assert_redirected_to new_session_path
    assert_match /Password reset instructions sent/, flash[:notice]
  end

  test "should handle concurrent password reset requests" do
    # Simulate multiple password reset requests
    token1 = @user.generate_token_for(:password_reset)
    token2 = @user.generate_token_for(:password_reset)
    
    # Both tokens should be valid initially
    get :edit, params: { token: token1 }
    assert_response :success
    
    get :edit, params: { token: token2 }
    assert_response :success
    
    # Using one token should not invalidate the other (depends on implementation)
    patch :update, params: { 
      token: token1,
      password: "newpassword123",
      password_confirmation: "newpassword123"
    }
    
    assert_redirected_to new_session_path
  end

  # ===============================
  # INTEGRATION TESTS
  # ===============================

  test "should complete full password reset workflow" do
    # Step 1: Request password reset
    post :create, params: { 
      email_address: @user.email_address 
    }
    assert_redirected_to new_session_path
    assert_match /Password reset instructions sent/, flash[:notice]
    
    # Step 2: Access reset form with token
    token = @user.generate_token_for(:password_reset)
    get :edit, params: { token: token }
    assert_response :success
    
    # Step 3: Update password
    new_password = "my_new_secure_password123"
    patch :update, params: { 
      token: token,
      password: new_password,
      password_confirmation: new_password
    }
    
    assert_redirected_to new_session_path
    assert_match /Password has been reset/, flash[:notice]
    
    # Step 4: Verify new password works
    @user.reload
    assert @user.authenticate(new_password)
    assert_not @user.authenticate("password")  # Old password should not work
  end
end