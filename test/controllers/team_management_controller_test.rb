require "test_helper"

class TeamManagementControllerTest < ActionController::TestCase
  def setup
    # Create test users and accounts
    @owner_user = users(:one)
    @member_user = users(:two)
    @admin_user = users(:admin)
    @external_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "External",
      last_name: "User"
    )
    
    @account = accounts(:one)
    @account.update!(
      account_type: 'vendor', 
      status: 'approved', 
      confirmed_at: Time.current,
      owner: @owner_user
    )
    
    # Clear existing account users to avoid duplicates
    @account.account_users.destroy_all
    
    # Set up account users
    @account.account_users.create!(user: @owner_user, role: 'admin')
    @member_account_user = @account.account_users.create!(user: @member_user, role: 'member')
    
    @admin_user.update!(super_admin: true)
    
    # Create pending account for testing approval requirements
    @pending_account = Account.create!(
      name: "Pending Account",
      account_type: "vendor", 
      status: "pending",
      confirmed_at: Time.current,
      owner: @external_user
    )
    @pending_account.account_users.create!(user: @external_user, role: 'admin')
  end

  # ===============================
  # AUTHENTICATION TESTS
  # ===============================

  test "should require authentication for all actions" do
    get :index
    assert_redirected_to new_session_path
    
    get :show_member, params: { id: @member_account_user.id }
    assert_redirected_to new_session_path
    
    get :edit_member, params: { id: @member_account_user.id }
    assert_redirected_to new_session_path
    
    patch :update_member, params: { id: @member_account_user.id, account_user: { role: 'admin' } }
    assert_redirected_to new_session_path
  end

  test "should use dashboard layout" do
    sign_in @owner_user
    get :index
    assert_response :success
  end

  # ===============================
  # AUTHORIZATION TESTS
  # ===============================

  test "should require account admin access" do
    sign_in @member_user
    # Force session to try to access the owner's account (not their own)
    session[:account_id] = @account.id
    
    get :index
    assert_response :redirect
    
    get :edit_member, params: { id: @member_account_user.id }
    assert_response :redirect
  end

  test "should allow account owner access" do
    sign_in @owner_user
    
    get :index
    assert_response :success
    
    get :edit_member, params: { id: @member_account_user.id }
    assert_response :success
  end

  test "should require approved account" do
    sign_in @external_user
    
    get :index
    assert_response :redirect
  end

  test "should allow super admin access" do
    sign_in @admin_user
    
    # Create account for admin to avoid orphaned user redirect
    admin_account = Account.create!(
      name: "Admin Test Account",
      account_type: "vendor",
      status: "approved",
      confirmed_at: Time.current,
      owner: @admin_user
    )
    AccountUser.create!(account: admin_account, user: @admin_user, role: 'admin')
    
    get :index
    assert_response :success
  end

  # ===============================
  # INDEX ACTION TESTS
  # ===============================

  test "should show team management index" do
    sign_in @owner_user
    
    get :index
    assert_response :success
    assert_not_nil assigns(:team_members)
    assert_not_nil assigns(:pending_invitations)
  end

  test "should load team members with users" do
    sign_in @owner_user
    
    get :index
    assert_response :success
    
    team_members = assigns(:team_members)
    assert team_members.any? { |tm| tm.user == @owner_user }
    assert team_members.any? { |tm| tm.user == @member_user }
  end

  test "should load pending invitations" do
    sign_in @owner_user
    
    # Create a pending invitation
    invitation = @account.team_invitations.create!(
      email: "<EMAIL>",
      role: "member",
      invited_by: @owner_user,
      token: SecureRandom.urlsafe_base64(32),
      expires_at: 7.days.from_now,
      status: 'pending'
    )
    
    get :index
    assert_response :success
    
    pending_invitations = assigns(:pending_invitations)
    assert pending_invitations.include?(invitation)
  end

  # ===============================
  # SHOW MEMBER TESTS
  # ===============================

  test "should show team member details" do
    sign_in @owner_user
    
    get :show_member, params: { id: @member_account_user.id }
    assert_response :success
    assert_equal @member_account_user, assigns(:team_member)
  end

  test "should handle nonexistent team member" do
    sign_in @owner_user
    
    get :show_member, params: { id: 99999 }
    assert_redirected_to team_management_index_path
    assert_match /Team member not found/, flash[:alert]
  end

  # ===============================
  # EDIT MEMBER TESTS
  # ===============================

  test "should show edit team member page" do
    sign_in @owner_user
    
    get :edit_member, params: { id: @member_account_user.id }
    assert_response :success
    assert_equal @member_account_user, assigns(:team_member)
  end

  test "should handle edit nonexistent team member" do
    sign_in @owner_user
    
    get :edit_member, params: { id: 99999 }
    assert_redirected_to team_management_index_path
    assert_match /Team member not found/, flash[:alert]
  end

  # ===============================
  # UPDATE MEMBER TESTS
  # ===============================

  test "should update team member" do
    sign_in @owner_user
    
    patch :update_member, params: {
      id: @member_account_user.id,
      account_user: { role: 'admin' }
    }
    
    assert_redirected_to team_management_index_path
    assert_match /Team member updated successfully/, flash[:notice]
    
    @member_account_user.reload
    assert_equal 'admin', @member_account_user.role
  end

  test "should handle update team member with validation errors" do
    sign_in @owner_user
    
    # Test with nil role which should trigger validation error
    patch :update_member, params: {
      id: @member_account_user.id,
      account_user: { role: nil }
    }
    
    assert_response :unprocessable_entity
    assert_not_nil assigns(:team_member)
    
    @member_account_user.reload
    assert_equal 'member', @member_account_user.role  # Should not change
  end

  test "should handle update nonexistent team member" do
    sign_in @owner_user
    
    patch :update_member, params: {
      id: 99999,
      account_user: { role: 'admin' }
    }
    
    assert_redirected_to team_management_index_path
    assert_match /Team member not found/, flash[:alert]
  end

  # ===============================
  # UPDATE MEMBER ROLE TESTS
  # ===============================

  test "should update team member role" do
    sign_in @owner_user
    
    patch :update_member_role, params: {
      id: @member_account_user.id,
      account_user: { role: 'admin' }
    }
    
    assert_redirected_to team_management_index_path
    assert_match /Team member role updated successfully/, flash[:notice]
    
    @member_account_user.reload
    assert_equal 'admin', @member_account_user.role
  end

  test "should handle update role with validation errors" do
    sign_in @owner_user
    
    # Test with nil role which should trigger validation error
    patch :update_member_role, params: {
      id: @member_account_user.id,
      account_user: { role: nil }
    }
    
    assert_response :unprocessable_entity
    
    @member_account_user.reload
    assert_equal 'member', @member_account_user.role  # Should not change
  end

  # ===============================
  # DESTROY MEMBER TESTS
  # ===============================

  test "should remove team member" do
    sign_in @owner_user
    
    assert_difference '@account.account_users.count', -1 do
      delete :destroy_member, params: { id: @member_account_user.id }
    end
    
    assert_redirected_to team_management_index_path
    assert_match /Team member removed successfully/, flash[:notice]
  end

  test "should not remove account owner" do
    sign_in @owner_user
    owner_account_user = @account.account_users.find_by(user: @owner_user)
    
    assert_no_difference '@account.account_users.count' do
      delete :destroy_member, params: { id: owner_account_user.id }
    end
    
    assert_redirected_to team_management_index_path
    assert_match /Cannot remove the account owner/, flash[:alert]
  end

  test "should handle destroy nonexistent team member" do
    sign_in @owner_user
    
    delete :destroy_member, params: { id: 99999 }
    
    assert_redirected_to team_management_index_path
    assert_match /Team member not found/, flash[:alert]
  end

  # ===============================
  # NEW INVITATION TESTS
  # ===============================

  test "should show new invitation page" do
    sign_in @owner_user
    
    get :new_invitation
    assert_response :success
    assert_instance_of TeamInvitation, assigns(:team_invitation)
    assert assigns(:team_invitation).new_record?
  end

  # ===============================
  # CREATE INVITATION TESTS
  # ===============================

  test "should create team invitation" do
    sign_in @owner_user
    
    # Use same domain as owner user to pass domain validation
    owner_domain = @owner_user.email_address.split('@').last
    invitation_params = {
      email: "newmember@#{owner_domain}",
      role: "member",
      message: "Welcome to our team!"
    }
    
    assert_difference 'TeamInvitation.count', 1 do
      post :create_invitation, params: { team_invitation: invitation_params }
    end
    
    assert_redirected_to team_management_index_path
    assert_match /Team invitation sent successfully/, flash[:notice]
    
    invitation = TeamInvitation.last
    assert_equal @account, invitation.account
    assert_equal @owner_user, invitation.invited_by
    assert_equal "newmember@#{owner_domain}", invitation.email
    assert_not_nil invitation.token
    assert_not_nil invitation.expires_at
  end

  test "should not create invalid team invitation" do
    sign_in @owner_user
    
    # Use a valid email format but different domain to test domain validation
    invalid_params = {
      email: "<EMAIL>",  # Different domain from owner
      role: "member",
      message: "Test"
    }
    
    assert_no_difference 'TeamInvitation.count' do
      post :create_invitation, params: { team_invitation: invalid_params }
    end
    
    assert_response :unprocessable_entity
    assert_not_nil assigns(:team_invitation)
    assert assigns(:team_invitation).errors.any?
  end

  test "should generate secure token and expiration for invitation" do
    sign_in @owner_user
    
    # Use same domain as owner user to pass domain validation
    owner_domain = @owner_user.email_address.split('@').last
    invitation_params = {
      email: "secure@#{owner_domain}",
      role: "member",
      message: "Join us!"
    }
    
    post :create_invitation, params: { team_invitation: invitation_params }
    
    invitation = TeamInvitation.last
    assert_not_nil invitation.token
    assert invitation.token.length >= 32  # Secure token length
    assert invitation.expires_at > Time.current
    assert invitation.expires_at <= 7.days.from_now
  end

  # ===============================
  # DESTROY INVITATION TESTS
  # ===============================

  test "should destroy team invitation" do
    sign_in @owner_user
    
    invitation = @account.team_invitations.create!(
      email: "<EMAIL>",
      role: "member",
      invited_by: @owner_user,
      token: SecureRandom.urlsafe_base64(32),
      expires_at: 7.days.from_now
    )
    
    assert_difference 'TeamInvitation.count', -1 do
      delete :destroy_invitation, params: { id: invitation.id }
    end
    
    assert_redirected_to team_management_index_path
    assert_match /Invitation cancelled successfully/, flash[:notice]
  end

  test "should handle destroy nonexistent invitation" do
    sign_in @owner_user
    
    delete :destroy_invitation, params: { id: 99999 }
    
    assert_redirected_to team_management_index_path
    assert_match /Invitation not found/, flash[:alert]
  end

  # ===============================
  # CURRENT ACCOUNT TESTS
  # ===============================

  test "should use session account_id when available" do
    sign_in @owner_user
    session[:account_id] = @account.id
    
    get :index
    assert_equal @account, assigns(:account)
  end

  test "should fall back to first account when no session" do
    sign_in @owner_user
    session.delete(:account_id)
    
    get :index
    assert_equal @owner_user.accounts.first, assigns(:account)
  end

  # ===============================
  # PARAMETER SECURITY TESTS
  # ===============================

  test "should filter unpermitted team member parameters" do
    sign_in @owner_user
    
    original_user_id = @member_account_user.user_id
    original_account_id = @member_account_user.account_id
    
    patch :update_member, params: {
      id: @member_account_user.id,
      account_user: {
        role: 'admin',
        user_id: @external_user.id,  # Should be filtered
        account_id: @pending_account.id,  # Should be filtered
        joined_at: 1.year.ago  # Should be filtered
      }
    }
    
    @member_account_user.reload
    assert_equal 'admin', @member_account_user.role
    assert_equal original_user_id, @member_account_user.user_id  # Should not change
    assert_equal original_account_id, @member_account_user.account_id  # Should not change
  end

  test "should filter unpermitted invitation parameters" do
    sign_in @owner_user
    
    post :create_invitation, params: {
      team_invitation: {
        email: "<EMAIL>",
        role: "member",
        message: "Welcome!",
        account_id: @pending_account.id,  # Should be filtered
        invited_by_id: @external_user.id,  # Should be filtered
        token: "hacked_token",  # Should be filtered
        expires_at: 1.year.from_now  # Should be filtered
      }
    }
    
    invitation = TeamInvitation.last
    assert_equal @account, invitation.account  # Should use current account
    assert_equal @owner_user, invitation.invited_by  # Should use current user
    assert_not_equal "hacked_token", invitation.token  # Should generate secure token
    assert invitation.expires_at <= 7.days.from_now  # Should use default expiration
  end

  # ===============================
  # ERROR HANDLING TESTS
  # ===============================

  test "should handle user with no accounts gracefully" do
    user_without_accounts = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "No",
      last_name: "Accounts"
    )
    sign_in user_without_accounts
    
    get :index
    # Should handle gracefully (may redirect or show error)
    assert_response :redirect
  end

  test "should handle concurrent team member deletion" do
    sign_in @owner_user
    
    # Simulate concurrent deletion
    @member_account_user.destroy
    
    patch :update_member, params: {
      id: @member_account_user.id,
      account_user: { role: 'admin' }
    }
    
    assert_redirected_to team_management_index_path
    assert_match /Team member not found/, flash[:alert]
  end

  test "should handle concurrent invitation deletion" do
    sign_in @owner_user
    
    invitation = @account.team_invitations.create!(
      email: "<EMAIL>",
      role: "member",
      invited_by: @owner_user,
      token: SecureRandom.urlsafe_base64(32),
      expires_at: 7.days.from_now
    )
    
    # Simulate concurrent deletion
    invitation.destroy
    
    delete :destroy_invitation, params: { id: invitation.id }
    
    assert_redirected_to team_management_index_path
    assert_match /Invitation not found/, flash[:alert]
  end

  # ===============================
  # INTEGRATION TESTS
  # ===============================

  test "should complete full team member management workflow" do
    sign_in @owner_user
    
    # Step 1: View team management
    get :index
    assert_response :success
    
    # Step 2: View member details
    get :show_member, params: { id: @member_account_user.id }
    assert_response :success
    
    # Step 3: Edit member
    get :edit_member, params: { id: @member_account_user.id }
    assert_response :success
    
    # Step 4: Update member role
    patch :update_member_role, params: {
      id: @member_account_user.id,
      account_user: { role: 'admin' }
    }
    
    assert_redirected_to team_management_index_path
    
    # Step 5: Verify change
    @member_account_user.reload
    assert_equal 'admin', @member_account_user.role
    
    # Step 6: View updated team
    get :index
    assert_response :success
  end

  test "should complete full invitation workflow" do
    sign_in @owner_user
    
    # Step 1: View team management
    get :index
    assert_response :success
    
    # Step 2: Show new invitation form
    get :new_invitation
    assert_response :success
    
    # Step 3: Create invitation
    owner_domain = @owner_user.email_address.split('@').last
    assert_difference 'TeamInvitation.count', 1 do
      post :create_invitation, params: {
        team_invitation: {
          email: "newteam@#{owner_domain}",
          role: "member",
          message: "Join our team!"
        }
      }
    end
    
    assert_redirected_to team_management_index_path
    
    # Step 4: View team with pending invitation
    get :index
    assert_response :success
    
    pending_invitations = assigns(:pending_invitations)
    invitation = pending_invitations.find { |inv| inv.email == "newteam@#{owner_domain}" }
    assert_not_nil invitation
    
    # Step 5: Cancel invitation
    assert_difference 'TeamInvitation.count', -1 do
      delete :destroy_invitation, params: { id: invitation.id }
    end
    
    assert_redirected_to team_management_index_path
  end

  # ===============================
  # ACCOUNT ACCESS TESTS
  # ===============================

  test "should check account user membership for access" do
    non_member = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Non",
      last_name: "Member"
    )
    
    # Create their own account so they're not orphaned
    their_account = Account.create!(
      name: "Their Account",
      account_type: "vendor",
      status: "approved",
      confirmed_at: Time.current,
      owner: non_member
    )
    AccountUser.create!(account: their_account, user: non_member, role: 'admin')
    
    sign_in non_member
    
    get :index
    # Should access their own account, not the test account
    assert_response :success
    assert_equal their_account, assigns(:account)
  end

  private

  def sign_in(user)
    session_record = user.sessions.create!(
      user_agent: 'Test User Agent',
      ip_address: '127.0.0.1'
    )
    Current.session = session_record
    cookies.signed[:session_id] = session_record.id
  end
end