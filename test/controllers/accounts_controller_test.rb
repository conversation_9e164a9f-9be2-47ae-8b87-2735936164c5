require "test_helper"

class AccountsControllerTest < ActionController::TestCase
  def setup
    # Create test users and accounts
    @owner_user = users(:one)
    @member_user = users(:two)
    @admin_user = users(:admin)
    @external_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "External",
      last_name: "User"
    )
    
    @account = accounts(:one)
    @account.update!(
      account_type: 'vendor', 
      status: 'approved', 
      confirmed_at: Time.current,
      owner: @owner_user
    )
    
    # Clear existing account users to avoid duplicates
    @account.account_users.destroy_all
    
    # Set up account users
    @account.account_users.create!(user: @owner_user, role: 'admin')
    @member_account_user = @account.account_users.create!(user: @member_user, role: 'member')
    
    @admin_user.update!(super_admin: true)
    
    # Create pending account for testing approval requirements
    @pending_account = Account.create!(
      name: "Pending Account",
      account_type: "vendor", 
      status: "pending",
      confirmed_at: Time.current,
      owner: @external_user
    )
    @pending_account.account_users.create!(user: @external_user, role: 'admin')
  end

  # ===============================
  # AUTHENTICATION TESTS
  # ===============================

  test "should require authentication for all actions" do
    get :show
    assert_redirected_to new_session_path
    
    get :edit_profile
    assert_redirected_to new_session_path
    
    get :team_members
    assert_redirected_to new_session_path
  end

  test "should use dashboard layout" do
    sign_in @owner_user
    get :show
    assert_response :success
  end

  # ===============================
  # SHOW ACTION TESTS
  # ===============================

  test "should show account for account admin" do
    sign_in @owner_user
    get :show
    assert_response :success
    assert_equal @account, assigns(:account)
    assert_equal @owner_user, assigns(:user)
  end

  test "should allow super admin access to show" do
    sign_in @admin_user
    
    # Super admins should be able to access any account  
    # But they still need at least one account to avoid orphaned user redirect
    admin_account = Account.create!(
      name: "Admin Test Account",
      account_type: "vendor",
      status: "approved",
      confirmed_at: Time.current,
      owner: @admin_user
    )
    AccountUser.create!(account: admin_account, user: @admin_user, role: 'admin')
    
    get :show
    assert_response :success
  end

  # ===============================
  # EDIT PROFILE ACTION TESTS
  # ===============================

  test "should get edit profile page" do
    sign_in @owner_user
    get :edit_profile
    assert_response :success
    assert_equal @owner_user, assigns(:user)
  end

  test "should allow regular members to edit their profile" do
    sign_in @member_user
    get :edit_profile
    assert_response :success
  end

  # ===============================
  # EDIT ACCOUNT ACTION TESTS
  # ===============================

  test "should get edit account page for admin" do
    sign_in @owner_user
    get :edit_account
    assert_response :success
    assert_equal @account, assigns(:account)
  end

  # ===============================
  # UPDATE ACCOUNT TESTS
  # ===============================

  test "should update account information" do
    sign_in @owner_user
    
    new_name = "Updated Company Name"
    new_location = "San Francisco, CA"
    
    patch :update_account, params: {
      account: {
        name: new_name,
        headquarters_location: new_location,
        description: "Updated description"
      }
    }
    
    assert_redirected_to account_path
    assert_match /Account information updated successfully/, flash[:notice]
    
    @account.reload
    assert_equal new_name, @account.name
    assert_equal new_location, @account.headquarters_location
  end

  test "should not update account with invalid data" do
    sign_in @owner_user
    
    patch :update_account, params: {
      account: {
        name: "",  # Invalid - required field
        headquarters_location: "New Location"
      }
    }
    
    assert_response :unprocessable_entity
    assert_not_nil assigns(:account)
    
    @account.reload
    assert_not_equal "", @account.name  # Should not have changed
  end

  # ===============================
  # UPDATE PROFILE TESTS
  # ===============================

  test "should update user profile" do
    sign_in @owner_user
    
    new_first_name = "UpdatedFirst"
    new_job_title = "Chief Executive Officer"
    
    patch :update_profile, params: {
      user: {
        first_name: new_first_name,
        last_name: "UpdatedLast",
        job_title: new_job_title,
        phone: "555-9999"
      }
    }
    
    assert_redirected_to account_path
    assert_match /Profile updated successfully/, flash[:notice]
    
    @owner_user.reload
    assert_equal new_first_name, @owner_user.first_name
    assert_equal new_job_title, @owner_user.job_title
  end

  test "should not update profile with invalid email" do
    sign_in @owner_user
    
    patch :update_profile, params: {
      user: {
        first_name: "Updated",
        email_address: "invalid-email"
      }
    }
    
    assert_response :unprocessable_entity
    
    @owner_user.reload
    assert_not_equal "invalid-email", @owner_user.email_address
  end

  test "should allow members to update their own profile" do
    sign_in @member_user
    
    patch :update_profile, params: {
      user: {
        first_name: "UpdatedMember",
        job_title: "Senior Developer"
      }
    }
    
    assert_redirected_to account_path
    
    @member_user.reload
    assert_equal "UpdatedMember", @member_user.first_name
  end

  # ===============================
  # UPDATE PASSWORD TESTS
  # ===============================

  test "should update password" do
    sign_in @owner_user
    
    new_password = "newpassword123"
    
    patch :update_password, params: {
      user: {
        password: new_password,
        password_confirmation: new_password
      }
    }
    
    assert_redirected_to account_path
    assert_match /Password updated successfully/, flash[:notice]
    
    @owner_user.reload
    assert @owner_user.authenticate(new_password)
  end

  test "should not update with blank password" do
    sign_in @owner_user
    
    patch :update_password, params: {
      user: {
        password: "",
        password_confirmation: ""
      }
    }
    
    assert_redirected_to edit_profile_account_path
    assert_match /Password cannot be blank/, flash[:alert]
    
    # Should still authenticate with old password
    @owner_user.reload
    assert @owner_user.authenticate("password")
  end

  test "should not update with mismatched password confirmation" do
    sign_in @owner_user
    
    patch :update_password, params: {
      user: {
        password: "newpassword123",
        password_confirmation: "differentpassword"
      }
    }
    
    assert_response :unprocessable_entity
    
    # Should still authenticate with old password
    @owner_user.reload
    assert @owner_user.authenticate("password")
  end

  # ===============================
  # TEAM MEMBERS ACTION TESTS
  # ===============================

  test "should show team members for account admin" do
    sign_in @owner_user
    
    get :team_members
    assert_response :success
    assert_not_nil assigns(:team_members)
    assert_not_nil assigns(:pending_invitations)
    
    team_members = assigns(:team_members)
    assert team_members.any? { |tm| tm.user == @owner_user }
    assert team_members.any? { |tm| tm.user == @member_user }
  end

  # ===============================
  # TEAM MEMBER UPDATE TESTS
  # ===============================

  test "should update team member role" do
    sign_in @owner_user
    
    patch :update_team_member_role, params: {
      id: @member_account_user.id,
      account_user: { role: 'admin' }
    }
    
    assert_redirected_to team_members_account_path
    assert_match /Team member role updated successfully/, flash[:notice]
    
    @member_account_user.reload
    assert_equal 'admin', @member_account_user.role
  end

  test "should update team member" do
    sign_in @owner_user
    
    patch :update_team_member, params: {
      id: @member_account_user.id,
      account_user: { role: 'admin' }
    }
    
    assert_redirected_to team_members_account_path
    assert_match /Team member updated successfully/, flash[:notice]
  end

  test "should not update nonexistent team member" do
    sign_in @owner_user
    
    patch :update_team_member, params: {
      id: 99999,
      account_user: { role: 'admin' }
    }
    
    assert_redirected_to team_members_account_path
    assert_match /Team member not found/, flash[:alert]
  end

  # ===============================
  # TEAM MEMBER REMOVAL TESTS
  # ===============================

  test "should remove team member" do
    sign_in @owner_user
    
    assert_difference '@account.account_users.count', -1 do
      delete :destroy_team_member, params: { id: @member_account_user.id }
    end
    
    assert_redirected_to team_members_account_path
    assert_match /Team member removed successfully/, flash[:notice]
  end

  test "should not remove account owner" do
    sign_in @owner_user
    owner_account_user = @account.account_users.find_by(user: @owner_user)
    
    assert_no_difference '@account.account_users.count' do
      delete :destroy_team_member, params: { id: owner_account_user.id }
    end
    
    assert_redirected_to team_members_account_path
    assert_match /Cannot remove the account owner/, flash[:alert]
  end

  # ===============================
  # TEAM INVITATION TESTS
  # ===============================


  test "should create team invitation" do
    sign_in @owner_user
    
    invitation_params = {
      email: "<EMAIL>",
      role: "member",
      message: "Welcome to our team!"
    }
    
    assert_difference 'TeamInvitation.count', 1 do
      post :create_team_invitation, params: { team_invitation: invitation_params }
    end
    
    assert_redirected_to team_members_account_path
    assert_match /Team invitation sent successfully/, flash[:notice]
    
    invitation = TeamInvitation.last
    assert_equal @account, invitation.account
    assert_equal @owner_user, invitation.invited_by
    assert_equal "<EMAIL>", invitation.email
    assert_not_nil invitation.token
    assert_not_nil invitation.expires_at
  end

  test "should destroy team invitation" do
    sign_in @owner_user
    
    invitation = @account.team_invitations.create!(
      email: "<EMAIL>",
      role: "member",
      invited_by: @owner_user,
      token: SecureRandom.urlsafe_base64(32),
      expires_at: 7.days.from_now
    )
    
    assert_difference 'TeamInvitation.count', -1 do
      delete :destroy_team_invitation, params: { id: invitation.id }
    end
    
    assert_redirected_to team_members_account_path
    assert_match /Invitation cancelled successfully/, flash[:notice]
  end

  test "should not destroy nonexistent invitation" do
    sign_in @owner_user
    
    delete :destroy_team_invitation, params: { id: 99999 }
    
    assert_redirected_to team_members_account_path
    assert_match /Invitation not found/, flash[:alert]
  end

  # ===============================
  # AUTHORIZATION TESTS
  # ===============================

  test "should handle external user access" do
    # External user has account but not access to the primary account being tested
    sign_in @external_user
    
    get :show
    # Should be able to access their own account (the pending account)
    assert_response :success
  end

  test "should allow admin user access" do
    sign_in @admin_user
    
    # Admin users can access any account but need appropriate session setup
    get :show
    assert_response :redirect  # Will redirect to appropriate account or login
  end

  # ===============================
  # CURRENT ACCOUNT TESTS
  # ===============================

  test "should use session account_id when available" do
    sign_in @owner_user
    session[:account_id] = @account.id
    
    get :show
    assert_equal @account, assigns(:account)
  end

  test "should fall back to first account when no session" do
    sign_in @owner_user
    session.delete(:account_id)
    
    get :show
    assert_equal @owner_user.accounts.first, assigns(:account)
  end

  # ===============================
  # INTEGRATION TESTS
  # ===============================

  test "should complete full profile update workflow" do
    sign_in @owner_user
    
    # Step 1: Edit profile
    get :edit_profile
    assert_response :success
    
    # Step 2: Update profile
    patch :update_profile, params: {
      user: {
        first_name: "Updated",
        last_name: "Name",
        job_title: "CEO"
      }
    }
    
    assert_redirected_to account_path
    
    # Step 3: Verify update
    @owner_user.reload
    assert_equal "Updated", @owner_user.first_name
    assert_equal "CEO", @owner_user.job_title
  end

  test "should complete team invitation creation workflow" do
    sign_in @owner_user
    
    # Create invitation
    assert_difference 'TeamInvitation.count', 1 do
      post :create_team_invitation, params: {
        team_invitation: {
          email: "<EMAIL>",
          role: "member",
          message: "Join our team!"
        }
      }
    end
    
    assert_redirected_to team_members_account_path
    
    # View team members with pending invitation
    get :team_members
    assert_response :success
    
    pending_invitations = assigns(:pending_invitations)
    assert pending_invitations.any? { |inv| inv.email == "<EMAIL>" }
  end

  private

  def sign_in(user)
    session_record = user.sessions.create!(
      user_agent: 'Test User Agent',
      ip_address: '127.0.0.1'
    )
    Current.session = session_record
    cookies.signed[:session_id] = session_record.id
  end
end