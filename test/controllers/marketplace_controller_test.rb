require "test_helper"

class MarketplaceControllerTest < ActionController::TestCase
  setup do
    @vendor_user = users(:one)
    @vendor_account = accounts(:one)
    @agency_user = users(:two) 
    @agency_account = accounts(:two)

    # Configure accounts properly
    @vendor_account.update!(
      account_type: 'vendor',
      status: 'approved',
      confirmed_at: Time.current,
      owner: @vendor_user
    )

    @agency_account.update!(
      account_type: 'agency',
      status: 'approved',
      confirmed_at: Time.current,
      owner: @agency_user
    )

    # Ensure account users associations exist
    @vendor_account.account_users.find_or_create_by(user: @vendor_user, role: 'admin')
    @agency_account.account_users.find_or_create_by(user: @agency_user, role: 'admin')

    # Clear existing data to ensure clean test state
    Category.destroy_all
    Product.destroy_all
    CaseStudy.destroy_all

    # Create test categories with hierarchy
    @root_category = Category.create!(
      name: "Software",
      description: "Software solutions",
      parent: nil
    )
    
    @sub_category = Category.create!(
      name: "Productivity Tools",
      description: "Tools for productivity",
      parent: @root_category
    )

    # Create test products
    @product1 = Product.create!(
      account: @vendor_account,
      name: "Test Product 1",
      description: "A test product for marketplace",
      published: true
    )
    @product1.categories << @sub_category

    @product2 = Product.create!(
      account: @vendor_account,
      name: "Test Product 2",
      description: "Another test product",
      published: true
    )
    @product2.categories << @sub_category

    @unpublished_product = Product.create!(
      account: @vendor_account,
      name: "Unpublished Product",
      description: "This product is not published",
      published: false
    )
  end

  # Index Tests
  test "should get index without authentication" do
    get :index
    assert_response :success
    assert_not_nil assigns(:root_categories)
    assert_not_nil assigns(:featured_companies)
    assert_not_nil assigns(:featured_products)
    assert_not_nil assigns(:recently_added_companies)
    assert_not_nil assigns(:recently_added_products)
    assert_not_nil assigns(:recently_added_case_studies)
  end

  test "should display featured companies and products" do
    get :index
    
    companies = assigns(:featured_companies)
    products = assigns(:featured_products)
    
    assert companies.any?
    assert products.any?
    
    # Test data integrity
    companies.each do |company|
      assert_equal 'vendor', company.account_type
      assert_equal 'approved', company.status
    end
    
    products.each do |product|
      assert product.published?
    end
  end

  test "should limit featured content correctly" do
    get :index
    
    assert assigns(:featured_companies).count <= 8
    assert assigns(:featured_products).count <= 12
    assert assigns(:recently_added_companies).count <= 6
    assert assigns(:recently_added_products).count <= 8
    assert assigns(:recently_added_case_studies).count <= 6
  end

  # Categories Tests
  test "should get categories index" do
    get :categories
    assert_response :success
    assert_not_nil assigns(:root_categories)
  end

  test "should search categories by name" do
    get :categories, params: { search: 'Software' }
    assert_response :success
    
    categories = assigns(:root_categories)
    categories.each do |category|
      assert category.name.downcase.include?('software')
    end
  end

  # Category Show Tests
  test "should show category with products" do
    get :category, params: { id: @sub_category.friendly_id }
    assert_response :success
    assert_equal @sub_category, assigns(:category)
    assert_not_nil assigns(:products)
    assert_not_nil assigns(:sort_options)
  end

  test "should sort category products by different criteria" do
    # Create products with different creation times
    old_product = Product.create!(
      account: @vendor_account,
      name: "A Old Product",
      description: "Created earlier",
      published: true,
      created_at: 2.days.ago
    )
    old_product.categories << @sub_category

    # Test newest sort
    get :category, params: { id: @sub_category.friendly_id, sort: 'newest' }
    products = assigns(:products)
    assert products.first.created_at >= products.last.created_at

    # Test name sort
    get :category, params: { id: @sub_category.friendly_id, sort: 'name_asc' }
    products = assigns(:products)
    assert products.first.name <= products.last.name
  end

  # Products Index Tests
  test "should get products index" do
    get :products
    assert_response :success
    assert_not_nil assigns(:products)
    assert_not_nil assigns(:categories_with_products)
    assert_not_nil assigns(:root_categories)
    assert_not_nil assigns(:subcategories)
    assert_not_nil assigns(:sort_options)
  end

  test "should filter products by category" do
    get :products, params: { category_id: @sub_category.id }
    assert_response :success
    
    products = assigns(:products)
    products.each do |product|
      assert_includes product.categories, @sub_category
    end
  end

  # Product Show Tests
  test "should show published product" do
    get :product, params: { id: @product1.friendly_id }
    assert_response :success
    assert_equal @product1, assigns(:product)
    assert_not_nil assigns(:related_products)
    assert_not_nil assigns(:similar_products)
    assert_not_nil assigns(:lead)
    assert_not_nil assigns(:company)
  end

  test "should not show unpublished product to unauthenticated user" do
    get :product, params: { id: @unpublished_product.friendly_id }
    
    # Check that we either get a 404 or are redirected
    assert_includes [404, 302], response.status
  end

  test "should show unpublished product to product owner" do
    sign_in @vendor_user

    get :product, params: { id: @unpublished_product.friendly_id }
    assert_response :success
    assert_equal @unpublished_product, assigns(:product)
  end

  # Vendor Products Tests
  test "should show vendor products" do
    get :vendor_products, params: { id: @vendor_account.friendly_id }
    assert_response :success
    assert_equal @vendor_account, assigns(:vendor)
    assert_not_nil assigns(:products)
    assert_not_nil assigns(:sort_options)
  end

  test "should only show published products for vendor" do
    get :vendor_products, params: { id: @vendor_account.friendly_id }
    
    products = assigns(:products)
    products.each do |product|
      assert product.published?
      assert_equal @vendor_account, product.account
    end
  end

  # Search Tests
  test "should perform search" do
    get :search, params: { q: 'Test Product' }
    assert_response :success
    assert_not_nil assigns(:query)
    assert_not_nil assigns(:categories)
    assert_not_nil assigns(:products)
  end

  test "should return empty results for blank search" do
    get :search, params: { q: '' }
    assert_response :success
    assert_equal '', assigns(:query)
    assert_equal [], assigns(:categories)
    assert_equal [], assigns(:products)
  end

  test "should find products and categories by search term" do
    get :search, params: { q: 'Software' }
    assert_response :success
    
    categories = assigns(:categories)
    
    # Should find the category named "Software"
    assert categories.any? { |cat| cat.name.downcase.include?('software') }
  end

  # API Search Tests
  test "should perform API search" do
    get :api_search, params: { q: 'Test' }, format: :json
    assert_response :success
    
    response_data = JSON.parse(response.body)
    assert response_data.key?('categories')
    assert response_data.key?('products')
  end

  test "should return empty JSON for short search query" do
    get :api_search, params: { q: 'T' }, format: :json
    assert_response :success
    
    response_data = JSON.parse(response.body)
    assert_equal [], response_data['categories']
    assert_equal [], response_data['products']
  end

  test "should limit API search results" do
    get :api_search, params: { q: 'Test' }, format: :json
    assert_response :success
    
    response_data = JSON.parse(response.body)
    assert response_data['categories'].count <= 5
    assert response_data['products'].count <= 5
  end

  # Content Access Tests
  test "should deny product content access to unauthenticated user" do
    content = ProductContent.new(
      product: @product1,
      title: "Test Content",
      description: "Test content description",
      published: true,
      position: 1
    )
    content.file.attach(
      io: StringIO.new("test file content"),
      filename: "content.pdf",
      content_type: "application/pdf"
    )
    content.save!

    get :product_content, params: { product_id: @product1.friendly_id, id: content.id }
    assert_redirected_to marketplace_product_path(@product1)
    assert_equal "Access denied. Only approved government agencies and departments can view this content.", flash[:alert]
  end

  test "should allow product content access to agency user" do
    content = ProductContent.new(
      product: @product1,
      title: "Test Content",
      description: "Test content description",
      published: true,
      position: 1
    )
    content.file.attach(
      io: StringIO.new("test file content"),
      filename: "content.pdf",
      content_type: "application/pdf"
    )
    content.save!

    sign_in @agency_user

    get :product_content, params: { product_id: @product1.friendly_id, id: content.id }
    assert_response :success
    assert_equal @product1, assigns(:product)
    assert_equal content, assigns(:product_content)
  end

  # Feature Tests
  test "should show product feature" do
    feature = ProductFeature.create!(
      product: @product1,
      name: "Test Feature",
      description: "A test feature",
      published: true,
      position: 1
    )

    get :product_feature, params: { product_id: @product1.friendly_id, id: feature.id }
    assert_response :success
    assert_equal @product1, assigns(:product)
    assert_equal feature, assigns(:product_feature)
  end

  # Helper Method Tests
  test "can_view_draft_product should work for product owner" do
    sign_in @vendor_user
    session[:account_id] = @vendor_account.id
    get :product, params: { id: @product1.friendly_id }
    
    # Manual session restoration for unauthenticated controllers
    if cookies.signed[:session_id] && Current.session.nil?
      Current.session = Session.find_by(id: cookies.signed[:session_id])
    end
    
    assert @controller.send(:can_view_draft_product?, @unpublished_product)
  end

  test "can_view_product_content should work for agency user" do
    sign_in @agency_user
    session[:account_id] = @agency_account.id
    get :product, params: { id: @product1.friendly_id }
    
    # Manual session restoration for unauthenticated controllers
    if cookies.signed[:session_id] && Current.session.nil?
      Current.session = Session.find_by(id: cookies.signed[:session_id])
    end
    
    assert @controller.send(:can_view_product_content?, @product1)
  end

  test "should allow unauthenticated access to all public actions" do
    # Test that controller allows unauthenticated access
    get :index
    assert_response :success
    
    get :categories
    assert_response :success
    
    get :products
    assert_response :success
    
    get :product, params: { id: @product1.friendly_id }
    assert_response :success
  end

  # Error Handling Tests
  test "should handle nonexistent product gracefully" do
    get :product, params: { id: 'nonexistent' }
    assert_response 302  # Rails redirects to error page
    assert_redirected_to '/errors/not_found'
  end

  test "should handle nonexistent category gracefully" do
    get :category, params: { id: 'nonexistent' }
    assert_response 302  # Rails redirects to error page
    assert_redirected_to '/errors/not_found'
  end

  test "should handle nonexistent vendor gracefully" do
    get :vendor_products, params: { id: 'nonexistent' }
    assert_response 302  # Rails redirects to error page
    assert_redirected_to '/errors/not_found'
  end

  private

  def sign_in(user)
    session_record = user.sessions.create!(
      user_agent: 'Test User Agent',
      ip_address: '127.0.0.1'
    )
    Current.session = session_record
    cookies.signed[:session_id] = session_record.id
  end
end