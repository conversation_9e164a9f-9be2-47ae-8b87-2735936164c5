require "test_helper"

class AccountConfirmationsControllerTest < ActionController::TestCase
  def setup
    @user = users(:one)
    @account = accounts(:one)
    @pending_account = accounts(:pending_vendor)
    
    # Set up accounts with different confirmation states
    @unconfirmed_account = Account.create!(
      name: "Unconfirmed Company",
      account_type: "vendor",
      status: "pending",
      owner: @user,
      confirmation_token: SecureRandom.urlsafe_base64(32),
      confirmation_sent_at: Time.current,
      confirmed_at: nil
    )
    
    @confirmed_account = Account.create!(
      name: "Confirmed Company",
      account_type: "vendor",
      status: "pending",
      owner: @user,
      confirmation_token: SecureRandom.urlsafe_base64(32),
      confirmation_sent_at: 1.hour.ago,
      confirmed_at: 1.hour.ago
    )
    
    @invalid_token = "invalid_token_12345"
  end

  # ===============================
  # SHOW ACTION TESTS - SUCCESS CASES
  # ===============================

  test "should confirm account with valid token" do
    assert_nil @unconfirmed_account.confirmed_at
    
    get :show, params: { token: @unconfirmed_account.confirmation_token }
    
    assert_response :success
    assert_not_nil assigns(:account)
    assert assigns(:confirmation_success)
    
    @unconfirmed_account.reload
    assert_not_nil @unconfirmed_account.confirmed_at
    assert @unconfirmed_account.confirmed_at <= Time.current
  end

  test "should handle already confirmed account gracefully" do
    assert_not_nil @confirmed_account.confirmed_at
    original_confirmed_at = @confirmed_account.confirmed_at
    
    get :show, params: { token: @confirmed_account.confirmation_token }
    
    assert_response :success
    assert_not_nil assigns(:account)
    assert_not assigns(:confirmation_success)
    
    @confirmed_account.reload
    assert_equal original_confirmed_at.to_i, @confirmed_account.confirmed_at.to_i
  end

  test "should allow unauthenticated access to show" do
    get :show, params: { token: @unconfirmed_account.confirmation_token }
    
    assert_response :success
    assert_nil session[:return_to_after_authenticating]
  end

  # ===============================
  # SHOW ACTION TESTS - FAILURE CASES
  # ===============================

  test "should redirect with invalid token" do
    get :show, params: { token: @invalid_token }
    
    assert_redirected_to root_path
    assert_match /Invalid confirmation link/, flash[:alert]
  end

  test "should redirect with missing token" do
    get :show, params: {}
    
    assert_redirected_to root_path
    assert_match /Invalid confirmation link/, flash[:alert]
  end

  test "should redirect with empty token" do
    get :show, params: { token: "" }
    
    assert_redirected_to root_path
    assert_match /Invalid confirmation link/, flash[:alert]
  end

  test "should redirect with nil token" do
    get :show, params: { token: nil }
    
    assert_redirected_to root_path
    assert_match /Invalid confirmation link/, flash[:alert]
  end

  test "should handle nonexistent token gracefully" do
    nonexistent_token = SecureRandom.urlsafe_base64(32)
    
    get :show, params: { token: nonexistent_token }
    
    assert_redirected_to root_path
    assert_match /Invalid confirmation link/, flash[:alert]
  end

  # ===============================
  # NEW ACTION TESTS
  # ===============================

  test "should get new confirmation request page" do
    get :new
    assert_response :success
  end

  test "should allow unauthenticated access to new" do
    get :new
    assert_response :success
    assert_nil session[:return_to_after_authenticating]
  end

  test "should not require authentication for new action" do
    get :new
    assert_response :success
  end

  # ===============================
  # CREATE ACTION TESTS - SUCCESS CASES
  # ===============================

  test "should resend confirmation email for unconfirmed account" do
    sign_in @user
    
    original_token = @unconfirmed_account.confirmation_token
    original_sent_at = @unconfirmed_account.confirmation_sent_at
    
    assert_enqueued_email_with AccountMailer, :confirmation_instructions do
      post :create, params: { account_id: @unconfirmed_account.id }
    end
    
    assert_redirected_to pending_path
    assert_match /Confirmation email sent/, flash[:notice]
    
    @unconfirmed_account.reload
    assert_not_equal original_token, @unconfirmed_account.confirmation_token
    assert @unconfirmed_account.confirmation_sent_at > original_sent_at
  end

  test "should generate new confirmation token on resend" do
    sign_in @user
    
    original_token = @unconfirmed_account.confirmation_token
    
    post :create, params: { account_id: @unconfirmed_account.id }
    
    @unconfirmed_account.reload
    assert_not_equal original_token, @unconfirmed_account.confirmation_token
    assert_not_nil @unconfirmed_account.confirmation_token
  end

  test "should update confirmation_sent_at timestamp" do
    sign_in @user
    
    original_sent_at = @unconfirmed_account.confirmation_sent_at
    
    post :create, params: { account_id: @unconfirmed_account.id }
    
    @unconfirmed_account.reload
    assert @unconfirmed_account.confirmation_sent_at > original_sent_at
    assert @unconfirmed_account.confirmation_sent_at <= Time.current
  end

  # ===============================
  # CREATE ACTION TESTS - FAILURE CASES
  # ===============================

  test "should redirect unauthenticated user" do
    post :create, params: { account_id: @unconfirmed_account.id }
    
    assert_redirected_to new_session_path
  end

  test "should not resend for nonexistent account" do
    sign_in @user
    
    assert_no_enqueued_emails do
      post :create, params: { account_id: 99999 }
    end
    
    assert_redirected_to pending_path
    assert_match /Account not found/, flash[:alert]
  end

  test "should not resend for account user does not own" do
    other_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Other",
      last_name: "User"
    )
    other_account = Account.create!(
      name: "Other Company",
      account_type: "vendor",
      status: "pending",
      owner: other_user,
      confirmation_token: SecureRandom.urlsafe_base64(32),
      confirmed_at: nil
    )
    
    sign_in @user
    
    assert_no_enqueued_emails do
      post :create, params: { account_id: other_account.id }
    end
    
    assert_redirected_to pending_path
    assert_match /Account not found/, flash[:alert]
  end

  test "should not resend for already confirmed account" do
    sign_in @user
    
    assert_no_enqueued_emails do
      post :create, params: { account_id: @confirmed_account.id }
    end
    
    assert_redirected_to pending_path
    assert_match /already confirmed/, flash[:notice]
  end

  test "should handle missing account_id parameter" do
    sign_in @user
    
    assert_no_enqueued_emails do
      post :create, params: {}
    end
    
    assert_redirected_to pending_path
    assert_match /Account not found/, flash[:alert]
  end

  test "should handle invalid account_id parameter" do
    sign_in @user
    
    assert_no_enqueued_emails do
      post :create, params: { account_id: "invalid" }
    end
    
    assert_redirected_to pending_path
    assert_match /Account not found/, flash[:alert]
  end

  # ===============================
  # SECURITY TESTS
  # ===============================

  test "should allow unauthenticated access only to allowed actions" do
    # show, new, create should be accessible without authentication
    # (though create will redirect unauthenticated users)
    
    get :new
    assert_response :success
    
    get :show, params: { token: @unconfirmed_account.confirmation_token }
    assert_response :success
    
    post :create, params: { account_id: @unconfirmed_account.id }
    assert_redirected_to new_session_path  # Requires auth for create
  end

  test "should handle token timing attacks" do
    # Both invalid and valid tokens should take similar time to process
    
    start_time = Time.current
    get :show, params: { token: @invalid_token }
    invalid_time = Time.current - start_time
    
    start_time = Time.current
    get :show, params: { token: @unconfirmed_account.confirmation_token }
    valid_time = Time.current - start_time
    
    # Times should be relatively close
    assert (invalid_time - valid_time).abs < 1.second
  end

  test "should handle SQL injection attempts in token" do
    malicious_token = "'; DROP TABLE accounts; --"
    
    get :show, params: { token: malicious_token }
    
    assert_redirected_to root_path
    assert_match /Invalid confirmation link/, flash[:alert]
    
    # Verify accounts table still exists
    assert Account.count > 0
  end

  test "should handle XSS attempts in token" do
    xss_token = "<script>alert('xss')</script>"
    
    get :show, params: { token: xss_token }
    
    assert_redirected_to root_path
    assert_match /Invalid confirmation link/, flash[:alert]
    assert_not flash[:alert].include?("<script>")
  end

  # ===============================
  # TOKEN HANDLING TESTS
  # ===============================

  test "should handle malformed tokens gracefully" do
    malformed_tokens = [
      "short",
      "a" * 1000,  # Very long token
      "token with spaces",
      "token/with/slashes",
      "token%20with%20encoding",
      "токен",  # Unicode
      "🔒🔑",   # Emoji
    ]
    
    malformed_tokens.each do |token|
      get :show, params: { token: token }
      assert_redirected_to root_path
      assert_match /Invalid confirmation link/, flash[:alert]
    end
  end

  test "should handle token case sensitivity" do
    # Tokens should be case sensitive
    uppercase_token = @unconfirmed_account.confirmation_token.upcase
    
    if uppercase_token != @unconfirmed_account.confirmation_token
      get :show, params: { token: uppercase_token }
      assert_redirected_to root_path
      assert_match /Invalid confirmation link/, flash[:alert]
    end
  end

  test "should handle token reuse" do
    # First use should work
    get :show, params: { token: @unconfirmed_account.confirmation_token }
    assert_response :success
    assert assigns(:confirmation_success)
    
    # Second use should still work but show already confirmed message
    get :show, params: { token: @unconfirmed_account.confirmation_token }
    assert_response :success
    assert_not assigns(:confirmation_success)
  end

  # ===============================
  # EMAIL DELIVERY TESTS
  # ===============================

  test "should send confirmation email with valid data" do
    sign_in @user
    
    assert_enqueued_email_with AccountMailer, :confirmation_instructions do
      post :create, params: { account_id: @unconfirmed_account.id }
    end
  end

  test "should not send email for already confirmed account" do
    sign_in @user
    
    assert_no_enqueued_emails do
      post :create, params: { account_id: @confirmed_account.id }
    end
  end

  test "should not send email for nonexistent account" do
    sign_in @user
    
    assert_no_enqueued_emails do
      post :create, params: { account_id: 99999 }
    end
  end

  # ===============================
  # EDGE CASE TESTS
  # ===============================

  test "should handle concurrent confirmation attempts" do
    # Simulate multiple confirmation attempts with same token
    token = @unconfirmed_account.confirmation_token
    
    # First confirmation
    get :show, params: { token: token }
    assert_response :success
    assert assigns(:confirmation_success)
    
    # Second confirmation (concurrent or subsequent)
    get :show, params: { token: token }
    assert_response :success
    assert_not assigns(:confirmation_success)
    
    # Account should only be confirmed once
    @unconfirmed_account.reload
    assert_not_nil @unconfirmed_account.confirmed_at
  end

  test "should handle very long tokens" do
    very_long_token = "a" * 1000
    
    get :show, params: { token: very_long_token }
    
    assert_redirected_to root_path
    assert_match /Invalid confirmation link/, flash[:alert]
  end

  test "should handle special characters in tokens" do
    special_tokens = [
      "token-with-dashes",
      "token_with_underscores",
      "token.with.dots",
      "token+with+plus"
    ]
    
    special_tokens.each do |token|
      get :show, params: { token: token }
      assert_redirected_to root_path
      assert_match /Invalid confirmation link/, flash[:alert]
    end
  end

  # ===============================
  # ACCOUNT STATE TESTS
  # ===============================

  test "should handle account with nil confirmation_token" do
    account_without_token = Account.create!(
      name: "No Token Company",
      account_type: "vendor",
      status: "pending",
      owner: @user,
      confirmation_token: nil,
      confirmed_at: nil
    )
    
    get :show, params: { token: "any_token" }
    
    assert_redirected_to root_path
    assert_match /Invalid confirmation link/, flash[:alert]
  end

  test "should handle deleted account gracefully" do
    token = @unconfirmed_account.confirmation_token
    @unconfirmed_account.destroy
    
    get :show, params: { token: token }
    
    assert_redirected_to root_path
    assert_match /Invalid confirmation link/, flash[:alert]
  end

  # ===============================
  # INTEGRATION TESTS
  # ===============================

  test "should complete full confirmation workflow" do
    # Start with unconfirmed account
    assert_nil @unconfirmed_account.confirmed_at
    token = @unconfirmed_account.confirmation_token
    
    # Confirm account
    get :show, params: { token: token }
    
    assert_response :success
    assert assigns(:confirmation_success)
    
    # Verify account is now confirmed
    @unconfirmed_account.reload
    assert_not_nil @unconfirmed_account.confirmed_at
    
    # Subsequent confirmation attempts should show already confirmed
    get :show, params: { token: token }
    
    assert_response :success
    assert_not assigns(:confirmation_success)
  end

  test "should complete full resend confirmation workflow" do
    sign_in @user
    
    # Request new confirmation email
    original_token = @unconfirmed_account.confirmation_token
    
    assert_enqueued_email_with AccountMailer, :confirmation_instructions do
      post :create, params: { account_id: @unconfirmed_account.id }
    end
    
    assert_redirected_to pending_path
    assert_match /Confirmation email sent/, flash[:notice]
    
    # Verify new token was generated
    @unconfirmed_account.reload
    new_token = @unconfirmed_account.confirmation_token
    assert_not_equal original_token, new_token
    
    # Confirm with new token
    get :show, params: { token: new_token }
    
    assert_response :success
    assert assigns(:confirmation_success)
    
    @unconfirmed_account.reload
    assert_not_nil @unconfirmed_account.confirmed_at
  end

  private

  def sign_in(user)
    # Simulate signing in by creating a session
    session_record = user.sessions.create!(
      user_agent: 'Test User Agent',
      ip_address: '127.0.0.1'
    )
    Current.session = session_record
    @request.cookies.signed[:session_id] = session_record.id
  end
end