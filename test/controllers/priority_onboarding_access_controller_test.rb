require "test_helper"

class PriorityOnboardingAccessControllerTest < ActionController::TestCase
  def setup
    @admin_user = users(:admin)
  end

  # ===============================
  # AUTHENTICATION TESTS
  # ===============================

  test "should allow unauthenticated access" do
    get :new
    assert_response :success
    assert_nil session[:return_to_after_authenticating]
  end

  test "should not require authentication for any action" do
    get :new
    assert_response :success
    
    post :create, params: {
      user: {
        first_name: "Test",
        last_name: "User",
        email_address: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123",
        job_title: "Manager",
        account_name: "Test Company"
      },
      account_type: "vendor"
    }
    assert_response :redirect
  end

  # ===============================
  # NEW ACTION TESTS
  # ===============================

  test "should get new priority onboarding form" do
    get :new
    assert_response :success
    assert_template :new
  end

  test "should initialize user and account objects" do
    get :new
    
    assert_not_nil assigns(:user)
    assert assigns(:user).is_a?(User)
    assert assigns(:user).new_record?
    
    assert_not_nil assigns(:account)
    assert assigns(:account).is_a?(Account)
    assert assigns(:account).new_record?
  end

  # ===============================
  # CREATE ACTION TESTS - VENDOR ACCOUNTS
  # ===============================

  test "should create vendor user and account successfully" do
    assert_difference ['User.count', 'Account.count', 'AccountUser.count'], 1 do
      post :create, params: {
        user: {
          first_name: "John",
          last_name: "Doe",
          email_address: "<EMAIL>",
          password: "password123",
          password_confirmation: "password123",
          job_title: "CEO",
          account_name: "TechCorp Solutions"
        },
        account_type: "vendor"
      }
    end
    
    # Should redirect to wherever after_authentication_url points
    assert_response :redirect
    assert_match /Welcome to Platia/, flash[:notice]
  end

  test "should create vendor account with proper attributes" do
    post :create, params: {
      user: {
        first_name: "Jane",
        last_name: "Smith",
        email_address: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123",
        job_title: "CTO",
        account_name: "VendorCorp Inc"
      },
      account_type: "vendor"
    }
    
    account = Account.last
    assert_equal "vendor", account.account_type
    assert_equal "VendorCorp Inc", account.name
    assert_equal "approved", account.status
    assert_not_nil account.approved_at
    assert_not_nil account.confirmed_at
    assert_equal User.last, account.owner
  end

  test "should use default vendor account name if not provided" do
    post :create, params: {
      user: {
        first_name: "Default",
        last_name: "Vendor",
        email_address: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123",
        job_title: "Owner"
      },
      account_type: "vendor"
    }
    
    account = Account.last
    assert_equal "Default Vendor's Vendor Account", account.name
  end

  # ===============================
  # CREATE ACTION TESTS - AGENCY ACCOUNTS
  # ===============================

  test "should create agency user and account successfully" do
    assert_difference ['User.count', 'Account.count', 'AccountUser.count'], 1 do
      post :create, params: {
        user: {
          first_name: "Maria",
          last_name: "Rodriguez",
          email_address: "<EMAIL>",
          password: "password123",
          password_confirmation: "password123",
          job_title: "IT Director",
          account_name: "City Government IT"
        },
        account_type: "agency"
      }
    end
    
    # Should redirect to wherever after_authentication_url points
    assert_response :redirect
    assert_match /Welcome to Platia/, flash[:notice]
  end

  test "should create agency account with proper attributes" do
    post :create, params: {
      user: {
        first_name: "Bob",
        last_name: "Wilson",
        email_address: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123",
        job_title: "Administrator",
        account_name: "State Agency Department"
      },
      account_type: "agency"
    }
    
    account = Account.last
    assert_equal "agency", account.account_type
    assert_equal "State Agency Department", account.name
    assert_equal "approved", account.status
    assert_not_nil account.approved_at
    assert_not_nil account.confirmed_at
    assert_equal User.last, account.owner
  end

  test "should use default agency account name if not provided" do
    post :create, params: {
      user: {
        first_name: "Default",
        last_name: "Agency",
        email_address: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123",
        job_title: "Manager"
      },
      account_type: "agency"
    }
    
    account = Account.last
    assert_equal "Default Agency's Agency Account", account.name
  end

  test "should default to vendor if account_type not specified" do
    post :create, params: {
      user: {
        first_name: "Unknown",
        last_name: "Type",
        email_address: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123",
        job_title: "Worker"
      }
    }
    
    account = Account.last
    assert_equal "vendor", account.account_type
  end

  # ===============================
  # CREATE ACTION TESTS - ACCOUNT USER ASSOCIATION
  # ===============================

  test "should create account_user association with admin role" do
    post :create, params: {
      user: {
        first_name: "Admin",
        last_name: "User",
        email_address: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123",
        job_title: "Founder"
      },
      account_type: "vendor"
    }
    
    account_user = AccountUser.last
    assert_equal "admin", account_user.role
    assert_equal Account.last, account_user.account
    assert_equal User.last, account_user.user
  end

  # ===============================
  # CREATE ACTION TESTS - AUTHENTICATION
  # ===============================

  test "should sign in user after successful creation" do
    post :create, params: {
      user: {
        first_name: "Signin",
        last_name: "Test",
        email_address: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123",
        job_title: "Tester"
      },
      account_type: "vendor"
    }
    
    assert_not_nil Current.session
    assert_equal User.last, Current.user
  end

  # ===============================
  # CREATE ACTION TESTS - VALIDATION FAILURES
  # ===============================

  test "should handle user validation errors" do
    assert_no_difference ['User.count', 'Account.count'] do
      post :create, params: {
        user: {
          first_name: "",  # Invalid - required
          last_name: "Doe",
          email_address: "invalid-email",  # Invalid format
          password: "123",  # Too short
          password_confirmation: "456",  # Doesn't match
          job_title: "Manager"
        },
        account_type: "vendor"
      }
    end
    
    assert_response :unprocessable_entity
    assert_template :new
    assert_not_nil assigns(:user)
    assert_not_nil assigns(:account)
  end

  test "should preserve form data on validation errors" do
    post :create, params: {
      user: {
        first_name: "",  # Invalid
        last_name: "Preserve",
        email_address: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123",
        job_title: "Preserver"
      },
      account_type: "vendor"
    }
    
    user = assigns(:user)
    assert_equal "Preserve", user.last_name
    assert_equal "<EMAIL>", user.email_address
    assert_equal "Preserver", user.job_title
  end

  test "should handle missing user parameters" do
    assert_no_difference ['User.count', 'Account.count'] do
      post :create, params: { account_type: "vendor" }
    end
    
    # Should redirect with error instead of 422
    assert_redirected_to root_path
    assert_match /error creating your account/, flash[:alert]
  end

  test "should handle empty user parameters" do
    assert_no_difference ['User.count', 'Account.count'] do
      post :create, params: {
        user: {},
        account_type: "vendor"
      }
    end
    
    # Should redirect with error instead of 422
    assert_redirected_to root_path
    assert_match /error creating your account/, flash[:alert]
  end

  # ===============================
  # CREATE ACTION TESTS - DATABASE ERRORS
  # ===============================

  test "should handle database errors gracefully" do
    # Simulate database constraint violation
    post :create, params: {
      user: {
        first_name: "Error",
        last_name: "Test",
        email_address: "<EMAIL>",  # Duplicate email from fixtures
        password: "password123",
        password_confirmation: "password123",
        job_title: "Tester"
      },
      account_type: "vendor"
    }
    
    # Should redirect to root with error message
    assert_redirected_to root_path
    assert_match /error creating your account/, flash[:alert]
  end

  test "should rollback transaction on account creation failure" do
    # Mock account creation to fail by stubbing save! method
    Account.any_instance.expects(:save!).raises(ActiveRecord::RecordInvalid.new(Account.new))
    
    assert_no_difference ['User.count', 'Account.count'] do
      post :create, params: {
        user: {
          first_name: "Rollback",
          last_name: "Test",
          email_address: "<EMAIL>",
          password: "password123",
          password_confirmation: "password123",
          job_title: "Tester"
        },
        account_type: "vendor"
      }
    end
    
    assert_redirected_to root_path
    assert_match /error creating your account/, flash[:alert]
  end

  # ===============================
  # PARAMETER HANDLING TESTS
  # ===============================

  test "should permit user parameters" do
    post :create, params: {
      user: {
        first_name: "Param",
        last_name: "Test",
        email_address: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123",
        job_title: "Parameter Tester",
        malicious_param: "should_be_ignored"
      },
      account_type: "vendor"
    }
    
    user = User.last
    assert_equal "Param", user.first_name
    assert_equal "Test", user.last_name
    assert_equal "<EMAIL>", user.email_address
    assert_equal "Parameter Tester", user.job_title
  end

  test "should handle account_name in user params" do
    post :create, params: {
      user: {
        first_name: "Account",
        last_name: "Name",
        email_address: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123",
        job_title: "Namer",
        account_name: "Custom Account Name"
      },
      account_type: "vendor"
    }
    
    account = Account.last
    assert_equal "Custom Account Name", account.name
  end

  # ===============================
  # EDGE CASE TESTS
  # ===============================

  test "should handle invalid account_type parameter" do
    post :create, params: {
      user: {
        first_name: "Invalid",
        last_name: "Type",
        email_address: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123",
        job_title: "Tester"
      },
      account_type: "invalid_type"
    }
    
    account = Account.last
    assert_equal "vendor", account.account_type  # Should default to vendor
  end

  test "should handle missing account_type parameter" do
    post :create, params: {
      user: {
        first_name: "Missing",
        last_name: "Type",
        email_address: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123",
        job_title: "Tester"
      }
    }
    
    account = Account.last
    assert_equal "vendor", account.account_type  # Should default to vendor
  end

  # ===============================
  # SECURITY TESTS
  # ===============================

  test "should not allow mass assignment of unauthorized attributes" do
    post :create, params: {
      user: {
        first_name: "Security",
        last_name: "Test",
        email_address: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123",
        job_title: "Tester",
        super_admin: true  # Should be ignored
      },
      account_type: "vendor"
    }
    
    user = User.last
    assert_not user.super_admin
  end

  test "should create session securely" do
    post :create, params: {
      user: {
        first_name: "Session",
        last_name: "Test",
        email_address: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123",
        job_title: "Tester"
      },
      account_type: "vendor"
    }
    
    assert_not_nil Current.session
    assert Current.session.is_a?(Session)
    assert_equal User.last, Current.session.user
  end

  # ===============================
  # INTEGRATION TESTS
  # ===============================

  test "should handle complete priority onboarding flow" do
    # Show form
    get :new
    assert_response :success
    
    # Submit form
    assert_difference ['User.count', 'Account.count', 'AccountUser.count'], 1 do
      post :create, params: {
        user: {
          first_name: "Complete",
          last_name: "Flow",
          email_address: "<EMAIL>",
          password: "password123",
          password_confirmation: "password123",
          job_title: "Integration Tester",
          account_name: "Complete Flow Company"
        },
        account_type: "vendor"
      }
    end
    
    # Should be redirected and signed in
    # Should redirect to wherever after_authentication_url points
    assert_response :redirect
    assert_not_nil Current.session
    
    # Verify account and user are properly set up
    user = User.last
    account = Account.last
    account_user = AccountUser.last
    
    assert_equal "Complete Flow Company", account.name
    assert_equal "approved", account.status
    assert_equal user, account.owner
    assert_equal "admin", account_user.role
  end

  test "should handle form resubmission after validation error" do
    # Submit invalid form
    post :create, params: {
      user: {
        first_name: "",  # Invalid
        last_name: "Resubmit",
        email_address: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123",
        job_title: "Tester"
      },
      account_type: "vendor"
    }
    
    assert_response :unprocessable_entity
    assert_template :new
    
    # Resubmit with valid data
    assert_difference ['User.count', 'Account.count'], 1 do
      post :create, params: {
        user: {
          first_name: "Fixed",  # Now valid
          last_name: "Resubmit",
          email_address: "<EMAIL>",
          password: "password123",
          password_confirmation: "password123",
          job_title: "Tester"
        },
        account_type: "vendor"
      }
    end
    
    # Should redirect to wherever after_authentication_url points
    assert_response :redirect
  end
end