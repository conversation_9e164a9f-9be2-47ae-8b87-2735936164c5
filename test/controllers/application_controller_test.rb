require "test_helper"

class ApplicationControllerTest < ActionController::TestCase
  # Since ApplicationController is abstract, we'll test through a concrete controller
  # Let's create a test controller that inherits from ApplicationController
  
  class TestController < ApplicationController
    def index
      render plain: "index"
    end
    
    def show
      render plain: "show"
    end
    
    def vendor_only
      require_vendor_account
      render plain: "vendor_only"
    end
    
    def agency_only
      require_agency_account
      render plain: "agency_only"
    end
    
    def admin_only
      require_admin
      render plain: "admin_only"
    end
    
    def account_required
      require_account
      render plain: "account_required"
    end
    
    def approved_account_required
      require_approved_account
      render plain: "approved_account_required"
    end
    
    def account_admin_required
      require_account_admin
      render plain: "account_admin_required"
    end
    
    # Allow unauthenticated access for testing
    allow_unauthenticated_access only: [:index]
  end
  
  def setup
    @controller = TestController.new
    
    # Set up routes for our test controller
    Rails.application.routes.draw do
      resources :test, only: [:index, :show] do
        collection do
          get :vendor_only
          get :agency_only
          get :admin_only
          get :account_required
          get :approved_account_required
          get :account_admin_required
        end
      end
    end
    
    # Create test users and accounts
    @admin_user = users(:admin)
    @vendor_user = users(:one)
    @agency_user = users(:two)
    @vendor_account = accounts(:one)
    @agency_account = accounts(:two)
    @pending_account = accounts(:pending_vendor)
    
    # Ensure accounts have proper types
    @vendor_account.update!(account_type: 'vendor', status: 'approved')
    @agency_account.update!(account_type: 'agency', status: 'approved') 
    @pending_account.update!(account_type: 'vendor', status: 'pending')
  end
  
  def teardown
    # Restore original routes
    Rails.application.reload_routes!
  end

  # ===============================
  # AUTHENTICATION TESTS
  # ===============================

  test "should allow unauthenticated access to allowed actions" do
    get :index
    assert_response :success
    assert_equal "index", response.body
  end

  test "should require authentication for protected actions" do
    get :show, params: { id: 1 }
    assert_redirected_to new_session_path
  end

  test "should store return_to path when requiring authentication" do
    get :show, params: { id: 1 }
    assert_equal test_url(id: 1), session[:return_to_after_authenticating]
  end

  # ===============================
  # CURRENT USER TESTS
  # ===============================

  test "current_user should return nil when not authenticated" do
    assert_nil @controller.send(:current_user)
  end

  test "current_user should return user when authenticated" do
    sign_in @vendor_user
    assert_equal @vendor_user, @controller.send(:current_user)
  end

  # ===============================
  # CURRENT ACCOUNT TESTS
  # ===============================

  test "current_account should return nil when not authenticated" do
    assert_nil @controller.send(:current_account)
  end

  test "current_account should return first account when no account_id in session" do
    sign_in @vendor_user
    assert_equal @vendor_account, @controller.send(:current_account)
  end

  test "current_account should return specific account when account_id in session" do
    sign_in @vendor_user
    # Assume user has multiple accounts
    session[:account_id] = @vendor_account.id
    assert_equal @vendor_account, @controller.send(:current_account)
  end

  test "set_current_account should update session and instance variable" do
    sign_in @vendor_user
    @controller.send(:set_current_account, @vendor_account)
    assert_equal @vendor_account.id, session[:account_id]
    assert_equal @vendor_account, @controller.send(:current_account)
  end

  test "set_current_account should handle nil account" do
    sign_in @vendor_user
    @controller.send(:set_current_account, nil)
    assert_nil session[:account_id]
    assert_nil @controller.send(:current_account)
  end

  # ===============================
  # AUTHORIZATION TESTS
  # ===============================

  test "require_account should redirect when no account" do
    user_without_accounts = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "No",
      last_name: "Accounts"
    )
    sign_in user_without_accounts
    
    get :account_required
    assert_redirected_to orphaned_users_path
  end

  test "require_account should allow access when account exists" do
    sign_in @vendor_user
    get :account_required
    assert_response :success
    assert_equal "account_required", response.body
  end

  test "require_vendor_account should redirect non-vendor" do
    sign_in @agency_user
    get :vendor_only
    assert_redirected_to root_path
  end

  test "require_vendor_account should allow vendor access" do
    sign_in @vendor_user
    get :vendor_only
    assert_response :success
    assert_equal "vendor_only", response.body
  end

  test "require_agency_account should redirect non-agency" do
    sign_in @vendor_user
    get :agency_only
    assert_redirected_to root_path
  end

  test "require_agency_account should allow agency access" do
    sign_in @agency_user
    get :agency_only
    assert_response :success
    assert_equal "agency_only", response.body
  end

  test "require_admin should redirect non-admin" do
    sign_in @vendor_user
    get :admin_only
    assert_redirected_to root_path
  end

  test "require_admin should allow admin access" do
    sign_in @admin_user
    get :admin_only
    assert_response :success
    assert_equal "admin_only", response.body
  end

  test "require_approved_account should redirect pending account" do
    pending_user = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Pending",
      last_name: "User"
    )
    @pending_account.account_users.create!(user: pending_user, role: 'member')
    sign_in pending_user
    
    get :approved_account_required
    assert_redirected_to root_path
  end

  test "require_approved_account should allow approved account" do
    sign_in @vendor_user
    get :approved_account_required
    assert_response :success
    assert_equal "approved_account_required", response.body
  end

  # ===============================
  # ACCOUNT ADMIN TESTS
  # ===============================

  test "current_user_account_admin? should return false when not authenticated" do
    assert_not @controller.send(:current_user_account_admin?)
  end

  test "current_user_account_admin? should return true for account owner" do
    sign_in @vendor_user
    assert @controller.send(:current_user_account_admin?)
  end

  test "current_user_account_admin? should return true for admin user" do
    sign_in @admin_user
    # Set current account to any account
    @controller.send(:set_current_account, @vendor_account)
    assert @controller.send(:current_user_account_admin?)
  end

  test "current_user_account_admin? should return true for account admin role" do
    admin_member = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Account",
      last_name: "Admin"
    )
    @vendor_account.account_users.create!(user: admin_member, role: 'admin')
    sign_in admin_member
    
    assert @controller.send(:current_user_account_admin?)
  end

  test "current_user_account_admin? should return false for regular member" do
    regular_member = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Regular",
      last_name: "Member"
    )
    @vendor_account.account_users.create!(user: regular_member, role: 'member')
    sign_in regular_member
    
    assert_not @controller.send(:current_user_account_admin?)
  end

  test "require_account_admin should redirect non-admin members" do
    regular_member = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "Regular",
      last_name: "Member"
    )
    @vendor_account.account_users.create!(user: regular_member, role: 'member')
    sign_in regular_member
    
    get :account_admin_required
    assert_redirected_to root_path
    assert_equal 'You must be an account admin to access this page.', flash[:alert]
  end

  test "require_account_admin should allow account admin access" do
    sign_in @vendor_user  # Owner is always admin
    get :account_admin_required
    assert_response :success
    assert_equal "account_admin_required", response.body
  end

  # ===============================
  # HELPER METHOD TESTS
  # ===============================

  test "helper methods should be available in views" do
    sign_in @vendor_user
    get :index
    
    # These should not raise errors
    assert_nothing_raised do
      @controller.view_context.current_user
      @controller.view_context.current_account
      @controller.view_context.current_user_account_admin?
    end
  end

  # ===============================
  # SESSION MANAGEMENT TESTS
  # ===============================

  test "should maintain session across requests" do
    sign_in @vendor_user
    get :index
    assert_equal @vendor_user, @controller.send(:current_user)
    
    get :show, params: { id: 1 }
    assert_equal @vendor_user, @controller.send(:current_user)
  end

  test "should handle session expiration gracefully" do
    sign_in @vendor_user
    
    # Manually expire session
    Current.session.destroy if Current.session
    
    get :show, params: { id: 1 }
    assert_redirected_to new_session_path
  end

  # ===============================
  # EDGE CASE TESTS
  # ===============================

  test "should handle user with no accounts" do
    user_without_accounts = User.create!(
      email_address: "<EMAIL>",
      password: "password123",
      first_name: "No",
      last_name: "Accounts"
    )
    sign_in user_without_accounts
    
    assert_nil @controller.send(:current_account)
  end

  test "should handle invalid account_id in session" do
    sign_in @vendor_user
    session[:account_id] = 99999  # Non-existent account
    
    # Should return nil for invalid account
    assert_nil @controller.send(:current_account)
  end

  test "should handle account user doesn't belong to" do
    sign_in @vendor_user
    session[:account_id] = @agency_account.id  # Account user doesn't belong to
    
    # Should return nil for account user doesn't belong to
    assert_nil @controller.send(:current_account)
  end

  private

  def sign_in(user)
    # Simulate signing in by creating a session
    session_record = user.sessions.create!(
      user_agent: 'Test User Agent',
      ip_address: '127.0.0.1'
    )
    Current.session = session_record
    @request.cookies.signed[:session_id] = session_record.id
  end
end