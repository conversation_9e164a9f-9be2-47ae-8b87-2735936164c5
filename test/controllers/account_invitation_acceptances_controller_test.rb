require "test_helper"

class AccountInvitationAcceptancesControllerTest < ActionController::TestCase
  def setup
    @pending_vendor_invitation = account_invitations(:pending_vendor_invitation)
    @pending_agency_invitation = account_invitations(:pending_agency_invitation)
    @expired_invitation = account_invitations(:expired_invitation)
    @accepted_invitation = account_invitations(:accepted_invitation)
    @admin_user = users(:admin)
    
    # Ensure invitations have proper setup
    @pending_vendor_invitation.update!(
      token: "valid_vendor_token_123",
      status: "pending",
      expires_at: 1.week.from_now
    )
    
    @pending_agency_invitation.update!(
      token: "valid_agency_token_456",
      status: "pending",
      expires_at: 1.week.from_now
    )
    
    @expired_invitation.update!(
      token: "expired_token_789",
      status: "pending",
      expires_at: 1.day.ago
    )
    
    @accepted_invitation.update!(
      token: "accepted_token_abc",
      status: "accepted"
    )
  end

  # ===============================
  # AUTHENTICATION TESTS
  # ===============================

  test "should allow unauthenticated access" do
    get :show, params: { token: @pending_vendor_invitation.token }
    assert_response :success
    assert_nil session[:return_to_after_authenticating]
  end

  test "should not require authentication for show action" do
    get :show, params: { token: @pending_vendor_invitation.token }
    assert_response :success
  end

  test "should not require authentication for create action" do
    post :create, params: {
      token: @pending_vendor_invitation.token,
      account: { name: "Test Company" },
      user: {
        first_name: "John",
        last_name: "Doe",
        password: "password123",
        password_confirmation: "password123"
      }
    }
    assert_response :redirect
  end

  # ===============================
  # SHOW ACTION TESTS - VALID INVITATIONS
  # ===============================

  test "should show vendor invitation acceptance form" do
    get :show, params: { token: @pending_vendor_invitation.token }
    
    assert_response :success
    assert_template :show
    assert_equal @pending_vendor_invitation, assigns(:invitation)
  end

  test "should show agency invitation acceptance form" do
    get :show, params: { token: @pending_agency_invitation.token }
    
    assert_response :success
    assert_template :show
    assert_equal @pending_agency_invitation, assigns(:invitation)
  end

  test "should initialize account with invitation data" do
    get :show, params: { token: @pending_vendor_invitation.token }
    
    account = assigns(:account)
    assert_not_nil account
    assert_equal @pending_vendor_invitation.account_type, account.account_type
    assert_equal @pending_vendor_invitation.account_name, account.name
  end

  test "should initialize user with invitation data" do
    get :show, params: { token: @pending_vendor_invitation.token }
    
    user = assigns(:user)
    assert_not_nil user
    assert_equal @pending_vendor_invitation.first_name, user.first_name
    assert_equal @pending_vendor_invitation.last_name, user.last_name
    assert_equal @pending_vendor_invitation.email, user.email_address
    assert_equal @pending_vendor_invitation.phone, user.phone
    assert_equal @pending_vendor_invitation.job_title, user.job_title
    assert_equal @pending_vendor_invitation.department, user.department
  end

  # ===============================
  # SHOW ACTION TESTS - INVALID INVITATIONS
  # ===============================

  test "should redirect for invalid token" do
    get :show, params: { token: "invalid_token" }
    
    assert_redirected_to root_path
    assert_match /Invalid invitation link/, flash[:alert]
  end

  test "should redirect for expired invitation" do
    get :show, params: { token: @expired_invitation.token }
    
    assert_redirected_to root_path
    assert_match /expired/, flash[:alert]
  end

  test "should redirect for already accepted invitation" do
    get :show, params: { token: @accepted_invitation.token }
    
    assert_redirected_to root_path
    assert_match /already been accepted/, flash[:alert]
  end

  # ===============================
  # CREATE ACTION TESTS - SUCCESS CASES
  # ===============================

  test "should create account and user from vendor invitation" do
    assert_difference ['User.count', 'Account.count', 'AccountUser.count'], 1 do
      post :create, params: {
        token: @pending_vendor_invitation.token,
        account: { 
          name: "New Vendor Company"
        },
        user: {
          first_name: "John",
          last_name: "Doe",
          phone: "(*************",
          job_title: "CEO",
          department: "Executive",
          password: "password123",
          password_confirmation: "password123"
        }
      }
    end
    
    # Should redirect to somewhere appropriate
    assert_response :redirect
    assert_match /Welcome to Platia/, flash[:notice]
  end

  test "should create account and user from agency invitation" do
    assert_difference ['User.count', 'Account.count', 'AccountUser.count'], 1 do
      post :create, params: {
        token: @pending_agency_invitation.token,
        account: { 
          name: "New Agency Department"
        },
        user: {
          first_name: "Jane",
          last_name: "Smith",
          password: "password123",
          password_confirmation: "password123"
        }
      }
    end
    
    # Should redirect to somewhere appropriate
    assert_response :redirect
    assert_match /Welcome to Platia/, flash[:notice]
  end

  test "should set proper account attributes" do
    post :create, params: {
      token: @pending_vendor_invitation.token,
      account: { name: "Test Company" },
      user: {
        first_name: "John",
        last_name: "Doe",
        password: "password123",
        password_confirmation: "password123"
      }
    }
    
    account = Account.last
    assert_equal @pending_vendor_invitation.account_type, account.account_type
    assert_equal "approved", account.status
    assert_not_nil account.confirmed_at
    assert_not_nil account.approved_at
    assert_equal @pending_vendor_invitation.invited_by, account.approved_by
  end

  test "should create account_user association with admin role" do
    post :create, params: {
      token: @pending_vendor_invitation.token,
      account: { name: "Test Company" },
      user: {
        first_name: "John",
        last_name: "Doe",
        password: "password123",
        password_confirmation: "password123"
      }
    }
    
    account_user = AccountUser.last
    assert_equal "admin", account_user.role
    assert_equal Account.last, account_user.account
    assert_equal User.last, account_user.user
  end

  test "should mark invitation as accepted" do
    post :create, params: {
      token: @pending_vendor_invitation.token,
      account: { name: "Test Company" },
      user: {
        first_name: "John",
        last_name: "Doe",
        password: "password123",
        password_confirmation: "password123"
      }
    }
    
    @pending_vendor_invitation.reload
    assert_equal "accepted", @pending_vendor_invitation.status
  end

  test "should sign in the user after successful creation" do
    post :create, params: {
      token: @pending_vendor_invitation.token,
      account: { name: "Test Company" },
      user: {
        first_name: "John",
        last_name: "Doe",
        password: "password123",
        password_confirmation: "password123"
      }
    }
    
    # Should create a session and sign in the user
    user = User.last
    assert user.sessions.any?, "User should have at least one session"
    assert_not_nil cookies.signed[:session_id], "Session cookie should be set"
  end

  # ===============================
  # CREATE ACTION TESTS - FAILURE CASES
  # ===============================

  test "should handle invalid user data" do
    assert_no_difference ['User.count', 'Account.count'] do
      post :create, params: {
        token: @pending_vendor_invitation.token,
        account: { name: "Test Company" },
        user: {
          first_name: "",  # Invalid - required field
          last_name: "Doe",
          password: "123",  # Invalid - too short
          password_confirmation: "456"  # Invalid - doesn't match
        }
      }
    end
    
    assert_response :unprocessable_entity
    assert_template :show
  end

  test "should handle invalid account data" do
    assert_no_difference ['User.count', 'Account.count'] do
      post :create, params: {
        token: @pending_vendor_invitation.token,
        account: { name: "" },  # Invalid - required field
        user: {
          first_name: "John",
          last_name: "Doe",
          phone: "(*************",
          job_title: "CEO",
          department: "Executive",
          password: "password123",
          password_confirmation: "password123"
        }
      }
    end
    
    assert_response :unprocessable_entity
    assert_template :show
  end

  # ===============================
  # CREATE ACTION TESTS - INVALID INVITATIONS
  # ===============================

  test "should not create account for invalid token" do
    assert_no_difference ['User.count', 'Account.count'] do
      post :create, params: {
        token: "invalid_token",
        account: { name: "Test Company" },
        user: {
          first_name: "John",
          last_name: "Doe",
          phone: "(*************",
          job_title: "CEO",
          department: "Executive",
          password: "password123",
          password_confirmation: "password123"
        }
      }
    end
    
    assert_redirected_to root_path
    assert_match /Invalid invitation link/, flash[:alert]
  end

  test "should not create account for expired invitation" do
    assert_no_difference ['User.count', 'Account.count'] do
      post :create, params: {
        token: @expired_invitation.token,
        account: { name: "Test Company" },
        user: {
          first_name: "John",
          last_name: "Doe",
          phone: "(*************",
          job_title: "CEO",
          department: "Executive",
          password: "password123",
          password_confirmation: "password123"
        }
      }
    end
    
    assert_redirected_to root_path
    assert_match /expired/, flash[:alert]
  end

  test "should not create account for already accepted invitation" do
    assert_no_difference ['User.count', 'Account.count'] do
      post :create, params: {
        token: @accepted_invitation.token,
        account: { name: "Test Company" },
        user: {
          first_name: "John",
          last_name: "Doe",
          phone: "(*************",
          job_title: "CEO",
          department: "Executive",
          password: "password123",
          password_confirmation: "password123"
        }
      }
    end
    
    assert_redirected_to root_path
    assert_match /already been accepted/, flash[:alert]
  end

  # ===============================
  # PARAMETER HANDLING TESTS
  # ===============================

  test "should permit account parameters" do
    post :create, params: {
      token: @pending_vendor_invitation.token,
      account: { 
        name: "Test Company",
        malicious_param: "should_be_ignored"
      },
      user: {
        first_name: "John",
        last_name: "Doe",
        password: "password123",
        password_confirmation: "password123"
      }
    }
    
    account = Account.last
    assert_equal "Test Company", account.name
  end

  test "should permit user parameters" do
    post :create, params: {
      token: @pending_vendor_invitation.token,
      account: { name: "Test Company" },
      user: {
        first_name: "John",
        last_name: "Doe",
        phone: "(*************",
        job_title: "Manager",
        password: "password123",
        password_confirmation: "password123",
        malicious_param: "should_be_ignored"
      }
    }
    
    user = User.last
    assert_equal "John", user.first_name
    assert_equal "Doe", user.last_name
    assert_equal "(*************", user.phone
    assert_equal "CEO", user.job_title
    assert_equal "Executive", user.department
  end

  test "should force email from invitation" do
    post :create, params: {
      token: @pending_vendor_invitation.token,
      account: { name: "Test Company" },
      user: {
        first_name: "John",
        last_name: "Doe",
        email_address: "<EMAIL>",  # Should be ignored
        password: "password123",
        password_confirmation: "password123"
      }
    }
    
    user = User.last
    assert_equal @pending_vendor_invitation.email, user.email_address
  end

  # ===============================
  # SECURITY TESTS
  # ===============================

  test "should not expose invitation token in response" do
    get :show, params: { token: @pending_vendor_invitation.token }
    
    assert_response :success
    # Token should not appear in meta tags (canonical URL, og:url, etc)
    assert_not_includes response.body, "href=\"http://test.host/account-invitations/#{@pending_vendor_invitation.token}\""
    assert_not_includes response.body, "content=\"http://test.host/account-invitations/#{@pending_vendor_invitation.token}\""
    # The token will appear in form action, which is expected and necessary
  end

  test "should handle missing parameters gracefully" do
    post :create, params: { token: @pending_vendor_invitation.token }
    
    # Should redirect with error instead of crashing
    assert_redirected_to root_path
    assert_match /Required information is missing/, flash[:alert]
  end

  # ===============================
  # INTEGRATION TESTS
  # ===============================

  test "should handle complete invitation acceptance flow" do
    # Start with show
    get :show, params: { token: @pending_vendor_invitation.token }
    assert_response :success
    
    # Complete acceptance
    post :create, params: {
      token: @pending_vendor_invitation.token,
      account: { name: "Complete Flow Company" },
      user: {
        first_name: "Integration",
        last_name: "Test",
        password: "password123",
        password_confirmation: "password123"
      }
    }
    
    # Should redirect and sign in user
    assert_response :redirect
    user = User.find_by(email_address: @pending_vendor_invitation.email)
    assert user.sessions.any?, "User should have at least one session"
    
    # Verify invitation is marked as accepted
    @pending_vendor_invitation.reload
    assert_equal "accepted", @pending_vendor_invitation.status
  end
end