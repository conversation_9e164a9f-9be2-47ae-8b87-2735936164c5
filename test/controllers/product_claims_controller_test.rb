require "test_helper"

class ProductClaimsControllerTest < ActionController::TestCase
  def setup
    # Create test users and accounts
    @vendor_user = users(:one)
    @agency_user = users(:two)
    
    @vendor_account = accounts(:one)
    @vendor_account.update!(account_type: 'vendor', status: 'approved', confirmed_at: Time.current)
    
    # Create test products
    @published_product = products(:published_product)
    @published_product.update!(published: true, account: @vendor_account)
    
    @draft_product = products(:one)
    @draft_product.update!(published: false, account: @vendor_account)
  end

  # ===============================
  # NEW ACTION TESTS
  # ===============================

  test "should show new product claim form for published product" do
    get :new, params: { id: @published_product.id }
    assert_response :success
    assert_instance_of ProductClaim, assigns(:product_claim)
    assert assigns(:product_claim).new_record?
    assert_equal @published_product, assigns(:product)
  end

  test "should show new product claim form for unauthenticated users" do
    get :new, params: { id: @published_product.id }
    assert_response :success
    # Should be accessible to anyone
  end

  test "should handle nonexistent product" do
    assert_raises(ActiveRecord::RecordNotFound) do
      get :new, params: { id: "nonexistent" }
    end
  end

  test "should show form for draft product" do
    # Product claims should be available even for draft products
    get :new, params: { id: @draft_product.id }
    assert_response :success
    assert_equal @draft_product, assigns(:product)
  end

  # ===============================
  # CREATE ACTION TESTS
  # ===============================

  test "should create product claim with valid data" do
    claim_params = {
      name: "John Doe",
      email: "<EMAIL>",
      company: "Acme Corp",
      title: "CEO",
      message: "I am the owner of this product and would like to claim it."
    }
    
    assert_difference 'ProductClaim.count', 1 do
      post :create, params: { 
        id: @published_product.id, 
        product_claim: claim_params 
      }
    end
    
    assert_redirected_to marketplace_product_path(@published_product)
    assert_match /Thank you for your claim request/, flash[:notice]
    
    claim = ProductClaim.last
    assert_equal @published_product, claim.product
    assert_equal "John Doe", claim.name
    assert_equal "<EMAIL>", claim.email
    assert_equal "Acme Corp", claim.company
    assert_equal "CEO", claim.title
    assert_equal "I am the owner of this product and would like to claim it.", claim.message
  end

  test "should not create product claim with invalid data" do
    invalid_params = {
      name: "",  # Required field
      email: "invalid-email",  # Invalid format
      company: "",
      title: "",
      message: ""
    }
    
    assert_no_difference 'ProductClaim.count' do
      post :create, params: { 
        id: @published_product.id, 
        product_claim: invalid_params 
      }
    end
    
    assert_response :success  # Renders :new
    assert_not_nil assigns(:product_claim)
    assert assigns(:product_claim).errors.any?
  end

  test "should create claim with minimal required data" do
    minimal_params = {
      name: "Jane Smith",
      email: "<EMAIL>",
      company: "Smith Inc",
      title: "Owner",
      message: "This is my product."
    }
    
    assert_difference 'ProductClaim.count', 1 do
      post :create, params: { 
        id: @published_product.id, 
        product_claim: minimal_params 
      }
    end
    
    assert_redirected_to marketplace_product_path(@published_product)
    
    claim = ProductClaim.last
    assert_equal "Jane Smith", claim.name
    assert_equal "<EMAIL>", claim.email
  end

  test "should handle long message in claim" do
    long_message = "a" * 2000  # Very long message
    
    claim_params = {
      name: "Test User",
      email: "<EMAIL>",
      company: "Test Company",
      title: "Tester",
      message: long_message
    }
    
    post :create, params: { 
      id: @published_product.id, 
      product_claim: claim_params 
    }
    
    # Should either accept or reject based on validation rules
    if response.redirect?
      assert_redirected_to marketplace_product_path(@published_product)
      claim = ProductClaim.last
      assert_equal long_message, claim.message
    else
      assert_response :success  # Validation error
      assert assigns(:product_claim).errors.any?
    end
  end

  # ===============================
  # EMAIL VALIDATION TESTS
  # ===============================

  test "should validate email format" do
    invalid_emails = [
      "not-an-email",
      "@example.com",
      "test@",
      "<EMAIL>",
      "test@example"
    ]
    
    invalid_emails.each do |invalid_email|
      claim_params = {
        name: "Test User",
        email: invalid_email,
        company: "Test Company", 
        title: "Tester",
        message: "Test message"
      }
      
      assert_no_difference 'ProductClaim.count' do
        post :create, params: { 
          id: @published_product.id, 
          product_claim: claim_params 
        }
      end
      
      assert_response :success  # Should render form with errors
      assert assigns(:product_claim).errors[:email].any?, "Email #{invalid_email} should be invalid"
    end
  end

  test "should accept valid email formats" do
    valid_emails = [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>"
    ]
    
    valid_emails.each_with_index do |valid_email, index|
      claim_params = {
        name: "Test User #{index}",
        email: valid_email,
        company: "Test Company",
        title: "Tester",
        message: "Test message for #{valid_email}"
      }
      
      assert_difference 'ProductClaim.count', 1 do
        post :create, params: { 
          id: @published_product.id, 
          product_claim: claim_params 
        }
      end
      
      assert_redirected_to marketplace_product_path(@published_product)
    end
  end

  # ===============================
  # PARAMETER SECURITY TESTS
  # ===============================

  test "should filter unpermitted parameters" do
    claim_params = {
      name: "Test User",
      email: "<EMAIL>",
      company: "Test Company",
      title: "CEO",
      message: "Valid message",
      product_id: @draft_product.id,  # Should be filtered
      created_at: 1.year.ago,  # Should be filtered
      admin_approved: true  # Should be filtered
    }
    
    assert_difference 'ProductClaim.count', 1 do
      post :create, params: { 
        id: @published_product.id, 
        product_claim: claim_params 
      }
    end
    
    claim = ProductClaim.last
    assert_equal @published_product, claim.product  # Should use URL param, not filtered param
    assert_equal "Test User", claim.name
    assert_equal "<EMAIL>", claim.email
  end

  test "should allow only permitted parameters" do
    permitted_params = {
      name: "Permitted User",
      email: "<EMAIL>", 
      company: "Permitted Company",
      title: "Permitted Title",
      message: "Permitted message"
    }
    
    post :create, params: { 
      id: @published_product.id, 
      product_claim: permitted_params 
    }
    
    claim = ProductClaim.last
    assert_equal "Permitted User", claim.name
    assert_equal "<EMAIL>", claim.email
    assert_equal "Permitted Company", claim.company
    assert_equal "Permitted Title", claim.title
    assert_equal "Permitted message", claim.message
  end

  # ===============================
  # PRODUCT ASSOCIATION TESTS
  # ===============================

  test "should associate claim with correct product" do
    claim_params = {
      name: "Product Owner",
      email: "<EMAIL>",
      company: "Product Company",
      title: "Founder",
      message: "This is my product"
    }
    
    post :create, params: { 
      id: @published_product.id, 
      product_claim: claim_params 
    }
    
    claim = ProductClaim.last
    assert_equal @published_product, claim.product
    assert_not_equal @draft_product, claim.product
  end

  test "should handle claim for draft product" do
    claim_params = {
      name: "Draft Owner",
      email: "<EMAIL>",
      company: "Draft Company",
      title: "Owner",
      message: "I own this draft product"
    }
    
    assert_difference 'ProductClaim.count', 1 do
      post :create, params: { 
        id: @draft_product.id, 
        product_claim: claim_params 
      }
    end
    
    claim = ProductClaim.last
    assert_equal @draft_product, claim.product
  end

  # ===============================
  # ERROR HANDLING TESTS
  # ===============================

  test "should handle missing required fields gracefully" do
    incomplete_params = {
      name: "Incomplete User"
      # Missing email, company, title, message
    }
    
    assert_no_difference 'ProductClaim.count' do
      post :create, params: { 
        id: @published_product.id, 
        product_claim: incomplete_params 
      }
    end
    
    assert_response :success  # Should render form with errors
    assert_not_nil assigns(:product_claim)
    assert assigns(:product_claim).errors.any?
  end

  test "should handle empty parameters gracefully" do
    empty_params = {}
    
    assert_no_difference 'ProductClaim.count' do
      post :create, params: { 
        id: @published_product.id, 
        product_claim: empty_params 
      }
    end
    
    assert_response :success
    assert assigns(:product_claim).errors.any?
  end

  test "should preserve form data on validation errors" do
    invalid_params = {
      name: "Test User",
      email: "invalid-email",  # Will cause validation error
      company: "Test Company",
      title: "CEO",
      message: "Test message"
    }
    
    post :create, params: { 
      id: @published_product.id, 
      product_claim: invalid_params 
    }
    
    assert_response :success
    claim = assigns(:product_claim)
    assert_equal "Test User", claim.name
    assert_equal "invalid-email", claim.email
    assert_equal "Test Company", claim.company
    assert_equal "CEO", claim.title
    assert_equal "Test message", claim.message
  end

  # ===============================
  # SECURITY TESTS
  # ===============================

  test "should handle potential XSS in form fields" do
    xss_params = {
      name: "<script>alert('xss')</script>",
      email: "<EMAIL>",
      company: "<img src=x onerror=alert('xss')>",
      title: "<svg onload=alert('xss')>",
      message: "Normal message with <script>malicious code</script>"
    }
    
    assert_difference 'ProductClaim.count', 1 do
      post :create, params: { 
        id: @published_product.id, 
        product_claim: xss_params 
      }
    end
    
    claim = ProductClaim.last
    # Data should be stored as-is, but should be escaped when displayed
    assert_equal "<script>alert('xss')</script>", claim.name
    assert_equal "<img src=x onerror=alert('xss')>", claim.company
  end

  test "should handle very long field values" do
    long_params = {
      name: "a" * 1000,
      email: "<EMAIL>",
      company: "b" * 1000,
      title: "c" * 1000,
      message: "d" * 5000
    }
    
    post :create, params: { 
      id: @published_product.id, 
      product_claim: long_params 
    }
    
    # Should either accept or reject based on validation rules
    if response.redirect?
      assert_redirected_to marketplace_product_path(@published_product)
    else
      assert_response :success  # Validation errors
      assert assigns(:product_claim).errors.any?
    end
  end

  # ===============================
  # INTEGRATION TESTS
  # ===============================

  test "should complete full product claim workflow" do
    # Step 1: View product page (would normally have claim link)
    get :new, params: { id: @published_product.id }
    assert_response :success
    
    # Step 2: Fill out claim form
    claim_params = {
      name: "Workflow Test User",
      email: "<EMAIL>",
      company: "Workflow Test Corp",
      title: "Product Manager",
      message: "I am submitting a claim for this product as part of our verification process."
    }
    
    # Step 3: Submit claim
    assert_difference 'ProductClaim.count', 1 do
      post :create, params: { 
        id: @published_product.id, 
        product_claim: claim_params 
      }
    end
    
    # Step 4: Verify redirect and success message
    assert_redirected_to marketplace_product_path(@published_product)
    assert_match /Thank you for your claim request/, flash[:notice]
    
    # Step 5: Verify claim was created correctly
    claim = ProductClaim.last
    assert_equal @published_product, claim.product
    assert_equal "Workflow Test User", claim.name
    assert_equal "<EMAIL>", claim.email
    assert_equal "Workflow Test Corp", claim.company
  end

  test "should handle claim form with validation errors and resubmission" do
    # Step 1: Show form
    get :new, params: { id: @published_product.id }
    assert_response :success
    
    # Step 2: Submit invalid data
    invalid_params = {
      name: "",  # Invalid
      email: "invalid-email",  # Invalid
      company: "Test Company",
      title: "CEO",
      message: "Test message"
    }
    
    assert_no_difference 'ProductClaim.count' do
      post :create, params: { 
        id: @published_product.id, 
        product_claim: invalid_params 
      }
    end
    
    assert_response :success  # Should render form with errors
    assert assigns(:product_claim).errors.any?
    
    # Step 3: Fix errors and resubmit
    valid_params = {
      name: "Fixed User",
      email: "<EMAIL>",
      company: "Test Company",
      title: "CEO",
      message: "Test message"
    }
    
    assert_difference 'ProductClaim.count', 1 do
      post :create, params: { 
        id: @published_product.id, 
        product_claim: valid_params 
      }
    end
    
    assert_redirected_to marketplace_product_path(@published_product)
    assert_match /Thank you for your claim request/, flash[:notice]
  end

  private

  def sign_in(user)
    session_record = user.sessions.create!(
      user_agent: 'Test User Agent',
      ip_address: '127.0.0.1'
    )
    Current.session = session_record
    cookies.signed[:session_id] = session_record.id
  end
end