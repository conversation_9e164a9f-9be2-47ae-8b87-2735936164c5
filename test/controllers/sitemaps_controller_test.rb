require "test_helper"

class SitemapsControllerTest < ActionController::TestCase
  def setup
    @published_product = products(:published_product)
    @draft_product = products(:draft_product)
    @agency_product = products(:agency_product)
    
    # Ensure products are set up correctly
    @published_product.update!(published: true)
    @draft_product.update!(published: false)
    @agency_product.update!(published: true)
  end

  # ===============================
  # INDEX ACTION TESTS (XML SITEMAP)
  # ===============================

  test "should allow unauthenticated access to sitemap" do
    get :index, format: :xml
    assert_response :success
    assert_nil session[:return_to_after_authenticating]
  end

  test "should get sitemap index" do
    get :index, format: :xml
    assert_response :success
  end

  test "should respond to XML format only" do
    get :index, format: :xml
    assert_response :success
    assert_equal "application/xml; charset=utf-8", response.content_type
  end

  test "should set static pages instance variable" do
    get :index, format: :xml
    
    static_pages = assigns(:static_pages)
    assert_not_nil static_pages
    assert static_pages.is_a?(Array)
    assert static_pages.any?
    
    # Check structure of static pages
    static_pages.each do |page|
      assert page.key?(:url)
      assert page.key?(:changefreq)
      assert page.key?(:priority)
    end
  end

  test "should include expected static pages" do
    get :index, format: :xml
    
    static_pages = assigns(:static_pages)
    urls = static_pages.map { |page| page[:url] }
    
    assert_includes urls, root_url
    assert_includes urls, marketplace_url
    assert_includes urls, marketplace_products_url
  end

  test "should set products instance variable with published products only" do
    get :index, format: :xml
    
    products = assigns(:products)
    assert_not_nil products
    
    # Should only include published products
    published_products = products.select(&:published?)
    assert_equal products.count, published_products.count
    
    # Should include our published products
    product_ids = products.map(&:id)
    assert_includes product_ids, @published_product.id
    assert_includes product_ids, @agency_product.id
    
    # Should not include draft products
    assert_not_includes product_ids, @draft_product.id
  end

  test "should set companies instance variable" do
    get :index, format: :xml
    
    companies = assigns(:companies)
    assert_not_nil companies
    assert companies.is_a?(Array)
    # Should be empty array since no Company model exists
    assert_equal 0, companies.count
  end

  test "should include account and categories associations" do
    get :index, format: :xml
    
    products = assigns(:products)
    
    # Test that associations are loaded (no additional queries)
    assert_no_queries do
      products.each do |product|
        product.account&.name
        product.categories.to_a
      end
    end
  end

  test "should have proper priorities for static pages" do
    get :index, format: :xml
    
    static_pages = assigns(:static_pages)
    root_page = static_pages.find { |page| page[:url] == root_url }
    marketplace_page = static_pages.find { |page| page[:url] == marketplace_url }
    products_page = static_pages.find { |page| page[:url] == marketplace_products_url }
    
    assert_equal 1.0, root_page[:priority]
    assert_equal 0.9, marketplace_page[:priority]
    assert_equal 0.8, products_page[:priority]
  end

  test "should have proper changefreq for static pages" do
    get :index, format: :xml
    
    static_pages = assigns(:static_pages)
    
    static_pages.each do |page|
      assert_equal 'daily', page[:changefreq]
    end
  end

  # ===============================
  # ROBOTS ACTION TESTS
  # ===============================

  test "should allow unauthenticated access to robots" do
    get :robots, format: :txt
    assert_response :success
    assert_nil session[:return_to_after_authenticating]
  end

  test "should get robots.txt" do
    get :robots, format: :txt
    assert_response :success
  end

  test "should respond to TXT format" do
    get :robots, format: :txt
    assert_response :success
    assert_equal "text/plain; charset=utf-8", response.content_type
  end

  test "robots.txt should contain proper directives" do
    get :robots, format: :txt
    
    robots_content = response.body
    
    # Should allow all user agents
    assert_match /User-agent: \*/, robots_content
    assert_match /Allow: \//, robots_content
    
    # Should disallow admin areas
    assert_match /Disallow: \/admin\//, robots_content
    assert_match /Disallow: \/dashboard\//, robots_content
    
    # Should include sitemap location
    assert_match /Sitemap: .*\/sitemap\.xml/, robots_content
  end

  test "robots.txt should be properly formatted" do
    get :robots, format: :txt
    
    robots_content = response.body
    lines = robots_content.strip.split("\n")
    
    # Should not be empty
    assert lines.any?
    
    # Should not have trailing whitespace
    lines.each do |line|
      assert_equal line, line.rstrip, "Line should not have trailing whitespace: '#{line}'"
    end
  end

  # ===============================
  # PERFORMANCE TESTS
  # ===============================

  test "should handle large number of products efficiently" do
    # Create additional products
    account = accounts(:one)
    50.times do |i|
      Product.create!(
        account: account,
        name: "Test Product #{i}",
        slug: "test-product-#{i}",
        description: "Test description",
        published: true
      )
    end

    # Should handle the request without timing out
    assert_nothing_raised do
      get :index, format: :xml
    end
    
    assert_response :success
    
    # Should include the additional products
    products = assigns(:products)
    assert products.count >= 50
  end

  # ===============================
  # ERROR HANDLING TESTS
  # ===============================

  test "should handle missing products gracefully" do
    # Remove all products
    Product.destroy_all
    
    get :index, format: :xml
    assert_response :success
    
    products = assigns(:products)
    assert_equal 0, products.count
  end

  test "should handle missing accounts gracefully" do
    # This tests the includes(:account) part
    get :index, format: :xml
    assert_response :success
  end

  # ===============================
  # ROUTING TESTS
  # ===============================

  test "should route to sitemap index" do
    assert_routing({ method: :get, path: "/sitemap.xml" }, 
                  { controller: "sitemaps", action: "index", format: "xml" })
  end

  test "should route to robots.txt" do
    assert_routing({ method: :get, path: "/robots.txt" }, 
                  { controller: "sitemaps", action: "robots", format: "txt" })
  end

  # ===============================
  # CACHING TESTS
  # ===============================

  test "should set appropriate cache headers for sitemap" do
    get :index, format: :xml
    
    # Should allow caching
    assert_not_nil response.headers["Cache-Control"]
  end

  test "should set appropriate cache headers for robots" do
    get :robots, format: :txt
    
    # Should allow caching
    assert_not_nil response.headers["Cache-Control"]
  end
end