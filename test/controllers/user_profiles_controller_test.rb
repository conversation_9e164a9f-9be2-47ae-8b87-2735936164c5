require "test_helper"

class UserProfilesControllerTest < ActionController::TestCase
  def setup
    # Create test users
    @user = users(:one)
    @other_user = users(:two)
    @admin_user = users(:admin)
    
    # Set up accounts to avoid orphaned user redirects
    @account = accounts(:one)
    @account.update!(
      account_type: 'vendor', 
      status: 'approved', 
      confirmed_at: Time.current,
      owner: @user
    )
    
    # Clear existing account users to avoid duplicates
    @account.account_users.destroy_all
    @account.account_users.create!(user: @user, role: 'admin')
    
    @admin_user.update!(super_admin: true)
  end

  # ===============================
  # AUTHENTICATION TESTS
  # ===============================

  test "should require authentication for all actions" do
    get :show
    assert_redirected_to new_session_path
    
    get :edit
    assert_redirected_to new_session_path
    
    patch :update, params: { user: { first_name: "Test" } }
    assert_redirected_to new_session_path
    
    patch :update_password, params: { user: { password: "newpass", password_confirmation: "newpass" } }
    assert_redirected_to new_session_path
    
    delete :remove_profile_photo
    assert_redirected_to new_session_path
  end

  test "should use dashboard layout" do
    sign_in @user
    get :show
    assert_response :success
  end

  # ===============================
  # SHOW ACTION TESTS
  # ===============================

  test "should show user profile" do
    sign_in @user
    
    get :show
    assert_response :success
    assert_equal @user, assigns(:user)
  end

  test "should show current user's profile only" do
    sign_in @user
    
    get :show
    assert_response :success
    assert_equal @user, assigns(:user)
    assert_not_equal @other_user, assigns(:user)
  end

  test "should display user information on profile" do
    sign_in @user
    
    get :show
    assert_response :success
    assert_not_nil assigns(:user)
    assert_equal @user.first_name, assigns(:user).first_name
    assert_equal @user.last_name, assigns(:user).last_name
    assert_equal @user.email_address, assigns(:user).email_address
  end

  # ===============================
  # EDIT ACTION TESTS
  # ===============================

  test "should show edit profile page" do
    sign_in @user
    
    get :edit
    assert_response :success
    assert_equal @user, assigns(:user)
  end

  test "should load user data for editing" do
    sign_in @user
    
    get :edit
    assert_response :success
    assert_not_nil assigns(:user)
    assert_equal @user.first_name, assigns(:user).first_name
    assert_equal @user.email_address, assigns(:user).email_address
    assert_equal @user.job_title, assigns(:user).job_title
  end

  # ===============================
  # UPDATE PROFILE TESTS
  # ===============================

  test "should update user profile" do
    sign_in @user
    
    new_first_name = "UpdatedFirst"
    new_last_name = "UpdatedLast"
    new_job_title = "Chief Executive Officer"
    new_phone = "555-9999"
    
    patch :update, params: {
      user: {
        first_name: new_first_name,
        last_name: new_last_name,
        job_title: new_job_title,
        phone: new_phone
      }
    }
    
    assert_redirected_to user_profile_path
    assert_match /Profile updated successfully/, flash[:notice]
    
    @user.reload
    assert_equal new_first_name, @user.first_name
    assert_equal new_last_name, @user.last_name
    assert_equal new_job_title, @user.job_title
    assert_equal new_phone, @user.phone
  end

  test "should update email address" do
    sign_in @user
    
    new_email = "<EMAIL>"
    
    patch :update, params: {
      user: {
        first_name: @user.first_name,
        email_address: new_email
      }
    }
    
    assert_redirected_to user_profile_path
    assert_match /Profile updated successfully/, flash[:notice]
    
    @user.reload
    assert_equal new_email, @user.email_address
  end

  test "should not update profile with invalid data" do
    sign_in @user
    
    original_first_name = @user.first_name
    original_email = @user.email_address
    
    patch :update, params: {
      user: {
        first_name: "",  # Invalid - required field
        email_address: "invalid-email"  # Invalid format
      }
    }
    
    assert_response :unprocessable_entity
    assert_not_nil assigns(:user)
    
    @user.reload
    assert_equal original_first_name, @user.first_name  # Should not have changed
    assert_equal original_email, @user.email_address  # Should not have changed
  end

  test "should handle update with blank optional fields" do
    sign_in @user
    
    patch :update, params: {
      user: {
        first_name: "Valid Name",
        last_name: "Valid Last",
        email_address: @user.email_address,
        phone: "",  # Optional field
        job_title: ""  # Optional field
      }
    }
    
    assert_redirected_to user_profile_path
    assert_match /Profile updated successfully/, flash[:notice]
    
    @user.reload
    assert_equal "Valid Name", @user.first_name
    assert_equal "Valid Last", @user.last_name
  end

  # ===============================
  # UPDATE PASSWORD TESTS
  # ===============================

  test "should update password" do
    sign_in @user
    
    new_password = "newpassword123"
    
    patch :update_password, params: {
      user: {
        password: new_password,
        password_confirmation: new_password
      }
    }
    
    assert_redirected_to user_profile_path
    assert_match /Password updated successfully/, flash[:notice]
    
    @user.reload
    assert @user.authenticate(new_password)
  end

  test "should not update with blank password" do
    sign_in @user
    
    patch :update_password, params: {
      user: {
        password: "",
        password_confirmation: ""
      }
    }
    
    assert_redirected_to edit_user_profile_path
    assert_match /Password cannot be blank/, flash[:alert]
    
    # Should still authenticate with old password
    @user.reload
    assert @user.authenticate("password")  # Default fixture password
  end

  test "should not update with mismatched password confirmation" do
    sign_in @user
    
    patch :update_password, params: {
      user: {
        password: "newpassword123",
        password_confirmation: "differentpassword"
      }
    }
    
    assert_response :unprocessable_entity
    
    # Should still authenticate with old password
    @user.reload
    assert @user.authenticate("password")  # Default fixture password
  end

  test "should not update with weak password" do
    sign_in @user
    
    weak_password = "123"
    
    patch :update_password, params: {
      user: {
        password: weak_password,
        password_confirmation: weak_password
      }
    }
    
    assert_response :unprocessable_entity
    assert_not_nil assigns(:user)
    
    # Should still authenticate with old password
    @user.reload
    assert @user.authenticate("password")  # Default fixture password
  end

  # ===============================
  # PROFILE PHOTO TESTS
  # ===============================

  test "should remove profile photo when photo attached" do
    sign_in @user
    
    # First attach a photo (simulate)
    @user.profile_photo.attach(
      io: StringIO.new("fake image data"),
      filename: "profile.jpg",
      content_type: "image/jpeg"
    )
    
    assert @user.profile_photo.attached?
    
    delete :remove_profile_photo
    
    assert_redirected_to user_profile_path
    assert_match /Profile photo removed successfully/, flash[:notice]
    
    @user.reload
    assert_not @user.profile_photo.attached?
  end

  test "should handle remove photo when no photo attached" do
    sign_in @user
    
    assert_not @user.profile_photo.attached?
    
    delete :remove_profile_photo
    
    assert_redirected_to user_profile_path
    assert_match /Profile photo removed successfully/, flash[:notice]
  end

  # ===============================
  # PARAMETER SECURITY TESTS
  # ===============================

  test "should filter unpermitted parameters in profile update" do
    sign_in @user
    
    original_created_at = @user.created_at
    
    patch :update, params: {
      user: {
        first_name: "Updated Name",
        super_admin: true,  # Should be filtered
        created_at: 1.year.ago,  # Should be filtered
        id: @other_user.id  # Should be filtered
      }
    }
    
    @user.reload
    assert_equal "Updated Name", @user.first_name
    assert_not @user.super_admin  # Should not change
    assert_equal original_created_at.to_i, @user.created_at.to_i  # Should not change
    assert_not_equal @other_user.id, @user.id  # Should not change
  end

  test "should allow only permitted profile parameters" do
    sign_in @user
    
    patch :update, params: {
      user: {
        first_name: "New First",
        last_name: "New Last",
        email_address: "<EMAIL>",
        phone: "555-1234",
        job_title: "New Title"
      }
    }
    
    assert_redirected_to user_profile_path
    
    @user.reload
    assert_equal "New First", @user.first_name
    assert_equal "New Last", @user.last_name
    assert_equal "<EMAIL>", @user.email_address
    assert_equal "555-1234", @user.phone
    assert_equal "New Title", @user.job_title
  end

  test "should allow only permitted password parameters" do
    sign_in @user
    
    patch :update_password, params: {
      user: {
        password: "newpassword123",
        password_confirmation: "newpassword123",
        first_name: "Hacked Name"  # Should be filtered in password update
      }
    }
    
    assert_redirected_to user_profile_path
    
    @user.reload
    assert @user.authenticate("newpassword123")
    assert_not_equal "Hacked Name", @user.first_name  # Should not change
  end

  # ===============================
  # ERROR HANDLING TESTS
  # ===============================

  test "should handle concurrent profile updates" do
    sign_in @user
    
    # Simulate concurrent update by modifying user in between
    @user.update!(first_name: "Concurrent Update")
    
    patch :update, params: {
      user: {
        first_name: "My Update",
        last_name: "Updated Last"
      }
    }
    
    assert_redirected_to user_profile_path
    
    @user.reload
    assert_equal "My Update", @user.first_name  # Should use the controller update
    assert_equal "Updated Last", @user.last_name
  end

  test "should handle validation errors gracefully" do
    sign_in @user
    
    patch :update, params: {
      user: {
        first_name: "",  # Invalid
        email_address: "invalid"  # Invalid
      }
    }
    
    assert_response :unprocessable_entity
    assert_not_nil assigns(:user)
    assert assigns(:user).errors.any?
  end

  test "should handle very long field values" do
    sign_in @user
    
    long_name = "a" * 300  # Very long name
    
    patch :update, params: {
      user: {
        first_name: long_name,
        last_name: "Valid Last",
        email_address: @user.email_address
      }
    }
    
    # Should either accept or gracefully handle based on validation rules
    assert_response :redirect  # or :unprocessable_entity based on validation
  end

  # ===============================
  # INTEGRATION TESTS
  # ===============================

  test "should complete full profile update workflow" do
    sign_in @user
    
    # Step 1: View current profile
    get :show
    assert_response :success
    
    # Step 2: Go to edit page
    get :edit
    assert_response :success
    
    # Step 3: Update profile information
    patch :update, params: {
      user: {
        first_name: "Workflow",
        last_name: "Test",
        job_title: "Test Engineer",
        phone: "555-WORKFLOW"
      }
    }
    
    assert_redirected_to user_profile_path
    
    # Step 4: Verify changes were saved
    @user.reload
    assert_equal "Workflow", @user.first_name
    assert_equal "Test", @user.last_name
    assert_equal "Test Engineer", @user.job_title
    assert_equal "555-WORKFLOW", @user.phone
    
    # Step 5: View updated profile
    get :show
    assert_response :success
  end

  test "should complete password change workflow" do
    sign_in @user
    
    old_password = "password"  # Default fixture password
    new_password = "secure_new_password_123"
    
    # Step 1: Verify old password works
    assert @user.authenticate(old_password)
    
    # Step 2: Go to edit page
    get :edit
    assert_response :success
    
    # Step 3: Change password
    patch :update_password, params: {
      user: {
        password: new_password,
        password_confirmation: new_password
      }
    }
    
    assert_redirected_to user_profile_path
    assert_match /Password updated successfully/, flash[:notice]
    
    # Step 4: Verify password was changed
    @user.reload
    assert_not @user.authenticate(old_password)
    assert @user.authenticate(new_password)
    
    # Step 5: View profile to confirm success
    get :show
    assert_response :success
  end

  test "should handle edit and update with validation errors" do
    sign_in @user
    
    # Step 1: Go to edit page
    get :edit
    assert_response :success
    
    # Step 2: Submit invalid data
    patch :update, params: {
      user: {
        first_name: "",  # Invalid
        email_address: "invalid"  # Invalid
      }
    }
    
    assert_response :unprocessable_entity
    assert_not_nil assigns(:user)
    
    # Step 3: Fix the errors and resubmit
    patch :update, params: {
      user: {
        first_name: "Fixed Name",
        last_name: "Fixed Last",
        email_address: "<EMAIL>"
      }
    }
    
    assert_redirected_to user_profile_path
    assert_match /Profile updated successfully/, flash[:notice]
  end

  # ===============================
  # USER ISOLATION TESTS
  # ===============================

  test "should only access current user's profile" do
    sign_in @user
    
    get :show
    assert_response :success
    assert_equal @user, assigns(:user)
    
    get :edit
    assert_response :success
    assert_equal @user, assigns(:user)
    
    # Controller should always use current_user, not allow access to other users
    assert_not_equal @other_user, assigns(:user)
    assert_not_equal @admin_user, assigns(:user)
  end

  test "should not be able to update other users' profiles" do
    sign_in @user
    
    original_other_name = @other_user.first_name
    
    # Try to update profile (should only affect current user)
    patch :update, params: {
      user: {
        first_name: "Hacked Name"
      }
    }
    
    # Should update current user, not other user
    @user.reload
    @other_user.reload
    
    assert_equal "Hacked Name", @user.first_name
    assert_equal original_other_name, @other_user.first_name
  end

  private

  def sign_in(user)
    session_record = user.sessions.create!(
      user_agent: 'Test User Agent',
      ip_address: '127.0.0.1'
    )
    Current.session = session_record
    cookies.signed[:session_id] = session_record.id
  end
end