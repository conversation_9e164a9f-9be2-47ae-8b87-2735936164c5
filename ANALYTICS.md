# Product Analytics Feature

## Overview

The Product Analytics feature tracks visitor behavior on product pages, providing insights into page views, unique visitors, user demographics, and geographic data. The system is designed to be privacy-conscious while collecting actionable analytics data.

## What Was Implemented

### 1. Database Schema

Created `product_analytics` table with the following structure:

```ruby
create_table :product_analytics do |t|
  t.references :product, null: false, foreign_key: true
  t.references :user, null: true, foreign_key: true      # Optional for anonymous users
  t.string :event_type, null: false                      # 'page_view' or 'unique_view'
  t.string :ip_address, null: false
  t.text :user_agent, null: false
  t.string :visitor_id, null: false                      # Privacy-conscious identifier
  t.json :metadata                                        # Browser, device, location data
  t.timestamps
end
```

**Indexes for Performance:**
- `[:product_id, :event_type]` - Fast analytics queries
- `[:visitor_id, :product_id]` - Unique visitor tracking (with uniqueness constraint)
- `:created_at` - Time-based analytics

### 2. Models & Associations

#### ProductAnalytic Model
- **Associations**: `belongs_to :product`, `belongs_to :user, optional: true`
- **Validations**: Ensures required fields and valid event types
- **Scopes**: Pre-built queries for common analytics needs

#### Product Model
- Added `has_many :product_analytics, dependent: :destroy`

### 3. Analytics Service (`ProductAnalyticsService`)

Core service handling data collection and processing:

```ruby
class ProductAnalyticsService
  # Main tracking method
  def self.track_page_view(product, request, user = nil)
  
  # Privacy-conscious visitor identification
  def self.generate_visitor_id(request)
  
  # User agent parsing (browser, OS, device)
  def self.parse_user_agent(user_agent_string)
  
  # IP geolocation
  def self.get_location_from_ip(ip_address)
end
```

### 4. Background Processing (`AnalyticsJob`)

Asynchronous job for processing analytics data:
- Parses user agent information
- Performs IP geolocation lookups
- Stores enriched analytics records
- Handles duplicate unique view attempts gracefully

### 5. Controller Integration

Automatic tracking integrated into `MarketplaceController#product`:

```ruby
def product
  # ... existing code ...
  
  # Track analytics for this product view
  ProductAnalyticsService.track_page_view(@product, request, current_user)
  
  # ... rest of method ...
end
```

## Features Available

### Core Analytics
- **Page Views**: Every product page visit is tracked
- **Unique Visitors**: First-time visitors per product (daily uniqueness reset)
- **User Attribution**: Links analytics to logged-in users when available
- **Anonymous Tracking**: Tracks visitors without requiring authentication

### Device & Browser Detection
- Browser name and version
- Operating system information
- Device type (mobile, desktop, tablet)
- Device brand and model

### Location Tracking
- Country and country code
- State/region information
- City-level location data
- Timezone information

### Privacy Features
- **Hashed Visitor IDs**: Uses SHA256 hash of IP + User Agent + daily salt
- **No Persistent Cookies**: Visitor identification resets daily
- **Local IP Filtering**: Skips location lookup for local/private IP addresses

## Dependencies

The following gems were added to support analytics:

```ruby
# Analytics gems
gem "device_detector", "~> 1.1"    # User agent parsing
gem "geocoder", "~> 1.8"           # IP geolocation
```

## Usage Examples

### Basic Analytics Queries

```ruby
product = Product.find(1)

# Total page views for a product
ProductAnalytic.total_page_views_for_product(product)
# => 1547

# Unique visitors for a product
ProductAnalytic.unique_visitors_for_product(product)
# => 892

# Analytics summary with time periods
ProductAnalytic.analytics_summary_for_product(product, :today)
# => { total_page_views: 23, unique_visitors: 18, period: :today }

ProductAnalytic.analytics_summary_for_product(product, :week)
# => { total_page_views: 156, unique_visitors: 124, period: :week }

ProductAnalytic.analytics_summary_for_product(product, :month)
# => { total_page_views: 612, unique_visitors: 445, period: :month }
```

### Advanced Queries

```ruby
# Get analytics for specific time periods
ProductAnalytic.for_product(product).today
ProductAnalytic.for_product(product).this_week
ProductAnalytic.for_product(product).this_month
ProductAnalytic.for_product(product).in_date_range(1.week.ago, Date.current)

# Filter by event type
ProductAnalytic.for_product(product).page_views
ProductAnalytic.for_product(product).unique_views

# Device and location analysis
analytics = ProductAnalytic.for_product(product).includes(:product)

# Mobile vs Desktop breakdown
mobile_visitors = analytics.select(&:mobile_device?).count
desktop_visitors = analytics.select(&:desktop_device?).count

# Geographic distribution
countries = analytics.map { |a| a.location_info['country'] }.compact.tally
browsers = analytics.map { |a| a.browser_info['browser'] }.compact.tally
```

### Product-Level Analytics

```ruby
# Access analytics through product association
product.product_analytics.count                    # Total analytics records
product.product_analytics.page_views.count         # Total page views
product.product_analytics.unique_views.count       # Total unique visitors
product.product_analytics.today.count              # Today's analytics
```

### Individual Analytics Record

```ruby
analytic = ProductAnalytic.first

# Browser information
analytic.browser_info
# => { "browser" => "Chrome", "browser_version" => "91.0.4472.124", "os" => "macOS" }

# Location information
analytic.location_info
# => { "country" => "United States", "region" => "California", "city" => "San Francisco" }

# Device checks
analytic.mobile_device?     # => false
analytic.desktop_device?    # => true
```

## Database Queries for Reporting

### Popular Products
```ruby
# Most viewed products (last 30 days)
popular_products = Product.joins(:product_analytics)
                          .where(product_analytics: { created_at: 30.days.ago.. })
                          .group('products.id')
                          .order('COUNT(product_analytics.id) DESC')
                          .limit(10)
```

### Traffic Analytics
```ruby
# Daily page views for last week
daily_views = ProductAnalytic.where(created_at: 7.days.ago..)
                            .group_by_day(:created_at)
                            .count

# Unique visitors by country
country_visitors = ProductAnalytic.where(created_at: 30.days.ago..)
                                 .where.not("metadata->>'country'" => nil)
                                 .group("metadata->>'country'")
                                 .count
```

## Performance Considerations

### Database Optimization
- **Indexes**: Strategic indexes on commonly queried fields
- **Background Processing**: Analytics processing happens asynchronously
- **JSON Metadata**: Efficient storage of variable analytics data

### Privacy & GDPR Compliance
- **Daily Visitor ID Reset**: Visitor identification resets every 24 hours
- **No Personal Data**: Only technical information is stored
- **IP Hash Option**: Consider hashing IP addresses for additional privacy

### Scaling Considerations
- **Partitioning**: Consider partitioning by date for high-volume applications
- **Archival**: Implement data retention policies for old analytics
- **Caching**: Cache frequently accessed analytics summaries

## Configuration Options

### Geocoding Service
The system uses the `geocoder` gem with default settings. To configure:

```ruby
# config/initializers/geocoder.rb
Geocoder.configure(
  timeout: 3,                   # Timeout for geocoding requests
  lookup: :maxmind_geoip2,     # Use MaxMind for better accuracy
  # Add API keys if using commercial services
)
```

### Job Queue
Analytics jobs run on the default queue. To prioritize:

```ruby
# In AnalyticsJob
queue_as :analytics  # Use dedicated analytics queue
```

## Monitoring & Alerts

Consider setting up monitoring for:
- Failed analytics jobs
- Unusual traffic spikes
- Geocoding API rate limits
- Database performance on analytics queries

## Future Enhancements

Potential additions to consider:
- **Session Tracking**: Track user sessions across multiple page views
- **Referrer Tracking**: Track traffic sources and referrers
- **Event Tracking**: Track specific user actions (downloads, form submissions)
- **Real-time Dashboard**: Live analytics dashboard for admins
- **Export Features**: CSV/PDF export of analytics data
- **A/B Testing**: Integration with feature flags for testing