# Summary

Date : 2025-07-18 08:38:57

Directory /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market

Total : 340 files,  47356 codes, 2640 comments, 20219 blanks, all 70215 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| HTML | 7 | 19,571 | 52 | 17,122 | 36,745 |
| erb | 116 | 12,570 | 19 | 1,028 | 13,617 |
| Ruby | 166 | 6,702 | 1,740 | 1,484 | 9,926 |
| JSON | 2 | 3,472 | 0 | 1 | 3,473 |
| JavaScript | 12 | 3,355 | 368 | 194 | 3,917 |
| Markdown | 7 | 679 | 0 | 179 | 858 |
| YAML | 24 | 501 | 419 | 118 | 1,038 |
| PostCSS | 4 | 472 | 21 | 71 | 564 |
| Docker | 1 | 31 | 21 | 21 | 73 |
| XML | 1 | 3 | 0 | 1 | 4 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 340 | 47,356 | 2,640 | 20,219 | 70,215 |
| . (Files) | 8 | 298 | 61 | 116 | 475 |
| .claude | 1 | 39 | 0 | 0 | 39 |
| .github | 2 | 161 | 0 | 30 | 191 |
| .github (Files) | 1 | 12 | 0 | 1 | 13 |
| .github/workflows | 1 | 149 | 0 | 29 | 178 |
| app | 203 | 16,741 | 608 | 1,925 | 19,274 |
| app/assets | 3 | 471 | 21 | 70 | 562 |
| app/assets/stylesheets | 2 | 384 | 18 | 50 | 452 |
| app/assets/tailwind | 1 | 87 | 3 | 20 | 110 |
| app/channels | 1 | 14 | 0 | 3 | 17 |
| app/channels/application_cable | 1 | 14 | 0 | 3 | 17 |
| app/controllers | 38 | 2,129 | 130 | 471 | 2,730 |
| app/controllers (Files) | 12 | 664 | 68 | 162 | 894 |
| app/controllers/accounts | 2 | 126 | 1 | 28 | 155 |
| app/controllers/admin | 11 | 672 | 24 | 132 | 828 |
| app/controllers/agencies | 3 | 67 | 1 | 12 | 80 |
| app/controllers/concerns | 3 | 255 | 17 | 51 | 323 |
| app/controllers/vendors | 7 | 345 | 19 | 86 | 450 |
| app/helpers | 7 | 194 | 2 | 26 | 222 |
| app/javascript | 9 | 396 | 30 | 76 | 502 |
| app/javascript (Files) | 1 | 4 | 1 | 2 | 7 |
| app/javascript/controllers | 8 | 392 | 29 | 74 | 495 |
| app/jobs | 2 | 79 | 11 | 21 | 111 |
| app/mailers | 7 | 185 | 0 | 32 | 217 |
| app/models | 17 | 571 | 361 | 175 | 1,107 |
| app/models (Files) | 16 | 537 | 352 | 165 | 1,054 |
| app/models/concerns | 1 | 34 | 9 | 10 | 53 |
| app/services | 1 | 71 | 0 | 6 | 77 |
| app/validators | 1 | 61 | 8 | 16 | 85 |
| app/views | 117 | 12,570 | 45 | 1,029 | 13,644 |
| app/views/account_confirmation_mailer | 2 | 37 | 0 | 15 | 52 |
| app/views/account_confirmations | 1 | 150 | 0 | 7 | 157 |
| app/views/accounts | 5 | 1,007 | 0 | 98 | 1,105 |
| app/views/accounts (Files) | 2 | 533 | 0 | 53 | 586 |
| app/views/accounts/team_invitations | 1 | 79 | 0 | 7 | 86 |
| app/views/accounts/team_members | 2 | 395 | 0 | 38 | 433 |
| app/views/active_storage | 1 | 13 | 0 | 2 | 15 |
| app/views/active_storage/blobs | 1 | 13 | 0 | 2 | 15 |
| app/views/admin | 35 | 3,423 | 0 | 234 | 3,657 |
| app/views/admin/accounts | 3 | 499 | 0 | 28 | 527 |
| app/views/admin/case_studies | 5 | 398 | 0 | 30 | 428 |
| app/views/admin/categories | 5 | 348 | 0 | 20 | 368 |
| app/views/admin/certifications | 5 | 307 | 0 | 15 | 322 |
| app/views/admin/email_previews | 1 | 76 | 0 | 5 | 81 |
| app/views/admin/overview | 1 | 273 | 0 | 18 | 291 |
| app/views/admin/product_contents | 5 | 454 | 0 | 38 | 492 |
| app/views/admin/product_videos | 5 | 389 | 0 | 35 | 424 |
| app/views/admin/products | 5 | 679 | 0 | 45 | 724 |
| app/views/admin_mailer | 1 | 69 | 0 | 11 | 80 |
| app/views/agencies | 2 | 335 | 0 | 16 | 351 |
| app/views/agencies/dashboard | 1 | 197 | 0 | 11 | 208 |
| app/views/agencies/interests | 1 | 138 | 0 | 5 | 143 |
| app/views/errors | 3 | 144 | 0 | 20 | 164 |
| app/views/home | 1 | 46 | 0 | 1 | 47 |
| app/views/invitation_acceptances | 1 | 264 | 0 | 18 | 282 |
| app/views/layouts | 5 | 422 | 6 | 51 | 479 |
| app/views/layouts (Files) | 4 | 419 | 6 | 50 | 475 |
| app/views/layouts/action_text | 1 | 3 | 0 | 1 | 4 |
| app/views/layouts/action_text/contents | 1 | 3 | 0 | 1 | 4 |
| app/views/lead_mailer | 2 | 150 | 0 | 21 | 171 |
| app/views/marketplace | 10 | 2,325 | 0 | 162 | 2,487 |
| app/views/pages | 4 | 530 | 0 | 54 | 584 |
| app/views/passwords | 2 | 173 | 0 | 18 | 191 |
| app/views/passwords_mailer | 2 | 20 | 0 | 10 | 30 |
| app/views/pwa | 2 | 22 | 26 | 2 | 50 |
| app/views/registrations | 1 | 173 | 0 | 18 | 191 |
| app/views/sessions | 1 | 88 | 0 | 11 | 99 |
| app/views/shared | 5 | 447 | 13 | 24 | 484 |
| app/views/sitemaps | 1 | 31 | 0 | 4 | 35 |
| app/views/team_invitation_mailer | 2 | 86 | 0 | 21 | 107 |
| app/views/user_mailer | 5 | 226 | 0 | 54 | 280 |
| app/views/vendors | 23 | 2,389 | 0 | 157 | 2,546 |
| app/views/vendors/case_studies | 5 | 404 | 0 | 32 | 436 |
| app/views/vendors/leads | 2 | 556 | 0 | 32 | 588 |
| app/views/vendors/overview | 1 | 120 | 0 | 3 | 123 |
| app/views/vendors/product_contents | 5 | 452 | 0 | 34 | 486 |
| app/views/vendors/product_videos | 5 | 390 | 0 | 31 | 421 |
| app/views/vendors/products | 5 | 467 | 0 | 25 | 492 |
| config | 25 | 342 | 473 | 175 | 990 |
| config (Files) | 13 | 253 | 188 | 85 | 526 |
| config/environments | 3 | 68 | 96 | 65 | 229 |
| config/initializers | 8 | 19 | 161 | 23 | 203 |
| config/locales | 1 | 2 | 28 | 2 | 32 |
| coverage | 4 | 22,312 | 0 | 16,974 | 39,286 |
| coverage (Files) | 2 | 22,304 | 0 | 16,973 | 39,277 |
| coverage/assets | 2 | 8 | 0 | 1 | 9 |
| coverage/assets/0.13.1 | 2 | 8 | 0 | 1 | 9 |
| db | 14 | 1,246 | 105 | 135 | 1,486 |
| db (Files) | 5 | 935 | 79 | 77 | 1,091 |
| db/migrate | 9 | 311 | 26 | 58 | 395 |
| lib | 1 | 4 | 2 | 3 | 9 |
| lib/tasks | 1 | 4 | 2 | 3 | 9 |
| plans | 6 | 809 | 47 | 125 | 981 |
| public | 6 | 428 | 5 | 146 | 579 |
| spec | 39 | 1,247 | 460 | 294 | 2,001 |
| spec (Files) | 2 | 34 | 125 | 13 | 172 |
| spec/factories | 8 | 168 | 154 | 35 | 357 |
| spec/helpers | 4 | 16 | 40 | 8 | 64 |
| spec/mailers | 5 | 57 | 2 | 12 | 71 |
| spec/mailers (Files) | 2 | 8 | 0 | 4 | 12 |
| spec/mailers/previews | 3 | 49 | 2 | 8 | 59 |
| spec/models | 6 | 553 | 135 | 121 | 809 |
| spec/requests | 6 | 192 | 2 | 50 | 244 |
| spec/requests (Files) | 5 | 84 | 1 | 22 | 107 |
| spec/requests/government | 1 | 108 | 1 | 28 | 137 |
| spec/support | 4 | 81 | 1 | 11 | 93 |
| spec/system | 1 | 134 | 1 | 38 | 173 |
| spec/system/government | 1 | 134 | 1 | 38 | 173 |
| spec/views | 3 | 12 | 0 | 6 | 18 |
| spec/views/pages | 1 | 4 | 0 | 2 | 6 |
| spec/views/registrations | 2 | 8 | 0 | 4 | 12 |
| test | 30 | 777 | 567 | 179 | 1,523 |
| test (Files) | 2 | 13 | 3 | 6 | 22 |
| test/fixtures | 12 | 165 | 265 | 39 | 469 |
| test/fixtures (Files) | 11 | 165 | 261 | 38 | 464 |
| test/fixtures/action_text | 1 | 0 | 4 | 1 | 5 |
| test/integration | 1 | 67 | 2 | 13 | 82 |
| test/mailers | 4 | 197 | 2 | 35 | 234 |
| test/mailers/previews | 4 | 197 | 2 | 35 | 234 |
| test/models | 11 | 335 | 295 | 86 | 716 |
| vendor | 1 | 2,952 | 312 | 117 | 3,381 |
| vendor/javascript | 1 | 2,952 | 312 | 117 | 3,381 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)