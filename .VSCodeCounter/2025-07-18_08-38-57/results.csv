"filename", "language", "Ruby", "YAML", "HTML", "XML", "JSON", "JavaScript", "Markdown", "PostCSS", "Docker", "erb", "comment", "blank", "total"
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/.annotaterb.yml", "YAML", 0, 63, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 64
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/.claude/settings.local.json", "JSON", 0, 0, 0, 0, 39, 0, 0, 0, 0, 0, 0, 0, 39
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/.github/dependabot.yml", "YAML", 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 13
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/.github/workflows/ci.yml", "YAML", 0, 149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 178
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/.rubocop.yml", "YAML", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 6, 2, 9
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/Dockerfile", "Docker", 0, 0, 0, 0, 0, 0, 0, 0, 31, 0, 21, 21, 73
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/Gemfile", "Ruby", 53, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 27, 111
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/README.md", "Markdown", 0, 0, 0, 0, 0, 0, 13, 0, 0, 0, 0, 12, 25
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/Rakefile", "Ruby", 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 7
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/assets/stylesheets/actiontext.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 384, 0, 0, 8, 49, 441
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/assets/stylesheets/application.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 1, 11
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/assets/tailwind/application.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 87, 0, 0, 3, 20, 110
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/channels/application_cable/connection.rb", "Ruby", 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 17
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/account_confirmations_controller.rb", "Ruby", 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 10, 44
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/accounts/team_invitations_controller.rb", "Ruby", 60, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 14, 75
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/accounts/team_members_controller.rb", "Ruby", 66, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 80
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/accounts_controller.rb", "Ruby", 72, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 92
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/accounts_controller.rb", "Ruby", 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 15, 68
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/base_controller.rb", "Ruby", 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 8
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/case_studies_controller.rb", "Ruby", 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 62
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/categories_controller.rb", "Ruby", 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 60
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/certifications_controller.rb", "Ruby", 50, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 13, 64
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/email_previews_controller.rb", "Ruby", 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 11, 116
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/health_controller.rb", "Ruby", 98, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 9, 109
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/overview_controller.rb", "Ruby", 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 15, 121
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/product_contents_controller.rb", "Ruby", 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 62
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/product_videos_controller.rb", "Ruby", 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 62
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/products_controller.rb", "Ruby", 73, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 18, 96
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/agencies/base_controller.rb", "Ruby", 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 16
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/agencies/dashboard_controller.rb", "Ruby", 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 19
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/agencies/interests_controller.rb", "Ruby", 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 7, 45
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/application_controller.rb", "Ruby", 43, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 15, 60
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/concerns/authentication.rb", "Ruby", 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 13, 69
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/concerns/error_handling.rb", "Ruby", 123, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 21, 151
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/concerns/security_headers.rb", "Ruby", 78, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 17, 103
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/errors_controller.rb", "Ruby", 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 36
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/home_controller.rb", "Ruby", 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/invitation_acceptances_controller.rb", "Ruby", 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 14, 113
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/marketplace_controller.rb", "Ruby", 227, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 58, 320
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/pages_controller.rb", "Ruby", 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 20
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/passwords_controller.rb", "Ruby", 26, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 33
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/registrations_controller.rb", "Ruby", 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 12, 62
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/sessions_controller.rb", "Ruby", 53, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 8, 70
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/sitemaps_controller.rb", "Ruby", 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 7, 39
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/base_controller.rb", "Ruby", 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 16
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/case_studies_controller.rb", "Ruby", 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 20, 93
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/leads_controller.rb", "Ruby", 52, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 10, 65
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/overview_controller.rb", "Ruby", 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/product_contents_controller.rb", "Ruby", 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 20, 93
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/product_videos_controller.rb", "Ruby", 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 20, 93
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/products_controller.rb", "Ruby", 58, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 13, 72
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/account_confirmations_helper.rb", "Ruby", 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/application_helper.rb", "Ruby", 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/caching_helper.rb", "Ruby", 70, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 10, 82
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/invitation_acceptances_helper.rb", "Ruby", 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/pages_helper.rb", "Ruby", 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/registrations_helper.rb", "Ruby", 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/seo_helper.rb", "Ruby", 113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 124
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/application.js", "JavaScript", 0, 0, 0, 0, 0, 4, 0, 0, 0, 0, 1, 2, 7
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/application.js", "JavaScript", 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 1, 4, 10
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/dark_mode_controller.js", "JavaScript", 0, 0, 0, 0, 0, 42, 0, 0, 0, 0, 1, 8, 51
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/delete_resource_controller.js", "JavaScript", 0, 0, 0, 0, 0, 26, 0, 0, 0, 0, 3, 5, 34
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/hello_controller.js", "JavaScript", 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 0, 2, 8
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/index.js", "JavaScript", 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 1, 1, 5
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/search_controller.js", "JavaScript", 0, 0, 0, 0, 0, 230, 0, 0, 0, 0, 14, 40, 284
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/sortable_controller.js", "JavaScript", 0, 0, 0, 0, 0, 54, 0, 0, 0, 0, 4, 7, 65
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/team_management_controller.js", "JavaScript", 0, 0, 0, 0, 0, 26, 0, 0, 0, 0, 5, 7, 38
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/jobs/application_job.rb", "Ruby", 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 2, 8
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/jobs/video_compression_job.rb", "Ruby", 77, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 19, 103
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/account_confirmation_mailer.rb", "Ruby", 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/admin_mailer.rb", "Ruby", 60, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 71
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/application_mailer.rb", "Ruby", 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/lead_mailer.rb", "Ruby", 40, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 45
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/passwords_mailer.rb", "Ruby", 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 8
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/team_invitation_mailer.rb", "Ruby", 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 15
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/user_mailer.rb", "Ruby", 51, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 61
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/account.rb", "Ruby", 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 27, 165
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/account_user.rb", "Ruby", 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 6, 43
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/application_record.rb", "Ruby", 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/case_study.rb", "Ruby", 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 8, 54
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/category.rb", "Ruby", 61, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 19, 102
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/certification.rb", "Ruby", 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 4, 29
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/concerns/performance_optimizable.rb", "Ruby", 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 10, 53
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/current.rb", "Ruby", 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/lead.rb", "Ruby", 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 4, 52
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product.rb", "Ruby", 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 24, 143
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product_category.rb", "Ruby", 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 2, 28
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product_certification.rb", "Ruby", 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 2, 28
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product_content.rb", "Ruby", 57, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 17, 99
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product_video.rb", "Ruby", 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 13, 79
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/session.rb", "Ruby", 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 1, 23
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/team_invitation.rb", "Ruby", 37, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 11, 76
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/user.rb", "Ruby", 73, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 25, 124
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/services/security_logger.rb", "Ruby", 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 77
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/validators/password_strength_validator.rb", "Ruby", 61, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 16, 85
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/account_confirmation_mailer/confirmation_email.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 0, 8, 36
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/account_confirmation_mailer/confirmation_email.text.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 7, 16
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/account_confirmations/show.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 150, 0, 7, 157
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/accounts/edit.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 356, 0, 42, 398
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/accounts/show.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 177, 0, 11, 188
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/accounts/team_invitations/new.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 79, 0, 7, 86
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/accounts/team_members/edit.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 68, 0, 5, 73
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/accounts/team_members/index.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 327, 0, 33, 360
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/active_storage/blobs/_blob.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 0, 2, 15
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/accounts/edit.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 152, 0, 10, 162
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/accounts/index.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 156, 0, 10, 166
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/accounts/show.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 191, 0, 8, 199
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/case_studies/_form.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 132, 0, 11, 143
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/case_studies/edit.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 0, 1, 24
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/case_studies/index.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 102, 0, 6, 108
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/case_studies/new.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 1, 23
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/case_studies/show.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 119, 0, 11, 130
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/categories/_form.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 0, 3, 55
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/categories/edit.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 0, 4, 31
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/categories/index.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 131, 0, 6, 137
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/categories/new.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 3, 24
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/categories/show.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 117, 0, 4, 121
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/certifications/_form.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 49, 0, 3, 52
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/certifications/edit.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 2, 12
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/certifications/index.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 124, 0, 5, 129
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/certifications/new.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 0, 2, 12
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/certifications/show.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 114, 0, 3, 117
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/email_previews/index.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 76, 0, 5, 81
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/overview/index.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 273, 0, 18, 291
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_contents/_form.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 146, 0, 12, 158
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_contents/edit.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 0, 4, 32
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_contents/index.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 102, 0, 6, 108
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_contents/new.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 3, 25
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_contents/show.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 156, 0, 13, 169
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_videos/_form.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 133, 0, 11, 144
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_videos/edit.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 0, 4, 32
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_videos/index.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 102, 0, 6, 108
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_videos/new.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 3, 25
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_videos/show.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 104, 0, 11, 115
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/products/_form.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 101, 0, 9, 110
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/products/edit.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 0, 3, 38
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/products/index.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 151, 0, 10, 161
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/products/new.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 0, 2, 31
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/products/show.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 363, 0, 21, 384
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin_mailer/new_user_signup.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 69, 0, 11, 80
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/agencies/dashboard/index.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 197, 0, 11, 208
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/agencies/interests/index.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 138, 0, 5, 143
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/errors/internal_server_error.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 58, 0, 7, 65
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/errors/not_found.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 0, 6, 44
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/errors/unprocessable_entity.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 0, 7, 55
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/home/<USER>", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 0, 1, 47
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/invitation_acceptances/show.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 264, 0, 18, 282
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/layouts/action_text/contents/_content.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 1, 4
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/layouts/application.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 77, 3, 16, 96
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/layouts/dashboard.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 329, 3, 31, 363
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/layouts/mailer.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 2, 14
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/layouts/mailer.text.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 2
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/lead_mailer/lead_status_update.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 77, 0, 10, 87
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/lead_mailer/new_lead_notification.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 73, 0, 11, 84
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/case_study.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 195, 0, 18, 213
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/categories.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 160, 0, 9, 169
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/companies.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 107, 0, 5, 112
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/company.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 183, 0, 16, 199
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/index.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 271, 0, 8, 279
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/product.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 608, 0, 37, 645
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/product_content.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 242, 0, 21, 263
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/product_video.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 183, 0, 17, 200
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/products.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 229, 0, 20, 249
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/search.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 147, 0, 11, 158
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/pages/landing.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 239, 0, 15, 254
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/pages/pending.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 188, 0, 13, 201
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/pages/privacy_policy.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 66, 0, 14, 80
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/pages/terms_of_service.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 0, 12, 49
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/passwords/edit.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 89, 0, 9, 98
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/passwords/new.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 84, 0, 9, 93
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/passwords_mailer/reset.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 0, 9, 27
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/passwords_mailer/reset.text.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 1, 3
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/pwa/manifest.json.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 0, 1, 23
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/pwa/service-worker.js", "JavaScript", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 1, 27
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/registrations/new.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 173, 0, 18, 191
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/sessions/new.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 88, 0, 11, 99
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/shared/_dashboard_sidebar_nav.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 117, 0, 0, 117
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/shared/_delete_resource.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 73, 13, 10, 96
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/shared/_pagination.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 3
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/shared/_public_footer.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 82, 0, 2, 84
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/shared/_public_nav.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 172, 0, 12, 184
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/sitemaps/index.xml.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 0, 4, 35
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/team_invitation_mailer/invitation_email.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 57, 0, 10, 67
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/team_invitation_mailer/invitation_email.text.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 0, 11, 40
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/user_mailer/account_approved.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 0, 9, 53
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/user_mailer/account_rejected.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 0, 10, 60
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/user_mailer/password_reset.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 0, 10, 58
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/user_mailer/welcome.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 0, 13, 72
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/user_mailer/welcome.text.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 0, 12, 37
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/case_studies/_form.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 135, 0, 11, 146
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/case_studies/edit.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 0, 3, 30
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/case_studies/index.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 102, 0, 6, 108
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/case_studies/new.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 1, 22
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/case_studies/show.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 119, 0, 11, 130
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/leads/index.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 264, 0, 11, 275
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/leads/show.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 292, 0, 21, 313
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/overview/index.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 0, 3, 123
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_contents/_form.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 146, 0, 12, 158
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_contents/edit.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 0, 2, 29
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_contents/index.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 102, 0, 6, 108
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_contents/new.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 1, 22
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_contents/show.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 156, 0, 13, 169
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_videos/_form.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 136, 0, 11, 147
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_videos/edit.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 0, 2, 29
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_videos/index.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 102, 0, 6, 108
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_videos/new.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 1, 22
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_videos/show.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 104, 0, 11, 115
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/products/_form.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 100, 0, 7, 107
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/products/edit.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 0, 2, 23
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/products/index.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 98, 0, 2, 100
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/products/new.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 2, 13
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/products/show.html.erb", "erb", 0, 0, 0, 0, 0, 0, 0, 0, 0, 237, 0, 12, 249
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config.ru", "Ruby", 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3, 7
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/application.rb", "Ruby", 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 7, 31
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/boot.rb", "Ruby", 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 5
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/cable.yml", "YAML", 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 4, 3, 18
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/cache.yml", "YAML", 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 2, 4, 17
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/database.yml", "YAML", 0, 39, 0, 0, 0, 0, 0, 0, 0, 0, 11, 6, 56
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/deploy.yml", "YAML", 0, 27, 0, 0, 0, 0, 0, 0, 0, 0, 70, 20, 117
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/environment.rb", "Ruby", 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 6
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/environments/development.rb", "Ruby", 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 24, 81
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/environments/production.rb", "Ruby", 24, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 26, 94
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/environments/test.rb", "Ruby", 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 15, 54
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/importmap.rb", "Ruby", 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2, 11
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/assets.rb", "Ruby", 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 3, 8
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/content_security_policy.rb", "Ruby", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 3, 26
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/filter_parameter_logging.rb", "Ruby", 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 2, 9
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/friendly_id.rb", "Ruby", 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 97, 6, 108
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/inflections.rb", "Ruby", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 3, 17
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/mission_control_jobs.rb", "Ruby", 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 4, 20
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/pagy.rb", "Ruby", 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 7
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/sentry.rb", "Ruby", 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 8
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/locales/en.yml", "YAML", 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 28, 2, 32
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/puma.rb", "Ruby", 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 5, 42
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/queue.yml", "YAML", 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 19
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/recurring.yml", "YAML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 1, 11
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/routes.rb", "Ruby", 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 23, 158
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/storage.yml", "YAML", 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 23, 6, 35
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/coverage/.resultset.json", "JSON", 0, 0, 0, 0, 3433, 0, 0, 0, 0, 0, 0, 1, 3434
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/coverage/assets/0.13.1/application.css", "PostCSS", 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 2
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/coverage/assets/0.13.1/application.js", "JavaScript", 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 7
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/coverage/index.html", "HTML", 0, 0, 18871, 0, 0, 0, 0, 0, 0, 0, 0, 16972, 35843
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/data-schema.md", "Markdown", 0, 0, 0, 0, 0, 0, 132, 0, 0, 0, 0, 47, 179
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/cable_schema.rb", "Ruby", 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 2, 24
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/cache_schema.rb", "Ruby", 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 2, 25
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250710131740_create_initial_database.rb", "Ruby", 136, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 25, 174
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250710131857_create_active_storage_tables.active_storage.rb", "Ruby", 46, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 10, 58
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250710131910_create_action_text_tables.action_text.rb", "Ruby", 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 5, 27
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250716000001_add_parent_id_to_categories.rb", "Ruby", 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 8
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250716000002_create_product_categories.rb", "Ruby", 10, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 13
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250716173201_add_description_to_categories.rb", "Ruby", 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 6
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250718120910_migrate_company_profile_to_account.rb", "Ruby", 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 87
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250718123410_create_certifications.rb", "Ruby", 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 11
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250718123443_create_product_certifications.rb", "Ruby", 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 11
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/queue_schema.rb", "Ruby", 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 13, 142
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/schema.rb", "Ruby", 238, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 21, 270
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/seeds.rb", "Ruby", 556, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 39, 630
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/lib/tasks/annotate_rb.rake", "Ruby", 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 9
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/plans/categories.md", "Markdown", 0, 0, 0, 0, 0, 0, 50, 0, 0, 0, 0, 12, 62
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/plans/first.md", "Markdown", 0, 0, 0, 0, 0, 0, 439, 0, 0, 0, 0, 95, 534
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/plans/logins.md", "Markdown", 0, 0, 0, 0, 0, 0, 6, 0, 0, 0, 0, 1, 7
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/plans/phase-5.md", "Markdown", 0, 0, 0, 0, 0, 0, 20, 0, 0, 0, 0, 12, 32
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/plans/select.md", "Markdown", 0, 0, 0, 0, 0, 0, 19, 0, 0, 0, 0, 0, 19
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/plans/sidebar_nav.html", "HTML", 0, 0, 275, 0, 0, 0, 0, 0, 0, 0, 47, 5, 327
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/public/400.html", "HTML", 0, 0, 85, 0, 0, 0, 0, 0, 0, 0, 1, 29, 115
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/public/404.html", "HTML", 0, 0, 85, 0, 0, 0, 0, 0, 0, 0, 1, 29, 115
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/public/406-unsupported-browser.html", "HTML", 0, 0, 85, 0, 0, 0, 0, 0, 0, 0, 1, 29, 115
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/public/422.html", "HTML", 0, 0, 85, 0, 0, 0, 0, 0, 0, 0, 1, 29, 115
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/public/500.html", "HTML", 0, 0, 85, 0, 0, 0, 0, 0, 0, 0, 1, 29, 115
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/public/icon.svg", "XML", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/account_users.rb", "Ruby", 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 4, 41
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/accounts.rb", "Ruby", 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 8, 77
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/categories.rb", "Ruby", 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 7, 57
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/certifications.rb", "Ruby", 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 1, 17
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/interests.rb", "Ruby", 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 25
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/product_certifications.rb", "Ruby", 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 1, 27
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/products.rb", "Ruby", 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 6, 67
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/users.rb", "Ruby", 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 5, 46
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/helpers/account_confirmations_helper_spec.rb", "Ruby", 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 2, 16
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/helpers/invitation_acceptances_helper_spec.rb", "Ruby", 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 2, 16
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/helpers/pages_helper_spec.rb", "Ruby", 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 2, 16
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/helpers/registrations_helper_spec.rb", "Ruby", 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 2, 16
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/mailers/account_confirmation_mailer_spec.rb", "Ruby", 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 6
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/mailers/previews/account_confirmation_mailer_preview.rb", "Ruby", 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 18
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/mailers/previews/team_invitation_mailer_preview.rb", "Ruby", 18, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 4, 23
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/mailers/previews/user_mailer_preview.rb", "Ruby", 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 18
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/mailers/team_invitation_mailer_spec.rb", "Ruby", 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 6
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/models/account_spec.rb", "Ruby", 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 26, 195
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/models/category_spec.rb", "Ruby", 109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 24, 155
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/models/certification_spec.rb", "Ruby", 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 2, 16
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/models/product_certification_spec.rb", "Ruby", 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 2, 26
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/models/product_spec.rb", "Ruby", 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 33, 229
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/models/user_spec.rb", "Ruby", 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 34, 188
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/rails_helper.rb", "Ruby", 25, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 9, 77
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/requests/account_confirmations_spec.rb", "Ruby", 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 8
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/requests/authentication_spec.rb", "Ruby", 48, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 11, 60
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/requests/government/marketplace_spec.rb", "Ruby", 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 28, 137
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/requests/invitation_acceptances_spec.rb", "Ruby", 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 8
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/requests/pages_spec.rb", "Ruby", 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 12
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/requests/registrations_spec.rb", "Ruby", 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 19
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/spec_helper.rb", "Ruby", 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 82, 4, 95
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/support/authentication_helpers.rb", "Ruby", 36, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 42
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/support/shared_examples.rb", "Ruby", 27, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 31
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/support/shoulda_matchers.rb", "Ruby", 6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/support/vcr.rb", "Ruby", 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 14
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/system/government/marketplace_browsing_spec.rb", "Ruby", 134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 38, 173
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/views/pages/landing.html.tailwindcss_spec.rb", "Ruby", 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 6
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/views/registrations/create.html.tailwindcss_spec.rb", "Ruby", 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 6
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/views/registrations/new.html.tailwindcss_spec.rb", "Ruby", 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 6
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/application_system_test_case.rb", "Ruby", 4, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 6
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/account_users.yml", "YAML", 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 23, 3, 36
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/accounts.yml", "YAML", 0, 45, 0, 0, 0, 0, 0, 0, 0, 0, 37, 7, 89
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/action_text/rich_texts.yml", "YAML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 1, 5
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/case_studies.yml", "YAML", 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 22, 3, 37
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/categories.yml", "YAML", 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 21, 3, 30
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/leads.yml", "YAML", 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 37, 3, 52
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/product_categories.yml", "YAML", 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 22, 3, 31
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/product_media.yml", "YAML", 0, 10, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3, 14
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/product_videos.yml", "YAML", 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 22, 3, 37
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/products.yml", "YAML", 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 27, 3, 44
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/team_invitations.yml", "YAML", 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 29, 3, 50
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/users.yml", "YAML", 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 20, 4, 44
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/integration/json_features_test.rb", "Ruby", 67, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 13, 82
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/mailers/previews/admin_preview.rb", "Ruby", 57, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 66
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/mailers/previews/lead_preview.rb", "Ruby", 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 14, 85
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/mailers/previews/passwords_mailer_preview.rb", "Ruby", 17, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 20
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/mailers/previews/user_preview.rb", "Ruby", 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 63
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/account_test.rb", "Ruby", 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 20, 137
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/account_user_test.rb", "Ruby", 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 2, 30
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/case_study_test.rb", "Ruby", 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 2, 29
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/category_test.rb", "Ruby", 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 2, 28
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/lead_test.rb", "Ruby", 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 2, 44
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/product_category_test.rb", "Ruby", 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 2, 29
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/product_content_test.rb", "Ruby", 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 2, 29
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/product_test.rb", "Ruby", 91, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 21, 138
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/product_video_test.rb", "Ruby", 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 2, 29
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/team_invitation_test.rb", "Ruby", 66, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 14, 108
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/user_test.rb", "Ruby", 76, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 17, 115
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/test_helper.rb", "Ruby", 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 4, 16
"/Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/vendor/javascript/sortablejs.js", "JavaScript", 0, 0, 0, 0, 0, 2952, 0, 0, 0, 0, 312, 117, 3381
"Total", "-", 6702, 501, 19571, 3, 3472, 3355, 679, 472, 31, 12570, 2640, 20219, 70215