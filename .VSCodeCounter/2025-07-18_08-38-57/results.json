{"file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/Gemfile": {"language": "<PERSON>", "code": 53, "comment": 31, "blank": 27}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/.github/dependabot.yml": {"language": "YAML", "code": 12, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/public/422.html": {"language": "HTML", "code": 85, "comment": 1, "blank": 29}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/schema.rb": {"language": "<PERSON>", "code": 238, "comment": 11, "blank": 21}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/seeds.rb": {"language": "<PERSON>", "code": 556, "comment": 35, "blank": 39}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/.rubocop.yml": {"language": "YAML", "code": 1, "comment": 6, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/public/404.html": {"language": "HTML", "code": 85, "comment": 1, "blank": 29}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/cable_schema.rb": {"language": "<PERSON>", "code": 11, "comment": 11, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/cache_schema.rb": {"language": "<PERSON>", "code": 12, "comment": 11, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/public/406-unsupported-browser.html": {"language": "HTML", "code": 85, "comment": 1, "blank": 29}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/queue_schema.rb": {"language": "<PERSON>", "code": 118, "comment": 11, "blank": 13}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/public/icon.svg": {"language": "XML", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/coverage/.resultset.json": {"language": "JSON", "code": 3433, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/public/500.html": {"language": "HTML", "code": 85, "comment": 1, "blank": 29}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/.github/workflows/ci.yml": {"language": "YAML", "code": 149, "comment": 0, "blank": 29}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250718123410_create_certifications.rb": {"language": "<PERSON>", "code": 9, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/vendor/javascript/sortablejs.js": {"language": "JavaScript", "code": 2952, "comment": 312, "blank": 117}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250710131910_create_action_text_tables.action_text.rb": {"language": "<PERSON>", "code": 20, "comment": 2, "blank": 5}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250716000001_add_parent_id_to_categories.rb": {"language": "<PERSON>", "code": 7, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/public/400.html": {"language": "HTML", "code": 85, "comment": 1, "blank": 29}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250710131857_create_active_storage_tables.active_storage.rb": {"language": "<PERSON>", "code": 46, "comment": 2, "blank": 10}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250716000002_create_product_categories.rb": {"language": "<PERSON>", "code": 10, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250718123443_create_product_certifications.rb": {"language": "<PERSON>", "code": 9, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250718120910_migrate_company_profile_to_account.rb": {"language": "<PERSON>", "code": 69, "comment": 9, "blank": 9}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250710131740_create_initial_database.rb": {"language": "<PERSON>", "code": 136, "comment": 13, "blank": 25}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/README.md": {"language": "<PERSON><PERSON>", "code": 13, "comment": 0, "blank": 12}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/data-schema.md": {"language": "<PERSON><PERSON>", "code": 132, "comment": 0, "blank": 47}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/Rakefile": {"language": "<PERSON>", "code": 2, "comment": 2, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/coverage/assets/0.13.1/application.js": {"language": "JavaScript", "code": 7, "comment": 0, "blank": 0}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/lib/tasks/annotate_rb.rake": {"language": "<PERSON>", "code": 4, "comment": 2, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/.annotaterb.yml": {"language": "YAML", "code": 63, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/.claude/settings.local.json": {"language": "JSON", "code": 39, "comment": 0, "blank": 0}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/rails_helper.rb": {"language": "<PERSON>", "code": 25, "comment": 43, "blank": 9}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/spec_helper.rb": {"language": "<PERSON>", "code": 9, "comment": 82, "blank": 4}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/coverage/assets/0.13.1/application.css": {"language": "PostCSS", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/helpers/account_confirmations_helper_spec.rb": {"language": "<PERSON>", "code": 4, "comment": 10, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/helpers/pages_helper_spec.rb": {"language": "<PERSON>", "code": 4, "comment": 10, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/helpers/registrations_helper_spec.rb": {"language": "<PERSON>", "code": 4, "comment": 10, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/helpers/invitation_acceptances_helper_spec.rb": {"language": "<PERSON>", "code": 4, "comment": 10, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/views/registrations/create.html.tailwindcss_spec.rb": {"language": "<PERSON>", "code": 4, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/certifications.rb": {"language": "<PERSON>", "code": 6, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/views/pages/landing.html.tailwindcss_spec.rb": {"language": "<PERSON>", "code": 4, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250716173201_add_description_to_categories.rb": {"language": "<PERSON>", "code": 5, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/views/registrations/new.html.tailwindcss_spec.rb": {"language": "<PERSON>", "code": 4, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/product_certifications.rb": {"language": "<PERSON>", "code": 6, "comment": 20, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/users.rb": {"language": "<PERSON>", "code": 21, "comment": 20, "blank": 5}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/accounts.rb": {"language": "<PERSON>", "code": 33, "comment": 36, "blank": 8}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/account_users.rb": {"language": "<PERSON>", "code": 15, "comment": 22, "blank": 4}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/interests.rb": {"language": "<PERSON>", "code": 22, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/support/shared_examples.rb": {"language": "<PERSON>", "code": 27, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/support/shoulda_matchers.rb": {"language": "<PERSON>", "code": 6, "comment": 0, "blank": 0}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/support/authentication_helpers.rb": {"language": "<PERSON>", "code": 36, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/support/vcr.rb": {"language": "<PERSON>", "code": 12, "comment": 1, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/requests/account_confirmations_spec.rb": {"language": "<PERSON>", "code": 6, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/requests/invitation_acceptances_spec.rb": {"language": "<PERSON>", "code": 6, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/requests/authentication_spec.rb": {"language": "<PERSON>", "code": 48, "comment": 1, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/requests/pages_spec.rb": {"language": "<PERSON>", "code": 9, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/models/category_spec.rb": {"language": "<PERSON>", "code": 109, "comment": 22, "blank": 24}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/requests/registrations_spec.rb": {"language": "<PERSON>", "code": 15, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/models/certification_spec.rb": {"language": "<PERSON>", "code": 4, "comment": 10, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/categories.rb": {"language": "<PERSON>", "code": 30, "comment": 20, "blank": 7}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/models/account_spec.rb": {"language": "<PERSON>", "code": 133, "comment": 36, "blank": 26}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/products.rb": {"language": "<PERSON>", "code": 35, "comment": 26, "blank": 6}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/system/government/marketplace_browsing_spec.rb": {"language": "<PERSON>", "code": 134, "comment": 1, "blank": 38}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/models/product_certification_spec.rb": {"language": "<PERSON>", "code": 4, "comment": 20, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/mailers/account_confirmation_mailer_spec.rb": {"language": "<PERSON>", "code": 4, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/models/product_spec.rb": {"language": "<PERSON>", "code": 170, "comment": 26, "blank": 33}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/models/user_spec.rb": {"language": "<PERSON>", "code": 133, "comment": 21, "blank": 34}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/mailers/team_invitation_mailer_spec.rb": {"language": "<PERSON>", "code": 4, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/requests/government/marketplace_spec.rb": {"language": "<PERSON>", "code": 108, "comment": 1, "blank": 28}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/mailers/previews/account_confirmation_mailer_preview.rb": {"language": "<PERSON>", "code": 15, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/mailers/previews/user_mailer_preview.rb": {"language": "<PERSON>", "code": 16, "comment": 1, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config.ru": {"language": "<PERSON>", "code": 3, "comment": 1, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/mailers/previews/team_invitation_mailer_preview.rb": {"language": "<PERSON>", "code": 18, "comment": 1, "blank": 4}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/plans/phase-5.md": {"language": "<PERSON><PERSON>", "code": 20, "comment": 0, "blank": 12}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/plans/categories.md": {"language": "<PERSON><PERSON>", "code": 50, "comment": 0, "blank": 12}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/plans/sidebar_nav.html": {"language": "HTML", "code": 275, "comment": 47, "blank": 5}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/plans/select.md": {"language": "<PERSON><PERSON>", "code": 19, "comment": 0, "blank": 0}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/inflections.rb": {"language": "<PERSON>", "code": 0, "comment": 14, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/friendly_id.rb": {"language": "<PERSON>", "code": 5, "comment": 97, "blank": 6}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/pagy.rb": {"language": "<PERSON>", "code": 4, "comment": 2, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/Dockerfile": {"language": "<PERSON>er", "code": 31, "comment": 21, "blank": 21}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/content_security_policy.rb": {"language": "<PERSON>", "code": 0, "comment": 23, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/assets.rb": {"language": "<PERSON>", "code": 1, "comment": 4, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/filter_parameter_logging.rb": {"language": "<PERSON>", "code": 3, "comment": 4, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/mission_control_jobs.rb": {"language": "<PERSON>", "code": 1, "comment": 15, "blank": 4}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/sentry.rb": {"language": "<PERSON>", "code": 5, "comment": 2, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/mailers/previews/admin_preview.rb": {"language": "<PERSON>", "code": 57, "comment": 0, "blank": 9}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/services/security_logger.rb": {"language": "<PERSON>", "code": 71, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/mailers/previews/user_preview.rb": {"language": "<PERSON>", "code": 54, "comment": 0, "blank": 9}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/test_helper.rb": {"language": "<PERSON>", "code": 9, "comment": 3, "blank": 4}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/application.js": {"language": "JavaScript", "code": 4, "comment": 1, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/sortable_controller.js": {"language": "JavaScript", "code": 54, "comment": 4, "blank": 7}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/mailers/previews/lead_preview.rb": {"language": "<PERSON>", "code": 69, "comment": 2, "blank": 14}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/mailers/previews/passwords_mailer_preview.rb": {"language": "<PERSON>", "code": 17, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/team_management_controller.js": {"language": "JavaScript", "code": 26, "comment": 5, "blank": 7}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/index.js": {"language": "JavaScript", "code": 3, "comment": 1, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/application_helper.rb": {"language": "<PERSON>", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/delete_resource_controller.js": {"language": "JavaScript", "code": 26, "comment": 3, "blank": 5}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/seo_helper.rb": {"language": "<PERSON>", "code": 113, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/dark_mode_controller.js": {"language": "JavaScript", "code": 42, "comment": 1, "blank": 8}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/caching_helper.rb": {"language": "<PERSON>", "code": 70, "comment": 2, "blank": 10}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/channels/application_cable/connection.rb": {"language": "<PERSON>", "code": 14, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/application.js": {"language": "JavaScript", "code": 5, "comment": 1, "blank": 4}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/search_controller.js": {"language": "JavaScript", "code": 230, "comment": 14, "blank": 40}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/pages_helper.rb": {"language": "<PERSON>", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/marketplace_controller.rb": {"language": "<PERSON>", "code": 227, "comment": 35, "blank": 58}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/registrations_controller.rb": {"language": "<PERSON>", "code": 44, "comment": 6, "blank": 12}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/assets/tailwind/application.css": {"language": "PostCSS", "code": 87, "comment": 3, "blank": 20}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/accounts_controller.rb": {"language": "<PERSON>", "code": 72, "comment": 0, "blank": 20}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/invitation_acceptances_controller.rb": {"language": "<PERSON>", "code": 87, "comment": 12, "blank": 14}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/account_confirmations_controller.rb": {"language": "<PERSON>", "code": 32, "comment": 2, "blank": 10}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/assets/stylesheets/application.css": {"language": "PostCSS", "code": 0, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/assets/stylesheets/actiontext.css": {"language": "PostCSS", "code": 384, "comment": 8, "blank": 49}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/errors_controller.rb": {"language": "<PERSON>", "code": 30, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/concerns/security_headers.rb": {"language": "<PERSON>", "code": 78, "comment": 8, "blank": 17}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/errors/unprocessable_entity.html.erb": {"language": "erb", "code": 48, "comment": 0, "blank": 7}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/concerns/error_handling.rb": {"language": "<PERSON>", "code": 123, "comment": 7, "blank": 21}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/errors/not_found.html.erb": {"language": "erb", "code": 38, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/errors/internal_server_error.html.erb": {"language": "erb", "code": 58, "comment": 0, "blank": 7}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/concerns/authentication.rb": {"language": "<PERSON>", "code": 54, "comment": 2, "blank": 13}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/home_controller.rb": {"language": "<PERSON>", "code": 5, "comment": 0, "blank": 0}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/agencies/dashboard_controller.rb": {"language": "<PERSON>", "code": 17, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/hello_controller.js": {"language": "JavaScript", "code": 6, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/agencies/interests_controller.rb": {"language": "<PERSON>", "code": 37, "comment": 1, "blank": 7}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/user_mailer/welcome.html.erb": {"language": "erb", "code": 59, "comment": 0, "blank": 13}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/user_mailer/account_rejected.html.erb": {"language": "erb", "code": 50, "comment": 0, "blank": 10}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/account_confirmations/show.html.erb": {"language": "erb", "code": 150, "comment": 0, "blank": 7}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/user_mailer/account_approved.html.erb": {"language": "erb", "code": 44, "comment": 0, "blank": 9}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/user_mailer/welcome.text.erb": {"language": "erb", "code": 25, "comment": 0, "blank": 12}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/sessions_controller.rb": {"language": "<PERSON>", "code": 53, "comment": 9, "blank": 8}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/passwords_controller.rb": {"language": "<PERSON>", "code": 26, "comment": 0, "blank": 7}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/user_mailer/password_reset.html.erb": {"language": "erb", "code": 48, "comment": 0, "blank": 10}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/registrations/new.html.erb": {"language": "erb", "code": 173, "comment": 0, "blank": 18}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/sitemaps_controller.rb": {"language": "<PERSON>", "code": 30, "comment": 2, "blank": 7}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/pages/terms_of_service.html.erb": {"language": "erb", "code": 37, "comment": 0, "blank": 12}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/pages/landing.html.erb": {"language": "erb", "code": 239, "comment": 0, "blank": 15}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/accounts/team_invitations_controller.rb": {"language": "<PERSON>", "code": 60, "comment": 1, "blank": 14}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/pages/pending.html.erb": {"language": "erb", "code": 188, "comment": 0, "blank": 13}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/accounts/team_members_controller.rb": {"language": "<PERSON>", "code": 66, "comment": 0, "blank": 14}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/pages/privacy_policy.html.erb": {"language": "erb", "code": 66, "comment": 0, "blank": 14}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/jobs/application_job.rb": {"language": "<PERSON>", "code": 2, "comment": 4, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/pages_controller.rb": {"language": "<PERSON>", "code": 15, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/pwa/service-worker.js": {"language": "JavaScript", "code": 0, "comment": 26, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/pwa/manifest.json.erb": {"language": "erb", "code": 22, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/lead_mailer/new_lead_notification.html.erb": {"language": "erb", "code": 73, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/agencies/base_controller.rb": {"language": "<PERSON>", "code": 13, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/lead_mailer/lead_status_update.html.erb": {"language": "erb", "code": 77, "comment": 0, "blank": 10}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/certifications_controller.rb": {"language": "<PERSON>", "code": 50, "comment": 1, "blank": 13}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/account_confirmation_mailer/confirmation_email.html.erb": {"language": "erb", "code": 28, "comment": 0, "blank": 8}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/account_confirmation_mailer/confirmation_email.text.erb": {"language": "erb", "code": 9, "comment": 0, "blank": 7}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/accounts_controller.rb": {"language": "<PERSON>", "code": 49, "comment": 4, "blank": 15}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/sitemaps/index.xml.erb": {"language": "erb", "code": 31, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/products_controller.rb": {"language": "<PERSON>", "code": 73, "comment": 5, "blank": 18}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/overview_controller.rb": {"language": "<PERSON>", "code": 99, "comment": 7, "blank": 15}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/email_previews_controller.rb": {"language": "<PERSON>", "code": 100, "comment": 5, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/case_studies_controller.rb": {"language": "<PERSON>", "code": 49, "comment": 0, "blank": 13}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/product_videos_controller.rb": {"language": "<PERSON>", "code": 49, "comment": 0, "blank": 13}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/product_contents_controller.rb": {"language": "<PERSON>", "code": 49, "comment": 0, "blank": 13}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/base_controller.rb": {"language": "<PERSON>", "code": 7, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/agencies/interests/index.html.erb": {"language": "erb", "code": 138, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/categories_controller.rb": {"language": "<PERSON>", "code": 49, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/layouts/mailer.text.erb": {"language": "erb", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/accounts/edit.html.erb": {"language": "erb", "code": 356, "comment": 0, "blank": 42}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/jobs/video_compression_job.rb": {"language": "<PERSON>", "code": 77, "comment": 7, "blank": 19}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/accounts/show.html.erb": {"language": "erb", "code": 177, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/layouts/mailer.html.erb": {"language": "erb", "code": 12, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/application_controller.rb": {"language": "<PERSON>", "code": 43, "comment": 2, "blank": 15}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/agencies/dashboard/index.html.erb": {"language": "erb", "code": 197, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/layouts/dashboard.html.erb": {"language": "erb", "code": 329, "comment": 3, "blank": 31}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/health_controller.rb": {"language": "<PERSON>", "code": 98, "comment": 2, "blank": 9}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/layouts/application.html.erb": {"language": "erb", "code": 77, "comment": 3, "blank": 16}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/products_controller.rb": {"language": "<PERSON>", "code": 58, "comment": 1, "blank": 13}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/overview_controller.rb": {"language": "<PERSON>", "code": 18, "comment": 0, "blank": 0}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/leads_controller.rb": {"language": "<PERSON>", "code": 52, "comment": 3, "blank": 10}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/product_contents_controller.rb": {"language": "<PERSON>", "code": 68, "comment": 5, "blank": 20}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/case_studies_controller.rb": {"language": "<PERSON>", "code": 68, "comment": 5, "blank": 20}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/accounts/team_members/edit.html.erb": {"language": "erb", "code": 68, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/product_videos_controller.rb": {"language": "<PERSON>", "code": 68, "comment": 5, "blank": 20}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/sessions/new.html.erb": {"language": "erb", "code": 88, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin_mailer/new_user_signup.html.erb": {"language": "erb", "code": 69, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/accounts/team_members/index.html.erb": {"language": "erb", "code": 327, "comment": 0, "blank": 33}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/accounts/team_invitations/new.html.erb": {"language": "erb", "code": 79, "comment": 0, "blank": 7}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/shared/_public_footer.html.erb": {"language": "erb", "code": 82, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/shared/_public_nav.html.erb": {"language": "erb", "code": 172, "comment": 0, "blank": 12}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/shared/_delete_resource.html.erb": {"language": "erb", "code": 73, "comment": 13, "blank": 10}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/active_storage/blobs/_blob.html.erb": {"language": "erb", "code": 13, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/shared/_dashboard_sidebar_nav.html.erb": {"language": "erb", "code": 117, "comment": 0, "blank": 0}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/shared/_pagination.html.erb": {"language": "erb", "code": 3, "comment": 0, "blank": 0}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/team_invitation_mailer/invitation_email.html.erb": {"language": "erb", "code": 57, "comment": 0, "blank": 10}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/registrations_helper.rb": {"language": "<PERSON>", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/invitation_acceptances/show.html.erb": {"language": "erb", "code": 264, "comment": 0, "blank": 18}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/team_invitation_mailer/invitation_email.text.erb": {"language": "erb", "code": 29, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/layouts/action_text/contents/_content.html.erb": {"language": "erb", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/account_confirmations_helper.rb": {"language": "<PERSON>", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/product_video.html.erb": {"language": "erb", "code": 183, "comment": 0, "blank": 17}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/invitation_acceptances_helper.rb": {"language": "<PERSON>", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/categories.html.erb": {"language": "erb", "code": 160, "comment": 0, "blank": 9}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/case_study.html.erb": {"language": "erb", "code": 195, "comment": 0, "blank": 18}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/search.html.erb": {"language": "erb", "code": 147, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/companies.html.erb": {"language": "erb", "code": 107, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/passwords_mailer/reset.html.erb": {"language": "erb", "code": 18, "comment": 0, "blank": 9}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/passwords_mailer/reset.text.erb": {"language": "erb", "code": 2, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/company.html.erb": {"language": "erb", "code": 183, "comment": 0, "blank": 16}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/products.html.erb": {"language": "erb", "code": 229, "comment": 0, "blank": 20}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/product_content.html.erb": {"language": "erb", "code": 242, "comment": 0, "blank": 21}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/product.html.erb": {"language": "erb", "code": 608, "comment": 0, "blank": 37}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/coverage/index.html": {"language": "HTML", "code": 18871, "comment": 0, "blank": 16972}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/validators/password_strength_validator.rb": {"language": "<PERSON>", "code": 61, "comment": 8, "blank": 16}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/base_controller.rb": {"language": "<PERSON>", "code": 13, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/categories/_form.html.erb": {"language": "erb", "code": 52, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/categories/edit.html.erb": {"language": "erb", "code": 27, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/categories/new.html.erb": {"language": "erb", "code": 21, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/categories/index.html.erb": {"language": "erb", "code": 131, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/categories/show.html.erb": {"language": "erb", "code": 117, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/overview/index.html.erb": {"language": "erb", "code": 273, "comment": 0, "blank": 18}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_contents/edit.html.erb": {"language": "erb", "code": 28, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_contents/index.html.erb": {"language": "erb", "code": 102, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/case_studies/new.html.erb": {"language": "erb", "code": 22, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/case_studies/show.html.erb": {"language": "erb", "code": 119, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/case_studies/edit.html.erb": {"language": "erb", "code": 23, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_contents/show.html.erb": {"language": "erb", "code": 156, "comment": 0, "blank": 13}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/case_studies/index.html.erb": {"language": "erb", "code": 102, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_contents/new.html.erb": {"language": "erb", "code": 22, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/home/<USER>": {"language": "erb", "code": 46, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_contents/_form.html.erb": {"language": "erb", "code": 146, "comment": 0, "blank": 12}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/passwords/edit.html.erb": {"language": "erb", "code": 89, "comment": 0, "blank": 9}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/index.html.erb": {"language": "erb", "code": 271, "comment": 0, "blank": 8}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/case_studies/_form.html.erb": {"language": "erb", "code": 132, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/leads/index.html.erb": {"language": "erb", "code": 264, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/leads/show.html.erb": {"language": "erb", "code": 292, "comment": 0, "blank": 21}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/passwords/new.html.erb": {"language": "erb", "code": 84, "comment": 0, "blank": 9}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_videos/new.html.erb": {"language": "erb", "code": 21, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_videos/show.html.erb": {"language": "erb", "code": 104, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_videos/edit.html.erb": {"language": "erb", "code": 27, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_videos/_form.html.erb": {"language": "erb", "code": 136, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/case_studies/_form.html.erb": {"language": "erb", "code": 135, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/case_studies/new.html.erb": {"language": "erb", "code": 21, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_videos/index.html.erb": {"language": "erb", "code": 102, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/case_studies/edit.html.erb": {"language": "erb", "code": 27, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_contents/new.html.erb": {"language": "erb", "code": 21, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/overview/index.html.erb": {"language": "erb", "code": 120, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/case_studies/index.html.erb": {"language": "erb", "code": 102, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_contents/show.html.erb": {"language": "erb", "code": 156, "comment": 0, "blank": 13}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_contents/_form.html.erb": {"language": "erb", "code": 146, "comment": 0, "blank": 12}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_contents/edit.html.erb": {"language": "erb", "code": 27, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/certifications/_form.html.erb": {"language": "erb", "code": 49, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/certifications/new.html.erb": {"language": "erb", "code": 10, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/certifications/show.html.erb": {"language": "erb", "code": 114, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_contents/index.html.erb": {"language": "erb", "code": 102, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/case_studies/show.html.erb": {"language": "erb", "code": 119, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/certifications/edit.html.erb": {"language": "erb", "code": 10, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/certifications/index.html.erb": {"language": "erb", "code": 124, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/accounts/index.html.erb": {"language": "erb", "code": 156, "comment": 0, "blank": 10}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/products/new.html.erb": {"language": "erb", "code": 11, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/products/_form.html.erb": {"language": "erb", "code": 100, "comment": 0, "blank": 7}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/products/edit.html.erb": {"language": "erb", "code": 21, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/products/show.html.erb": {"language": "erb", "code": 237, "comment": 0, "blank": 12}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/application_system_test_case.rb": {"language": "<PERSON>", "code": 4, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_videos/new.html.erb": {"language": "erb", "code": 22, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/products/index.html.erb": {"language": "erb", "code": 98, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_videos/edit.html.erb": {"language": "erb", "code": 28, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/accounts/show.html.erb": {"language": "erb", "code": 191, "comment": 0, "blank": 8}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_videos/_form.html.erb": {"language": "erb", "code": 133, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_videos/index.html.erb": {"language": "erb", "code": 102, "comment": 0, "blank": 6}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/products/index.html.erb": {"language": "erb", "code": 151, "comment": 0, "blank": 10}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/email_previews/index.html.erb": {"language": "erb", "code": 76, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/products/new.html.erb": {"language": "erb", "code": 29, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/accounts/edit.html.erb": {"language": "erb", "code": 152, "comment": 0, "blank": 10}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/products/_form.html.erb": {"language": "erb", "code": 101, "comment": 0, "blank": 9}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/products/show.html.erb": {"language": "erb", "code": 363, "comment": 0, "blank": 21}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/plans/logins.md": {"language": "<PERSON><PERSON>", "code": 6, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/importmap.rb": {"language": "<PERSON>", "code": 8, "comment": 1, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/products/edit.html.erb": {"language": "erb", "code": 35, "comment": 0, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/plans/first.md": {"language": "<PERSON><PERSON>", "code": 439, "comment": 0, "blank": 95}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/queue.yml": {"language": "YAML", "code": 15, "comment": 0, "blank": 4}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/boot.rb": {"language": "<PERSON>", "code": 3, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/cache.yml": {"language": "YAML", "code": 11, "comment": 2, "blank": 4}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_videos/show.html.erb": {"language": "erb", "code": 104, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/deploy.yml": {"language": "YAML", "code": 27, "comment": 70, "blank": 20}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/database.yml": {"language": "YAML", "code": 39, "comment": 11, "blank": 6}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/puma.rb": {"language": "<PERSON>", "code": 6, "comment": 31, "blank": 5}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/recurring.yml": {"language": "YAML", "code": 0, "comment": 10, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/storage.yml": {"language": "YAML", "code": 6, "comment": 23, "blank": 6}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/cable.yml": {"language": "YAML", "code": 11, "comment": 4, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/application.rb": {"language": "<PERSON>", "code": 10, "comment": 14, "blank": 7}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product_content.rb": {"language": "<PERSON>", "code": 57, "comment": 25, "blank": 17}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/certification.rb": {"language": "<PERSON>", "code": 11, "comment": 14, "blank": 4}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/environment.rb": {"language": "<PERSON>", "code": 2, "comment": 2, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/team_invitation.rb": {"language": "<PERSON>", "code": 37, "comment": 28, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/session.rb": {"language": "<PERSON>", "code": 3, "comment": 19, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/current.rb": {"language": "<PERSON>", "code": 4, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/admin_mailer.rb": {"language": "<PERSON>", "code": 60, "comment": 0, "blank": 11}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/lead.rb": {"language": "<PERSON>", "code": 12, "comment": 36, "blank": 4}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product_video.rb": {"language": "<PERSON>", "code": 41, "comment": 25, "blank": 13}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/lead_mailer.rb": {"language": "<PERSON>", "code": 40, "comment": 0, "blank": 5}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/case_study.rb": {"language": "<PERSON>", "code": 23, "comment": 23, "blank": 8}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/account_user.rb": {"language": "<PERSON>", "code": 15, "comment": 22, "blank": 6}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/application_mailer.rb": {"language": "<PERSON>", "code": 4, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/account_confirmation_mailer.rb": {"language": "<PERSON>", "code": 10, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product.rb": {"language": "<PERSON>", "code": 87, "comment": 32, "blank": 24}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/user_mailer.rb": {"language": "<PERSON>", "code": 51, "comment": 0, "blank": 10}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/passwords_mailer.rb": {"language": "<PERSON>", "code": 7, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/team_invitation_mailer.rb": {"language": "<PERSON>", "code": 13, "comment": 0, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/category.rb": {"language": "<PERSON>", "code": 61, "comment": 22, "blank": 19}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product_category.rb": {"language": "<PERSON>", "code": 5, "comment": 21, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/routes.rb": {"language": "<PERSON>", "code": 115, "comment": 20, "blank": 23}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/account.rb": {"language": "<PERSON>", "code": 100, "comment": 38, "blank": 27}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product_certification.rb": {"language": "<PERSON>", "code": 5, "comment": 21, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/users.yml": {"language": "YAML", "code": 20, "comment": 20, "blank": 4}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/categories.yml": {"language": "YAML", "code": 6, "comment": 21, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/accounts.yml": {"language": "YAML", "code": 45, "comment": 37, "blank": 7}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/integration/json_features_test.rb": {"language": "<PERSON>", "code": 67, "comment": 2, "blank": 13}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/product_videos.yml": {"language": "YAML", "code": 12, "comment": 22, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/product_categories.yml": {"language": "YAML", "code": 6, "comment": 22, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/account_test.rb": {"language": "<PERSON>", "code": 81, "comment": 36, "blank": 20}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/leads.yml": {"language": "YAML", "code": 12, "comment": 37, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/product_test.rb": {"language": "<PERSON>", "code": 91, "comment": 26, "blank": 21}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/team_invitations.yml": {"language": "YAML", "code": 18, "comment": 29, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/user.rb": {"language": "<PERSON>", "code": 73, "comment": 26, "blank": 25}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/account_user_test.rb": {"language": "<PERSON>", "code": 3, "comment": 25, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/lead_test.rb": {"language": "<PERSON>", "code": 3, "comment": 39, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/environments/development.rb": {"language": "<PERSON>", "code": 30, "comment": 27, "blank": 24}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/product_category_test.rb": {"language": "<PERSON>", "code": 3, "comment": 24, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/environments/test.rb": {"language": "<PERSON>", "code": 14, "comment": 25, "blank": 15}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/environments/production.rb": {"language": "<PERSON>", "code": 24, "comment": 44, "blank": 26}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/case_studies.yml": {"language": "YAML", "code": 12, "comment": 22, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/category_test.rb": {"language": "<PERSON>", "code": 3, "comment": 23, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/case_study_test.rb": {"language": "<PERSON>", "code": 3, "comment": 24, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/product_media.yml": {"language": "YAML", "code": 10, "comment": 1, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/product_content_test.rb": {"language": "<PERSON>", "code": 3, "comment": 24, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/product_video_test.rb": {"language": "<PERSON>", "code": 3, "comment": 24, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/account_users.yml": {"language": "YAML", "code": 10, "comment": 23, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/application_record.rb": {"language": "<PERSON>", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/products.yml": {"language": "YAML", "code": 14, "comment": 27, "blank": 3}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/user_test.rb": {"language": "<PERSON>", "code": 76, "comment": 22, "blank": 17}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/team_invitation_test.rb": {"language": "<PERSON>", "code": 66, "comment": 28, "blank": 14}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/concerns/performance_optimizable.rb": {"language": "<PERSON>", "code": 34, "comment": 9, "blank": 10}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/locales/en.yml": {"language": "YAML", "code": 2, "comment": 28, "blank": 2}, "file:///Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/action_text/rich_texts.yml": {"language": "YAML", "code": 0, "comment": 4, "blank": 1}}