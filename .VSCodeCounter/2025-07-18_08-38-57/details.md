# Details

Date : 2025-07-18 08:38:57

Directory /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market

Total : 340 files,  47356 codes, 2640 comments, 20219 blanks, all 70215 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.annotaterb.yml](/.annotaterb.yml) | YAML | 63 | 0 | 1 | 64 |
| [.claude/settings.local.json](/.claude/settings.local.json) | JSON | 39 | 0 | 0 | 39 |
| [.github/dependabot.yml](/.github/dependabot.yml) | YAML | 12 | 0 | 1 | 13 |
| [.github/workflows/ci.yml](/.github/workflows/ci.yml) | YAML | 149 | 0 | 29 | 178 |
| [.rubocop.yml](/.rubocop.yml) | YAML | 1 | 6 | 2 | 9 |
| [Dockerfile](/Dockerfile) | Docker | 31 | 21 | 21 | 73 |
| [Gemfile](/Gemfile) | Ruby | 53 | 31 | 27 | 111 |
| [README.md](/README.md) | Markdown | 13 | 0 | 12 | 25 |
| [Rakefile](/Rakefile) | Ruby | 2 | 2 | 3 | 7 |
| [app/assets/stylesheets/actiontext.css](/app/assets/stylesheets/actiontext.css) | PostCSS | 384 | 8 | 49 | 441 |
| [app/assets/stylesheets/application.css](/app/assets/stylesheets/application.css) | PostCSS | 0 | 10 | 1 | 11 |
| [app/assets/tailwind/application.css](/app/assets/tailwind/application.css) | PostCSS | 87 | 3 | 20 | 110 |
| [app/channels/application\_cable/connection.rb](/app/channels/application_cable/connection.rb) | Ruby | 14 | 0 | 3 | 17 |
| [app/controllers/account\_confirmations\_controller.rb](/app/controllers/account_confirmations_controller.rb) | Ruby | 32 | 2 | 10 | 44 |
| [app/controllers/accounts/team\_invitations\_controller.rb](/app/controllers/accounts/team_invitations_controller.rb) | Ruby | 60 | 1 | 14 | 75 |
| [app/controllers/accounts/team\_members\_controller.rb](/app/controllers/accounts/team_members_controller.rb) | Ruby | 66 | 0 | 14 | 80 |
| [app/controllers/accounts\_controller.rb](/app/controllers/accounts_controller.rb) | Ruby | 72 | 0 | 20 | 92 |
| [app/controllers/admin/accounts\_controller.rb](/app/controllers/admin/accounts_controller.rb) | Ruby | 49 | 4 | 15 | 68 |
| [app/controllers/admin/base\_controller.rb](/app/controllers/admin/base_controller.rb) | Ruby | 7 | 0 | 1 | 8 |
| [app/controllers/admin/case\_studies\_controller.rb](/app/controllers/admin/case_studies_controller.rb) | Ruby | 49 | 0 | 13 | 62 |
| [app/controllers/admin/categories\_controller.rb](/app/controllers/admin/categories_controller.rb) | Ruby | 49 | 0 | 11 | 60 |
| [app/controllers/admin/certifications\_controller.rb](/app/controllers/admin/certifications_controller.rb) | Ruby | 50 | 1 | 13 | 64 |
| [app/controllers/admin/email\_previews\_controller.rb](/app/controllers/admin/email_previews_controller.rb) | Ruby | 100 | 5 | 11 | 116 |
| [app/controllers/admin/health\_controller.rb](/app/controllers/admin/health_controller.rb) | Ruby | 98 | 2 | 9 | 109 |
| [app/controllers/admin/overview\_controller.rb](/app/controllers/admin/overview_controller.rb) | Ruby | 99 | 7 | 15 | 121 |
| [app/controllers/admin/product\_contents\_controller.rb](/app/controllers/admin/product_contents_controller.rb) | Ruby | 49 | 0 | 13 | 62 |
| [app/controllers/admin/product\_videos\_controller.rb](/app/controllers/admin/product_videos_controller.rb) | Ruby | 49 | 0 | 13 | 62 |
| [app/controllers/admin/products\_controller.rb](/app/controllers/admin/products_controller.rb) | Ruby | 73 | 5 | 18 | 96 |
| [app/controllers/agencies/base\_controller.rb](/app/controllers/agencies/base_controller.rb) | Ruby | 13 | 0 | 3 | 16 |
| [app/controllers/agencies/dashboard\_controller.rb](/app/controllers/agencies/dashboard_controller.rb) | Ruby | 17 | 0 | 2 | 19 |
| [app/controllers/agencies/interests\_controller.rb](/app/controllers/agencies/interests_controller.rb) | Ruby | 37 | 1 | 7 | 45 |
| [app/controllers/application\_controller.rb](/app/controllers/application_controller.rb) | Ruby | 43 | 2 | 15 | 60 |
| [app/controllers/concerns/authentication.rb](/app/controllers/concerns/authentication.rb) | Ruby | 54 | 2 | 13 | 69 |
| [app/controllers/concerns/error\_handling.rb](/app/controllers/concerns/error_handling.rb) | Ruby | 123 | 7 | 21 | 151 |
| [app/controllers/concerns/security\_headers.rb](/app/controllers/concerns/security_headers.rb) | Ruby | 78 | 8 | 17 | 103 |
| [app/controllers/errors\_controller.rb](/app/controllers/errors_controller.rb) | Ruby | 30 | 0 | 6 | 36 |
| [app/controllers/home\_controller.rb](/app/controllers/home_controller.rb) | Ruby | 5 | 0 | 0 | 5 |
| [app/controllers/invitation\_acceptances\_controller.rb](/app/controllers/invitation_acceptances_controller.rb) | Ruby | 87 | 12 | 14 | 113 |
| [app/controllers/marketplace\_controller.rb](/app/controllers/marketplace_controller.rb) | Ruby | 227 | 35 | 58 | 320 |
| [app/controllers/pages\_controller.rb](/app/controllers/pages_controller.rb) | Ruby | 15 | 0 | 5 | 20 |
| [app/controllers/passwords\_controller.rb](/app/controllers/passwords_controller.rb) | Ruby | 26 | 0 | 7 | 33 |
| [app/controllers/registrations\_controller.rb](/app/controllers/registrations_controller.rb) | Ruby | 44 | 6 | 12 | 62 |
| [app/controllers/sessions\_controller.rb](/app/controllers/sessions_controller.rb) | Ruby | 53 | 9 | 8 | 70 |
| [app/controllers/sitemaps\_controller.rb](/app/controllers/sitemaps_controller.rb) | Ruby | 30 | 2 | 7 | 39 |
| [app/controllers/vendors/base\_controller.rb](/app/controllers/vendors/base_controller.rb) | Ruby | 13 | 0 | 3 | 16 |
| [app/controllers/vendors/case\_studies\_controller.rb](/app/controllers/vendors/case_studies_controller.rb) | Ruby | 68 | 5 | 20 | 93 |
| [app/controllers/vendors/leads\_controller.rb](/app/controllers/vendors/leads_controller.rb) | Ruby | 52 | 3 | 10 | 65 |
| [app/controllers/vendors/overview\_controller.rb](/app/controllers/vendors/overview_controller.rb) | Ruby | 18 | 0 | 0 | 18 |
| [app/controllers/vendors/product\_contents\_controller.rb](/app/controllers/vendors/product_contents_controller.rb) | Ruby | 68 | 5 | 20 | 93 |
| [app/controllers/vendors/product\_videos\_controller.rb](/app/controllers/vendors/product_videos_controller.rb) | Ruby | 68 | 5 | 20 | 93 |
| [app/controllers/vendors/products\_controller.rb](/app/controllers/vendors/products_controller.rb) | Ruby | 58 | 1 | 13 | 72 |
| [app/helpers/account\_confirmations\_helper.rb](/app/helpers/account_confirmations_helper.rb) | Ruby | 2 | 0 | 1 | 3 |
| [app/helpers/application\_helper.rb](/app/helpers/application_helper.rb) | Ruby | 3 | 0 | 1 | 4 |
| [app/helpers/caching\_helper.rb](/app/helpers/caching_helper.rb) | Ruby | 70 | 2 | 10 | 82 |
| [app/helpers/invitation\_acceptances\_helper.rb](/app/helpers/invitation_acceptances_helper.rb) | Ruby | 2 | 0 | 1 | 3 |
| [app/helpers/pages\_helper.rb](/app/helpers/pages_helper.rb) | Ruby | 2 | 0 | 1 | 3 |
| [app/helpers/registrations\_helper.rb](/app/helpers/registrations_helper.rb) | Ruby | 2 | 0 | 1 | 3 |
| [app/helpers/seo\_helper.rb](/app/helpers/seo_helper.rb) | Ruby | 113 | 0 | 11 | 124 |
| [app/javascript/application.js](/app/javascript/application.js) | JavaScript | 4 | 1 | 2 | 7 |
| [app/javascript/controllers/application.js](/app/javascript/controllers/application.js) | JavaScript | 5 | 1 | 4 | 10 |
| [app/javascript/controllers/dark\_mode\_controller.js](/app/javascript/controllers/dark_mode_controller.js) | JavaScript | 42 | 1 | 8 | 51 |
| [app/javascript/controllers/delete\_resource\_controller.js](/app/javascript/controllers/delete_resource_controller.js) | JavaScript | 26 | 3 | 5 | 34 |
| [app/javascript/controllers/hello\_controller.js](/app/javascript/controllers/hello_controller.js) | JavaScript | 6 | 0 | 2 | 8 |
| [app/javascript/controllers/index.js](/app/javascript/controllers/index.js) | JavaScript | 3 | 1 | 1 | 5 |
| [app/javascript/controllers/search\_controller.js](/app/javascript/controllers/search_controller.js) | JavaScript | 230 | 14 | 40 | 284 |
| [app/javascript/controllers/sortable\_controller.js](/app/javascript/controllers/sortable_controller.js) | JavaScript | 54 | 4 | 7 | 65 |
| [app/javascript/controllers/team\_management\_controller.js](/app/javascript/controllers/team_management_controller.js) | JavaScript | 26 | 5 | 7 | 38 |
| [app/jobs/application\_job.rb](/app/jobs/application_job.rb) | Ruby | 2 | 4 | 2 | 8 |
| [app/jobs/video\_compression\_job.rb](/app/jobs/video_compression_job.rb) | Ruby | 77 | 7 | 19 | 103 |
| [app/mailers/account\_confirmation\_mailer.rb](/app/mailers/account_confirmation_mailer.rb) | Ruby | 10 | 0 | 2 | 12 |
| [app/mailers/admin\_mailer.rb](/app/mailers/admin_mailer.rb) | Ruby | 60 | 0 | 11 | 71 |
| [app/mailers/application\_mailer.rb](/app/mailers/application_mailer.rb) | Ruby | 4 | 0 | 1 | 5 |
| [app/mailers/lead\_mailer.rb](/app/mailers/lead_mailer.rb) | Ruby | 40 | 0 | 5 | 45 |
| [app/mailers/passwords\_mailer.rb](/app/mailers/passwords_mailer.rb) | Ruby | 7 | 0 | 1 | 8 |
| [app/mailers/team\_invitation\_mailer.rb](/app/mailers/team_invitation_mailer.rb) | Ruby | 13 | 0 | 2 | 15 |
| [app/mailers/user\_mailer.rb](/app/mailers/user_mailer.rb) | Ruby | 51 | 0 | 10 | 61 |
| [app/models/account.rb](/app/models/account.rb) | Ruby | 100 | 38 | 27 | 165 |
| [app/models/account\_user.rb](/app/models/account_user.rb) | Ruby | 15 | 22 | 6 | 43 |
| [app/models/application\_record.rb](/app/models/application_record.rb) | Ruby | 3 | 0 | 1 | 4 |
| [app/models/case\_study.rb](/app/models/case_study.rb) | Ruby | 23 | 23 | 8 | 54 |
| [app/models/category.rb](/app/models/category.rb) | Ruby | 61 | 22 | 19 | 102 |
| [app/models/certification.rb](/app/models/certification.rb) | Ruby | 11 | 14 | 4 | 29 |
| [app/models/concerns/performance\_optimizable.rb](/app/models/concerns/performance_optimizable.rb) | Ruby | 34 | 9 | 10 | 53 |
| [app/models/current.rb](/app/models/current.rb) | Ruby | 4 | 0 | 1 | 5 |
| [app/models/lead.rb](/app/models/lead.rb) | Ruby | 12 | 36 | 4 | 52 |
| [app/models/product.rb](/app/models/product.rb) | Ruby | 87 | 32 | 24 | 143 |
| [app/models/product\_category.rb](/app/models/product_category.rb) | Ruby | 5 | 21 | 2 | 28 |
| [app/models/product\_certification.rb](/app/models/product_certification.rb) | Ruby | 5 | 21 | 2 | 28 |
| [app/models/product\_content.rb](/app/models/product_content.rb) | Ruby | 57 | 25 | 17 | 99 |
| [app/models/product\_video.rb](/app/models/product_video.rb) | Ruby | 41 | 25 | 13 | 79 |
| [app/models/session.rb](/app/models/session.rb) | Ruby | 3 | 19 | 1 | 23 |
| [app/models/team\_invitation.rb](/app/models/team_invitation.rb) | Ruby | 37 | 28 | 11 | 76 |
| [app/models/user.rb](/app/models/user.rb) | Ruby | 73 | 26 | 25 | 124 |
| [app/services/security\_logger.rb](/app/services/security_logger.rb) | Ruby | 71 | 0 | 6 | 77 |
| [app/validators/password\_strength\_validator.rb](/app/validators/password_strength_validator.rb) | Ruby | 61 | 8 | 16 | 85 |
| [app/views/account\_confirmation\_mailer/confirmation\_email.html.erb](/app/views/account_confirmation_mailer/confirmation_email.html.erb) | erb | 28 | 0 | 8 | 36 |
| [app/views/account\_confirmation\_mailer/confirmation\_email.text.erb](/app/views/account_confirmation_mailer/confirmation_email.text.erb) | erb | 9 | 0 | 7 | 16 |
| [app/views/account\_confirmations/show.html.erb](/app/views/account_confirmations/show.html.erb) | erb | 150 | 0 | 7 | 157 |
| [app/views/accounts/edit.html.erb](/app/views/accounts/edit.html.erb) | erb | 356 | 0 | 42 | 398 |
| [app/views/accounts/show.html.erb](/app/views/accounts/show.html.erb) | erb | 177 | 0 | 11 | 188 |
| [app/views/accounts/team\_invitations/new.html.erb](/app/views/accounts/team_invitations/new.html.erb) | erb | 79 | 0 | 7 | 86 |
| [app/views/accounts/team\_members/edit.html.erb](/app/views/accounts/team_members/edit.html.erb) | erb | 68 | 0 | 5 | 73 |
| [app/views/accounts/team\_members/index.html.erb](/app/views/accounts/team_members/index.html.erb) | erb | 327 | 0 | 33 | 360 |
| [app/views/active\_storage/blobs/\_blob.html.erb](/app/views/active_storage/blobs/_blob.html.erb) | erb | 13 | 0 | 2 | 15 |
| [app/views/admin/accounts/edit.html.erb](/app/views/admin/accounts/edit.html.erb) | erb | 152 | 0 | 10 | 162 |
| [app/views/admin/accounts/index.html.erb](/app/views/admin/accounts/index.html.erb) | erb | 156 | 0 | 10 | 166 |
| [app/views/admin/accounts/show.html.erb](/app/views/admin/accounts/show.html.erb) | erb | 191 | 0 | 8 | 199 |
| [app/views/admin/case\_studies/\_form.html.erb](/app/views/admin/case_studies/_form.html.erb) | erb | 132 | 0 | 11 | 143 |
| [app/views/admin/case\_studies/edit.html.erb](/app/views/admin/case_studies/edit.html.erb) | erb | 23 | 0 | 1 | 24 |
| [app/views/admin/case\_studies/index.html.erb](/app/views/admin/case_studies/index.html.erb) | erb | 102 | 0 | 6 | 108 |
| [app/views/admin/case\_studies/new.html.erb](/app/views/admin/case_studies/new.html.erb) | erb | 22 | 0 | 1 | 23 |
| [app/views/admin/case\_studies/show.html.erb](/app/views/admin/case_studies/show.html.erb) | erb | 119 | 0 | 11 | 130 |
| [app/views/admin/categories/\_form.html.erb](/app/views/admin/categories/_form.html.erb) | erb | 52 | 0 | 3 | 55 |
| [app/views/admin/categories/edit.html.erb](/app/views/admin/categories/edit.html.erb) | erb | 27 | 0 | 4 | 31 |
| [app/views/admin/categories/index.html.erb](/app/views/admin/categories/index.html.erb) | erb | 131 | 0 | 6 | 137 |
| [app/views/admin/categories/new.html.erb](/app/views/admin/categories/new.html.erb) | erb | 21 | 0 | 3 | 24 |
| [app/views/admin/categories/show.html.erb](/app/views/admin/categories/show.html.erb) | erb | 117 | 0 | 4 | 121 |
| [app/views/admin/certifications/\_form.html.erb](/app/views/admin/certifications/_form.html.erb) | erb | 49 | 0 | 3 | 52 |
| [app/views/admin/certifications/edit.html.erb](/app/views/admin/certifications/edit.html.erb) | erb | 10 | 0 | 2 | 12 |
| [app/views/admin/certifications/index.html.erb](/app/views/admin/certifications/index.html.erb) | erb | 124 | 0 | 5 | 129 |
| [app/views/admin/certifications/new.html.erb](/app/views/admin/certifications/new.html.erb) | erb | 10 | 0 | 2 | 12 |
| [app/views/admin/certifications/show.html.erb](/app/views/admin/certifications/show.html.erb) | erb | 114 | 0 | 3 | 117 |
| [app/views/admin/email\_previews/index.html.erb](/app/views/admin/email_previews/index.html.erb) | erb | 76 | 0 | 5 | 81 |
| [app/views/admin/overview/index.html.erb](/app/views/admin/overview/index.html.erb) | erb | 273 | 0 | 18 | 291 |
| [app/views/admin/product\_contents/\_form.html.erb](/app/views/admin/product_contents/_form.html.erb) | erb | 146 | 0 | 12 | 158 |
| [app/views/admin/product\_contents/edit.html.erb](/app/views/admin/product_contents/edit.html.erb) | erb | 28 | 0 | 4 | 32 |
| [app/views/admin/product\_contents/index.html.erb](/app/views/admin/product_contents/index.html.erb) | erb | 102 | 0 | 6 | 108 |
| [app/views/admin/product\_contents/new.html.erb](/app/views/admin/product_contents/new.html.erb) | erb | 22 | 0 | 3 | 25 |
| [app/views/admin/product\_contents/show.html.erb](/app/views/admin/product_contents/show.html.erb) | erb | 156 | 0 | 13 | 169 |
| [app/views/admin/product\_videos/\_form.html.erb](/app/views/admin/product_videos/_form.html.erb) | erb | 133 | 0 | 11 | 144 |
| [app/views/admin/product\_videos/edit.html.erb](/app/views/admin/product_videos/edit.html.erb) | erb | 28 | 0 | 4 | 32 |
| [app/views/admin/product\_videos/index.html.erb](/app/views/admin/product_videos/index.html.erb) | erb | 102 | 0 | 6 | 108 |
| [app/views/admin/product\_videos/new.html.erb](/app/views/admin/product_videos/new.html.erb) | erb | 22 | 0 | 3 | 25 |
| [app/views/admin/product\_videos/show.html.erb](/app/views/admin/product_videos/show.html.erb) | erb | 104 | 0 | 11 | 115 |
| [app/views/admin/products/\_form.html.erb](/app/views/admin/products/_form.html.erb) | erb | 101 | 0 | 9 | 110 |
| [app/views/admin/products/edit.html.erb](/app/views/admin/products/edit.html.erb) | erb | 35 | 0 | 3 | 38 |
| [app/views/admin/products/index.html.erb](/app/views/admin/products/index.html.erb) | erb | 151 | 0 | 10 | 161 |
| [app/views/admin/products/new.html.erb](/app/views/admin/products/new.html.erb) | erb | 29 | 0 | 2 | 31 |
| [app/views/admin/products/show.html.erb](/app/views/admin/products/show.html.erb) | erb | 363 | 0 | 21 | 384 |
| [app/views/admin\_mailer/new\_user\_signup.html.erb](/app/views/admin_mailer/new_user_signup.html.erb) | erb | 69 | 0 | 11 | 80 |
| [app/views/agencies/dashboard/index.html.erb](/app/views/agencies/dashboard/index.html.erb) | erb | 197 | 0 | 11 | 208 |
| [app/views/agencies/interests/index.html.erb](/app/views/agencies/interests/index.html.erb) | erb | 138 | 0 | 5 | 143 |
| [app/views/errors/internal\_server\_error.html.erb](/app/views/errors/internal_server_error.html.erb) | erb | 58 | 0 | 7 | 65 |
| [app/views/errors/not\_found.html.erb](/app/views/errors/not_found.html.erb) | erb | 38 | 0 | 6 | 44 |
| [app/views/errors/unprocessable\_entity.html.erb](/app/views/errors/unprocessable_entity.html.erb) | erb | 48 | 0 | 7 | 55 |
| [app/views/home/<USER>/app/views/home/<USER>
| [app/views/invitation\_acceptances/show.html.erb](/app/views/invitation_acceptances/show.html.erb) | erb | 264 | 0 | 18 | 282 |
| [app/views/layouts/action\_text/contents/\_content.html.erb](/app/views/layouts/action_text/contents/_content.html.erb) | erb | 3 | 0 | 1 | 4 |
| [app/views/layouts/application.html.erb](/app/views/layouts/application.html.erb) | erb | 77 | 3 | 16 | 96 |
| [app/views/layouts/dashboard.html.erb](/app/views/layouts/dashboard.html.erb) | erb | 329 | 3 | 31 | 363 |
| [app/views/layouts/mailer.html.erb](/app/views/layouts/mailer.html.erb) | erb | 12 | 0 | 2 | 14 |
| [app/views/layouts/mailer.text.erb](/app/views/layouts/mailer.text.erb) | erb | 1 | 0 | 1 | 2 |
| [app/views/lead\_mailer/lead\_status\_update.html.erb](/app/views/lead_mailer/lead_status_update.html.erb) | erb | 77 | 0 | 10 | 87 |
| [app/views/lead\_mailer/new\_lead\_notification.html.erb](/app/views/lead_mailer/new_lead_notification.html.erb) | erb | 73 | 0 | 11 | 84 |
| [app/views/marketplace/case\_study.html.erb](/app/views/marketplace/case_study.html.erb) | erb | 195 | 0 | 18 | 213 |
| [app/views/marketplace/categories.html.erb](/app/views/marketplace/categories.html.erb) | erb | 160 | 0 | 9 | 169 |
| [app/views/marketplace/companies.html.erb](/app/views/marketplace/companies.html.erb) | erb | 107 | 0 | 5 | 112 |
| [app/views/marketplace/company.html.erb](/app/views/marketplace/company.html.erb) | erb | 183 | 0 | 16 | 199 |
| [app/views/marketplace/index.html.erb](/app/views/marketplace/index.html.erb) | erb | 271 | 0 | 8 | 279 |
| [app/views/marketplace/product.html.erb](/app/views/marketplace/product.html.erb) | erb | 608 | 0 | 37 | 645 |
| [app/views/marketplace/product\_content.html.erb](/app/views/marketplace/product_content.html.erb) | erb | 242 | 0 | 21 | 263 |
| [app/views/marketplace/product\_video.html.erb](/app/views/marketplace/product_video.html.erb) | erb | 183 | 0 | 17 | 200 |
| [app/views/marketplace/products.html.erb](/app/views/marketplace/products.html.erb) | erb | 229 | 0 | 20 | 249 |
| [app/views/marketplace/search.html.erb](/app/views/marketplace/search.html.erb) | erb | 147 | 0 | 11 | 158 |
| [app/views/pages/landing.html.erb](/app/views/pages/landing.html.erb) | erb | 239 | 0 | 15 | 254 |
| [app/views/pages/pending.html.erb](/app/views/pages/pending.html.erb) | erb | 188 | 0 | 13 | 201 |
| [app/views/pages/privacy\_policy.html.erb](/app/views/pages/privacy_policy.html.erb) | erb | 66 | 0 | 14 | 80 |
| [app/views/pages/terms\_of\_service.html.erb](/app/views/pages/terms_of_service.html.erb) | erb | 37 | 0 | 12 | 49 |
| [app/views/passwords/edit.html.erb](/app/views/passwords/edit.html.erb) | erb | 89 | 0 | 9 | 98 |
| [app/views/passwords/new.html.erb](/app/views/passwords/new.html.erb) | erb | 84 | 0 | 9 | 93 |
| [app/views/passwords\_mailer/reset.html.erb](/app/views/passwords_mailer/reset.html.erb) | erb | 18 | 0 | 9 | 27 |
| [app/views/passwords\_mailer/reset.text.erb](/app/views/passwords_mailer/reset.text.erb) | erb | 2 | 0 | 1 | 3 |
| [app/views/pwa/manifest.json.erb](/app/views/pwa/manifest.json.erb) | erb | 22 | 0 | 1 | 23 |
| [app/views/pwa/service-worker.js](/app/views/pwa/service-worker.js) | JavaScript | 0 | 26 | 1 | 27 |
| [app/views/registrations/new.html.erb](/app/views/registrations/new.html.erb) | erb | 173 | 0 | 18 | 191 |
| [app/views/sessions/new.html.erb](/app/views/sessions/new.html.erb) | erb | 88 | 0 | 11 | 99 |
| [app/views/shared/\_dashboard\_sidebar\_nav.html.erb](/app/views/shared/_dashboard_sidebar_nav.html.erb) | erb | 117 | 0 | 0 | 117 |
| [app/views/shared/\_delete\_resource.html.erb](/app/views/shared/_delete_resource.html.erb) | erb | 73 | 13 | 10 | 96 |
| [app/views/shared/\_pagination.html.erb](/app/views/shared/_pagination.html.erb) | erb | 3 | 0 | 0 | 3 |
| [app/views/shared/\_public\_footer.html.erb](/app/views/shared/_public_footer.html.erb) | erb | 82 | 0 | 2 | 84 |
| [app/views/shared/\_public\_nav.html.erb](/app/views/shared/_public_nav.html.erb) | erb | 172 | 0 | 12 | 184 |
| [app/views/sitemaps/index.xml.erb](/app/views/sitemaps/index.xml.erb) | erb | 31 | 0 | 4 | 35 |
| [app/views/team\_invitation\_mailer/invitation\_email.html.erb](/app/views/team_invitation_mailer/invitation_email.html.erb) | erb | 57 | 0 | 10 | 67 |
| [app/views/team\_invitation\_mailer/invitation\_email.text.erb](/app/views/team_invitation_mailer/invitation_email.text.erb) | erb | 29 | 0 | 11 | 40 |
| [app/views/user\_mailer/account\_approved.html.erb](/app/views/user_mailer/account_approved.html.erb) | erb | 44 | 0 | 9 | 53 |
| [app/views/user\_mailer/account\_rejected.html.erb](/app/views/user_mailer/account_rejected.html.erb) | erb | 50 | 0 | 10 | 60 |
| [app/views/user\_mailer/password\_reset.html.erb](/app/views/user_mailer/password_reset.html.erb) | erb | 48 | 0 | 10 | 58 |
| [app/views/user\_mailer/welcome.html.erb](/app/views/user_mailer/welcome.html.erb) | erb | 59 | 0 | 13 | 72 |
| [app/views/user\_mailer/welcome.text.erb](/app/views/user_mailer/welcome.text.erb) | erb | 25 | 0 | 12 | 37 |
| [app/views/vendors/case\_studies/\_form.html.erb](/app/views/vendors/case_studies/_form.html.erb) | erb | 135 | 0 | 11 | 146 |
| [app/views/vendors/case\_studies/edit.html.erb](/app/views/vendors/case_studies/edit.html.erb) | erb | 27 | 0 | 3 | 30 |
| [app/views/vendors/case\_studies/index.html.erb](/app/views/vendors/case_studies/index.html.erb) | erb | 102 | 0 | 6 | 108 |
| [app/views/vendors/case\_studies/new.html.erb](/app/views/vendors/case_studies/new.html.erb) | erb | 21 | 0 | 1 | 22 |
| [app/views/vendors/case\_studies/show.html.erb](/app/views/vendors/case_studies/show.html.erb) | erb | 119 | 0 | 11 | 130 |
| [app/views/vendors/leads/index.html.erb](/app/views/vendors/leads/index.html.erb) | erb | 264 | 0 | 11 | 275 |
| [app/views/vendors/leads/show.html.erb](/app/views/vendors/leads/show.html.erb) | erb | 292 | 0 | 21 | 313 |
| [app/views/vendors/overview/index.html.erb](/app/views/vendors/overview/index.html.erb) | erb | 120 | 0 | 3 | 123 |
| [app/views/vendors/product\_contents/\_form.html.erb](/app/views/vendors/product_contents/_form.html.erb) | erb | 146 | 0 | 12 | 158 |
| [app/views/vendors/product\_contents/edit.html.erb](/app/views/vendors/product_contents/edit.html.erb) | erb | 27 | 0 | 2 | 29 |
| [app/views/vendors/product\_contents/index.html.erb](/app/views/vendors/product_contents/index.html.erb) | erb | 102 | 0 | 6 | 108 |
| [app/views/vendors/product\_contents/new.html.erb](/app/views/vendors/product_contents/new.html.erb) | erb | 21 | 0 | 1 | 22 |
| [app/views/vendors/product\_contents/show.html.erb](/app/views/vendors/product_contents/show.html.erb) | erb | 156 | 0 | 13 | 169 |
| [app/views/vendors/product\_videos/\_form.html.erb](/app/views/vendors/product_videos/_form.html.erb) | erb | 136 | 0 | 11 | 147 |
| [app/views/vendors/product\_videos/edit.html.erb](/app/views/vendors/product_videos/edit.html.erb) | erb | 27 | 0 | 2 | 29 |
| [app/views/vendors/product\_videos/index.html.erb](/app/views/vendors/product_videos/index.html.erb) | erb | 102 | 0 | 6 | 108 |
| [app/views/vendors/product\_videos/new.html.erb](/app/views/vendors/product_videos/new.html.erb) | erb | 21 | 0 | 1 | 22 |
| [app/views/vendors/product\_videos/show.html.erb](/app/views/vendors/product_videos/show.html.erb) | erb | 104 | 0 | 11 | 115 |
| [app/views/vendors/products/\_form.html.erb](/app/views/vendors/products/_form.html.erb) | erb | 100 | 0 | 7 | 107 |
| [app/views/vendors/products/edit.html.erb](/app/views/vendors/products/edit.html.erb) | erb | 21 | 0 | 2 | 23 |
| [app/views/vendors/products/index.html.erb](/app/views/vendors/products/index.html.erb) | erb | 98 | 0 | 2 | 100 |
| [app/views/vendors/products/new.html.erb](/app/views/vendors/products/new.html.erb) | erb | 11 | 0 | 2 | 13 |
| [app/views/vendors/products/show.html.erb](/app/views/vendors/products/show.html.erb) | erb | 237 | 0 | 12 | 249 |
| [config.ru](/config.ru) | Ruby | 3 | 1 | 3 | 7 |
| [config/application.rb](/config/application.rb) | Ruby | 10 | 14 | 7 | 31 |
| [config/boot.rb](/config/boot.rb) | Ruby | 3 | 0 | 2 | 5 |
| [config/cable.yml](/config/cable.yml) | YAML | 11 | 4 | 3 | 18 |
| [config/cache.yml](/config/cache.yml) | YAML | 11 | 2 | 4 | 17 |
| [config/database.yml](/config/database.yml) | YAML | 39 | 11 | 6 | 56 |
| [config/deploy.yml](/config/deploy.yml) | YAML | 27 | 70 | 20 | 117 |
| [config/environment.rb](/config/environment.rb) | Ruby | 2 | 2 | 2 | 6 |
| [config/environments/development.rb](/config/environments/development.rb) | Ruby | 30 | 27 | 24 | 81 |
| [config/environments/production.rb](/config/environments/production.rb) | Ruby | 24 | 44 | 26 | 94 |
| [config/environments/test.rb](/config/environments/test.rb) | Ruby | 14 | 25 | 15 | 54 |
| [config/importmap.rb](/config/importmap.rb) | Ruby | 8 | 1 | 2 | 11 |
| [config/initializers/assets.rb](/config/initializers/assets.rb) | Ruby | 1 | 4 | 3 | 8 |
| [config/initializers/content\_security\_policy.rb](/config/initializers/content_security_policy.rb) | Ruby | 0 | 23 | 3 | 26 |
| [config/initializers/filter\_parameter\_logging.rb](/config/initializers/filter_parameter_logging.rb) | Ruby | 3 | 4 | 2 | 9 |
| [config/initializers/friendly\_id.rb](/config/initializers/friendly_id.rb) | Ruby | 5 | 97 | 6 | 108 |
| [config/initializers/inflections.rb](/config/initializers/inflections.rb) | Ruby | 0 | 14 | 3 | 17 |
| [config/initializers/mission\_control\_jobs.rb](/config/initializers/mission_control_jobs.rb) | Ruby | 1 | 15 | 4 | 20 |
| [config/initializers/pagy.rb](/config/initializers/pagy.rb) | Ruby | 4 | 2 | 1 | 7 |
| [config/initializers/sentry.rb](/config/initializers/sentry.rb) | Ruby | 5 | 2 | 1 | 8 |
| [config/locales/en.yml](/config/locales/en.yml) | YAML | 2 | 28 | 2 | 32 |
| [config/puma.rb](/config/puma.rb) | Ruby | 6 | 31 | 5 | 42 |
| [config/queue.yml](/config/queue.yml) | YAML | 15 | 0 | 4 | 19 |
| [config/recurring.yml](/config/recurring.yml) | YAML | 0 | 10 | 1 | 11 |
| [config/routes.rb](/config/routes.rb) | Ruby | 115 | 20 | 23 | 158 |
| [config/storage.yml](/config/storage.yml) | YAML | 6 | 23 | 6 | 35 |
| [coverage/.resultset.json](/coverage/.resultset.json) | JSON | 3,433 | 0 | 1 | 3,434 |
| [coverage/assets/0.13.1/application.css](/coverage/assets/0.13.1/application.css) | PostCSS | 1 | 0 | 1 | 2 |
| [coverage/assets/0.13.1/application.js](/coverage/assets/0.13.1/application.js) | JavaScript | 7 | 0 | 0 | 7 |
| [coverage/index.html](/coverage/index.html) | HTML | 18,871 | 0 | 16,972 | 35,843 |
| [data-schema.md](/data-schema.md) | Markdown | 132 | 0 | 47 | 179 |
| [db/cable\_schema.rb](/db/cable_schema.rb) | Ruby | 11 | 11 | 2 | 24 |
| [db/cache\_schema.rb](/db/cache_schema.rb) | Ruby | 12 | 11 | 2 | 25 |
| [db/migrate/20250710131740\_create\_initial\_database.rb](/db/migrate/20250710131740_create_initial_database.rb) | Ruby | 136 | 13 | 25 | 174 |
| [db/migrate/20250710131857\_create\_active\_storage\_tables.active\_storage.rb](/db/migrate/20250710131857_create_active_storage_tables.active_storage.rb) | Ruby | 46 | 2 | 10 | 58 |
| [db/migrate/20250710131910\_create\_action\_text\_tables.action\_text.rb](/db/migrate/20250710131910_create_action_text_tables.action_text.rb) | Ruby | 20 | 2 | 5 | 27 |
| [db/migrate/**************\_add\_parent\_id\_to\_categories.rb](/db/migrate/**************_add_parent_id_to_categories.rb) | Ruby | 7 | 0 | 1 | 8 |
| [db/migrate/**************\_create\_product\_categories.rb](/db/migrate/**************_create_product_categories.rb) | Ruby | 10 | 0 | 3 | 13 |
| [db/migrate/**************\_add\_description\_to\_categories.rb](/db/migrate/**************_add_description_to_categories.rb) | Ruby | 5 | 0 | 1 | 6 |
| [db/migrate/**************\_migrate\_company\_profile\_to\_account.rb](/db/migrate/**************_migrate_company_profile_to_account.rb) | Ruby | 69 | 9 | 9 | 87 |
| [db/migrate/**************\_create\_certifications.rb](/db/migrate/**************_create_certifications.rb) | Ruby | 9 | 0 | 2 | 11 |
| [db/migrate/**************\_create\_product\_certifications.rb](/db/migrate/**************_create_product_certifications.rb) | Ruby | 9 | 0 | 2 | 11 |
| [db/queue\_schema.rb](/db/queue_schema.rb) | Ruby | 118 | 11 | 13 | 142 |
| [db/schema.rb](/db/schema.rb) | Ruby | 238 | 11 | 21 | 270 |
| [db/seeds.rb](/db/seeds.rb) | Ruby | 556 | 35 | 39 | 630 |
| [lib/tasks/annotate\_rb.rake](/lib/tasks/annotate_rb.rake) | Ruby | 4 | 2 | 3 | 9 |
| [plans/categories.md](/plans/categories.md) | Markdown | 50 | 0 | 12 | 62 |
| [plans/first.md](/plans/first.md) | Markdown | 439 | 0 | 95 | 534 |
| [plans/logins.md](/plans/logins.md) | Markdown | 6 | 0 | 1 | 7 |
| [plans/phase-5.md](/plans/phase-5.md) | Markdown | 20 | 0 | 12 | 32 |
| [plans/select.md](/plans/select.md) | Markdown | 19 | 0 | 0 | 19 |
| [plans/sidebar\_nav.html](/plans/sidebar_nav.html) | HTML | 275 | 47 | 5 | 327 |
| [public/400.html](/public/400.html) | HTML | 85 | 1 | 29 | 115 |
| [public/404.html](/public/404.html) | HTML | 85 | 1 | 29 | 115 |
| [public/406-unsupported-browser.html](/public/406-unsupported-browser.html) | HTML | 85 | 1 | 29 | 115 |
| [public/422.html](/public/422.html) | HTML | 85 | 1 | 29 | 115 |
| [public/500.html](/public/500.html) | HTML | 85 | 1 | 29 | 115 |
| [public/icon.svg](/public/icon.svg) | XML | 3 | 0 | 1 | 4 |
| [spec/factories/account\_users.rb](/spec/factories/account_users.rb) | Ruby | 15 | 22 | 4 | 41 |
| [spec/factories/accounts.rb](/spec/factories/accounts.rb) | Ruby | 33 | 36 | 8 | 77 |
| [spec/factories/categories.rb](/spec/factories/categories.rb) | Ruby | 30 | 20 | 7 | 57 |
| [spec/factories/certifications.rb](/spec/factories/certifications.rb) | Ruby | 6 | 10 | 1 | 17 |
| [spec/factories/interests.rb](/spec/factories/interests.rb) | Ruby | 22 | 0 | 3 | 25 |
| [spec/factories/product\_certifications.rb](/spec/factories/product_certifications.rb) | Ruby | 6 | 20 | 1 | 27 |
| [spec/factories/products.rb](/spec/factories/products.rb) | Ruby | 35 | 26 | 6 | 67 |
| [spec/factories/users.rb](/spec/factories/users.rb) | Ruby | 21 | 20 | 5 | 46 |
| [spec/helpers/account\_confirmations\_helper\_spec.rb](/spec/helpers/account_confirmations_helper_spec.rb) | Ruby | 4 | 10 | 2 | 16 |
| [spec/helpers/invitation\_acceptances\_helper\_spec.rb](/spec/helpers/invitation_acceptances_helper_spec.rb) | Ruby | 4 | 10 | 2 | 16 |
| [spec/helpers/pages\_helper\_spec.rb](/spec/helpers/pages_helper_spec.rb) | Ruby | 4 | 10 | 2 | 16 |
| [spec/helpers/registrations\_helper\_spec.rb](/spec/helpers/registrations_helper_spec.rb) | Ruby | 4 | 10 | 2 | 16 |
| [spec/mailers/account\_confirmation\_mailer\_spec.rb](/spec/mailers/account_confirmation_mailer_spec.rb) | Ruby | 4 | 0 | 2 | 6 |
| [spec/mailers/previews/account\_confirmation\_mailer\_preview.rb](/spec/mailers/previews/account_confirmation_mailer_preview.rb) | Ruby | 15 | 0 | 3 | 18 |
| [spec/mailers/previews/team\_invitation\_mailer\_preview.rb](/spec/mailers/previews/team_invitation_mailer_preview.rb) | Ruby | 18 | 1 | 4 | 23 |
| [spec/mailers/previews/user\_mailer\_preview.rb](/spec/mailers/previews/user_mailer_preview.rb) | Ruby | 16 | 1 | 1 | 18 |
| [spec/mailers/team\_invitation\_mailer\_spec.rb](/spec/mailers/team_invitation_mailer_spec.rb) | Ruby | 4 | 0 | 2 | 6 |
| [spec/models/account\_spec.rb](/spec/models/account_spec.rb) | Ruby | 133 | 36 | 26 | 195 |
| [spec/models/category\_spec.rb](/spec/models/category_spec.rb) | Ruby | 109 | 22 | 24 | 155 |
| [spec/models/certification\_spec.rb](/spec/models/certification_spec.rb) | Ruby | 4 | 10 | 2 | 16 |
| [spec/models/product\_certification\_spec.rb](/spec/models/product_certification_spec.rb) | Ruby | 4 | 20 | 2 | 26 |
| [spec/models/product\_spec.rb](/spec/models/product_spec.rb) | Ruby | 170 | 26 | 33 | 229 |
| [spec/models/user\_spec.rb](/spec/models/user_spec.rb) | Ruby | 133 | 21 | 34 | 188 |
| [spec/rails\_helper.rb](/spec/rails_helper.rb) | Ruby | 25 | 43 | 9 | 77 |
| [spec/requests/account\_confirmations\_spec.rb](/spec/requests/account_confirmations_spec.rb) | Ruby | 6 | 0 | 2 | 8 |
| [spec/requests/authentication\_spec.rb](/spec/requests/authentication_spec.rb) | Ruby | 48 | 1 | 11 | 60 |
| [spec/requests/government/marketplace\_spec.rb](/spec/requests/government/marketplace_spec.rb) | Ruby | 108 | 1 | 28 | 137 |
| [spec/requests/invitation\_acceptances\_spec.rb](/spec/requests/invitation_acceptances_spec.rb) | Ruby | 6 | 0 | 2 | 8 |
| [spec/requests/pages\_spec.rb](/spec/requests/pages_spec.rb) | Ruby | 9 | 0 | 3 | 12 |
| [spec/requests/registrations\_spec.rb](/spec/requests/registrations_spec.rb) | Ruby | 15 | 0 | 4 | 19 |
| [spec/spec\_helper.rb](/spec/spec_helper.rb) | Ruby | 9 | 82 | 4 | 95 |
| [spec/support/authentication\_helpers.rb](/spec/support/authentication_helpers.rb) | Ruby | 36 | 0 | 6 | 42 |
| [spec/support/shared\_examples.rb](/spec/support/shared_examples.rb) | Ruby | 27 | 0 | 4 | 31 |
| [spec/support/shoulda\_matchers.rb](/spec/support/shoulda_matchers.rb) | Ruby | 6 | 0 | 0 | 6 |
| [spec/support/vcr.rb](/spec/support/vcr.rb) | Ruby | 12 | 1 | 1 | 14 |
| [spec/system/government/marketplace\_browsing\_spec.rb](/spec/system/government/marketplace_browsing_spec.rb) | Ruby | 134 | 1 | 38 | 173 |
| [spec/views/pages/landing.html.tailwindcss\_spec.rb](/spec/views/pages/landing.html.tailwindcss_spec.rb) | Ruby | 4 | 0 | 2 | 6 |
| [spec/views/registrations/create.html.tailwindcss\_spec.rb](/spec/views/registrations/create.html.tailwindcss_spec.rb) | Ruby | 4 | 0 | 2 | 6 |
| [spec/views/registrations/new.html.tailwindcss\_spec.rb](/spec/views/registrations/new.html.tailwindcss_spec.rb) | Ruby | 4 | 0 | 2 | 6 |
| [test/application\_system\_test\_case.rb](/test/application_system_test_case.rb) | Ruby | 4 | 0 | 2 | 6 |
| [test/fixtures/account\_users.yml](/test/fixtures/account_users.yml) | YAML | 10 | 23 | 3 | 36 |
| [test/fixtures/accounts.yml](/test/fixtures/accounts.yml) | YAML | 45 | 37 | 7 | 89 |
| [test/fixtures/action\_text/rich\_texts.yml](/test/fixtures/action_text/rich_texts.yml) | YAML | 0 | 4 | 1 | 5 |
| [test/fixtures/case\_studies.yml](/test/fixtures/case_studies.yml) | YAML | 12 | 22 | 3 | 37 |
| [test/fixtures/categories.yml](/test/fixtures/categories.yml) | YAML | 6 | 21 | 3 | 30 |
| [test/fixtures/leads.yml](/test/fixtures/leads.yml) | YAML | 12 | 37 | 3 | 52 |
| [test/fixtures/product\_categories.yml](/test/fixtures/product_categories.yml) | YAML | 6 | 22 | 3 | 31 |
| [test/fixtures/product\_media.yml](/test/fixtures/product_media.yml) | YAML | 10 | 1 | 3 | 14 |
| [test/fixtures/product\_videos.yml](/test/fixtures/product_videos.yml) | YAML | 12 | 22 | 3 | 37 |
| [test/fixtures/products.yml](/test/fixtures/products.yml) | YAML | 14 | 27 | 3 | 44 |
| [test/fixtures/team\_invitations.yml](/test/fixtures/team_invitations.yml) | YAML | 18 | 29 | 3 | 50 |
| [test/fixtures/users.yml](/test/fixtures/users.yml) | YAML | 20 | 20 | 4 | 44 |
| [test/integration/json\_features\_test.rb](/test/integration/json_features_test.rb) | Ruby | 67 | 2 | 13 | 82 |
| [test/mailers/previews/admin\_preview.rb](/test/mailers/previews/admin_preview.rb) | Ruby | 57 | 0 | 9 | 66 |
| [test/mailers/previews/lead\_preview.rb](/test/mailers/previews/lead_preview.rb) | Ruby | 69 | 2 | 14 | 85 |
| [test/mailers/previews/passwords\_mailer\_preview.rb](/test/mailers/previews/passwords_mailer_preview.rb) | Ruby | 17 | 0 | 3 | 20 |
| [test/mailers/previews/user\_preview.rb](/test/mailers/previews/user_preview.rb) | Ruby | 54 | 0 | 9 | 63 |
| [test/models/account\_test.rb](/test/models/account_test.rb) | Ruby | 81 | 36 | 20 | 137 |
| [test/models/account\_user\_test.rb](/test/models/account_user_test.rb) | Ruby | 3 | 25 | 2 | 30 |
| [test/models/case\_study\_test.rb](/test/models/case_study_test.rb) | Ruby | 3 | 24 | 2 | 29 |
| [test/models/category\_test.rb](/test/models/category_test.rb) | Ruby | 3 | 23 | 2 | 28 |
| [test/models/lead\_test.rb](/test/models/lead_test.rb) | Ruby | 3 | 39 | 2 | 44 |
| [test/models/product\_category\_test.rb](/test/models/product_category_test.rb) | Ruby | 3 | 24 | 2 | 29 |
| [test/models/product\_content\_test.rb](/test/models/product_content_test.rb) | Ruby | 3 | 24 | 2 | 29 |
| [test/models/product\_test.rb](/test/models/product_test.rb) | Ruby | 91 | 26 | 21 | 138 |
| [test/models/product\_video\_test.rb](/test/models/product_video_test.rb) | Ruby | 3 | 24 | 2 | 29 |
| [test/models/team\_invitation\_test.rb](/test/models/team_invitation_test.rb) | Ruby | 66 | 28 | 14 | 108 |
| [test/models/user\_test.rb](/test/models/user_test.rb) | Ruby | 76 | 22 | 17 | 115 |
| [test/test\_helper.rb](/test/test_helper.rb) | Ruby | 9 | 3 | 4 | 16 |
| [vendor/javascript/sortablejs.js](/vendor/javascript/sortablejs.js) | JavaScript | 2,952 | 312 | 117 | 3,381 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)