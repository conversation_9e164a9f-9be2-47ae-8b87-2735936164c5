Date : 2025-07-18 08:38:57
Directory : /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market
Total : 340 files,  47356 codes, 2640 comments, 20219 blanks, all 70215 lines

Languages
+------------+------------+------------+------------+------------+------------+
| language   | files      | code       | comment    | blank      | total      |
+------------+------------+------------+------------+------------+------------+
| HTML       |          7 |     19,571 |         52 |     17,122 |     36,745 |
| erb        |        116 |     12,570 |         19 |      1,028 |     13,617 |
| Ruby       |        166 |      6,702 |      1,740 |      1,484 |      9,926 |
| JSON       |          2 |      3,472 |          0 |          1 |      3,473 |
| JavaScript |         12 |      3,355 |        368 |        194 |      3,917 |
| Markdown   |          7 |        679 |          0 |        179 |        858 |
| YAML       |         24 |        501 |        419 |        118 |      1,038 |
| PostCSS    |          4 |        472 |         21 |         71 |        564 |
| Docker     |          1 |         31 |         21 |         21 |         73 |
| XML        |          1 |          3 |          0 |          1 |          4 |
+------------+------------+------------+------------+------------+------------+

Directories
+----------------------------------------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| path                                                                                                                                               | files      | code       | comment    | blank      | total      |
+----------------------------------------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| .                                                                                                                                                  |        340 |     47,356 |      2,640 |     20,219 |     70,215 |
| . (Files)                                                                                                                                          |          8 |        298 |         61 |        116 |        475 |
| .claude                                                                                                                                            |          1 |         39 |          0 |          0 |         39 |
| .github                                                                                                                                            |          2 |        161 |          0 |         30 |        191 |
| .github (Files)                                                                                                                                    |          1 |         12 |          0 |          1 |         13 |
| .github/workflows                                                                                                                                  |          1 |        149 |          0 |         29 |        178 |
| app                                                                                                                                                |        203 |     16,741 |        608 |      1,925 |     19,274 |
| app/assets                                                                                                                                         |          3 |        471 |         21 |         70 |        562 |
| app/assets/stylesheets                                                                                                                             |          2 |        384 |         18 |         50 |        452 |
| app/assets/tailwind                                                                                                                                |          1 |         87 |          3 |         20 |        110 |
| app/channels                                                                                                                                       |          1 |         14 |          0 |          3 |         17 |
| app/channels/application_cable                                                                                                                     |          1 |         14 |          0 |          3 |         17 |
| app/controllers                                                                                                                                    |         38 |      2,129 |        130 |        471 |      2,730 |
| app/controllers (Files)                                                                                                                            |         12 |        664 |         68 |        162 |        894 |
| app/controllers/accounts                                                                                                                           |          2 |        126 |          1 |         28 |        155 |
| app/controllers/admin                                                                                                                              |         11 |        672 |         24 |        132 |        828 |
| app/controllers/agencies                                                                                                                           |          3 |         67 |          1 |         12 |         80 |
| app/controllers/concerns                                                                                                                           |          3 |        255 |         17 |         51 |        323 |
| app/controllers/vendors                                                                                                                            |          7 |        345 |         19 |         86 |        450 |
| app/helpers                                                                                                                                        |          7 |        194 |          2 |         26 |        222 |
| app/javascript                                                                                                                                     |          9 |        396 |         30 |         76 |        502 |
| app/javascript (Files)                                                                                                                             |          1 |          4 |          1 |          2 |          7 |
| app/javascript/controllers                                                                                                                         |          8 |        392 |         29 |         74 |        495 |
| app/jobs                                                                                                                                           |          2 |         79 |         11 |         21 |        111 |
| app/mailers                                                                                                                                        |          7 |        185 |          0 |         32 |        217 |
| app/models                                                                                                                                         |         17 |        571 |        361 |        175 |      1,107 |
| app/models (Files)                                                                                                                                 |         16 |        537 |        352 |        165 |      1,054 |
| app/models/concerns                                                                                                                                |          1 |         34 |          9 |         10 |         53 |
| app/services                                                                                                                                       |          1 |         71 |          0 |          6 |         77 |
| app/validators                                                                                                                                     |          1 |         61 |          8 |         16 |         85 |
| app/views                                                                                                                                          |        117 |     12,570 |         45 |      1,029 |     13,644 |
| app/views/account_confirmation_mailer                                                                                                              |          2 |         37 |          0 |         15 |         52 |
| app/views/account_confirmations                                                                                                                    |          1 |        150 |          0 |          7 |        157 |
| app/views/accounts                                                                                                                                 |          5 |      1,007 |          0 |         98 |      1,105 |
| app/views/accounts (Files)                                                                                                                         |          2 |        533 |          0 |         53 |        586 |
| app/views/accounts/team_invitations                                                                                                                |          1 |         79 |          0 |          7 |         86 |
| app/views/accounts/team_members                                                                                                                    |          2 |        395 |          0 |         38 |        433 |
| app/views/active_storage                                                                                                                           |          1 |         13 |          0 |          2 |         15 |
| app/views/active_storage/blobs                                                                                                                     |          1 |         13 |          0 |          2 |         15 |
| app/views/admin                                                                                                                                    |         35 |      3,423 |          0 |        234 |      3,657 |
| app/views/admin/accounts                                                                                                                           |          3 |        499 |          0 |         28 |        527 |
| app/views/admin/case_studies                                                                                                                       |          5 |        398 |          0 |         30 |        428 |
| app/views/admin/categories                                                                                                                         |          5 |        348 |          0 |         20 |        368 |
| app/views/admin/certifications                                                                                                                     |          5 |        307 |          0 |         15 |        322 |
| app/views/admin/email_previews                                                                                                                     |          1 |         76 |          0 |          5 |         81 |
| app/views/admin/overview                                                                                                                           |          1 |        273 |          0 |         18 |        291 |
| app/views/admin/product_contents                                                                                                                   |          5 |        454 |          0 |         38 |        492 |
| app/views/admin/product_videos                                                                                                                     |          5 |        389 |          0 |         35 |        424 |
| app/views/admin/products                                                                                                                           |          5 |        679 |          0 |         45 |        724 |
| app/views/admin_mailer                                                                                                                             |          1 |         69 |          0 |         11 |         80 |
| app/views/agencies                                                                                                                                 |          2 |        335 |          0 |         16 |        351 |
| app/views/agencies/dashboard                                                                                                                       |          1 |        197 |          0 |         11 |        208 |
| app/views/agencies/interests                                                                                                                       |          1 |        138 |          0 |          5 |        143 |
| app/views/errors                                                                                                                                   |          3 |        144 |          0 |         20 |        164 |
| app/views/home                                                                                                                                     |          1 |         46 |          0 |          1 |         47 |
| app/views/invitation_acceptances                                                                                                                   |          1 |        264 |          0 |         18 |        282 |
| app/views/layouts                                                                                                                                  |          5 |        422 |          6 |         51 |        479 |
| app/views/layouts (Files)                                                                                                                          |          4 |        419 |          6 |         50 |        475 |
| app/views/layouts/action_text                                                                                                                      |          1 |          3 |          0 |          1 |          4 |
| app/views/layouts/action_text/contents                                                                                                             |          1 |          3 |          0 |          1 |          4 |
| app/views/lead_mailer                                                                                                                              |          2 |        150 |          0 |         21 |        171 |
| app/views/marketplace                                                                                                                              |         10 |      2,325 |          0 |        162 |      2,487 |
| app/views/pages                                                                                                                                    |          4 |        530 |          0 |         54 |        584 |
| app/views/passwords                                                                                                                                |          2 |        173 |          0 |         18 |        191 |
| app/views/passwords_mailer                                                                                                                         |          2 |         20 |          0 |         10 |         30 |
| app/views/pwa                                                                                                                                      |          2 |         22 |         26 |          2 |         50 |
| app/views/registrations                                                                                                                            |          1 |        173 |          0 |         18 |        191 |
| app/views/sessions                                                                                                                                 |          1 |         88 |          0 |         11 |         99 |
| app/views/shared                                                                                                                                   |          5 |        447 |         13 |         24 |        484 |
| app/views/sitemaps                                                                                                                                 |          1 |         31 |          0 |          4 |         35 |
| app/views/team_invitation_mailer                                                                                                                   |          2 |         86 |          0 |         21 |        107 |
| app/views/user_mailer                                                                                                                              |          5 |        226 |          0 |         54 |        280 |
| app/views/vendors                                                                                                                                  |         23 |      2,389 |          0 |        157 |      2,546 |
| app/views/vendors/case_studies                                                                                                                     |          5 |        404 |          0 |         32 |        436 |
| app/views/vendors/leads                                                                                                                            |          2 |        556 |          0 |         32 |        588 |
| app/views/vendors/overview                                                                                                                         |          1 |        120 |          0 |          3 |        123 |
| app/views/vendors/product_contents                                                                                                                 |          5 |        452 |          0 |         34 |        486 |
| app/views/vendors/product_videos                                                                                                                   |          5 |        390 |          0 |         31 |        421 |
| app/views/vendors/products                                                                                                                         |          5 |        467 |          0 |         25 |        492 |
| config                                                                                                                                             |         25 |        342 |        473 |        175 |        990 |
| config (Files)                                                                                                                                     |         13 |        253 |        188 |         85 |        526 |
| config/environments                                                                                                                                |          3 |         68 |         96 |         65 |        229 |
| config/initializers                                                                                                                                |          8 |         19 |        161 |         23 |        203 |
| config/locales                                                                                                                                     |          1 |          2 |         28 |          2 |         32 |
| coverage                                                                                                                                           |          4 |     22,312 |          0 |     16,974 |     39,286 |
| coverage (Files)                                                                                                                                   |          2 |     22,304 |          0 |     16,973 |     39,277 |
| coverage/assets                                                                                                                                    |          2 |          8 |          0 |          1 |          9 |
| coverage/assets/0.13.1                                                                                                                             |          2 |          8 |          0 |          1 |          9 |
| db                                                                                                                                                 |         14 |      1,246 |        105 |        135 |      1,486 |
| db (Files)                                                                                                                                         |          5 |        935 |         79 |         77 |      1,091 |
| db/migrate                                                                                                                                         |          9 |        311 |         26 |         58 |        395 |
| lib                                                                                                                                                |          1 |          4 |          2 |          3 |          9 |
| lib/tasks                                                                                                                                          |          1 |          4 |          2 |          3 |          9 |
| plans                                                                                                                                              |          6 |        809 |         47 |        125 |        981 |
| public                                                                                                                                             |          6 |        428 |          5 |        146 |        579 |
| spec                                                                                                                                               |         39 |      1,247 |        460 |        294 |      2,001 |
| spec (Files)                                                                                                                                       |          2 |         34 |        125 |         13 |        172 |
| spec/factories                                                                                                                                     |          8 |        168 |        154 |         35 |        357 |
| spec/helpers                                                                                                                                       |          4 |         16 |         40 |          8 |         64 |
| spec/mailers                                                                                                                                       |          5 |         57 |          2 |         12 |         71 |
| spec/mailers (Files)                                                                                                                               |          2 |          8 |          0 |          4 |         12 |
| spec/mailers/previews                                                                                                                              |          3 |         49 |          2 |          8 |         59 |
| spec/models                                                                                                                                        |          6 |        553 |        135 |        121 |        809 |
| spec/requests                                                                                                                                      |          6 |        192 |          2 |         50 |        244 |
| spec/requests (Files)                                                                                                                              |          5 |         84 |          1 |         22 |        107 |
| spec/requests/government                                                                                                                           |          1 |        108 |          1 |         28 |        137 |
| spec/support                                                                                                                                       |          4 |         81 |          1 |         11 |         93 |
| spec/system                                                                                                                                        |          1 |        134 |          1 |         38 |        173 |
| spec/system/government                                                                                                                             |          1 |        134 |          1 |         38 |        173 |
| spec/views                                                                                                                                         |          3 |         12 |          0 |          6 |         18 |
| spec/views/pages                                                                                                                                   |          1 |          4 |          0 |          2 |          6 |
| spec/views/registrations                                                                                                                           |          2 |          8 |          0 |          4 |         12 |
| test                                                                                                                                               |         30 |        777 |        567 |        179 |      1,523 |
| test (Files)                                                                                                                                       |          2 |         13 |          3 |          6 |         22 |
| test/fixtures                                                                                                                                      |         12 |        165 |        265 |         39 |        469 |
| test/fixtures (Files)                                                                                                                              |         11 |        165 |        261 |         38 |        464 |
| test/fixtures/action_text                                                                                                                          |          1 |          0 |          4 |          1 |          5 |
| test/integration                                                                                                                                   |          1 |         67 |          2 |         13 |         82 |
| test/mailers                                                                                                                                       |          4 |        197 |          2 |         35 |        234 |
| test/mailers/previews                                                                                                                              |          4 |        197 |          2 |         35 |        234 |
| test/models                                                                                                                                        |         11 |        335 |        295 |         86 |        716 |
| vendor                                                                                                                                             |          1 |      2,952 |        312 |        117 |      3,381 |
| vendor/javascript                                                                                                                                  |          1 |      2,952 |        312 |        117 |      3,381 |
+----------------------------------------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+

Files
+----------------------------------------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| filename                                                                                                                                           | language   | code       | comment    | blank      | total      |
+----------------------------------------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/.annotaterb.yml                                                          | YAML       |         63 |          0 |          1 |         64 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/.claude/settings.local.json                                              | JSON       |         39 |          0 |          0 |         39 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/.github/dependabot.yml                                                   | YAML       |         12 |          0 |          1 |         13 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/.github/workflows/ci.yml                                                 | YAML       |        149 |          0 |         29 |        178 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/.rubocop.yml                                                             | YAML       |          1 |          6 |          2 |          9 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/Dockerfile                                                               | Docker     |         31 |         21 |         21 |         73 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/Gemfile                                                                  | Ruby       |         53 |         31 |         27 |        111 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/README.md                                                                | Markdown   |         13 |          0 |         12 |         25 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/Rakefile                                                                 | Ruby       |          2 |          2 |          3 |          7 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/assets/stylesheets/actiontext.css                                    | PostCSS    |        384 |          8 |         49 |        441 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/assets/stylesheets/application.css                                   | PostCSS    |          0 |         10 |          1 |         11 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/assets/tailwind/application.css                                      | PostCSS    |         87 |          3 |         20 |        110 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/channels/application_cable/connection.rb                             | Ruby       |         14 |          0 |          3 |         17 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/account_confirmations_controller.rb                      | Ruby       |         32 |          2 |         10 |         44 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/accounts/team_invitations_controller.rb                  | Ruby       |         60 |          1 |         14 |         75 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/accounts/team_members_controller.rb                      | Ruby       |         66 |          0 |         14 |         80 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/accounts_controller.rb                                   | Ruby       |         72 |          0 |         20 |         92 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/accounts_controller.rb                             | Ruby       |         49 |          4 |         15 |         68 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/base_controller.rb                                 | Ruby       |          7 |          0 |          1 |          8 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/case_studies_controller.rb                         | Ruby       |         49 |          0 |         13 |         62 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/categories_controller.rb                           | Ruby       |         49 |          0 |         11 |         60 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/certifications_controller.rb                       | Ruby       |         50 |          1 |         13 |         64 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/email_previews_controller.rb                       | Ruby       |        100 |          5 |         11 |        116 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/health_controller.rb                               | Ruby       |         98 |          2 |          9 |        109 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/overview_controller.rb                             | Ruby       |         99 |          7 |         15 |        121 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/product_contents_controller.rb                     | Ruby       |         49 |          0 |         13 |         62 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/product_videos_controller.rb                       | Ruby       |         49 |          0 |         13 |         62 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/admin/products_controller.rb                             | Ruby       |         73 |          5 |         18 |         96 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/agencies/base_controller.rb                              | Ruby       |         13 |          0 |          3 |         16 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/agencies/dashboard_controller.rb                         | Ruby       |         17 |          0 |          2 |         19 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/agencies/interests_controller.rb                         | Ruby       |         37 |          1 |          7 |         45 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/application_controller.rb                                | Ruby       |         43 |          2 |         15 |         60 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/concerns/authentication.rb                               | Ruby       |         54 |          2 |         13 |         69 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/concerns/error_handling.rb                               | Ruby       |        123 |          7 |         21 |        151 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/concerns/security_headers.rb                             | Ruby       |         78 |          8 |         17 |        103 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/errors_controller.rb                                     | Ruby       |         30 |          0 |          6 |         36 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/home_controller.rb                                       | Ruby       |          5 |          0 |          0 |          5 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/invitation_acceptances_controller.rb                     | Ruby       |         87 |         12 |         14 |        113 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/marketplace_controller.rb                                | Ruby       |        227 |         35 |         58 |        320 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/pages_controller.rb                                      | Ruby       |         15 |          0 |          5 |         20 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/passwords_controller.rb                                  | Ruby       |         26 |          0 |          7 |         33 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/registrations_controller.rb                              | Ruby       |         44 |          6 |         12 |         62 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/sessions_controller.rb                                   | Ruby       |         53 |          9 |          8 |         70 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/sitemaps_controller.rb                                   | Ruby       |         30 |          2 |          7 |         39 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/base_controller.rb                               | Ruby       |         13 |          0 |          3 |         16 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/case_studies_controller.rb                       | Ruby       |         68 |          5 |         20 |         93 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/leads_controller.rb                              | Ruby       |         52 |          3 |         10 |         65 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/overview_controller.rb                           | Ruby       |         18 |          0 |          0 |         18 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/product_contents_controller.rb                   | Ruby       |         68 |          5 |         20 |         93 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/product_videos_controller.rb                     | Ruby       |         68 |          5 |         20 |         93 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/controllers/vendors/products_controller.rb                           | Ruby       |         58 |          1 |         13 |         72 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/account_confirmations_helper.rb                              | Ruby       |          2 |          0 |          1 |          3 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/application_helper.rb                                        | Ruby       |          3 |          0 |          1 |          4 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/caching_helper.rb                                            | Ruby       |         70 |          2 |         10 |         82 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/invitation_acceptances_helper.rb                             | Ruby       |          2 |          0 |          1 |          3 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/pages_helper.rb                                              | Ruby       |          2 |          0 |          1 |          3 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/registrations_helper.rb                                      | Ruby       |          2 |          0 |          1 |          3 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/helpers/seo_helper.rb                                                | Ruby       |        113 |          0 |         11 |        124 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/application.js                                            | JavaScript |          4 |          1 |          2 |          7 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/application.js                                | JavaScript |          5 |          1 |          4 |         10 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/dark_mode_controller.js                       | JavaScript |         42 |          1 |          8 |         51 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/delete_resource_controller.js                 | JavaScript |         26 |          3 |          5 |         34 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/hello_controller.js                           | JavaScript |          6 |          0 |          2 |          8 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/index.js                                      | JavaScript |          3 |          1 |          1 |          5 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/search_controller.js                          | JavaScript |        230 |         14 |         40 |        284 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/sortable_controller.js                        | JavaScript |         54 |          4 |          7 |         65 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/javascript/controllers/team_management_controller.js                 | JavaScript |         26 |          5 |          7 |         38 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/jobs/application_job.rb                                              | Ruby       |          2 |          4 |          2 |          8 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/jobs/video_compression_job.rb                                        | Ruby       |         77 |          7 |         19 |        103 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/account_confirmation_mailer.rb                               | Ruby       |         10 |          0 |          2 |         12 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/admin_mailer.rb                                              | Ruby       |         60 |          0 |         11 |         71 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/application_mailer.rb                                        | Ruby       |          4 |          0 |          1 |          5 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/lead_mailer.rb                                               | Ruby       |         40 |          0 |          5 |         45 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/passwords_mailer.rb                                          | Ruby       |          7 |          0 |          1 |          8 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/team_invitation_mailer.rb                                    | Ruby       |         13 |          0 |          2 |         15 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/mailers/user_mailer.rb                                               | Ruby       |         51 |          0 |         10 |         61 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/account.rb                                                    | Ruby       |        100 |         38 |         27 |        165 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/account_user.rb                                               | Ruby       |         15 |         22 |          6 |         43 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/application_record.rb                                         | Ruby       |          3 |          0 |          1 |          4 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/case_study.rb                                                 | Ruby       |         23 |         23 |          8 |         54 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/category.rb                                                   | Ruby       |         61 |         22 |         19 |        102 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/certification.rb                                              | Ruby       |         11 |         14 |          4 |         29 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/concerns/performance_optimizable.rb                           | Ruby       |         34 |          9 |         10 |         53 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/current.rb                                                    | Ruby       |          4 |          0 |          1 |          5 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/lead.rb                                                       | Ruby       |         12 |         36 |          4 |         52 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product.rb                                                    | Ruby       |         87 |         32 |         24 |        143 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product_category.rb                                           | Ruby       |          5 |         21 |          2 |         28 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product_certification.rb                                      | Ruby       |          5 |         21 |          2 |         28 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product_content.rb                                            | Ruby       |         57 |         25 |         17 |         99 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/product_video.rb                                              | Ruby       |         41 |         25 |         13 |         79 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/session.rb                                                    | Ruby       |          3 |         19 |          1 |         23 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/team_invitation.rb                                            | Ruby       |         37 |         28 |         11 |         76 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/models/user.rb                                                       | Ruby       |         73 |         26 |         25 |        124 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/services/security_logger.rb                                          | Ruby       |         71 |          0 |          6 |         77 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/validators/password_strength_validator.rb                            | Ruby       |         61 |          8 |         16 |         85 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/account_confirmation_mailer/confirmation_email.html.erb        | erb        |         28 |          0 |          8 |         36 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/account_confirmation_mailer/confirmation_email.text.erb        | erb        |          9 |          0 |          7 |         16 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/account_confirmations/show.html.erb                            | erb        |        150 |          0 |          7 |        157 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/accounts/edit.html.erb                                         | erb        |        356 |          0 |         42 |        398 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/accounts/show.html.erb                                         | erb        |        177 |          0 |         11 |        188 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/accounts/team_invitations/new.html.erb                         | erb        |         79 |          0 |          7 |         86 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/accounts/team_members/edit.html.erb                            | erb        |         68 |          0 |          5 |         73 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/accounts/team_members/index.html.erb                           | erb        |        327 |          0 |         33 |        360 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/active_storage/blobs/_blob.html.erb                            | erb        |         13 |          0 |          2 |         15 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/accounts/edit.html.erb                                   | erb        |        152 |          0 |         10 |        162 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/accounts/index.html.erb                                  | erb        |        156 |          0 |         10 |        166 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/accounts/show.html.erb                                   | erb        |        191 |          0 |          8 |        199 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/case_studies/_form.html.erb                              | erb        |        132 |          0 |         11 |        143 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/case_studies/edit.html.erb                               | erb        |         23 |          0 |          1 |         24 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/case_studies/index.html.erb                              | erb        |        102 |          0 |          6 |        108 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/case_studies/new.html.erb                                | erb        |         22 |          0 |          1 |         23 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/case_studies/show.html.erb                               | erb        |        119 |          0 |         11 |        130 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/categories/_form.html.erb                                | erb        |         52 |          0 |          3 |         55 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/categories/edit.html.erb                                 | erb        |         27 |          0 |          4 |         31 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/categories/index.html.erb                                | erb        |        131 |          0 |          6 |        137 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/categories/new.html.erb                                  | erb        |         21 |          0 |          3 |         24 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/categories/show.html.erb                                 | erb        |        117 |          0 |          4 |        121 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/certifications/_form.html.erb                            | erb        |         49 |          0 |          3 |         52 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/certifications/edit.html.erb                             | erb        |         10 |          0 |          2 |         12 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/certifications/index.html.erb                            | erb        |        124 |          0 |          5 |        129 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/certifications/new.html.erb                              | erb        |         10 |          0 |          2 |         12 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/certifications/show.html.erb                             | erb        |        114 |          0 |          3 |        117 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/email_previews/index.html.erb                            | erb        |         76 |          0 |          5 |         81 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/overview/index.html.erb                                  | erb        |        273 |          0 |         18 |        291 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_contents/_form.html.erb                          | erb        |        146 |          0 |         12 |        158 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_contents/edit.html.erb                           | erb        |         28 |          0 |          4 |         32 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_contents/index.html.erb                          | erb        |        102 |          0 |          6 |        108 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_contents/new.html.erb                            | erb        |         22 |          0 |          3 |         25 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_contents/show.html.erb                           | erb        |        156 |          0 |         13 |        169 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_videos/_form.html.erb                            | erb        |        133 |          0 |         11 |        144 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_videos/edit.html.erb                             | erb        |         28 |          0 |          4 |         32 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_videos/index.html.erb                            | erb        |        102 |          0 |          6 |        108 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_videos/new.html.erb                              | erb        |         22 |          0 |          3 |         25 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/product_videos/show.html.erb                             | erb        |        104 |          0 |         11 |        115 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/products/_form.html.erb                                  | erb        |        101 |          0 |          9 |        110 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/products/edit.html.erb                                   | erb        |         35 |          0 |          3 |         38 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/products/index.html.erb                                  | erb        |        151 |          0 |         10 |        161 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/products/new.html.erb                                    | erb        |         29 |          0 |          2 |         31 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin/products/show.html.erb                                   | erb        |        363 |          0 |         21 |        384 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/admin_mailer/new_user_signup.html.erb                          | erb        |         69 |          0 |         11 |         80 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/agencies/dashboard/index.html.erb                              | erb        |        197 |          0 |         11 |        208 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/agencies/interests/index.html.erb                              | erb        |        138 |          0 |          5 |        143 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/errors/internal_server_error.html.erb                          | erb        |         58 |          0 |          7 |         65 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/errors/not_found.html.erb                                      | erb        |         38 |          0 |          6 |         44 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/errors/unprocessable_entity.html.erb                           | erb        |         48 |          0 |          7 |         55 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/home/<USER>
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/invitation_acceptances/show.html.erb                           | erb        |        264 |          0 |         18 |        282 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/layouts/action_text/contents/_content.html.erb                 | erb        |          3 |          0 |          1 |          4 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/layouts/application.html.erb                                   | erb        |         77 |          3 |         16 |         96 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/layouts/dashboard.html.erb                                     | erb        |        329 |          3 |         31 |        363 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/layouts/mailer.html.erb                                        | erb        |         12 |          0 |          2 |         14 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/layouts/mailer.text.erb                                        | erb        |          1 |          0 |          1 |          2 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/lead_mailer/lead_status_update.html.erb                        | erb        |         77 |          0 |         10 |         87 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/lead_mailer/new_lead_notification.html.erb                     | erb        |         73 |          0 |         11 |         84 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/case_study.html.erb                                | erb        |        195 |          0 |         18 |        213 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/categories.html.erb                                | erb        |        160 |          0 |          9 |        169 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/companies.html.erb                                 | erb        |        107 |          0 |          5 |        112 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/company.html.erb                                   | erb        |        183 |          0 |         16 |        199 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/index.html.erb                                     | erb        |        271 |          0 |          8 |        279 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/product.html.erb                                   | erb        |        608 |          0 |         37 |        645 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/product_content.html.erb                           | erb        |        242 |          0 |         21 |        263 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/product_video.html.erb                             | erb        |        183 |          0 |         17 |        200 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/products.html.erb                                  | erb        |        229 |          0 |         20 |        249 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/marketplace/search.html.erb                                    | erb        |        147 |          0 |         11 |        158 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/pages/landing.html.erb                                         | erb        |        239 |          0 |         15 |        254 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/pages/pending.html.erb                                         | erb        |        188 |          0 |         13 |        201 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/pages/privacy_policy.html.erb                                  | erb        |         66 |          0 |         14 |         80 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/pages/terms_of_service.html.erb                                | erb        |         37 |          0 |         12 |         49 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/passwords/edit.html.erb                                        | erb        |         89 |          0 |          9 |         98 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/passwords/new.html.erb                                         | erb        |         84 |          0 |          9 |         93 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/passwords_mailer/reset.html.erb                                | erb        |         18 |          0 |          9 |         27 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/passwords_mailer/reset.text.erb                                | erb        |          2 |          0 |          1 |          3 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/pwa/manifest.json.erb                                          | erb        |         22 |          0 |          1 |         23 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/pwa/service-worker.js                                          | JavaScript |          0 |         26 |          1 |         27 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/registrations/new.html.erb                                     | erb        |        173 |          0 |         18 |        191 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/sessions/new.html.erb                                          | erb        |         88 |          0 |         11 |         99 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/shared/_dashboard_sidebar_nav.html.erb                         | erb        |        117 |          0 |          0 |        117 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/shared/_delete_resource.html.erb                               | erb        |         73 |         13 |         10 |         96 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/shared/_pagination.html.erb                                    | erb        |          3 |          0 |          0 |          3 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/shared/_public_footer.html.erb                                 | erb        |         82 |          0 |          2 |         84 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/shared/_public_nav.html.erb                                    | erb        |        172 |          0 |         12 |        184 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/sitemaps/index.xml.erb                                         | erb        |         31 |          0 |          4 |         35 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/team_invitation_mailer/invitation_email.html.erb               | erb        |         57 |          0 |         10 |         67 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/team_invitation_mailer/invitation_email.text.erb               | erb        |         29 |          0 |         11 |         40 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/user_mailer/account_approved.html.erb                          | erb        |         44 |          0 |          9 |         53 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/user_mailer/account_rejected.html.erb                          | erb        |         50 |          0 |         10 |         60 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/user_mailer/password_reset.html.erb                            | erb        |         48 |          0 |         10 |         58 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/user_mailer/welcome.html.erb                                   | erb        |         59 |          0 |         13 |         72 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/user_mailer/welcome.text.erb                                   | erb        |         25 |          0 |         12 |         37 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/case_studies/_form.html.erb                            | erb        |        135 |          0 |         11 |        146 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/case_studies/edit.html.erb                             | erb        |         27 |          0 |          3 |         30 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/case_studies/index.html.erb                            | erb        |        102 |          0 |          6 |        108 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/case_studies/new.html.erb                              | erb        |         21 |          0 |          1 |         22 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/case_studies/show.html.erb                             | erb        |        119 |          0 |         11 |        130 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/leads/index.html.erb                                   | erb        |        264 |          0 |         11 |        275 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/leads/show.html.erb                                    | erb        |        292 |          0 |         21 |        313 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/overview/index.html.erb                                | erb        |        120 |          0 |          3 |        123 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_contents/_form.html.erb                        | erb        |        146 |          0 |         12 |        158 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_contents/edit.html.erb                         | erb        |         27 |          0 |          2 |         29 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_contents/index.html.erb                        | erb        |        102 |          0 |          6 |        108 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_contents/new.html.erb                          | erb        |         21 |          0 |          1 |         22 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_contents/show.html.erb                         | erb        |        156 |          0 |         13 |        169 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_videos/_form.html.erb                          | erb        |        136 |          0 |         11 |        147 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_videos/edit.html.erb                           | erb        |         27 |          0 |          2 |         29 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_videos/index.html.erb                          | erb        |        102 |          0 |          6 |        108 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_videos/new.html.erb                            | erb        |         21 |          0 |          1 |         22 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/product_videos/show.html.erb                           | erb        |        104 |          0 |         11 |        115 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/products/_form.html.erb                                | erb        |        100 |          0 |          7 |        107 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/products/edit.html.erb                                 | erb        |         21 |          0 |          2 |         23 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/products/index.html.erb                                | erb        |         98 |          0 |          2 |        100 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/products/new.html.erb                                  | erb        |         11 |          0 |          2 |         13 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/app/views/vendors/products/show.html.erb                                 | erb        |        237 |          0 |         12 |        249 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config.ru                                                                | Ruby       |          3 |          1 |          3 |          7 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/application.rb                                                    | Ruby       |         10 |         14 |          7 |         31 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/boot.rb                                                           | Ruby       |          3 |          0 |          2 |          5 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/cable.yml                                                         | YAML       |         11 |          4 |          3 |         18 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/cache.yml                                                         | YAML       |         11 |          2 |          4 |         17 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/database.yml                                                      | YAML       |         39 |         11 |          6 |         56 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/deploy.yml                                                        | YAML       |         27 |         70 |         20 |        117 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/environment.rb                                                    | Ruby       |          2 |          2 |          2 |          6 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/environments/development.rb                                       | Ruby       |         30 |         27 |         24 |         81 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/environments/production.rb                                        | Ruby       |         24 |         44 |         26 |         94 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/environments/test.rb                                              | Ruby       |         14 |         25 |         15 |         54 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/importmap.rb                                                      | Ruby       |          8 |          1 |          2 |         11 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/assets.rb                                            | Ruby       |          1 |          4 |          3 |          8 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/content_security_policy.rb                           | Ruby       |          0 |         23 |          3 |         26 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/filter_parameter_logging.rb                          | Ruby       |          3 |          4 |          2 |          9 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/friendly_id.rb                                       | Ruby       |          5 |         97 |          6 |        108 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/inflections.rb                                       | Ruby       |          0 |         14 |          3 |         17 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/mission_control_jobs.rb                              | Ruby       |          1 |         15 |          4 |         20 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/pagy.rb                                              | Ruby       |          4 |          2 |          1 |          7 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/initializers/sentry.rb                                            | Ruby       |          5 |          2 |          1 |          8 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/locales/en.yml                                                    | YAML       |          2 |         28 |          2 |         32 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/puma.rb                                                           | Ruby       |          6 |         31 |          5 |         42 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/queue.yml                                                         | YAML       |         15 |          0 |          4 |         19 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/recurring.yml                                                     | YAML       |          0 |         10 |          1 |         11 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/routes.rb                                                         | Ruby       |        115 |         20 |         23 |        158 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/config/storage.yml                                                       | YAML       |          6 |         23 |          6 |         35 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/coverage/.resultset.json                                                 | JSON       |      3,433 |          0 |          1 |      3,434 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/coverage/assets/0.13.1/application.css                                   | PostCSS    |          1 |          0 |          1 |          2 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/coverage/assets/0.13.1/application.js                                    | JavaScript |          7 |          0 |          0 |          7 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/coverage/index.html                                                      | HTML       |     18,871 |          0 |     16,972 |     35,843 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/data-schema.md                                                           | Markdown   |        132 |          0 |         47 |        179 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/cable_schema.rb                                                       | Ruby       |         11 |         11 |          2 |         24 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/cache_schema.rb                                                       | Ruby       |         12 |         11 |          2 |         25 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250710131740_create_initial_database.rb                     | Ruby       |        136 |         13 |         25 |        174 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250710131857_create_active_storage_tables.active_storage.rb | Ruby       |         46 |          2 |         10 |         58 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250710131910_create_action_text_tables.action_text.rb       | Ruby       |         20 |          2 |          5 |         27 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250716000001_add_parent_id_to_categories.rb                 | Ruby       |          7 |          0 |          1 |          8 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250716000002_create_product_categories.rb                   | Ruby       |         10 |          0 |          3 |         13 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250716173201_add_description_to_categories.rb               | Ruby       |          5 |          0 |          1 |          6 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250718120910_migrate_company_profile_to_account.rb          | Ruby       |         69 |          9 |          9 |         87 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250718123410_create_certifications.rb                       | Ruby       |          9 |          0 |          2 |         11 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/migrate/20250718123443_create_product_certifications.rb               | Ruby       |          9 |          0 |          2 |         11 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/queue_schema.rb                                                       | Ruby       |        118 |         11 |         13 |        142 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/schema.rb                                                             | Ruby       |        238 |         11 |         21 |        270 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/db/seeds.rb                                                              | Ruby       |        556 |         35 |         39 |        630 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/lib/tasks/annotate_rb.rake                                               | Ruby       |          4 |          2 |          3 |          9 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/plans/categories.md                                                      | Markdown   |         50 |          0 |         12 |         62 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/plans/first.md                                                           | Markdown   |        439 |          0 |         95 |        534 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/plans/logins.md                                                          | Markdown   |          6 |          0 |          1 |          7 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/plans/phase-5.md                                                         | Markdown   |         20 |          0 |         12 |         32 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/plans/select.md                                                          | Markdown   |         19 |          0 |          0 |         19 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/plans/sidebar_nav.html                                                   | HTML       |        275 |         47 |          5 |        327 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/public/400.html                                                          | HTML       |         85 |          1 |         29 |        115 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/public/404.html                                                          | HTML       |         85 |          1 |         29 |        115 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/public/406-unsupported-browser.html                                      | HTML       |         85 |          1 |         29 |        115 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/public/422.html                                                          | HTML       |         85 |          1 |         29 |        115 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/public/500.html                                                          | HTML       |         85 |          1 |         29 |        115 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/public/icon.svg                                                          | XML        |          3 |          0 |          1 |          4 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/account_users.rb                                          | Ruby       |         15 |         22 |          4 |         41 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/accounts.rb                                               | Ruby       |         33 |         36 |          8 |         77 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/categories.rb                                             | Ruby       |         30 |         20 |          7 |         57 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/certifications.rb                                         | Ruby       |          6 |         10 |          1 |         17 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/interests.rb                                              | Ruby       |         22 |          0 |          3 |         25 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/product_certifications.rb                                 | Ruby       |          6 |         20 |          1 |         27 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/products.rb                                               | Ruby       |         35 |         26 |          6 |         67 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/factories/users.rb                                                  | Ruby       |         21 |         20 |          5 |         46 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/helpers/account_confirmations_helper_spec.rb                        | Ruby       |          4 |         10 |          2 |         16 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/helpers/invitation_acceptances_helper_spec.rb                       | Ruby       |          4 |         10 |          2 |         16 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/helpers/pages_helper_spec.rb                                        | Ruby       |          4 |         10 |          2 |         16 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/helpers/registrations_helper_spec.rb                                | Ruby       |          4 |         10 |          2 |         16 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/mailers/account_confirmation_mailer_spec.rb                         | Ruby       |          4 |          0 |          2 |          6 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/mailers/previews/account_confirmation_mailer_preview.rb             | Ruby       |         15 |          0 |          3 |         18 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/mailers/previews/team_invitation_mailer_preview.rb                  | Ruby       |         18 |          1 |          4 |         23 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/mailers/previews/user_mailer_preview.rb                             | Ruby       |         16 |          1 |          1 |         18 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/mailers/team_invitation_mailer_spec.rb                              | Ruby       |          4 |          0 |          2 |          6 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/models/account_spec.rb                                              | Ruby       |        133 |         36 |         26 |        195 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/models/category_spec.rb                                             | Ruby       |        109 |         22 |         24 |        155 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/models/certification_spec.rb                                        | Ruby       |          4 |         10 |          2 |         16 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/models/product_certification_spec.rb                                | Ruby       |          4 |         20 |          2 |         26 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/models/product_spec.rb                                              | Ruby       |        170 |         26 |         33 |        229 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/models/user_spec.rb                                                 | Ruby       |        133 |         21 |         34 |        188 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/rails_helper.rb                                                     | Ruby       |         25 |         43 |          9 |         77 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/requests/account_confirmations_spec.rb                              | Ruby       |          6 |          0 |          2 |          8 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/requests/authentication_spec.rb                                     | Ruby       |         48 |          1 |         11 |         60 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/requests/government/marketplace_spec.rb                             | Ruby       |        108 |          1 |         28 |        137 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/requests/invitation_acceptances_spec.rb                             | Ruby       |          6 |          0 |          2 |          8 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/requests/pages_spec.rb                                              | Ruby       |          9 |          0 |          3 |         12 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/requests/registrations_spec.rb                                      | Ruby       |         15 |          0 |          4 |         19 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/spec_helper.rb                                                      | Ruby       |          9 |         82 |          4 |         95 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/support/authentication_helpers.rb                                   | Ruby       |         36 |          0 |          6 |         42 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/support/shared_examples.rb                                          | Ruby       |         27 |          0 |          4 |         31 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/support/shoulda_matchers.rb                                         | Ruby       |          6 |          0 |          0 |          6 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/support/vcr.rb                                                      | Ruby       |         12 |          1 |          1 |         14 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/system/government/marketplace_browsing_spec.rb                      | Ruby       |        134 |          1 |         38 |        173 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/views/pages/landing.html.tailwindcss_spec.rb                        | Ruby       |          4 |          0 |          2 |          6 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/views/registrations/create.html.tailwindcss_spec.rb                 | Ruby       |          4 |          0 |          2 |          6 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/spec/views/registrations/new.html.tailwindcss_spec.rb                    | Ruby       |          4 |          0 |          2 |          6 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/application_system_test_case.rb                                     | Ruby       |          4 |          0 |          2 |          6 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/account_users.yml                                          | YAML       |         10 |         23 |          3 |         36 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/accounts.yml                                               | YAML       |         45 |         37 |          7 |         89 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/action_text/rich_texts.yml                                 | YAML       |          0 |          4 |          1 |          5 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/case_studies.yml                                           | YAML       |         12 |         22 |          3 |         37 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/categories.yml                                             | YAML       |          6 |         21 |          3 |         30 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/leads.yml                                                  | YAML       |         12 |         37 |          3 |         52 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/product_categories.yml                                     | YAML       |          6 |         22 |          3 |         31 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/product_media.yml                                          | YAML       |         10 |          1 |          3 |         14 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/product_videos.yml                                         | YAML       |         12 |         22 |          3 |         37 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/products.yml                                               | YAML       |         14 |         27 |          3 |         44 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/team_invitations.yml                                       | YAML       |         18 |         29 |          3 |         50 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/fixtures/users.yml                                                  | YAML       |         20 |         20 |          4 |         44 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/integration/json_features_test.rb                                   | Ruby       |         67 |          2 |         13 |         82 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/mailers/previews/admin_preview.rb                                   | Ruby       |         57 |          0 |          9 |         66 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/mailers/previews/lead_preview.rb                                    | Ruby       |         69 |          2 |         14 |         85 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/mailers/previews/passwords_mailer_preview.rb                        | Ruby       |         17 |          0 |          3 |         20 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/mailers/previews/user_preview.rb                                    | Ruby       |         54 |          0 |          9 |         63 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/account_test.rb                                              | Ruby       |         81 |         36 |         20 |        137 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/account_user_test.rb                                         | Ruby       |          3 |         25 |          2 |         30 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/case_study_test.rb                                           | Ruby       |          3 |         24 |          2 |         29 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/category_test.rb                                             | Ruby       |          3 |         23 |          2 |         28 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/lead_test.rb                                                 | Ruby       |          3 |         39 |          2 |         44 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/product_category_test.rb                                     | Ruby       |          3 |         24 |          2 |         29 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/product_content_test.rb                                      | Ruby       |          3 |         24 |          2 |         29 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/product_test.rb                                              | Ruby       |         91 |         26 |         21 |        138 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/product_video_test.rb                                        | Ruby       |          3 |         24 |          2 |         29 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/team_invitation_test.rb                                      | Ruby       |         66 |         28 |         14 |        108 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/models/user_test.rb                                                 | Ruby       |         76 |         22 |         17 |        115 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/test/test_helper.rb                                                      | Ruby       |          9 |          3 |          4 |         16 |
| /Users/<USER>/Desktop/micro-startups/rails-work/govtech-saas/zed-market/vendor/javascript/sortablejs.js                                          | JavaScript |      2,952 |        312 |        117 |      3,381 |
| Total                                                                                                                                              |            |     47,356 |      2,640 |     20,219 |     70,215 |
+----------------------------------------------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+