class UserMailer < ApplicationMailer
  default from: "<EMAIL>"

  def welcome(user, account = nil)
    @user = user
    @account = account || user.owned_accounts.first
    @login_url = new_session_url

    if @account&.confirmation_token
      @confirmation_url = confirm_account_url(token: @account.confirmation_token)
      @needs_confirmation = true
    else
      @needs_confirmation = false
    end

    mail(
      to: @user.email_address,
      subject: "Welcome to Platia!"
    )
  end

  def account_approved(user)
    @user = user
    @account = @user.owned_accounts.first || @user.accounts.first
    @dashboard_url = case @account&.account_type
    when "vendor"
                      vendors_root_url
    when "agency"
                      agencies_root_url
    else
                      root_url
    end

    mail(
      to: @user.email_address,
      subject: "Your Platia account has been approved!"
    )
  end

  def account_rejected(user, reason = nil)
    @user = user
    @reason = reason
    @contact_email = "<EMAIL>"

    mail(
      to: @user.email_address,
      subject: "Your Platia account has been rejected"
    )
  end

end
