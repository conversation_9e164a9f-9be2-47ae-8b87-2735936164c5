<% content_for :page_title, "Edit Account" %>

<div class="max-w-3xl">
  <!-- Account Information Form -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-6">Account Information</h3>
      
      <%= form_with model: @account, url: update_account_account_path, method: :patch, local: true, multipart: true, class: "space-y-6" do |form| %>
        <% if @account.errors.any? %>
          <div class="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4">
            <div class="flex">
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                  There were <%= pluralize(@account.errors.count, "error") %> with your submission:
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                  <ul role="list" class="list-disc space-y-1 pl-5">
                    <% @account.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div class="sm:col-span-2">
            <%= form.label :name, "#{@account.account_type.capitalize} Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.text_field :name, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" %>
          </div>

          <div>
            <%= form.label :website, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
              Website <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <%= form.url_field :website, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100", placeholder: "https://example.com" %>
          </div>

          <div>
            <%= form.label :headquarters_location, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
              Headquarters Location <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <%= form.text_field :headquarters_location, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100", placeholder: "City, State" %>
          </div>

          <div>
            <%= form.label :linkedin_url, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
              LinkedIn URL <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <%= form.url_field :linkedin_url, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100", placeholder: "https://linkedin.com/company/example" %>
          </div>

          <div class="sm:col-span-2">
            <%= form.label :description, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
              Description <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <%= form.rich_text_area :description, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100", rows: 4, placeholder: "Brief description of your organization..." %>
          </div>

          <div class="sm:col-span-2">
            <%= form.label :logo, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" do %>
              Logo <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            
            <div class="flex items-start space-x-6">
              <!-- Current Logo -->
              <div class="flex-shrink-0">
                <div class="w-24 h-24 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600">
                  <% if @account.logo.attached? %>
                    <%= image_tag @account.logo_small, 
                        class: "w-full h-full object-cover", 
                        alt: "#{@account.name} logo" %>
                  <% else %>
                    <div class="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500">
                      <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                      </svg>
                    </div>
                  <% end %>
                </div>
              </div>

              <!-- Upload Controls -->
              <div class="flex-1 min-w-0">
                <div class="mb-4">
                  <%= form.file_field :logo, 
                      accept: "image/jpeg,image/jpg,image/png,image/gif,image/webp,image/svg+xml",
                      class: "block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-blue-900/50 dark:file:text-blue-300 dark:hover:file:bg-blue-900/70" %>
                </div>
                
                <!-- File Requirements -->
                <div class="text-sm text-gray-500 dark:text-gray-400">
                  <p>• Maximum file size: 5MB</p>
                  <p>• Accepted formats: JPEG, PNG, GIF, WebP, SVG</p>
                  <p>• Recommended: Square image, at least 200x200 pixels</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3">
          <%= link_to "Cancel", account_path, class: "bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
          <%= form.submit "Update Account", class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
        </div>
      <% end %>
    </div>
  </div>
</div>