<% content_for :page_title, "Account Settings" %>

<div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Page Header -->
  <div class="mb-8">
    <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Account Settings</h1>
    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
      Manage your account information, team, and profile settings.
    </p>
  </div>

  <!-- Account Information Section -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mb-8">
    <div class="px-4 py-5 sm:p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Account Information</h3>
        <%= link_to "Edit Account", edit_account_account_path, 
            class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
      </div>
      <% if @user.accounts.any? %>
        <% account = @user.accounts.first %>
        <dl class="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Account Name</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= account.name %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Account Type</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
              <% if account.account_type == 'vendor' %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200">
                  Vendor
                </span>
              <% elsif account.account_type == 'agency' %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                  Government Agency
                </span>
              <% end %>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Headquarters</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= account.headquarters_location.present? ? account.headquarters_location : "Not provided" %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">LinkedIn</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
              <% if account.linkedin_url.present? %>
                <%= link_to account.linkedin_url, account.linkedin_url, target: '_blank', class: "text-blue-600 dark:text-blue-400 hover:underline" %>
              <% else %>
                Not provided
              <% end %>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
              <% if account.status == 'approved' %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                  Approved
                </span>
              <% else %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
                  <%= account.status&.titleize || 'Pending' %>
                </span>
              <% end %>
            </dd>
          </div>
        </dl>
        <% if account.description.present? %>
          <div class="mt-6">
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= account.description %></dd>
          </div>
        <% end %>
      <% else %>
        <div class="text-center py-8">
          <p class="text-sm text-gray-500 dark:text-gray-400">No account found.</p>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Team Management Section -->
  <% if @user.accounts.any? && current_user_account_admin? %>
    <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mb-8">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Team Members</h3>
          <%= link_to "Manage Team", team_members_account_path, 
              class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" %>
        </div>
        
        <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Email</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Job Title</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Department</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Role</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Joined</th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <% account.account_users.includes(:user).each do |account_user| %>
                <tr>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                    <%= account_user.user.full_name %>
                    <% if account_user.user == @user %>
                      <span class="text-xs text-gray-500 dark:text-gray-400">(You)</span>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    <%= account_user.user.email_address %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    <%= account_user.user.job_title.present? ? account_user.user.job_title : "Not provided" %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    <%= account_user.user.department.present? ? account_user.user.department : "Not provided" %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                      <% if account.owner_id == account_user.user.id %>
                        Owner
                      <% else %>
                        <%= account_user.role&.titleize || 'Member' %>
                      <% end %>
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    <%= account_user.joined_at&.strftime("%B %-d, %Y") || account_user.created_at.strftime("%B %-d, %Y") %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  <% end %>
  
  <!-- User Profile Section -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mb-8">
    <div class="px-4 py-5 sm:p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">User Profile</h3>
        <%= link_to "Edit Profile", edit_profile_account_path, 
            class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
      </div>
      
      <div class="flex items-start space-x-6 mb-6">
        <!-- Profile Photo -->
        <div class="w-16 h-16 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-700 flex-shrink-0">
          <% if @user.profile_photo.attached? %>
            <%= image_tag @user.profile_photo_avatar, 
                class: "w-full h-full object-cover", 
                alt: "#{@user.full_name}'s profile photo" %>
          <% else %>
            <div class="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
          <% end %>
        </div>
        <div class="flex-1">
          <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= @user.full_name %></h4>
          <p class="text-sm text-gray-500 dark:text-gray-400"><%= @user.email_address %></p>
        </div>
      </div>
      
      <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">First Name</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @user.first_name %></dd>
        </div>
        
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Name</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @user.last_name %></dd>
        </div>
        
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @user.email_address %></dd>
        </div>
        
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @user.phone.present? ? @user.phone : "Not provided" %></dd>
        </div>
        
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Job Title</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @user.job_title.present? ? @user.job_title : "Not provided" %></dd>
        </div>
        
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Department</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @user.department.present? ? @user.department : "Not provided" %></dd>
        </div>
        
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Organization</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @account&.name || "Not provided" %></dd>
        </div>
        
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Member Since</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @user.created_at.strftime("%B %-d, %Y") %></dd>
        </div>
      </dl>
    </div>
  </div>
  
  <!-- Priority Access Section -->
  <% if @user.accounts.any? && @user.accounts.first.account_type == 'vendor' && current_user_account_admin? %>
    <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mb-8">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center justify-between mb-6">
          <div>
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Priority Vendor Access</h3>
            <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
              Share this link with vendors to grant them immediate account approval and access.
            </p>
          </div>
        </div>
        
        <div class="mt-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Direct Signup URL</label>
          <div class="flex items-center space-x-3">
            <div class="flex-1">
              <input type="text" 
                     readonly 
                     id="signup-url-input"
                     value="<%= request.base_url %><%= direct_signup_path %>"
                     class="block w-full rounded-md border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 shadow-sm focus:border-blue-500 focus:ring-blue-500">
            </div>
            <button type="button" 
                    id="copy-url-btn"
                    class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
              Copy
            </button>
          </div>
          <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">
            Vendors who sign up through this link will have their accounts automatically approved and confirmed.
          </p>
        </div>
      </div>
    </div>
  <% end %>
  
  <!-- Sign Out Section -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mt-8">
    <div class="px-4 py-5 sm:p-6">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Sign Out</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Sign out of your account and return to the home page.
          </p>
        </div>
        <div class="ml-6">
          <%= link_to "Sign Out", session_path, 
              data: { "turbo-method": :delete },
              class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors" %>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  function initializeAccountPageHandlers() {
    const signupUrlInput = document.getElementById('signup-url-input');
    const copyUrlBtn = document.getElementById('copy-url-btn');
    
    if (!signupUrlInput || !copyUrlBtn) {
      return false; // Elements not ready yet
    }
    
    // Remove existing listeners to prevent duplicates
    if (window.accountPageListeners) {
      window.accountPageListeners.forEach(({ element, event, handler }) => {
        if (element) element.removeEventListener(event, handler);
      });
    }
    
    window.accountPageListeners = [];
    
    // Helper function to add listener and track it
    function addTrackedListener(element, event, handler) {
      if (element) {
        element.addEventListener(event, handler);
        window.accountPageListeners.push({ element, event, handler });
      }
    }
    
    // Input click handler - select all text
    const inputClickHandler = function() {
      this.select();
    };
    addTrackedListener(signupUrlInput, 'click', inputClickHandler);
    
    // Copy button click handler
    const copyClickHandler = function() {
      const url = '<%= request.base_url %><%= direct_signup_path %>';
      navigator.clipboard.writeText(url).then(() => {
        this.textContent = 'Copied!';
        setTimeout(() => {
          this.textContent = 'Copy';
        }, 2000);
      }).catch(err => {
        console.error('Failed to copy: ', err);
        // Fallback for older browsers
        signupUrlInput.select();
        document.execCommand('copy');
        this.textContent = 'Copied!';
        setTimeout(() => {
          this.textContent = 'Copy';
        }, 2000);
      });
    };
    addTrackedListener(copyUrlBtn, 'click', copyClickHandler);
    
    return true;
  }
  
  // Initialize on both DOMContentLoaded and turbo:load for Turbo compatibility
  document.addEventListener('DOMContentLoaded', initializeAccountPageHandlers);
  document.addEventListener('turbo:load', initializeAccountPageHandlers);
  
  // Fallback initialization with timeout for edge cases
  document.addEventListener('turbo:load', function() {
    setTimeout(function() {
      if (!initializeAccountPageHandlers()) {
        // Try again after another delay if still not ready
        setTimeout(initializeAccountPageHandlers, 100);
      }
    }, 50);
  });
</script>