<% content_for :page_title, "Account Settings" %>

<div class="max-w-3xl">
  <!-- Header -->
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Account Settings</h1>
    <%= link_to "Edit Account", edit_account_settings_path, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
  </div>

  <!-- Account Information -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mb-6">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-6">Account Information</h3>
      
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <!-- Logo -->
        <div class="sm:col-span-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Logo</label>
          <div class="w-24 h-24 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600">
            <% if @account.logo.attached? %>
              <%= image_tag @account.logo_small, 
                  class: "w-full h-full object-cover", 
                  alt: "#{@account.name} logo" %>
            <% else %>
              <div class="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Account Name -->
        <div class="sm:col-span-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300"><%= @account.account_type.capitalize %> Name</label>
          <p class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @account.name %></p>
        </div>

        <!-- Headquarters Location -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Headquarters Location</label>
          <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
            <%= @account.headquarters_location.presence || content_tag(:span, "Not provided", class: "text-gray-500 dark:text-gray-400") %>
          </p>
        </div>

        <!-- LinkedIn URL -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">LinkedIn URL</label>
          <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
            <% if @account.linkedin_url.present? %>
              <%= link_to @account.linkedin_url, @account.linkedin_url, target: "_blank", class: "text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300" %>
            <% else %>
              <span class="text-gray-500 dark:text-gray-400">Not provided</span>
            <% end %>
          </p>
        </div>

        <!-- Account Type -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Account Type</label>
          <p class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @account.account_type.capitalize %></p>
        </div>

        <!-- Status -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Status</label>
          <p class="mt-1">
            <% if @account.approved? %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200">
                Approved
              </span>
            <% else %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200">
                Pending Approval
              </span>
            <% end %>
          </p>
        </div>

        <!-- Description -->
        <div class="sm:col-span-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
          <div class="mt-1 text-sm text-gray-900 dark:text-gray-100">
            <% if @account.description.present? %>
              <%= @account.description %>
            <% else %>
              <span class="text-gray-500 dark:text-gray-400">No description provided</span>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>