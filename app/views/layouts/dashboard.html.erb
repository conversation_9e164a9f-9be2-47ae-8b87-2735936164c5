<!DOCTYPE html>
<html class="h-full bg-white dark:bg-gray-900">
  <head>
    <title><%= page_title(content_for(:page_title)) %></title>
    <meta name="description" content="<%= meta_description(content_for(:meta_description)) %>">
    <meta name="keywords" content="<%= meta_keywords(content_for(:meta_keywords)) %>">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    
    <!-- Discourage extension interference -->
    <meta name="format-detection" content="telephone=no">
    <meta name="color-scheme" content="light dark">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="<%= canonical_url(content_for(:canonical_url)) %>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<%= page_title(content_for(:page_title)) %>">
    <meta property="og:description" content="<%= meta_description(content_for(:meta_description)) %>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<%= canonical_url(content_for(:canonical_url)) %>">
    <meta property="og:image" content="<%= og_image(content_for(:og_image)) %>">
    <meta property="og:site_name" content="Platia">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<%= page_title(content_for(:page_title)) %>">
    <meta name="twitter:description" content="<%= meta_description(content_for(:meta_description)) %>">
    <meta name="twitter:image" content="<%= og_image(content_for(:og_image)) %>">
    
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>
    
    <!-- Structured Data -->
    <%= render_structured_data(structured_data_organization) %>
    <%= render_structured_data(structured_data_marketplace) %>
    <%= yield :structured_data if content_for?(:structured_data) %>

    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
    <%= Sentry.get_trace_propagation_meta.html_safe %>
  </head>

  <body class="h-full bg-white dark:bg-gray-900" data-controller="dark-mode">
    <div>
      <!-- Off-canvas menu for mobile, show/hide based on off-canvas menu state. -->
      <div class="relative z-50 lg:hidden" role="dialog" aria-modal="true" id="mobile-sidebar" style="display: none;">
        <!--
          Off-canvas menu backdrop, show/hide based on off-canvas menu state.

          Entering: "transition-opacity ease-linear duration-300"
            From: "opacity-0"
            To: "opacity-100"
          Leaving: "transition-opacity ease-linear duration-300"
            From: "opacity-100"
            To: "opacity-0"
        -->
        <div class="fixed inset-0 bg-gray-900/80 transition-opacity ease-linear duration-300 opacity-0" id="mobile-backdrop" aria-hidden="true"></div>

        <div class="fixed inset-0 flex">
          <!--
            Off-canvas menu, show/hide based on off-canvas menu state.

            Entering: "transition ease-in-out duration-300 transform"
              From: "-translate-x-full"
              To: "translate-x-0"
            Leaving: "transition ease-in-out duration-300 transform"
              From: "translate-x-0"
              To: "-translate-x-full"
          -->
          <div class="relative mr-16 flex w-full max-w-xs flex-1 transition ease-in-out duration-300 transform -translate-x-full" id="mobile-menu">
            <!--
              Close button, show/hide based on off-canvas menu state.

              Entering: "ease-in-out duration-300"
                From: "opacity-0"
                To: "opacity-100"
              Leaving: "ease-in-out duration-300"
                From: "opacity-100"
                To: "opacity-0"
            -->
            <div class="absolute left-full top-0 flex w-16 justify-center pt-5 transition ease-in-out duration-300 opacity-0" id="mobile-close">
              <button type="button" class="-m-2.5 p-2.5" id="close-sidebar-btn">
                <span class="sr-only">Close sidebar</span>
                <svg class="size-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <!-- Sidebar component, swap this element with another sidebar if you like -->
            <div class="flex grow flex-col gap-y-5 overflow-y-auto bg-white dark:bg-gray-900 px-6 pb-2">
              <div class="flex h-16 shrink-0 items-center">
                <%= link_to root_path, class: "flex items-center" do %>
                  <span class="text-2xl font-bold text-blue-600">Platia</span>
                <% end %>
              </div>
              <nav class="flex flex-1 flex-col">
                <ul role="list" class="flex flex-1 flex-col gap-y-7">
                  <li>
                    <%= render 'shared/dashboard_sidebar_nav' %>
                  </li>
                  <li class="-mx-6 mt-auto">
                    <button data-action="click->dark-mode#toggle" 
                            class="flex items-center gap-x-4 px-6 py-3 text-sm/6 font-semibold text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800 border-b border-gray-200 dark:border-gray-700 w-full text-left"
                            title="Toggle dark mode">
                      <!-- Sun Icon (visible in dark mode) -->
                      <svg data-dark-mode-target="sunIcon" class="size-6 shrink-0 text-gray-400 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                      </svg>
                      <!-- Moon Icon (visible in light mode) -->
                      <svg data-dark-mode-target="moonIcon" class="size-6 shrink-0 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 0 1 18.646 3.646 9.003 9.003 0 0 0 12 21a9.003 9.003 0 0 0 8.354-5.646z" />
                      </svg>
                      <span>Dark Mode</span>
                    </button>
                    <%= link_to marketplace_path, class: "flex items-center gap-x-4 px-6 py-3 text-sm/6 font-semibold text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800 border-b border-gray-200 dark:border-gray-700", data: { turbo: false } do %>
                      <svg class="size-6 shrink-0 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18" />
                      </svg>
                      <span>Back to Marketplace</span>
                    <% end %>
                    <%= link_to session_path, method: :delete, class: "flex items-center gap-x-4 px-6 py-3 text-sm/6 font-semibold text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800", data: { "turbo-method": :delete } do %>
                      <svg class="size-6 shrink-0 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75" />
                      </svg>
                      <span>Sign Out</span>
                    <% end %>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <!-- Static sidebar for desktop -->
      <div class="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
        <!-- Sidebar component, swap this element with another sidebar if you like -->
        <div class="flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 px-6">
          <div class="flex h-16 shrink-0 items-center">
            <%= link_to root_path, class: "flex items-center" do %>
              <span class="text-2xl font-bold text-blue-600">Platia</span>
            <% end %>
          </div>
          <nav class="flex flex-1 flex-col">
            <ul role="list" class="flex flex-1 flex-col gap-y-7">
              <li>
                <%= render 'shared/dashboard_sidebar_nav' %>
              </li>
              <li class="-mx-6 mt-auto">
                <button data-action="click->dark-mode#toggle" 
                        class="flex items-center gap-x-4 px-6 py-3 text-sm/6 font-semibold text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800 border-b border-gray-200 dark:border-gray-700 w-full text-left"
                        title="Toggle dark mode">
                  <!-- Sun Icon (visible in dark mode) -->
                  <svg data-dark-mode-target="sunIcon" class="size-6 shrink-0 text-gray-400 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                  <!-- Moon Icon (visible in light mode) -->
                  <svg data-dark-mode-target="moonIcon" class="size-6 shrink-0 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                  </svg>
                  <span>Dark Mode</span>
                </button>
                <%= link_to marketplace_path, class: "flex items-center gap-x-4 px-6 py-3 text-sm/6 font-semibold text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800 border-b border-gray-200 dark:border-gray-700", data: { turbo: false } do %>
                  <svg class="size-6 shrink-0 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18" />
                  </svg>
                  <span>Back to Marketplace</span>
                <% end %>
                <%= link_to session_path, method: :delete, class: "flex items-center gap-x-4 px-6 py-3 text-sm/6 font-semibold text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800", data: { "turbo-method": :delete } do %>
                  <svg class="size-6 shrink-0 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75" />
                  </svg>
                  <span>Sign Out</span>
                <% end %>
              </li>
            </ul>
          </nav>
        </div>
      </div>

      <!-- Mobile header -->
      <div class="sticky top-0 z-40 flex items-center gap-x-6 bg-white dark:bg-gray-900 px-4 py-4 shadow-sm sm:px-6 lg:hidden border-b border-gray-200 dark:border-gray-700">
        <button type="button" class="-m-2.5 p-2.5 text-gray-700 dark:text-gray-300 lg:hidden" id="open-sidebar-btn">
          <span class="sr-only">Open sidebar</span>
          <svg class="size-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true" data-slot="icon">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
          </svg>
        </button>
        <div class="flex-1 text-sm/6 font-semibold text-gray-900 dark:text-gray-100">
          <% if current_user&.admin? %>
            Admin Dashboard
          <% elsif current_user&.vendor? %>
            Vendor Dashboard
          <% elsif current_user&.agency? %>
            Agency Dashboard
          <% else %>
            Dashboard
          <% end %>
        </div>
        <div class="flex items-center space-x-4">
          <!-- Dark Mode Toggle -->
          <div>
            <button data-action="click->dark-mode#toggle" 
                    class="p-2 rounded-md text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                    title="Toggle dark mode">
              <!-- Sun Icon (visible in dark mode) -->
              <svg data-dark-mode-target="sunIcon" class="h-5 w-5 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
              <!-- Moon Icon (visible in light mode) -->
              <svg data-dark-mode-target="moonIcon" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
              </svg>
            </button>
          </div>
          <div class="relative">
            <button type="button" class="flex items-center gap-x-4 px-3 py-2 text-sm/6 font-semibold text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800" id="user-menu-button" aria-expanded="false" aria-haspopup="true">
              <span class="sr-only">Open user menu</span>
              <svg class="size-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275" />
              </svg>
            </button>
            <div class="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white dark:bg-gray-800 py-1 shadow-lg ring-1 ring-black/5 dark:ring-gray-700 focus:outline-none hidden" role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" id="user-menu">
              <% if current_user_account_admin? %>
                <%= link_to account_path, class: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700", role: "menuitem" do %>
                  Account Settings
                <% end %>
              <% end %>
              <%= link_to session_path, method: :delete, class: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700", role: "menuitem" do %>
                Sign out
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <main class="py-10 lg:pl-72 bg-white dark:bg-gray-900">
        <div class="px-4 sm:px-6 lg:px-8">

          <!-- Flash messages -->
          <% if notice %>
            <div class="mb-4 rounded-md bg-green-50 dark:bg-green-900/50 p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-green-800 dark:text-green-200"><%= notice %></p>
                </div>
              </div>
            </div>
          <% end %>

          <% if alert %>
            <div class="mb-4 rounded-md bg-red-50 dark:bg-red-900/50 p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-red-800 dark:text-red-200"><%= alert %></p>
                </div>
              </div>
            </div>
          <% end %>

          <!-- Your content -->
          <%= yield %>
        </div>
      </main>
    </div>

    <!-- Mobile sidebar toggle script -->
    <script>
      // Ensure the script runs after DOM is loaded
      function initializeDashboardNavigation() {
        const openSidebarBtn = document.getElementById('open-sidebar-btn');
        const closeSidebarBtn = document.getElementById('close-sidebar-btn');
        const mobileSidebar = document.getElementById('mobile-sidebar');
        const mobileBackdrop = document.getElementById('mobile-backdrop');
        const mobileMenu = document.getElementById('mobile-menu');
        const mobileClose = document.getElementById('mobile-close');
        const userMenuButton = document.getElementById('user-menu-button');
        const userMenu = document.getElementById('user-menu');

        // Debug: Log if elements are found
        console.log('Dashboard navigation elements:', { 
          openSidebarBtn: !!openSidebarBtn, 
          closeSidebarBtn: !!closeSidebarBtn, 
          mobileSidebar: !!mobileSidebar,
          mobileBackdrop: !!mobileBackdrop,
          mobileMenu: !!mobileMenu,
          mobileClose: !!mobileClose,
          userMenuButton: !!userMenuButton,
          userMenu: !!userMenu
        });

        // Mobile sidebar functions
        function showMobileSidebar() {
          console.log('Showing mobile sidebar');
          if (mobileSidebar) {
            mobileSidebar.style.display = 'block';
            // Force a reflow
            mobileSidebar.offsetHeight;
            requestAnimationFrame(() => {
              if (mobileBackdrop) {
                mobileBackdrop.classList.remove('opacity-0');
                mobileBackdrop.classList.add('opacity-100');
              }
              if (mobileMenu) {
                mobileMenu.classList.remove('-translate-x-full');
                mobileMenu.classList.add('translate-x-0');
              }
              if (mobileClose) {
                mobileClose.classList.remove('opacity-0');
                mobileClose.classList.add('opacity-100');
              }
            });
          }
        }

        function hideMobileSidebar() {
          console.log('Hiding mobile sidebar');
          if (mobileBackdrop) {
            mobileBackdrop.classList.remove('opacity-100');
            mobileBackdrop.classList.add('opacity-0');
          }
          if (mobileMenu) {
            mobileMenu.classList.remove('translate-x-0');
            mobileMenu.classList.add('-translate-x-full');
          }
          if (mobileClose) {
            mobileClose.classList.remove('opacity-100');
            mobileClose.classList.add('opacity-0');
          }
          setTimeout(() => {
            if (mobileSidebar) {
              mobileSidebar.style.display = 'none';
            }
          }, 300);
        }

        // Mobile sidebar event listeners
        if (openSidebarBtn) {
          openSidebarBtn.addEventListener('click', function(e) {
            console.log('Open sidebar clicked');
            e.preventDefault();
            e.stopPropagation();
            showMobileSidebar();
          });
        } else {
          console.warn('Open sidebar button not found');
        }

        if (closeSidebarBtn) {
          closeSidebarBtn.addEventListener('click', function(e) {
            console.log('Close sidebar clicked');
            e.preventDefault();
            e.stopPropagation();
            hideMobileSidebar();
          });
        }

        if (mobileBackdrop) {
          mobileBackdrop.addEventListener('click', function(e) {
            console.log('Backdrop clicked');
            hideMobileSidebar();
          });
        }

        // User menu toggle
        if (userMenuButton && userMenu) {
          userMenuButton.addEventListener('click', function(e) {
            console.log('User menu button clicked');
            e.preventDefault();
            e.stopPropagation();
            const isExpanded = userMenuButton.getAttribute('aria-expanded') === 'true';
            userMenuButton.setAttribute('aria-expanded', !isExpanded);
            userMenu.classList.toggle('hidden');
          });

          // Close user menu when clicking outside
          document.addEventListener('click', function(event) {
            if (!userMenuButton.contains(event.target) && !userMenu.contains(event.target)) {
              userMenu.classList.add('hidden');
              userMenuButton.setAttribute('aria-expanded', 'false');
            }
          });
        }
      }

      // Initialize when DOM is ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeDashboardNavigation);
      } else {
        initializeDashboardNavigation();
      }

      // Also initialize on Turbo load for Rails apps
      document.addEventListener('turbo:load', initializeDashboardNavigation);
    </script>
  </body>
</html>