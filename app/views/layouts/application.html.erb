<!DOCTYPE html>
<html>
  <head>
    <title><%= page_title(content_for(:page_title)) %></title>
    <meta name="description" content="<%= meta_description(content_for(:meta_description)) %>">
    <meta name="keywords" content="<%= meta_keywords(content_for(:meta_keywords)) %>">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    
    <!-- Discourage extension interference -->
    <meta name="format-detection" content="telephone=no">
    <meta name="color-scheme" content="light dark">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="<%= canonical_url(content_for(:canonical_url)) %>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<%= page_title(content_for(:page_title)) %>">
    <meta property="og:description" content="<%= meta_description(content_for(:meta_description)) %>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<%= canonical_url(content_for(:canonical_url)) %>">
    <meta property="og:image" content="<%= og_image(content_for(:og_image)) %>">
    <meta property="og:site_name" content="Platia">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<%= page_title(content_for(:page_title)) %>">
    <meta name="twitter:description" content="<%= meta_description(content_for(:meta_description)) %>">
    <meta name="twitter:image" content="<%= og_image(content_for(:og_image)) %>">
    
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>
    
    <!-- Structured Data -->
    <%= render_structured_data(structured_data_organization) %>
    <%= render_structured_data(structured_data_marketplace) %>
    <%= yield :structured_data if content_for?(:structured_data) %>

    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
    <%= Sentry.get_trace_propagation_meta.html_safe %>
  </head>

  <body class="min-h-screen flex flex-col bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors">
    <%= render 'shared/public_nav' %>
    
    <!-- Flash Messages -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
      <%= render 'shared/flash_messages' %>
    </div>
    
    <main class="flex-grow">
      <%= yield %>
    </main>
    
    <%= render 'shared/public_footer' %>
    
    <!-- Global error handler for browser extension conflicts -->
    <script>
      // Suppress common browser extension errors that don't affect functionality
      window.addEventListener('error', function(e) {
        // Check if error is from browser extension
        if (e.message && (
          e.message.includes('Cannot create item with duplicate id') ||
          e.message.includes('runtime.lastError') ||
          e.message.includes('Extension context invalidated') ||
          e.message.includes('chrome-extension:')
        )) {
          // Prevent the error from being logged to console
          e.preventDefault();
          e.stopPropagation();
          return true;
        }
      });
      
      // Also handle unhandled promise rejections from extensions
      window.addEventListener('unhandledrejection', function(e) {
        if (e.reason && e.reason.message && (
          e.reason.message.includes('Cannot create item with duplicate id') ||
          e.reason.message.includes('runtime.lastError') ||
          e.reason.message.includes('Extension context invalidated')
        )) {
          e.preventDefault();
          return true;
        }
      });
    </script>
  </body>
</html>
