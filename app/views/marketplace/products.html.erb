<% content_for :page_title, "Browse Products" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">Browse Products</h1>
    <p class="text-gray-600 dark:text-gray-400">
      Discover government technology solutions from verified vendors
    </p>
  </div>

  <!-- Filter Bar -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8 transition-colors">
    <%= form_with url: marketplace_products_path, method: :get, local: true, id: "filters-form", class: "flex flex-wrap items-end gap-4" do |form| %>
      
      <!-- Category Filter -->
      <div class="flex-1 min-w-64">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Filter by Category</label>
        <div class="grid grid-cols-1">
          <%= form.select :category_ids, 
              options_for_select([
                ['All Categories', '']
              ] + build_category_options(@categories_with_products), Array(params[:category_ids]).reject(&:blank?).first),
              {},
              { 
                class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 sm:text-sm",
                onchange: "document.getElementById('filters-form').submit();"
              } %>
          <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
            <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>

      <!-- Sort Filter -->
      <div class="min-w-48">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Sort by</label>
        <div class="grid grid-cols-1">
          <%= form.select :sort, 
              options_for_select(@sort_options, params[:sort]),
              { prompt: "Default" },
              { 
                class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 sm:text-sm",
                onchange: "document.getElementById('filters-form').submit();"
              } %>
          <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
            <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>

      <!-- Feature Filters -->
      <div class="flex flex-col space-y-3">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Features</label>
        
        <!-- Has Demo Video -->
        <div class="flex items-center">
          <%= form.check_box :has_demo_video, 
              { 
                checked: params[:has_demo_video] == '1', 
                onchange: "document.getElementById('filters-form').submit();",
                class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
              },
              '1', '' %>
          <label for="has_demo_video" class="ml-2 text-sm text-gray-700 dark:text-gray-300 cursor-pointer">
            Has demo video
          </label>
        </div>
        
        <!-- Has Case Studies -->
        <div class="flex items-center">
          <%= form.check_box :has_case_studies, 
              { 
                checked: params[:has_case_studies] == '1', 
                onchange: "document.getElementById('filters-form').submit();",
                class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700"
              },
              '1', '' %>
          <label for="has_case_studies" class="ml-2 text-sm text-gray-700 dark:text-gray-300 cursor-pointer">
            Has case studies
          </label>
        </div>
      </div>

      <!-- Clear Filters (only show if filters are applied) -->
      <% if params[:category_ids].present? || params[:sort].present? || params[:has_demo_video].present? || params[:has_case_studies].present? %>
        <div>
          <%= link_to "Clear Filters", marketplace_products_path, 
              class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
        </div>
      <% end %>

    <% end %>
  </div>

  <!-- Applied Filters -->
  <% if params[:category_ids].present? %>
    <div class="mb-6">
      <div class="flex flex-wrap gap-2">
        <% if params[:category_ids].present? %>
          <% Array(params[:category_ids]).reject(&:blank?).each do |category_id| %>
            <% category = @categories_with_products.find { |c| c.id == category_id.to_i } %>
            <% if category %>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 transition-colors">
                <%= category.name %>
                <%= link_to marketplace_products_path(request.query_parameters.except(:category_ids)), class: "ml-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors" do %>
                  ×
                <% end %>
              </span>
            <% end %>
          <% end %>
        <% end %>
      </div>
    </div>
  <% end %>

  <!-- Results Summary -->
  <div class="flex items-center justify-between mb-6">
    <p class="text-sm text-gray-500 dark:text-gray-400">
      Showing <%= @products.count %> product<%= @products.count == 1 ? '' : 's' %>
    </p>
  </div>

  <!-- Products Grid -->
  <% if @products.any? %>
    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
      <% @products.each do |product| %>
        <%= render 'shared/product_card', product: product %>
      <% end %>
    </div>
  <% else %>
    <div class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8v2a1 1 0 01-1 1H7a1 1 0 01-1-1V5a1 1 0 011-1h10a1 1 0 011 1zM9 7h6" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No products found</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Try adjusting your filters or search terms.
      </p>
      <% if params[:category_ids].present? || params[:sort].present? || params[:has_demo_video].present? || params[:has_case_studies].present? %>
        <div class="mt-4">
          <%= link_to "Clear all filters", marketplace_products_path, 
              class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
        </div>
      <% end %>
    </div>
  <% end %>
</div>