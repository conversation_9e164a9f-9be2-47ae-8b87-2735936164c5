<% content_for :page_title, @category.name %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Back Button -->
  <div class="mb-6">
    <%= link_to marketplace_categories_path, 
        class: "inline-flex items-center text-blue-600 hover:text-blue-700 font-medium" do %>
      <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
      </svg>
      Back to All Categories
    <% end %>
  </div>

  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-2">
      <%= @category.name %>
    </h1>
    <% if @category.description.present? %>
      <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl">
        <%= @category.description %>
      </p>
    <% end %>
  </div>

  <!-- Sort Options -->
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
    <%= form_with url: marketplace_category_path(@category), method: :get, local: true, class: "flex flex-col md:flex-row gap-4 items-center" do |form| %>
      <div class="flex items-center space-x-3">
        <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Sort by:</label>
        <div class="grid grid-cols-1 min-w-48">
          <%= form.select :sort, 
              options_for_select(@sort_options, params[:sort]),
              { include_blank: false },
              { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-gray-100 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 sm:text-sm transition-colors",
                onchange: "this.form.submit();" } %>
          <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
            <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>
      <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
        <%= pluralize(@products.count, 'product') %> found
      </div>
    <% end %>
  </div>

  <!-- Products -->
  <% if @products.any? %>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <% @products.each do |product| %>
        <%= render 'shared/product_card', product: product %>
      <% end %>
    </div>
  <% else %>
    <!-- Empty State -->
    <div class="text-center py-12">
      <div class="max-w-md mx-auto">
        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No products found</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          No products are available in this category yet.
        </p>
      </div>
    </div>
  <% end %>
</div>