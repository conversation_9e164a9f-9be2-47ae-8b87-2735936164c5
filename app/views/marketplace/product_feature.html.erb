<% content_for :page_title, "#{@product_feature.name} - #{@product.name}" %>
<% content_for :meta_description, "#{@product_feature.name} from #{@product.name} by #{@product.company_name}. #{strip_tags(@product_feature.description.to_s).truncate(120)}" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="lg:grid lg:grid-cols-4 lg:gap-8">
    
    <!-- Main Content -->
    <div class="lg:col-span-3">
      <!-- Breadcrumb -->
      <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-2">
          <li>
            <%= link_to marketplace_path, class: "text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors" do %>
              Marketplace
            <% end %>
          </li>
          <li>
            <svg class="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </li>
          <li>
            <%= link_to marketplace_products_path, class: "text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors" do %>
              Products
            <% end %>
          </li>
          <li>
            <svg class="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </li>
          <li>
            <%= link_to marketplace_product_path(@product), class: "text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors" do %>
              <%= truncate(@product.name, length: 30) %>
            <% end %>
          </li>
          <li>
            <svg class="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </li>
          <li class="text-gray-500 dark:text-gray-400">
            <%= truncate(@product_feature.name, length: 40) %>
          </li>
        </ol>
      </nav>

      <!-- Feature Header -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
        <div class="px-6 py-8">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2 transition-colors">
                <%= @product_feature.name %>
              </h1>
              <p class="text-lg text-gray-600 dark:text-gray-400 mb-4 transition-colors">
                Feature from <%= link_to @product.name, marketplace_product_path(@product), 
                          class: "text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-colors" %>
                by <%= @product.company_name %>
              </p>
              
              <% if @product_feature.description.present? %>
                <div class="prose max-w-none dark:prose-invert">
                  <%= simple_format(@product_feature.description) %>
                </div>
              <% end %>
            </div>
            
            <% if @product_feature.attachment.attached? && !@product_feature.image? && !@product_feature.video? %>
              <div class="ml-6">
                <%= link_to url_for(@product_feature.attachment), 
                    download: @product_feature.attachment.filename,
                    class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors" do %>
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Download File
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Feature Media -->
      <% if @product_feature.attachment.attached? %>
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
          <div class="px-6 py-8">
            <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">
              <%= @product_feature.image? ? 'Feature Image' : @product_feature.video? ? 'Feature Video' : 'Feature Attachment' %>
            </h2>
            
            <% if @product_feature.image? %>
              <div class="rounded-lg overflow-hidden">
                <%= image_tag @product_feature.attachment, 
                    class: "w-full h-auto object-contain max-h-96",
                    alt: @product_feature.name %>
              </div>
            <% elsif @product_feature.video? %>
              <div class="aspect-video bg-black rounded-lg overflow-hidden">
                <video 
                  controls 
                  controlsList="nodownload"
                  class="w-full h-full object-contain"
                  preload="metadata">
                  <source src="<%= url_for(@product_feature.attachment) %>" type="<%= @product_feature.attachment.content_type %>">
                  Your browser does not support the video tag.
                </video>
              </div>
            <% else %>
              <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-8 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Feature Attachment</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  <%= link_to "Download #{@product_feature.attachment.filename}", 
                      url_for(@product_feature.attachment), 
                      class: "text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300",
                      download: true %>
                </p>
              </div>
            <% end %>
          </div>
        </div>
      <% else %>
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
          <div class="px-6 py-8">
            <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-8 text-center">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No attachment</h3>
              <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">This feature doesn't have any attached media.</p>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Feature Details -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-colors">
        <div class="px-6 py-8">
          <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">Feature Details</h2>
          <dl class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <% if @product_feature.attachment.attached? %>
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">File Name</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product_feature.attachment.filename %></dd>
              </div>
              
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">File Size</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= number_to_human_size(@product_feature.attachment.byte_size) %></dd>
              </div>
              
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Content Type</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product_feature.attachment.content_type %></dd>
              </div>
            <% end %>
            
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Added</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product_feature.created_at.strftime("%B %-d, %Y") %></dd>
            </div>
          </dl>
        </div>
      </div>

    </div>

    <!-- Sidebar -->
    <div class="lg:col-span-1 mt-8 lg:mt-0">
      <div class="sticky top-6">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-colors">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">About This Product</h3>
          
          <div class="space-y-4">
            <div>
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Product</p>
              <p class="text-sm text-gray-900 dark:text-gray-100">
                <%= link_to @product.name, marketplace_product_path(@product), 
                    class: "text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" %>
              </p>
            </div>
            
            <% if @product.primary_category %>
              <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Category</p>
                <p class="text-sm text-gray-900 dark:text-gray-100"><%= @product.primary_category.name %></p>
              </div>
            <% end %>
            
            <div>
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Vendor</p>
              <p class="text-sm text-gray-900 dark:text-gray-100">
                <%= @product.company_name %>
              </p>
            </div>
            
            <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
              <%= link_to 'Back to Product', marketplace_product_path(@product), 
                  class: 'w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors' %>
            </div>
          </div>
        </div>
      </div>
    </div>
    
  </div>
</div>