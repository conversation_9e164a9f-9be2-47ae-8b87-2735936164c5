<!-- Product Screenshots Section -->
<% if product.product_screenshots.any? %>
  <!-- Section Heading -->
  <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">Product Screenshots</h2>
  
  <% if can_view_product_section?(product, :product_screenshots) %>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <% product.product_screenshots.ordered.each do |screenshot| %>
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden relative group">
              <!-- Screenshot Image -->
              <div class="aspect-w-16 aspect-h-9 bg-gray-100 dark:bg-gray-600 relative">
                <% if screenshot.image.attached? %>
                  <%= image_tag screenshot.image, 
                      class: "w-full h-48 object-cover",
                      alt: screenshot.title %>
                  
                  <!-- Action Buttons Overlay -->
                  <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                    <div class="flex space-x-3">
                      <!-- View Button -->
                      <button type="button" 
                              class="content-view-btn bg-white bg-opacity-90 hover:bg-opacity-100 p-3 rounded-full text-gray-700 hover:text-indigo-600 transition-all duration-200 shadow-lg"
                              data-product-id="<%= product.id %>"
                              data-content-type="product_screenshot"
                              data-content-id="<%= screenshot.id %>"
                              data-file-url="<%= url_for(screenshot.image) %>"
                              data-file-type="image"
                              data-title="<%= screenshot.title %>"
                              title="View Screenshot">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                          <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                        </svg>
                      </button>
                      
                      <!-- Download Button -->
                      <a href="<%= url_for(screenshot.image) %>" 
                         download
                         class="content-download-btn bg-white bg-opacity-90 hover:bg-opacity-100 p-3 rounded-full text-gray-700 hover:text-green-600 transition-all duration-200 shadow-lg"
                         data-product-id="<%= product.id %>"
                         data-content-type="product_screenshot"
                         data-content-id="<%= screenshot.id %>"
                         title="Download Screenshot">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3" />
                        </svg>
                      </a>
                    </div>
                  </div>
                <% else %>
                  <div class="w-full h-48 flex items-center justify-center">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                <% end %>
              </div>

              <!-- Screenshot Info -->
              <div class="p-4">
                <h3 class="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  <%= screenshot.title %>
                </h3>
                <% if screenshot.description.present? %>
                  <p class="text-sm text-gray-600 dark:text-gray-400">
                    <%= truncate(screenshot.description, length: 120) %>
                  </p>
                <% end %>
              </div>
            </div>
      <% end %>
    </div>
  <% else %>
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
      <div class="px-6 py-8">
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Access Restricted</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Only approved government agencies and departments can view this content.</p>
        </div>
      </div>
    </div>
  <% end %>
<% end %>