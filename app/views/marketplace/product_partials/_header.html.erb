<!-- Product Header -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
  <div class="px-6 py-8">
    <div class="flex items-start space-x-6">
      <!-- Product Logo -->
      <div class="flex-shrink-0">
        <% if product.account&.logo&.attached? %>
          <div class="w-20 h-20 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
            <%= image_tag product.account.logo_medium, 
                class: "max-w-full max-h-full object-contain", 
                alt: "#{product.account.name} logo" %>
          </div>
        <% else %>
          <div class="w-20 h-20 rounded-lg bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
            <svg class="w-10 h-10 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
        <% end %>
      </div>
      
      <!-- Product Info -->
      <div class="flex-1 min-w-0">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4 transition-colors">
          <%= product.name %>
          <% unless product.published? %>
            <% if can_view_draft_product?(product) %>
              <span class="ml-3 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
                <svg class="mr-1 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
                Draft Preview
              </span>
            <% end %>
          <% end %>
        </h1>
        
        <!-- Categories -->
        <div class="flex flex-wrap gap-2">
          <% product.categories.each do |category| %>
            <%= link_to marketplace_category_path(category), target: "_blank", class: "inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors" do %>
              <span><%= category.name %></span>
              <svg class="ml-1 h-2 w-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
            <% end %>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>