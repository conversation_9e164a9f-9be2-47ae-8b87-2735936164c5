<!-- Product Features Section -->
<% if product.product_features.any? %>
  <!-- Section Heading -->
  <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">Product Features</h2>
  
  <% if can_view_product_section?(product, :product_features) %>
    <div class="overflow-hidden shadow ring-1 ring-black/5 md:rounded-lg mb-8">
      <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
        <thead class="bg-gray-50 dark:bg-gray-900/50">
          <tr>
            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Feature</th>
            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Description</th>
            <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
              <span class="sr-only">Actions</span>
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
          <% product.product_features.ordered.each do |feature| %>
            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
              <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-gray-100 sm:pl-6">
                <div class="flex items-center">
                  <div class="h-10 w-10 flex-shrink-0">
                    <% if feature.attachment.attached? && feature.image? %>
                      <%= image_tag feature.attachment, class: "h-10 w-10 object-cover rounded-lg" %>
                    <% elsif feature.attachment.attached? && feature.video? %>
                      <div class="h-10 w-10 rounded-lg bg-purple-100 dark:bg-purple-900/50 flex items-center justify-center">
                        <svg class="h-5 w-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      </div>
                    <% else %>
                      <div class="h-10 w-10 rounded-lg bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center">
                        <svg class="h-5 w-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                    <% end %>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                      <%= feature.name %>
                    </div>
                    <% if feature.attachment.attached? %>
                      <div class="text-sm text-gray-500 dark:text-gray-400">
                        <%= feature.attachment_type&.capitalize %> attachment
                      </div>
                    <% end %>
                  </div>
                </div>
              </td>
              <td class="px-3 py-4 text-sm text-gray-600 dark:text-gray-400">
                <% if feature.description.present? %>
                  <%= truncate(strip_tags(feature.description), length: 100) %>
                <% else %>
                  <span class="text-gray-400 dark:text-gray-500 italic">No description</span>
                <% end %>
              </td>
              <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                <div class="flex items-center justify-end space-x-2">
                  <% if feature.attachment.attached? %>
                    <!-- View Icon Button -->
                    <button type="button" 
                            class="content-view-btn p-2 text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                            data-product-id="<%= product.id %>"
                            data-content-type="product_feature"
                            data-content-id="<%= feature.id %>"
                            data-file-url="<%= url_for(feature.attachment) %>"
                            data-file-type="<%= feature.image? ? 'image' : (feature.video? ? 'video' : 'file') %>"
                            data-title="<%= feature.name %>"
                            title="View Feature">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                      </svg>
                    </button>
                    
                    <!-- Download Icon Button -->
                    <a href="<%= url_for(feature.attachment) %>" 
                       download
                       class="content-download-btn p-2 text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                       data-product-id="<%= product.id %>"
                       data-content-type="product_feature"
                       data-content-id="<%= feature.id %>"
                       title="Download Feature">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3" />
                      </svg>
                    </a>
                  <% else %>
                    <span class="text-gray-400 dark:text-gray-500 italic text-sm">No file</span>
                  <% end %>
                </div>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  <% else %>
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
      <div class="px-6 py-8">
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Access Restricted</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Only approved government agencies and departments can view this content.</p>
        </div>
      </div>
    </div>
  <% end %>
<% end %>