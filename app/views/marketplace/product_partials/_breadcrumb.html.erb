<!-- Breadcrumb -->
<nav class="flex mb-6" aria-label="Breadcrumb">
  <ol class="flex items-center space-x-2">
    <li>
      <%= link_to marketplace_path, class: "text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors" do %>
        Marketplace
      <% end %>
    </li>
    <li>
      <svg class="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600 transition-colors" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
      </svg>
    </li>
    <li>
      <%= link_to marketplace_products_path, class: "text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors" do %>
        Products
      <% end %>
    </li>
    <li>
      <svg class="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600 transition-colors" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
      </svg>
    </li>
    <li class="text-gray-500 dark:text-gray-400 transition-colors">
      <%= truncate(product.name, length: 50) %>
    </li>
  </ol>
</nav>