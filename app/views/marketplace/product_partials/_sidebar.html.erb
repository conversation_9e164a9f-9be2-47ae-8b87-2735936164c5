<div class="lg:col-span-1">
  <div class="sticky top-6">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-colors">
      <div class="text-center mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
          Have questions, want more info or need a personalized demo?
        </h3>
        <p class="text-sm text-gray-600 dark:text-gray-400 transition-colors">
          <% if product.has_vendor_info? %>
            Connect directly with the vendor to learn more about this solution.
          <% else %>
            Get in touch to learn more about this solution.
          <% end %>
        </p>
      </div>


    <% if existing_interest %>
      <!-- Already expressed interest -->
      <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg p-4 mb-4 transition-colors">
        <div class="flex">
          <svg class="w-5 h-5 text-green-400 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <div>
            <h4 class="text-sm font-medium text-green-800 dark:text-green-200 transition-colors">Interest Submitted</h4>
            <p class="text-sm text-green-700 dark:text-green-300 mt-1 transition-colors">
              You expressed interest on <%= existing_interest.created_at.strftime("%B %-d, %Y") %>. 
              The vendor will contact you soon.
            </p>
          </div>
        </div>
      </div>
      
    <% else %>
      <!-- Form for government agency users -->
      <% if current_user&.has_agency_account? %>
        <%= form_with model: [product, lead], url: marketplace_product_leads_path(product), local: true, class: "space-y-4" do |form| %>
          <div>
            <%= form.label :contact_name, "Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" %>
            <%= form.text_field :contact_name, 
                value: current_user&.full_name,
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
          </div>

          <div>
            <%= form.label :contact_email, "Email", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" %>
            <%= form.email_field :contact_email, 
                value: current_user&.email_address,
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
          </div>

          <div>
            <%= form.label :contact_company, "Agency/Department", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" %>
            <%= form.text_field :contact_company, 
                value: current_user&.accounts&.first&.name,
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
          </div>

          <div>
            <%= form.label :contact_phone, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" do %>
              Phone <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <%= form.telephone_field :contact_phone, 
                value: current_user&.phone,
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
          </div>

          <div>
            <%= form.label :timeline, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
              Timeline <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <div class="mt-2 grid grid-cols-1">
              <%= form.select :timeline, 
                  options_for_select([
                    ['Immediate (1-30 days)', 'immediate'],
                    ['Short term (1-3 months)', 'short_term'],
                    ['Medium term (3-6 months)', 'medium_term'],
                    ['Long term (6+ months)', 'long_term'],
                    ['Just exploring', 'exploring']
                  ]),
                  { prompt: 'Select timeline' },
                  { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 dark:focus:outline-indigo-500 sm:text-sm" } %>
              <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>

          <div>
            <%= form.label :budget_range, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" do %>
              Budget Range <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <div class="mt-2 grid grid-cols-1">
              <%= form.select :budget_range, 
                  options_for_select([
                    ['Under $10K', 'under_10k'],
                    ['$10K - $50K', '10k_50k'],
                    ['$50K - $100K', '50k_100k'],
                    ['$100K - $500K', '100k_500k'],
                    ['$500K+', '500k_plus']
                  ]),
                  { prompt: 'Select budget range' },
                  { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 dark:focus:outline-indigo-500 sm:text-sm" } %>
              <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>

          <div>
            <%= form.label :message, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" do %>
              Message <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <%= form.text_area :message, 
                rows: 3,
                placeholder: "Tell us about your specific needs or questions...",
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 transition-colors" %>
          </div>

          <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors">
            Message Vendor
          </button>
        <% end %>
      <% else %>
        <!-- Form for non-agency users (vendors, admin accounts, or unauthenticated users) -->
        <%= form_with model: [product, lead], url: marketplace_product_leads_path(product), local: true, class: "space-y-4" do |form| %>
          <div>
            <%= form.label :contact_name, "Your Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" %>
            <%= form.text_field :contact_name, 
                value: current_user&.full_name,
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
          </div>

          <div>
            <%= form.label :contact_email, "Email", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" %>
            <%= form.email_field :contact_email, 
                value: current_user&.email_address,
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
          </div>

          <div>
            <%= form.label :contact_company, "Agency/Department", class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" %>
            <%= form.text_field :contact_company, 
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
          </div>

          <div>
            <%= form.label :contact_phone, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" do %>
              Phone <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <%= form.telephone_field :contact_phone, 
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
          </div>

          <div>
            <%= form.label :timeline, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" do %>
              Timeline <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <div class="mt-2 grid grid-cols-1">
              <%= form.select :timeline, 
                  options_for_select([
                    ['Immediate (1-30 days)', 'immediate'],
                    ['Short term (1-3 months)', 'short_term'],
                    ['Medium term (3-6 months)', 'medium_term'],
                    ['Long term (6+ months)', 'long_term'],
                    ['Just exploring', 'exploring']
                  ]),
                  { prompt: 'Select timeline' },
                  { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 dark:focus:outline-indigo-500 sm:text-sm border-gray-300 dark:border-gray-600 transition-colors" } %>
              <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>

          <div>
            <%= form.label :budget_range, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" do %>
              Budget Range <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <div class="mt-2 grid grid-cols-1">
              <%= form.select :budget_range, 
                  options_for_select([
                    ['Under $10K', 'under_10k'],
                    ['$10K - $50K', '10k_50k'],
                    ['$50K - $100K', '50k_100k'],
                    ['$100K - $500K', '100k_500k'],
                    ['$500K+', '500k_plus']
                  ]),
                  { prompt: 'Select budget range' },
                  { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 dark:focus:outline-indigo-500 sm:text-sm border-gray-300 dark:border-gray-600 transition-colors" } %>
              <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>

          <div>
            <%= form.label :message, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 transition-colors" do %>
              Message <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
            <% end %>
            <%= form.text_area :message, 
                rows: 3,
                placeholder: "Tell us about your specific needs or questions...",
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder:text-gray-500 dark:placeholder:text-gray-400 transition-colors" %>
          </div>

          <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors">
            Message Vendor
          </button>
        <% end %>
      <% end %>
    <% end %>

    <!-- Company Info -->
    <% if company %>
      <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 transition-colors">
        <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-3 transition-colors">About the Vendor</h4>
        
        <div class="text-sm text-gray-600 dark:text-gray-400 space-y-2 transition-colors">
          <p><strong><%= company.name %></strong></p>
          <% if company.headquarters_location.present? %>
            <p><%= company.headquarters_location %></p>
          <% end %>
          <% if product.website.present? %>
            <p>
              <%= link_to "Website", product.website, 
                  target: "_blank", 
                  class: "text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" %>
            </p>
          <% end %>
        </div>
      </div>
    <% else %>
      <!-- Claim Profile Section -->
      <div class="mt-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800">
        <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">Is this your product?</h4>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
          This profile was created by our team. If this is your company's product, you can claim ownership.
        </p>
        <%= link_to "Claim this profile", new_product_claim_path(product), 
            class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors" %>
      </div>
    <% end %>

  </div>
</div>
</div>