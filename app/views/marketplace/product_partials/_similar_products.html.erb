<!-- Similar Products -->
<% if similar_products.any? %>
  <div class="mb-8">
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 transition-colors">Similar products</h2>
      <% if product.primary_category %>
        <%= link_to marketplace_category_path(product.primary_category), 
            class: "text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-colors" do %>
          View all similar products →
        <% end %>
      <% end %>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <% similar_products.each do |similar_product| %>
        <div class="group">
          <%= link_to marketplace_product_path(similar_product), 
              class: "block border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-md transition-all" do %>
            <h3 class="font-medium text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 mb-2 transition-colors">
              <%= truncate(similar_product.name, length: 40) %>
            </h3>
            <% if similar_product.description.present? %>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-2 transition-colors">
                <%= truncate(strip_tags(similar_product.description), length: 80) %>
              </p>
            <% end %>
            <!-- Categories -->
            <% if similar_product.categories.any? %>
              <div class="flex flex-wrap gap-1 mt-2">
                <% similar_product.categories.limit(2).each do |category| %>
                  <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300">
                    <%= category.name %>
                  </span>
                <% end %>
                <% if similar_product.categories.count > 2 %>
                  <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400">
                    +<%= similar_product.categories.count - 2 %>
                  </span>
                <% end %>
              </div>
            <% end %>
          <% end %>
        </div>
      <% end %>
    </div>
  </div>
<% end %>