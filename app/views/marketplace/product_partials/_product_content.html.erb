<!-- Product Content Section -->
<% if product_content.any? %>
  <!-- Section Heading -->
  <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">Product Content</h2>
  
  <% if can_view_product_section?(product, :product_content) %>
    <div class="overflow-hidden shadow ring-1 ring-black/5 md:rounded-lg mb-8">
      <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
        <thead class="bg-gray-50 dark:bg-gray-900/50">
          <tr>
            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Content</th>
            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Type</th>
            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Description</th>
            <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
              <span class="sr-only">Actions</span>
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
              <% product_content.each do |content| %>
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                  <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-gray-100 sm:pl-6">
                    <div class="flex items-center">
                      <div class="h-10 w-10 flex-shrink-0">
                        <div class="h-10 w-10 rounded-lg <%= content.file_type == 'pdf' ? 'bg-red-100 dark:bg-red-900/50' : 'bg-purple-100 dark:bg-purple-900/50' %> flex items-center justify-center">
                          <% if content.file_type == 'pdf' %>
                            <svg class="h-5 w-5 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                            </svg>
                          <% else %>
                            <svg class="h-5 w-5 text-purple-600 dark:text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
                            </svg>
                          <% end %>
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                          <%= content.title %>
                        </div>
                        <% if content.file.attached? %>
                          <div class="text-sm text-gray-500 dark:text-gray-400">
                            <%= number_to_human_size(content.file.byte_size) %>
                          </div>
                        <% end %>
                      </div>
                    </div>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= content.file_type == 'pdf' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' : 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200' %>">
                      <%= content.file_type&.upcase %>
                    </span>
                  </td>
                  <td class="px-3 py-4 text-sm text-gray-600 dark:text-gray-400">
                    <% if content.description.present? %>
                      <%= truncate(strip_tags(content.description), length: 100) %>
                    <% else %>
                      <span class="text-gray-400 dark:text-gray-500 italic">No description</span>
                    <% end %>
                  </td>
                  <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                    <div class="flex items-center justify-end space-x-2">
                      <% if content.file.attached? %>
                        <!-- View Icon Button -->
                        <button type="button" 
                                class="content-view-btn p-2 text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 transition-colors rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                                data-product-id="<%= product.id %>"
                                data-content-type="product_content"
                                data-content-id="<%= content.id %>"
                                data-file-url="<%= url_for(content.file) %>"
                                data-file-type="<%= content.file_type %>"
                                data-title="<%= content.title %>"
                                title="View Content">
                          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                            <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                          </svg>
                        </button>
                        
                        <% unless content.file_type == 'video' %>
                          <!-- Download Icon Button -->
                          <a href="<%= url_for(content.file) %>" 
                             download
                             class="content-download-btn p-2 text-gray-400 hover:text-green-600 dark:hover:text-green-400 transition-colors rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                             data-product-id="<%= product.id %>"
                             data-content-type="product_content"
                             data-content-id="<%= content.id %>"
                             title="Download Content">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
                              <path stroke-linecap="round" stroke-linejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3" />
                            </svg>
                          </a>
                        <% end %>
                      <% else %>
                        <span class="text-gray-400 dark:text-gray-500 italic text-sm">No file</span>
                      <% end %>
                    </div>
                  </td>
                </tr>
              <% end %>
        </tbody>
      </table>
    </div>
  <% else %>
    <div class="text-center py-12 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Access Restricted</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Only approved government agencies and departments can view this content.</p>
    </div>
  <% end %>
<% end %>