<!-- Products from the same vendor -->
<% if product.has_vendor_info? && related_products.any? %>
  <div class="mb-8">
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 transition-colors">
        More from <%= product.account&.name %>
      </h2>
      <% if product.account&.products&.published&.where&.not(id: product.id)&.count.to_i > 3 %>
        <%= link_to marketplace_vendor_products_path(product.account), 
            class: "text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-colors" do %>
          View all products by <%= product.account&.name %> →
        <% end %>
      <% end %>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <% related_products.each do |related_product| %>
        <div class="group">
          <%= link_to marketplace_product_path(related_product), 
              class: "block border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-md transition-all" do %>
            <h3 class="font-medium text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 mb-2 transition-colors">
              <%= truncate(related_product.name, length: 40) %>
            </h3>
            <% if related_product.description.present? %>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-2 transition-colors">
                <%= truncate(strip_tags(related_product.description), length: 80) %>
              </p>
            <% end %>
            <!-- Categories -->
            <% if related_product.categories.any? %>
              <div class="flex flex-wrap gap-1 mt-2">
                <% related_product.categories.limit(2).each do |category| %>
                  <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                    <%= category.name %>
                  </span>
                <% end %>
                <% if related_product.categories.count > 2 %>
                  <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                    +<%= related_product.categories.count - 2 %>
                  </span>
                <% end %>
              </div>
            <% end %>
          <% end %>
        </div>
      <% end %>
    </div>
  </div>
<% end %>