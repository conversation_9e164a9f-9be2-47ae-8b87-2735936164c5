<!-- Featured Video Section -->
<!-- Section Heading -->
<h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">Product Demo</h2>

<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
  <div class="px-6 py-8">
    <% if product.featured_video.attached? %>
      <% if can_view_product_section?(product, :featured_video) %>
        <div class="aspect-w-16 aspect-h-9 bg-gray-900 rounded-lg overflow-hidden">
          <video 
            id="featuredVideo"
            controls 
            controlsList="nodownload"
            class="w-full h-full object-cover no-context-menu"
            preload="metadata"
            data-product-id="<%= product.id %>"
            oncontextmenu="return false;">
            <source src="<%= url_for(product.featured_video) %>" type="<%= product.featured_video.content_type %>">
            Your browser does not support the video tag.
          </video>
        </div>
      <% else %>
        <% if product.featured_video_preview.attached? %>
          <!-- Video Preview with Play Button Overlay -->
          <div class="aspect-w-16 aspect-h-9 bg-gray-900 rounded-lg overflow-hidden relative cursor-pointer" onclick="showVideoAccessModal()">
            <!-- Video Preview Image -->
            <img 
              src="<%= url_for(product.featured_video_preview) %>" 
              alt="Video Preview" 
              class="w-full h-full object-cover">
            
            <!-- Faded Gray Overlay -->
            <div class="absolute inset-0 bg-gray-500/50"></div>
            
            <!-- Play Button -->
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="bg-white dark:bg-gray-800 rounded-full p-4 shadow-lg hover:scale-110 transition-transform duration-200">
                <svg class="h-8 w-8 text-gray-900 dark:text-gray-100 ml-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
            
            <!-- Access Notice -->
            <div class="absolute bottom-4 left-4 right-4">
              <div class="bg-black bg-opacity-60 rounded-lg px-3 py-2">
                <p class="text-white text-sm font-medium">Restricted Content</p>
                <p class="text-gray-200 text-xs">Click to learn about access</p>
              </div>
            </div>
          </div>
        <% else %>
          <!-- Fallback when no preview is available -->
          <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Access Restricted</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Only approved government agencies and departments can view this content.</p>
          </div>
        <% end %>
      <% end %>
    <% else %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No Demo Video Available</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">This product doesn't have a demo video yet.</p>
      </div>
    <% end %>
  </div>
</div>