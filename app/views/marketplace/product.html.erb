<% content_for :page_title, @product.name %>
<% content_for :meta_description, "#{@product.name} by #{@product.company_name}. #{strip_tags(@product.description.to_s).truncate(120)}" %>
<% content_for :meta_keywords, [@product.category_names, @product.feature_names, 'government technology', 'govtech solution'].flatten.compact %>

<% content_for :structured_data do %>
  <%= render_structured_data(structured_data_product(@product)) %>
<% end %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="lg:grid lg:grid-cols-4 lg:gap-8">
  
    <!-- Main Content -->
    <div class="lg:col-span-3">
      
      <%= render 'marketplace/product_partials/breadcrumb', product: @product %>
      
      <%= render 'marketplace/product_partials/header', product: @product %>

      <%= render 'marketplace/product_partials/featured_video', product: @product %>
      
      <%= render 'marketplace/product_partials/description_and_pricing', product: @product %>
      
      <%= render 'marketplace/product_partials/product_features', product: @product %>
      
      <%= render 'marketplace/product_partials/product_customers', product: @product %>
      
      <%= render 'marketplace/product_partials/product_videos', 
          product: @product, 
          product_videos: @product_videos %>
      
      <%= render 'marketplace/product_partials/product_screenshots', product: @product %>
      
      <%= render 'marketplace/product_partials/case_studies', 
          product: @product, 
          case_studies: @case_studies %>
      
      <%= render 'marketplace/product_partials/sample_contracts', 
          product: @product, 
          product_contracts: @product_contracts %>
      
      <%= render 'marketplace/product_partials/product_content', 
          product: @product, 
          product_content: @product_content %>
      
      <%= render 'marketplace/product_partials/related_products', 
          product: @product, 
          related_products: @related_products %>
      
      <%= render 'marketplace/product_partials/similar_products', 
          product: @product, 
          similar_products: @similar_products %>
      
    </div>
    
    <!-- Sidebar -->
    <div class="lg:col-span-1 mt-8 lg:mt-0">
      <!-- Save to List Button (Agency Users Only) -->
      <% if current_user&.has_agency_account? %>
        <div class="mb-6">
          <button type="button" 
                  data-product-id="<%= @product.id %>"
                  data-product-name="<%= @product.name %>"
                  class="save-to-list-btn w-full inline-flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors shadow-sm">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
            </svg>
            Save to List
          </button>
        </div>
      <% end %>
      
      <%= render 'marketplace/product_partials/sidebar', 
          product: @product,
          existing_interest: @existing_interest,
          lead: @lead,
          company: @company %>
    </div>
    
  </div>
</div>

<% if current_user&.has_agency_account? %>
  <!-- Modal for Save to List -->
  <div id="saveToListModal" class="hidden fixed inset-0 z-50 overflow-y-auto" aria-labelledby="save-to-list-modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 bg-gray-500/50 transition-opacity" aria-hidden="true" onclick="closeSaveToListModal()"></div>
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
      <div class="relative inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full">
        <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="save-to-list-modal-title">Save to List</h3>
            <button type="button" class="bg-white dark:bg-gray-800 rounded-md text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none" onclick="closeSaveToListModal()">
              <span class="sr-only">Close</span>
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <!-- Existing Lists -->
          <div id="existingLists" class="mb-4">
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Your Lists</h4>
            <div id="listsContainer" class="space-y-2 max-h-40 overflow-y-auto">
              <!-- Lists will be populated here -->
            </div>
          </div>
          
          <!-- Manage Lists Link -->
          <div class="border-t border-gray-200 dark:border-gray-700 pt-4 text-center">
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Need to create a new list?</p>
            <a href="/agencies/lists" target="_blank" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Manage Lists
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Modal for content viewing (shared across all sections) -->
  <div id="contentModal" class="hidden fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
      <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
        <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title"></h3>
            <button type="button" class="bg-white dark:bg-gray-800 rounded-md text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none" onclick="closeContentModal()">
              <span class="sr-only">Close</span>
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div id="modalContent" class="mt-2"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Video Access Modal -->
  <div id="videoAccessModal" class="hidden fixed inset-0 z-50 overflow-y-auto" aria-labelledby="video-access-modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 bg-gray-500/50 transition-opacity" aria-hidden="true" onclick="closeVideoAccessModal()"></div>
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
      <div class="relative inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full">
        <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="video-access-modal-title">Video Access Restricted</h3>
            <button type="button" class="bg-white dark:bg-gray-800 rounded-md text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none" onclick="closeVideoAccessModal()">
              <span class="sr-only">Close</span>
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-gray-700 dark:text-gray-300 mb-4">
                This video is available for approved government agencies and department employees only. 
                Sign up for an account to request access to this content.
              </p>
              
              <div class="flex flex-col sm:flex-row gap-3">
                <a href="<%= new_registration_path %>" 
                   class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                  <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                  </svg>
                  Sign Up for Account
                </a>
                
                <button type="button" 
                        onclick="closeVideoAccessModal()"
                        class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                  Maybe Later
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Content viewing functionality
    document.addEventListener('DOMContentLoaded', function() {
      // Handle save to list button
      const saveToListBtn = document.querySelector('.save-to-list-btn');
      console.log('Save to list button found:', saveToListBtn);
      if (saveToListBtn) {
        saveToListBtn.addEventListener('click', function(e) {
          e.preventDefault();
          console.log('Save to list button clicked');
          const productId = this.dataset.productId;
          const productName = this.dataset.productName;
          console.log('Product data:', productId, productName);
          openSaveToListModal(productId, productName);
        });
      } else {
        console.log('Save to list button not found');
      }

      // Handle content view buttons
      document.querySelectorAll('.content-view-btn').forEach(button => {
        button.addEventListener('click', function(e) {
          e.preventDefault();
          const productId = this.dataset.productId;
          const contentType = this.dataset.contentType;
          const contentId = this.dataset.contentId;
          const fileUrl = this.dataset.fileUrl;
          const fileType = this.dataset.fileType;
          const title = this.dataset.title;
          
          openContentModal(title, fileUrl, fileType);
          trackContentView(productId, contentType, contentId);
        });
      });

      // Handle content download buttons
      document.querySelectorAll('.content-download-btn').forEach(button => {
        button.addEventListener('click', function(e) {
          const productId = this.dataset.productId;
          const contentType = this.dataset.contentType;
          const contentId = this.dataset.contentId;
          
          trackContentDownload(productId, contentType, contentId);
        });
      });

      // Video tracking
      const featuredVideo = document.getElementById('featuredVideo');
      if (featuredVideo) {
        let watchDuration = 0;
        let lastUpdateTime = 0;

        featuredVideo.addEventListener('play', function() {
          const productId = this.dataset.productId;
          trackVideoPlay(productId, 'featured_video', null);
        });

        featuredVideo.addEventListener('timeupdate', function() {
          watchDuration = this.currentTime;
          
          // Update duration every 10 seconds
          if (watchDuration - lastUpdateTime >= 10) {
            const productId = this.dataset.productId;
            updateVideoWatchDuration(productId, 'featured_video', null, watchDuration);
            lastUpdateTime = watchDuration;
          }
        });
      }
      
      // Add right-click protection to all videos
      function addVideoProtection() {
        document.querySelectorAll('video').forEach(video => {
          video.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            return false;
          });
          
          // Also prevent drag and drop
          video.addEventListener('dragstart', function(e) {
            e.preventDefault();
            return false;
          });
        });
      }

      // Apply protection to existing videos and new ones
      addVideoProtection();
      
      // Use MutationObserver to apply protection to dynamically added videos
      const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
          if (mutation.type === 'childList') {
            addVideoProtection();
          }
        });
      });
      
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    });

    function openContentModal(title, fileUrl, fileType) {
      const modal = document.getElementById('contentModal');
      const modalTitle = document.getElementById('modal-title');
      const modalContent = document.getElementById('modalContent');
      
      modalTitle.textContent = title;
      
      if (fileType === 'image') {
        modalContent.innerHTML = `<img src="${fileUrl}" alt="${title}" class="max-w-full h-auto rounded-lg">`;
      } else if (fileType === 'video') {
        modalContent.innerHTML = `
          <video controls controlsList="nodownload" class="w-full max-h-96 rounded-lg no-context-menu" oncontextmenu="return false;">
            <source src="${fileUrl}" type="video/mp4">
            Your browser does not support the video tag.
          </video>
        `;
      } else if (fileType === 'pdf') {
        modalContent.innerHTML = `
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">PDF Document</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Click below to view or download the PDF document.</p>
            <a href="${fileUrl}" target="_blank" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
              View PDF
            </a>
          </div>
        `;
      } else {
        modalContent.innerHTML = `
          <div class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">File</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Click below to download the file.</p>
            <a href="${fileUrl}" download class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
              <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Download
            </a>
          </div>
        `;
      }
      
      modal.classList.remove('hidden');
    }

    function closeContentModal() {
      document.getElementById('contentModal').classList.add('hidden');
    }

    // Analytics tracking functions
    function trackContentView(productId, contentType, contentId) {
      fetch(`/marketplace/products/${productId}/track_content_view`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        },
        body: JSON.stringify({
          content_type: contentType,
          content_id: contentId
        })
      });
    }

    function trackContentDownload(productId, contentType, contentId) {
      fetch(`/marketplace/products/${productId}/track_content_download`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        },
        body: JSON.stringify({
          content_type: contentType,
          content_id: contentId
        })
      });
    }

    function trackVideoPlay(productId, contentType, contentId) {
      fetch(`/marketplace/products/${productId}/track_video_play`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        },
        body: JSON.stringify({
          content_type: contentType,
          content_id: contentId
        })
      });
    }

    function updateVideoWatchDuration(productId, contentType, contentId, duration) {
      fetch(`/marketplace/products/${productId}/update_video_watch_duration`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        },
        body: JSON.stringify({
          content_type: contentType,
          content_id: contentId,
          duration: duration
        })
      });
    }

    // Save to List functionality
    let currentProductId = null;
    let currentProductName = null;

    function openSaveToListModal(productId, productName) {
      console.log('Opening modal for product:', productId, productName);
      currentProductId = productId;
      currentProductName = productName;
      
      const modal = document.getElementById('saveToListModal');
      const modalTitle = document.getElementById('save-to-list-modal-title');
      
      if (!modal) {
        console.error('Modal element not found');
        return;
      }
      
      if (!modalTitle) {
        console.error('Modal title element not found');
        return;
      }
      
      modalTitle.textContent = `Save "${productName}" to List`;
      
      // Load existing lists
      loadUserLists(productId);
      
      modal.classList.remove('hidden');
      console.log('Modal should be visible now');
    }

    function closeSaveToListModal() {
      document.getElementById('saveToListModal').classList.add('hidden');
      currentProductId = null;
      currentProductName = null;
    }

    function loadUserLists(productId) {
      console.log('Loading lists for product:', productId);
      const container = document.getElementById('listsContainer');
      
      if (!container) {
        console.error('Lists container element not found');
        return;
      }
      
      // Show loading state
      container.innerHTML = '<p class="text-sm text-gray-500 dark:text-gray-400">Loading lists...</p>';
      
      fetch(`/lists/for_product/${productId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        }
      })
      .then(response => {
        console.log('Response status:', response.status);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then(lists => {
        console.log('Loaded lists:', lists);
        
        if (lists.length === 0) {
          container.innerHTML = '<p class="text-sm text-gray-500 dark:text-gray-400 italic">No lists yet. Create your first list below.</p>';
        } else {
          container.innerHTML = lists.map(list => `
            <div class="flex items-center justify-between p-2 border border-gray-200 dark:border-gray-600 rounded-md">
              <div class="flex-1">
                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">${list.name}</div>
                ${list.description ? `<div class="text-xs text-gray-500 dark:text-gray-400">${list.description}</div>` : ''}
              </div>
              <button 
                onclick="toggleProductInList(${list.id}, '${list.name}', ${list.has_product})"
                class="ml-2 px-3 py-1 text-xs font-medium rounded-md ${
                  list.has_product 
                    ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' 
                    : 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800'
                }"
                ${list.has_product ? 'disabled' : ''}
              >
                ${list.has_product ? 'Added' : 'Add'}
              </button>
            </div>
          `).join('');
        }
      })
      .catch(error => {
        console.error('Error loading lists:', error);
        document.getElementById('listsContainer').innerHTML = '<p class="text-sm text-red-500">Error loading lists. Please try again.</p>';
      });
    }

    function toggleProductInList(listId, listName, hasProduct) {
      if (hasProduct) return; // Already in list
      
      fetch(`/lists/${listId}/items`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        },
        body: JSON.stringify({
          product_id: currentProductId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Show success message
          showToast(`"${currentProductName}" added to "${listName}"`, 'success');
          
          // Close modal
          closeSaveToListModal();
        } else {
          showToast(data.errors ? data.errors.join(', ') : 'Failed to add to list', 'error');
        }
      })
      .catch(error => {
        console.error('Error adding to list:', error);
        showToast('Error adding to list. Please try again.', 'error');
      });
    }


    // Video access modal functions
    function showVideoAccessModal() {
      document.getElementById('videoAccessModal').classList.remove('hidden');
    }

    function closeVideoAccessModal() {
      document.getElementById('videoAccessModal').classList.add('hidden');
    }

    // Make showVideoAccessModal available globally
    window.showVideoAccessModal = showVideoAccessModal;

    // Simple toast notification function
    function showToast(message, type = 'info') {
      const toast = document.createElement('div');
      toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-md text-white font-medium transition-opacity duration-300 ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 
        'bg-blue-500'
      }`;
      toast.textContent = message;
      
      document.body.appendChild(toast);
      
      // Fade out and remove after 3 seconds
      setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => {
          document.body.removeChild(toast);
        }, 300);
      }, 3000);
    }
  </script>
<% end %>