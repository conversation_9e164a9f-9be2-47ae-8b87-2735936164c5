<% content_for :page_title, "Products by #{@vendor.name}" %>
<% content_for :meta_description, "Browse all products from #{@vendor.name}. Find government technology solutions and services." %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="mb-8">
    <nav class="flex mb-4" aria-label="Breadcrumb">
      <ol class="flex items-center space-x-2">
        <li>
          <%= link_to marketplace_path, class: "text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors" do %>
            Marketplace
          <% end %>
        </li>
        <li>
          <svg class="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
        </li>
        <li>
          <%= link_to marketplace_products_path, class: "text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors" do %>
            Products
          <% end %>
        </li>
        <li>
          <svg class="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
          </svg>
        </li>
        <li class="text-gray-500 dark:text-gray-400">
          <%= @vendor.name %>
        </li>
      </ol>
    </nav>
    
    <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">Products by <%= @vendor.name %></h1>
    
    <% if @vendor.description.present? %>
      <p class="text-lg text-gray-600 dark:text-gray-400 mb-6">
        <%= @vendor.description %>
      </p>
    <% end %>
    
    <div class="flex items-center justify-between">
      <p class="text-sm text-gray-500 dark:text-gray-400">
        Showing <%= @products.count %> product<%= @products.count == 1 ? '' : 's' %>
      </p>
      
      <!-- Sort Options -->
      <div class="flex items-center space-x-4">
        <label for="sort" class="text-sm font-medium text-gray-700 dark:text-gray-300">Sort by:</label>
        <%= form_with url: marketplace_vendor_products_path(@vendor), method: :get, local: true, class: "flex items-center space-x-2" do |form| %>
          <%= form.select :sort, options_for_select(@sort_options, params[:sort]), 
              { prompt: 'Select...' }, 
              { class: "rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
                onchange: "this.form.submit();" } %>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Products Grid -->
  <% if @products.any? %>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <% @products.each do |product| %>
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200 overflow-hidden">
          <%= link_to marketplace_product_path(product), class: "block h-full" do %>
            <div class="p-6">
              <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3 line-clamp-2">
                <%= product.name %>
              </h3>
              
              <% if product.description.present? %>
                <p class="text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
                  <%= strip_tags(product.description) %>
                </p>
              <% end %>
              
              <!-- Categories -->
              <% if product.categories.any? %>
                <div class="flex flex-wrap gap-2 mb-4">
                  <% product.categories.limit(3).each do |category| %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                      <%= category.name %>
                    </span>
                  <% end %>
                  <% if product.categories.count > 3 %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                      +<%= product.categories.count - 3 %> more
                    </span>
                  <% end %>
                </div>
              <% end %>
              
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-blue-600 dark:text-blue-400">
                  View Details →
                </span>
                <span class="text-sm text-gray-500 dark:text-gray-400">
                  <%= product.created_at.strftime("%b %Y") %>
                </span>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
  <% else %>
    <div class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8v2a1 1 0 01-1 1H7a1 1 0 01-1-1V5a1 1 0 011-1h10a1 1 0 011 1zM9 7h6" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No products found</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        This vendor hasn't published any products yet.
      </p>
    </div>
  <% end %>
</div>