<% content_for :page_title, "#{@product_content.title} - #{@product.name}" %>
<% content_for :meta_description, "#{@product_content.title} from #{@product.name} by #{@product.company_name}. #{strip_tags(@product_content.description.to_s).truncate(120)}" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="lg:grid lg:grid-cols-4 lg:gap-8">
    
    <!-- Main Content -->
    <div class="lg:col-span-3">
      <!-- Breadcrumb -->
      <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-2">
          <li>
            <%= link_to marketplace_path, class: "text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors" do %>
              Marketplace
            <% end %>
          </li>
          <li>
            <svg class="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </li>
          <li>
            <%= link_to marketplace_products_path, class: "text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors" do %>
              Products
            <% end %>
          </li>
          <li>
            <svg class="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </li>
          <li>
            <%= link_to marketplace_product_path(@product), class: "text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors" do %>
              <%= truncate(@product.name, length: 30) %>
            <% end %>
          </li>
          <li>
            <svg class="flex-shrink-0 h-5 w-5 text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
            </svg>
          </li>
          <li class="text-gray-500 dark:text-gray-400">
            <%= truncate(@product_content.title, length: 40) %>
          </li>
        </ol>
      </nav>

      <!-- Content Header -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
        <div class="px-6 py-8">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2 transition-colors">
                <%= @product_content.title %>
              </h1>
              <p class="text-lg text-gray-600 dark:text-gray-400 mb-4 transition-colors">
                From <%= link_to @product.name, marketplace_product_path(@product), 
                          class: "text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-colors" %>
                by <%= @product.company_name %>
              </p>
              
              <% if @product_content.description.present? %>
                <div class="prose max-w-none dark:prose-invert">
                  <%= simple_format(@product_content.description) %>
                </div>
              <% end %>
              
              <% if @product_content.file_type.present? %>
                <div class="mt-4">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <%= @product_content.file_type == 'pdf' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' : 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200' %>">
                    <%= @product_content.file_type&.upcase %> Content
                  </span>
                </div>
              <% end %>
            </div>
            
            <% if @product_content.file.attached? %>
              <div class="ml-6">
                <%= link_to url_for(@product_content.file), 
                    download: @product_content.file.filename,
                    class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white #{@product_content.file_type == 'pdf' ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500' : 'bg-purple-600 hover:bg-purple-700 focus:ring-purple-500'} focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors" do %>
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Download File
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- File Viewer -->
      <% if @product_content.file.attached? %>
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
          <div class="px-6 py-8">
            <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">
              <%= @product_content.file_type == 'pdf' ? 'PDF Preview' : @product_content.file_type == 'video' ? 'Video Preview' : 'File Preview' %>
            </h2>
            
            <% if @product_content.file_type == 'pdf' %>
              <!-- PDF Viewer -->
              <div class="aspect-[8.5/11] bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
                <iframe 
                  src="<%= url_for(@product_content.file) %>#toolbar=0&navpanes=0&scrollbar=0"
                  class="w-full h-full border-0"
                  title="<%= @product_content.title %> PDF">
                </iframe>
              </div>
            <% elsif @product_content.file_type == 'video' %>
              <!-- Video Player -->
              <div class="aspect-video bg-black rounded-lg overflow-hidden">
                <video 
                  controls 
                  controlsList="nodownload"
                  class="w-full h-full object-contain"
                  preload="metadata">
                  <source src="<%= url_for(@product_content.file) %>" type="<%= @product_content.file.content_type %>">
                  Your browser does not support the video tag.
                </video>
              </div>
            <% else %>
              <!-- Unsupported file type -->
              <div class="aspect-video bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                <div class="text-center">
                  <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                  <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">File Preview</h3>
                  <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Download the file to view its contents.</p>
                </div>
              </div>
            <% end %>
            
            <div class="mt-4 flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <%= @product_content.file_type == 'pdf' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' : 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200' %>">
                  <% if @product_content.file_type == 'pdf' %>
                    <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                    </svg>
                  <% else %>
                    <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
                    </svg>
                  <% end %>
                  <%= @product_content.file_type&.upcase %> • <%= number_to_human_size(@product_content.file.byte_size) %>
                </span>
              </div>
              <div class="flex space-x-2">
                <%= link_to 'Open in New Tab', url_for(@product_content.file), 
                    target: '_blank',
                    class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
              </div>
            </div>
          </div>
        </div>
      <% else %>
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-8 transition-colors">
          <div class="px-6 py-8">
            <div class="aspect-video bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
              <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No file available</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">File not available for this content.</p>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Content Details -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-colors">
        <div class="px-6 py-8">
          <h2 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 transition-colors">Content Details</h2>
          <dl class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <% if @product_content.file.attached? %>
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">File Name</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product_content.file.filename %></dd>
              </div>
              
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">File Size</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= number_to_human_size(@product_content.file.byte_size) %></dd>
              </div>
              
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Content Type</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product_content.file.content_type %></dd>
              </div>
            <% end %>
            
            <% if @product_content.file_type.present? %>
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">File Type</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 capitalize"><%= @product_content.file_type %></dd>
              </div>
            <% end %>
            
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product_content.created_at.strftime("%B %-d, %Y") %></dd>
            </div>
          </dl>
        </div>
      </div>

    </div>

    <!-- Sidebar -->
    <div class="lg:col-span-1 mt-8 lg:mt-0">
      <div class="sticky top-6">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 transition-colors">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">About This Product</h3>
          
          <div class="space-y-4">
            <div>
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Product</p>
              <p class="text-sm text-gray-900 dark:text-gray-100">
                <%= link_to @product.name, marketplace_product_path(@product), 
                    class: "text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors" %>
              </p>
            </div>
            
            <% if @product.primary_category %>
              <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Category</p>
                <p class="text-sm text-gray-900 dark:text-gray-100"><%= @product.primary_category.name %></p>
              </div>
            <% end %>
            
            <div>
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Vendor</p>
              <p class="text-sm text-gray-900 dark:text-gray-100">
                <%= @product.company_name %>
              </p>
            </div>
            
            <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
              <%= link_to 'Back to Product', marketplace_product_path(@product), 
                  class: 'w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors' %>
            </div>
          </div>
        </div>
      </div>
    </div>
    
  </div>
</div>