<% content_for :page_title, @product_customer.name %>

<div class="bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        <li>
          <%= link_to "Marketplace", marketplace_path, class: "text-gray-500 hover:text-gray-700" %>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <%= link_to @product.name, marketplace_product_path(@product), class: "ml-1 text-gray-500 hover:text-gray-700 md:ml-2" %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span class="ml-1 font-medium text-gray-900 md:ml-2"><%= @product_customer.name %></span>
          </div>
        </li>
      </ol>
    </nav>

    <!-- Customer Header -->
    <div class="mb-12">
      <div class="flex items-start space-x-6">
        <% if @product_customer.logo.attached? && @product_customer.image? %>
          <div class="flex-shrink-0">
            <%= image_tag @product_customer.logo, class: "w-24 h-24 object-contain rounded-lg border border-gray-200" %>
          </div>
        <% end %>
        
        <div class="flex-1">
          <h1 class="text-3xl font-bold text-gray-900 mb-2"><%= @product_customer.name %></h1>
          <p class="text-lg text-gray-600 mb-4">Customer using <%= @product.name %></p>
          
          <% if @product_customer.description.present? %>
            <div class="prose prose-lg max-w-none">
              <p class="text-gray-700"><%= @product_customer.description %></p>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Product Context -->
    <div class="bg-gray-50 rounded-lg p-6 mb-8">
      <h2 class="text-xl font-semibold text-gray-900 mb-4">About <%= @product.name %></h2>
      <div class="flex items-start space-x-4">
        <% if @product.logo.attached? %>
          <div class="flex-shrink-0">
            <%= image_tag @product.logo, class: "w-16 h-16 object-cover rounded-lg" %>
          </div>
        <% end %>
        
        <div class="flex-1">
          <h3 class="text-lg font-medium text-gray-900 mb-2"><%= @product.name %></h3>
          <% if @product.description.present? %>
            <div class="text-gray-600 mb-4">
              <%= truncate(strip_tags(@product.description), length: 200) %>
            </div>
          <% end %>
          
          <div class="flex items-center space-x-4">
            <%= link_to "View Product", marketplace_product_path(@product), class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
            
            <% if @product.account.present? %>
              <span class="text-sm text-gray-500">
                by <%= @product.company_name %>
              </span>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <!-- Related Customers -->
    <% other_customers = @product.product_customers.where.not(id: @product_customer.id).limit(3) %>
    <% if other_customers.any? %>
      <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Other Customers Using <%= @product.name %></h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <% other_customers.each do |customer| %>
            <div class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div class="flex items-center space-x-3">
                <% if customer.logo.attached? && customer.image? %>
                  <%= image_tag customer.logo, class: "w-12 h-12 object-contain rounded-lg" %>
                <% else %>
                  <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                  </div>
                <% end %>
                
                <div class="flex-1">
                  <h3 class="font-medium text-gray-900"><%= customer.name %></h3>
                  <% if customer.description.present? %>
                    <p class="text-sm text-gray-500 mt-1"><%= truncate(customer.description, length: 80) %></p>
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>

    <!-- Navigation -->
    <div class="flex justify-between items-center">
      <%= link_to "← Back to #{@product.name}", marketplace_product_path(@product), class: "text-blue-600 hover:text-blue-900 font-medium" %>
    </div>
  </div>
</div>