<% content_for :page_title, "Browse Categories" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="text-center mb-8">
    <h1 class="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
      Browse Categories
    </h1>
    <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
      Explore government technology solutions organized by category
    </p>
  </div>


  <% if @root_categories.any? %>
    <!-- Category Hierarchy -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <% @root_categories.each do |root_category| %>
        <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <!-- Root Category Header -->
          <h2 class="text-base font-semibold text-gray-900 dark:text-gray-100 mb-3">
            <%= root_category.name %>
          </h2>
          
          <!-- Separator -->
          <hr class="border-gray-200 dark:border-gray-600 mb-3">
          
          <!-- Subcategories -->
          <% if root_category.subcategories.any? %>
            <div class="space-y-1">
              <% root_category.subcategories.order(:name).each do |subcategory| %>
                <div>
                  <%= link_to marketplace_category_path(subcategory),
                      class: "group flex items-center justify-between py-1 hover:text-blue-600 dark:hover:text-blue-400 transition-colors" do %>
                    <span class="text-gray-800 dark:text-gray-200 group-hover:text-blue-600 dark:group-hover:text-blue-400">
                      <%= subcategory.name %>
                    </span>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                      <%= subcategory.products.count %>
                    </span>
                  <% end %>
                  
                  <!-- Sub-subcategories (if any) -->
                  <% if subcategory.subcategories.any? %>
                    <div class="ml-4 space-y-1">
                      <% subcategory.subcategories.order(:name).each do |sub_subcategory| %>
                        <%= link_to marketplace_category_path(sub_subcategory),
                            class: "group flex items-center justify-between py-1 hover:text-blue-600 dark:hover:text-blue-400 transition-colors" do %>
                          <span class="text-sm text-gray-700 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400">
                            <%= sub_subcategory.name %>
                          </span>
                          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                            <%= sub_subcategory.products.count %>
                          </span>
                        <% end %>
                      <% end %>
                    </div>
                  <% end %>
                </div>
              <% end %>
            </div>
          <% else %>
            <p class="text-gray-500 dark:text-gray-400">No subcategories in this category yet.</p>
          <% end %>
        </div>
      <% end %>
    </div>
  <% else %>
    <!-- Empty State -->
    <div class="text-center py-12">
      <div class="max-w-md mx-auto">
        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No categories found</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          No categories are available at this time.
        </p>
      </div>
    </div>
  <% end %>
</div>