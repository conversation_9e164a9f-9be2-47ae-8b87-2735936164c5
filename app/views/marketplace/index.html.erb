<% content_for :page_title, "Marketplace" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Hero Search Section -->
  <div class="bg-gradient-to-r from-blue-600 to-blue-800 dark:from-blue-700 dark:to-blue-900 rounded-xl shadow-xl mb-8">
    <div class="px-8 py-12 text-center">
    <h1 class="text-4xl font-bold text-white mb-4">
      Discover Government Technology Solutions
    </h1>
    <p class="text-xl text-blue-100 dark:text-blue-200 mb-8 max-w-3xl mx-auto">
      Find the perfect technology partners and products to modernize your government operations
    </p>
    
    <div data-controller="search" data-search-url-value="<%= api_search_path %>" class="max-w-4xl mx-auto relative">
      <%= form_with url: marketplace_search_path, method: :get, local: true, class: "relative" do |form| %>
        <div class="flex flex-col sm:flex-row gap-4">
          <div class="flex-1 relative">
            <%= form.text_field :q, 
                placeholder: "Search categories and products...", 
                value: params[:q],
                class: "w-full px-6 py-4 text-lg bg-white dark:bg-gray-800 dark:text-white border-0 rounded-xl shadow-lg focus:ring-4 focus:ring-blue-300 focus:outline-none",
                data: { 
                  search_target: "input",
                  action: "input->search#search focus->search#focus blur->search#blur keydown->search#keydown"
                } %>
          </div>
          <div>
            <%= form.submit "Search", 
                class: "w-full sm:w-auto px-8 py-4 bg-white dark:bg-gray-100 text-blue-600 dark:text-blue-700 font-semibold rounded-xl shadow-lg hover:bg-gray-50 dark:hover:bg-gray-200 transition-colors focus:outline-none focus:ring-4 focus:ring-blue-300" %>
          </div>
        </div>
      <% end %>
      
      <!-- Hero Search Dropdown -->
      <div data-search-target="dropdown" class="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl shadow-xl z-50 max-h-96 overflow-y-auto scrollbar-thin hidden text-left">
        <div data-search-target="results"></div>
      </div>
    </div>
  </div>
</div>

<!-- Hierarchical Category Browser -->
<div class="mb-12">
  <div data-controller="simple-category-browser" class="space-y-6">
    
    <!-- Category Browser Cards -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Root Categories Card -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4">
        <div data-simple-category-browser-target="rootCard">
          <div class="space-y-1">
            <h2 class="text-base font-semibold text-gray-900 dark:text-gray-100 mb-1">Browse by Category</h2>
            <% @root_categories.each do |category| %>
              <div class="category-item cursor-pointer transition-colors group hover:bg-gray-50 dark:hover:bg-gray-800"
                   data-action="click->simple-category-browser#selectRootCategory"
                   data-category-id="<%= category.id %>"
                   data-category-name="<%= category.name %>">
                <div class="flex items-center justify-between py-1">
                  <div class="flex-1">
                    <h3 class="text-sm text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      <%= category.name %>
                    </h3>
                  </div>
                  <div class="text-blue-600 dark:text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity ml-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
      
      <!-- Subcategories Card -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 hidden" data-simple-category-browser-target="subCard">
        <div>
          <div class="space-y-0">
            <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100 mb-1">Subcategories</h3>
            
            <!-- Subcategories for each root category (hidden by default) -->
            <% @root_categories.each do |root_category| %>
              <div class="space-y-0 hidden" data-root-category-id="<%= root_category.id %>">
                <% root_category.subcategories.each do |subcategory| %>
                  <div class="category-item cursor-pointer transition-colors group hover:bg-gray-50 dark:hover:bg-gray-800"
                       data-action="click->simple-category-browser#selectSubcategory"
                       data-category-id="<%= subcategory.id %>"
                       data-category-name="<%= subcategory.name %>"
                       data-has-subcategories="<%= subcategory.subcategories.any? %>">
                    <div class="flex items-center justify-between py-1">
                      <div class="flex-1">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                          <%= subcategory.name %>
                        </h4>
                      </div>
                      <div class="text-blue-600 dark:text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity ml-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      </div>
      
      <!-- Sub-subcategories Card -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 hidden" data-simple-category-browser-target="leafCard">
        <div>
          <div class="space-y-0">
            <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100 mb-1">Sub-subcategories</h3>
            
            <!-- Sub-subcategories for each subcategory (hidden by default) -->
            <% @root_categories.each do |root_category| %>
              <% root_category.subcategories.each do |subcategory| %>
                <div class="space-y-0 hidden" data-subcategory-id="<%= subcategory.id %>">
                  <% subcategory.subcategories.each do |sub_subcategory| %>
                    <div class="category-item cursor-pointer transition-colors group hover:bg-gray-50 dark:hover:bg-gray-800"
                         data-action="click->simple-category-browser#selectSubSubcategory"
                         data-category-id="<%= sub_subcategory.id %>"
                         data-category-name="<%= sub_subcategory.name %>">
                      <div class="flex items-center justify-between py-1">
                        <div class="flex-1">
                          <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                            <%= sub_subcategory.name %>
                          </h4>
                        </div>
                        <div class="text-blue-600 dark:text-blue-400 opacity-0 group-hover:opacity-100 transition-opacity ml-2">
                          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                          </svg>
                        </div>
                      </div>
                    </div>
                  <% end %>
                  <% if subcategory.subcategories.empty? %>
                    <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                      <p class="text-sm">No subcategories available</p>
                    </div>
                  <% end %>
                </div>
              <% end %>
            <% end %>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Products Row -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 hidden" data-simple-category-browser-target="productsRow">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-base font-semibold text-gray-900 dark:text-gray-100" data-simple-category-browser-target="productsTitle">
          Products
        </h3>
        <a href="#" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors" data-simple-category-browser-target="productsViewAll">
          View All Products →
        </a>
      </div>
      
      <!-- Products for each leaf category (hidden by default) -->
      <% @root_categories.each do |root_category| %>
        <% root_category.subcategories.each do |subcategory| %>
          <% if subcategory.subcategories.empty? %>
            <!-- This subcategory has products directly -->
            <div class="hidden" data-category-products="<%= subcategory.id %>">
              <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                <% subcategory.products.published.includes(:account).limit(12).each do |product| %>
                  <div class="group cursor-pointer" data-action="click->simple-category-browser#viewProduct" data-product-slug="<%= product.slug %>">
                    <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-lg mb-2 flex items-center justify-center group-hover:bg-gray-200 dark:group-hover:bg-gray-600 transition-colors mx-auto">
                      <% if product.logo.attached? %>
                        <%= image_tag product.logo, class: "w-16 h-16 object-cover rounded-lg" %>
                      <% else %>
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-lg">
                          <%= product.name.first.upcase %>
                        </div>
                      <% end %>
                    </div>
                    <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors text-center">
                      <%= truncate(product.name, length: 20) %>
                    </h4>
                  </div>
                <% end %>
              </div>
              <% if subcategory.products.published.count == 0 %>
                <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                  <p>No products available in this category</p>
                </div>
              <% end %>
            </div>
          <% else %>
            <!-- This subcategory has sub-subcategories with products -->
            <% subcategory.subcategories.each do |sub_subcategory| %>
              <div class="hidden" data-category-products="<%= sub_subcategory.id %>">
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  <% sub_subcategory.products.published.includes(:account).limit(12).each do |product| %>
                    <div class="group cursor-pointer" data-action="click->simple-category-browser#viewProduct" data-product-slug="<%= product.slug %>">
                      <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-lg mb-2 flex items-center justify-center group-hover:bg-gray-200 dark:group-hover:bg-gray-600 transition-colors mx-auto">
                        <% if product.logo.attached? %>
                          <%= image_tag product.logo, class: "w-16 h-16 object-cover rounded-lg" %>
                        <% else %>
                          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-lg">
                            <%= product.name.first.upcase %>
                          </div>
                        <% end %>
                      </div>
                      <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors text-center">
                        <%= truncate(product.name, length: 20) %>
                      </h4>
                    </div>
                  <% end %>
                </div>
                <% if sub_subcategory.products.published.count == 0 %>
                  <div class="text-center py-8 text-gray-500 dark:text-gray-400">
                    <p>No products available in this category</p>
                  </div>
                <% end %>
              </div>
            <% end %>
          <% end %>
        <% end %>
      <% end %>
    </div>
    
    <!-- Quick Actions -->
    <div class="text-center">
      <p class="text-gray-600 dark:text-gray-400 mb-4">
        Looking for something specific?
      </p>
      <div class="flex flex-wrap justify-center gap-4">
        <%= link_to "Browse All Products", marketplace_products_path, 
            class: "inline-flex items-center px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors" %>
        <%= link_to "Browse All Categories", marketplace_categories_path, 
            class: "inline-flex items-center px-4 py-2 bg-gray-600 dark:bg-gray-700 text-white rounded-lg hover:bg-gray-700 dark:hover:bg-gray-600 transition-colors" %>
      </div>
    </div>
  </div>
</div>

<!-- Featured Products -->
<% if @featured_products.any? %>
  <% cache(['featured_products', @featured_products.maximum(:updated_at), current_account&.account_type], expires_in: 30.minutes) do %>
    <div class="mb-12">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Featured Products</h2>
        <%= link_to "View all products", marketplace_products_path, 
            class: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium" %>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <% @featured_products.each do |product| %>
          <% cache(['product_card', product, product.account&.updated_at], expires_in: 1.hour) do %>
            <div class="group">
              <%= link_to marketplace_product_path(product), 
                  class: "block bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden group-hover:border-blue-300 dark:group-hover:border-blue-500" do %>
                <div class="h-24 bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                  <!-- Product logo or initials -->
                  <% if product.logo.attached? %>
                    <%= image_tag product.logo, class: "w-16 h-16 object-cover rounded-lg" %>
                  <% else %>
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-lg">
                      <%= product.name.first.upcase %>
                    </div>
                  <% end %>
                </div>
                <div class="p-4">
                  <h3 class="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors mb-1">
                    <%= truncate(product.name, length: 50) %>
                  </h3>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    by <%= product.company_name %>
                  </p>
                  <% if product.description.present? %>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      <%= truncate(strip_tags(product.description), length: 80) %>
                    </p>
                  <% end %>
                  <div class="flex items-center justify-between">
                    <% if product.primary_category.present? %>
                      <span class="text-xs text-gray-500 dark:text-gray-400">
                        <%= product.primary_category.name %>
                      </span>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div>
          <% end %>
        <% end %>
      </div>
    </div>
  <% end %>
<% end %>

<!-- Recently Added Products -->
<% if @recently_added_products.any? %>
  <div class="mb-12">
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Recently Added Products</h2>
      <%= link_to "View all", marketplace_products_path(sort: 'newest'), 
          class: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium" %>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <% @recently_added_products.each do |product| %>
        <div class="group">
          <%= link_to marketplace_product_path(product), 
              class: "block bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden group-hover:border-green-300 dark:group-hover:border-green-500" do %>
            <div class="h-24 bg-gray-100 dark:bg-gray-700 relative flex items-center justify-center">
              <!-- Product logo or initials -->
              <% if product.logo.attached? %>
                <%= image_tag product.logo, class: "w-16 h-16 object-cover rounded-lg" %>
              <% else %>
                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center text-white font-bold text-lg">
                  <%= product.name.first.upcase %>
                </div>
              <% end %>
              <div class="absolute top-2 right-2">
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                  New
                </span>
              </div>
            </div>
            <div class="p-4">
              <h3 class="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors mb-1">
                <%= truncate(product.name, length: 50) %>
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                by <%= product.company_name %>
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400 mb-2">
                Added <%= time_ago_in_words(product.created_at) %> ago
              </p>
              <% if product.description.present? %>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  <%= truncate(strip_tags(product.description), length: 80) %>
                </p>
              <% end %>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
  </div>
<% end %>

<!-- Recently Added Case Studies -->
<% if @recently_added_case_studies.any? %>
  <div class="mb-12">
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Recently Added Case Studies</h2>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <% @recently_added_case_studies.each do |case_study| %>
        <div class="group">
          <%= link_to marketplace_product_path(case_study.product), 
              class: "block bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden group-hover:border-purple-300 dark:group-hover:border-purple-500" do %>
            <div class="p-6">
              <div class="flex items-start justify-between mb-3">
                <h3 class="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                  <%= truncate(case_study.title, length: 60) %>
                </h3>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 ml-2 flex-shrink-0">
                  Case Study
                </span>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                for <%= case_study.product.name %>
              </p>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                by <%= case_study.product.company_name %>
              </p>
              <% if case_study.description.present? %>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  <%= truncate(strip_tags(case_study.description), length: 120) %>
                </p>
              <% end %>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                Added <%= time_ago_in_words(case_study.created_at) %> ago
              </p>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
  </div>
<% end %>

</div>