<% content_for :page_title, "List Details" %>

<div class="">
    <!-- Header -->
    <div class="md:flex md:items-center md:justify-between mb-8">
      <div class="min-w-0 flex-1">
        <h1 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl ">
          <%= @list.name %>
        </h1>
        <% if @list.description.present? %>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            <%= @list.description %>
          </p>
        <% end %>
      </div>
      <div class="mt-4 flex md:ml-4 md:mt-0 space-x-3">
        <%= link_to "Edit List", edit_agencies_list_path(@list), 
            class: "inline-flex items-center rounded-md bg-white dark:bg-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-100 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600" %>
        <%= link_to "← Back to Lists", agencies_lists_path,
            class: "inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600" %>
      </div>
    </div>

    <!-- List Items -->
    <div class="">
      <div class="mt-8">
              <% if @list_items.any? %>
                <!-- Desktop Table View (hidden on mobile) -->
                <div class="hidden md:block">
                  <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                    <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
                      <thead class="bg-gray-50 dark:bg-gray-900/50">
                        <tr>
                          <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Product</th>
                          <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Category</th>
                          <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Vendor</th>
                          <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Added</th>
                          <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                            <span class="sr-only">Actions</span>
                          </th>
                        </tr>
                      </thead>
                      <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
                        <% @list_items.each do |list_item| %>
                          <% product = list_item.product %>
                          <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                              <div class="flex items-center">
                                <div class="h-10 w-10 flex-shrink-0">
                                  <% if product.logo.attached? %>
                                    <%= image_tag product.logo, class: "h-10 w-10 rounded-full object-cover" %>
                                  <% else %>
                                    <div class="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center">
                                      <span class="text-sm font-medium text-blue-600 dark:text-blue-300">
                                        <%= product.name.first.upcase %>
                                      </span>
                                    </div>
                                  <% end %>
                                </div>
                                <div class="ml-4">
                                  <div class="font-medium text-gray-900 dark:text-gray-100">
                                    <%= link_to product.name, marketplace_product_path(product), class: "hover:text-blue-600 dark:hover:text-blue-400" %>
                                  </div>
                                  <% if product.description.present? %>
                                    <div class="text-gray-500 dark:text-gray-400 text-xs">
                                      <%= truncate(strip_tags(product.description), length: 50) %>
                                    </div>
                                  <% end %>
                                </div>
                              </div>
                            </td>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-900 dark:text-gray-100">
                              <% if product.categories.any? %>
                                <%= product.categories.first.name %>
                              <% else %>
                                <span class="text-gray-500 dark:text-gray-400">-</span>
                              <% end %>
                            </td>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-900 dark:text-gray-100">
                              <% if product.account %>
                                <%= product.account.name %>
                              <% else %>
                                <span class="text-gray-500 dark:text-gray-400">-</span>
                              <% end %>
                            </td>
                            <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                              <%= time_ago_in_words(list_item.created_at) %> ago
                            </td>
                            <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                              <div class="flex justify-end">
                                <%= link_to "View Product", marketplace_product_path(product), target: "_blank", class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                              </div>
                            </td>
                          </tr>
                        <% end %>
                      </tbody>
                    </table>
                  </div>
                </div>

                <!-- Mobile Card View (visible on mobile only) -->
                <div class="md:hidden space-y-4">
                  <% @list_items.each do |list_item| %>
                    <% product = list_item.product %>
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div class="flex items-start justify-between">
                        <div class="flex-1">
                          <div class="flex items-center">
                            <div class="h-10 w-10 flex-shrink-0 mr-3">
                              <% if product.logo.attached? %>
                                <%= image_tag product.logo, class: "h-10 w-10 rounded-full object-cover" %>
                              <% else %>
                                <div class="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center">
                                  <span class="text-sm font-medium text-blue-600 dark:text-blue-300">
                                    <%= product.name.first.upcase %>
                                  </span>
                                </div>
                              <% end %>
                            </div>
                            <div>
                              <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                <%= link_to product.name, marketplace_product_path(product), class: "hover:text-blue-600 dark:hover:text-blue-400" %>
                              </h4>
                              <% if product.description.present? %>
                                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                                  <%= truncate(strip_tags(product.description), length: 80) %>
                                </p>
                              <% end %>
                            </div>
                          </div>
                          <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                            <div>Category: 
                              <% if product.categories.any? %>
                                <%= product.categories.first.name %>
                              <% else %>
                                None
                              <% end %>
                            </div>
                            <div class="mt-1">Vendor: 
                              <% if product.account %>
                                <%= product.account.name %>
                              <% else %>
                                Unknown
                              <% end %>
                            </div>
                          </div>
                          <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                            Added <%= time_ago_in_words(list_item.created_at) %> ago
                          </div>
                        </div>
                        <div class="ml-4">
                          <%= link_to "View Product", marketplace_product_path(product), target: "_blank",
                              class: "text-sm text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                        </div>
                      </div>
                    </div>
                  <% end %>
                </div>
              <% else %>
                <div class="text-center py-12">
                  <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No products in this list</h3>
                  <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Start browsing the marketplace to add products to this list.</p>
                  <div class="mt-6">
                    <%= link_to "Browse Marketplace", marketplace_products_path, 
                        class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
                  </div>
                </div>
              <% end %>
      </div>
    </div>
</div>