<% content_for :page_title, "New List" %>

<div class="max-w-3xl">
    <!-- Header -->
    <div class="mb-8">
      <div class="md:flex md:items-center md:justify-between">
        <div class="min-w-0 flex-1">
          <h1 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl ">
            New List
          </h1>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Create a new list to organize products
          </p>
        </div>
        <div class="mt-4 flex md:ml-4 md:mt-0">
          <%= link_to "← Back to Lists", agencies_lists_path,
              class: "inline-flex items-center rounded-md bg-gray-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-gray-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-600" %>
        </div>
      </div>
    </div>

    <!-- Create Form -->
    <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <%= form_with model: [@list], url: agencies_lists_path, local: true, class: "space-y-6" do |form| %>
          <% if @list.errors.any? %>
            <div class="rounded-md bg-red-50 dark:bg-red-900/50 p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10 18C14.4183 18 18 14.4183 18 10C18 5.58172 14.4183 2 10 2C5.58172 2 2 5.58172 2 10C2 14.4183 5.58172 18 10 18Z" stroke="currentColor" stroke-width="2"/>
                    <path d="M10 6V10" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                    <path d="M10 14H10.01" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                    Please fix the following errors:
                  </h3>
                  <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                    <ul class="list-disc pl-5 space-y-1">
                      <% @list.errors.full_messages.each do |message| %>
                        <li><%= message %></li>
                      <% end %>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          <% end %>

          <div>
            <%= form.label :name, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <div class="mt-1">
              <%= form.text_field :name, 
                  class: "block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
                  placeholder: "Enter list name",
                  required: true %>
            </div>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Give your list a descriptive name.</p>
          </div>

          <div>
            <%= form.label :description, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <div class="mt-1">
              <%= form.text_area :description, 
                  rows: 3,
                  class: "block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
                  placeholder: "Optional description for your list" %>
            </div>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Optional description to help you remember what this list is for.</p>
          </div>

          <div class="pt-6 border-t border-gray-200 dark:border-gray-600">
            <div class="flex justify-end space-x-3">
              <%= link_to "Cancel", agencies_lists_path, 
                  class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" %>
              <%= form.submit "Create List", 
                  class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-sm transition-colors" %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
</div>