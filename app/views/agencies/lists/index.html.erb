<% content_for :page_title, "List Management" %>

<div class="">
  <div class="sm:flex sm:items-center mb-8">
    <div class="sm:flex-auto">
      <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100">List Management</h1>
    </div>
    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
      <%= link_to "New List", new_agencies_list_path, 
          class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
    </div>
  </div>

  <!-- Desktop Table View (hidden on mobile) -->
  <div class="hidden md:block mt-8 flow-root">
    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
          <% if @lists.any? %>
            <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">List</th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Items</th>
                  <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                    <span class="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
                <% @lists.each do |list| %>
                  <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                      <div class="flex items-center">
                        <div>
                          <div class="font-medium text-gray-900 dark:text-gray-100">
                            <%= link_to list.name, agencies_list_path(list), class: "hover:text-blue-600 dark:hover:text-blue-400" %>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                      <%= pluralize(list.list_items.count, 'item') %>
                    </td>
                    <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                      <div class="flex justify-end">
                        <%= link_to "View", agencies_list_path(list), class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                      </div>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          <% else %>
            <div class="text-center py-12">
              <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No lists yet</h3>
              <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by creating your first list to organize products.</p>
              <div class="mt-6">
                <%= link_to "New List", new_agencies_list_path, 
                    class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile Card View (visible on mobile only) -->
  <div class="md:hidden mt-8 space-y-4">
    <% if @lists.any? %>
      <% @lists.each do |list| %>
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
          <div class="px-4 py-5 sm:px-6">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                  <%= link_to list.name, agencies_list_path(list), class: "hover:text-blue-600 dark:hover:text-blue-400" %>
                </h3>
                <% if list.description.present? %>
                  <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    <%= truncate(list.description, length: 80) %>
                  </p>
                <% end %>
              </div>
            </div>
            
            <div class="mt-4 space-y-3">
              <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500 dark:text-gray-400">Items:</span>
                  <span class="font-medium text-gray-900 dark:text-gray-100"><%= pluralize(list.list_items.count, 'item') %></span>
                </div>
              </div>
              
            </div>
            
            <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
              <%= link_to "View Details", agencies_list_path(list), class: "inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600" %>
            </div>
          </div>
        </div>
      <% end %>
    <% else %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No lists yet</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by creating your first list to organize products.</p>
        <div class="mt-6">
          <%= link_to "New List", new_agencies_list_path, 
              class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
        </div>
      </div>
    <% end %>
  </div>

  <% if @lists.empty? %>
    <div class="text-center py-8">
      <p class="text-sm text-gray-500 dark:text-gray-400">No lists found.</p>
    </div>
  <% end %>
</div>