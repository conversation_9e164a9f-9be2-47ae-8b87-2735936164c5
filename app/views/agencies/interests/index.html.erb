<% content_for :page_title, "My Requests" %>

<div class="sm:flex sm:items-center mb-6">
  <div class="sm:flex-auto">
    <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100">My Product Requests</h1>
    <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">Track all the products you've requested information about.</p>
  </div>
</div>

<% if @leads.any? %>
  <!-- Desktop Table View (hidden on mobile) -->
  <div class="hidden md:block">
    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
      <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
        <thead class="bg-gray-50 dark:bg-gray-900/50">
          <tr>
            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Product</th>
            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Vendor</th>
            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Requested</th>
            <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
              <span class="sr-only">Actions</span>
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
          <% @leads.each do |lead| %>
            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
              <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                <div>
                  <div class="font-medium text-gray-900 dark:text-gray-100">
                    <%= link_to lead.product.name, marketplace_product_path(lead.product), class: "hover:text-blue-600 dark:hover:text-blue-400" %>
                  </div>
                </div>
              </td>
              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-900 dark:text-gray-100">
                <%= lead.account&.name || "Platform Admin" %>
              </td>
              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                <div>
                  <%= lead.created_at.strftime("%b %-d, %Y") %>
                </div>
                <div class="text-xs">
                  <%= lead.created_at.strftime("%-I:%M %p") %>
                </div>
              </td>
              <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                <div class="flex justify-end">
                  <%= link_to "View Product", marketplace_product_path(lead.product), target: "_blank", class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                </div>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Mobile Card View (visible on mobile only) -->
  <div class="md:hidden space-y-4">
    <% @leads.each do |lead| %>
      <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                <%= link_to lead.product.name, marketplace_product_path(lead.product), class: "hover:text-blue-600 dark:hover:text-blue-400" %>
              </h4>
              <% if lead.message.present? %>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                  <%= truncate(lead.message, length: 80) %>
                </p>
              <% end %>
            </div>
            <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
              <div>Vendor: <%= lead.account&.name || "Platform Admin" %></div>
              <% if lead.timeline.present? %>
                <div class="mt-1">Timeline: <%= lead.timeline.humanize %></div>
              <% end %>
            </div>
            <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
              Requested <%= lead.created_at.strftime("%b %-d, %Y at %-I:%M %p") %>
            </div>
          </div>
          <div class="ml-4">
            <%= link_to "View Product", marketplace_product_path(lead.product), target: "_blank",
                class: "text-sm text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
<% else %>
  <div class="text-center py-12">
    <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No requests yet</h3>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Start exploring products and request information to connect with vendors.</p>
    <div class="mt-6">
      <%= link_to "Browse Products", marketplace_products_path, 
          class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors" %>
    </div>
  </div>
<% end %>