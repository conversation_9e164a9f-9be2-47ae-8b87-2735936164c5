<% content_for :page_title, "Dashboard" %>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
  <!-- Recent Requests -->
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">Recent Requests</h3>
      <% if @recent_interests.any? %>
        <div class="flow-root">
          <ul role="list" class="-my-5 divide-y divide-gray-200 dark:divide-gray-700">
            <% @recent_interests.each do |interest| %>
              <li class="py-4">
                <div class="flex items-center space-x-4">
                  <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      <%= interest.product.name %>
                    </p>
                    <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
                      <%= interest.account&.name || "Admin-managed" %>
                    </p>
                  </div>
                  <div class="inline-flex items-center text-base font-semibold text-gray-900 dark:text-gray-100">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                      <%= case interest.status
                          when 'pending' then 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
                          when 'contacted' then 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
                          when 'qualified' then 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                          else 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200'
                          end %>">
                      <%= interest.status.humanize %>
                    </span>
                  </div>
                </div>
              </li>
            <% end %>
          </ul>
        </div>
        <div class="mt-6">
          <%= link_to "View all requests", agencies_interests_path, 
              class: "w-full flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors" %>
        </div>
      <% else %>
        <p class="text-gray-500 dark:text-gray-400 text-center py-4">No requests yet. Explore the marketplace to find products!</p>
        <div class="mt-4">
          <%= link_to "Browse Products", marketplace_products_path, 
              class: "w-full flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition-colors" %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">Quick Actions</h3>
      <div class="space-y-4">
        <%= link_to marketplace_path, 
            class: "w-full flex items-center px-4 py-3 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors" do %>
          <span class="ml-3">Browse Marketplace</span>
        <% end %>
        
        <%= link_to marketplace_products_path, 
            class: "w-full flex items-center px-4 py-3 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors" do %>
          <span class="ml-3">Search Products</span>
        <% end %>
      </div>
    </div>
  </div>
</div>