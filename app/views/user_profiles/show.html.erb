<% content_for :page_title, "User Profile" %>

<div class="max-w-3xl">
  <!-- Header -->
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">User Profile</h1>
    <%= link_to "Edit Profile", edit_user_profile_path, class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
  </div>

  <!-- Profile Information -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mb-6">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-6">Profile Information</h3>
      
      <div class="flex items-start space-x-6 mb-6">
        <!-- Profile Photo -->
        <div class="flex-shrink-0">
          <div class="w-24 h-24 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-700">
            <% if @user.profile_photo.attached? %>
              <%= image_tag @user.profile_photo_avatar, 
                  class: "w-full h-full object-cover", 
                  alt: "#{@user.full_name}'s profile photo" %>
            <% else %>
              <div class="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500">
                <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Basic Info -->
        <div class="flex-1 min-w-0">
          <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= @user.full_name %></h4>
          <p class="text-sm text-gray-500 dark:text-gray-400"><%= @user.email_address %></p>
          <% if @user.job_title.present? %>
            <p class="text-sm text-gray-500 dark:text-gray-400"><%= @user.job_title %></p>
          <% end %>
        </div>
      </div>
      
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <!-- First Name -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">First Name</label>
          <p class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @user.first_name %></p>
        </div>

        <!-- Last Name -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Last Name</label>
          <p class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @user.last_name %></p>
        </div>

        <!-- Email Address -->
        <div class="sm:col-span-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email Address</label>
          <p class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @user.email_address %></p>
        </div>

        <!-- Phone -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Phone</label>
          <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
            <%= @user.phone.presence || content_tag(:span, "Not provided", class: "text-gray-500 dark:text-gray-400") %>
          </p>
        </div>

        <!-- Job Title -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Job Title</label>
          <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
            <%= @user.job_title.presence || content_tag(:span, "Not provided", class: "text-gray-500 dark:text-gray-400") %>
          </p>
        </div>

        <!-- Department -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Agency/Department</label>
          <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
            <%= @user.department.presence || content_tag(:span, "Not provided", class: "text-gray-500 dark:text-gray-400") %>
          </p>
        </div>

        <!-- Organization Name -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            <% if @user.accounts.any? && @user.accounts.first.agency? %>
              City/County/State/Organization
            <% else %>
              Company Name
            <% end %>
          </label>
          <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
            <%= @user.accounts.first&.name || content_tag(:span, "No account associated", class: "text-gray-500 dark:text-gray-400") %>
          </p>
        </div>

        <!-- Admin Status -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Role</label>
          <p class="mt-1">
            <% if @user.admin? %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200">
                Super Admin
              </span>
            <% else %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900/50 dark:text-gray-200">
                User
              </span>
            <% end %>
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Security Information -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-6">Security</h3>
      
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Password</label>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">••••••••</p>
          <%= link_to "Change Password", edit_user_profile_path(anchor: "change-password"), class: "text-sm text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300" %>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Account Created</label>
          <p class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @user.created_at.strftime("%B %d, %Y") %></p>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Last Updated</label>
          <p class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @user.updated_at.strftime("%B %d, %Y") %></p>
        </div>
      </div>
    </div>
  </div>
</div>