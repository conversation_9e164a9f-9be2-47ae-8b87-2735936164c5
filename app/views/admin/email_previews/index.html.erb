<% content_for :page_title, "Email Previews" %>

<div class="sm:flex sm:items-center mb-8">
  <div class="sm:flex-auto">
    <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Email Previews</h1>
    <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
      Preview and test email templates used throughout the application. Click on any email type to see how it will appear to recipients.
    </p>
  </div>
</div>

<!-- Email Preview Categories -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  <% @email_previews.each do |preview| %>
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg transition-colors">
      <div class="px-4 py-5 sm:p-6">
        <div class="mb-6">
          <h3 class="text-base font-medium text-gray-900 dark:text-gray-100 mb-2"><%= preview[:description].gsub(/ email notifications?$/, '') %></h3>
        </div>

        <div class="space-y-2">
          <% preview[:emails].each do |email_method| %>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-900 dark:text-gray-100">
                <%= email_method.to_s.humanize %>
              </span>
              <div class="flex items-center space-x-2">
                <%= link_to admin_email_preview_path(preview[:name].underscore, email_method: email_method), 
                    class: "text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium transition-colors",
                    target: "_blank" do %>
                  HTML
                <% end %>
                <%= link_to admin_email_preview_path(preview[:name].underscore, email_method: email_method, part: 'text'), 
                    class: "text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 font-medium transition-colors",
                    target: "_blank" do %>
                  Text
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  <% end %>
</div>

<% if @email_previews.empty? %>
  <!-- Empty State -->
  <div class="text-center py-12">
    <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No email previews found</h3>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      Create email preview classes in your test/mailers/previews directory to preview emails here.
    </p>
    <div class="mt-6">
      <a href="https://guides.rubyonrails.org/action_mailer_basics.html#previewing-emails" 
         target="_blank"
         class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800">
        Learn about Email Previews
      </a>
    </div>
  </div>
<% end %>