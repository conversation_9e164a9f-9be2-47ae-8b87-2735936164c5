<% content_for :page_title, "System Overview" %>

<div class="mb-8">
  <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">System Overview</h1>
  <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">Monitor system health, user activity, and platform performance.</p>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
  <!-- User Statistics -->
  <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition-colors">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Users</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= number_with_delimiter(@user_stats[:total_users]) %></dd>
          </dl>
        </div>
      </div>
    </div>
    <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3 transition-colors">
      <div class="text-sm">
        <span class="text-gray-600 dark:text-gray-300">Pending approval:</span>
        <span class="font-medium text-yellow-600 dark:bg-yellow-900 dark:text-yellow-200 px-2 py-1 rounded transition-colors"><%= @user_stats[:pending_approval] %></span>
      </div>
    </div>
  </div>

  <!-- Account Statistics -->
  <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition-colors">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Accounts</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= number_with_delimiter(@account_stats[:total_accounts]) %></dd>
          </dl>
        </div>
      </div>
    </div>
    <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3 transition-colors">
      <div class="text-sm">
        <span class="text-gray-600 dark:text-gray-300">Vendors:</span>
        <span class="font-medium text-indigo-600 dark:text-indigo-400"><%= @account_stats[:vendor_accounts] %></span>
        <span class="text-gray-600 dark:text-gray-300 ml-2">Gov:</span>
        <span class="font-medium text-blue-600 dark:text-blue-400"><%= @account_stats[:agency_accounts] %></span>
      </div>
    </div>
  </div>

  <!-- Product Statistics -->
  <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition-colors">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Products</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= number_with_delimiter(@content_stats[:total_products]) %></dd>
          </dl>
        </div>
      </div>
    </div>
    <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3 transition-colors">
      <div class="text-sm">
        <span class="text-gray-600 dark:text-gray-300">Published:</span>
        <span class="font-medium text-green-600 dark:text-green-400"><%= @content_stats[:published_products] %></span>
        <span class="text-gray-600 dark:text-gray-300 ml-2">Draft:</span>
        <span class="font-medium text-gray-600 dark:text-gray-400"><%= @content_stats[:draft_products] %></span>
      </div>
    </div>
  </div>

  <!-- Lead Statistics -->
  <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition-colors">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Leads</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= number_with_delimiter(@engagement_stats[:total_leads]) %></dd>
          </dl>
        </div>
      </div>
    </div>
    <div class="bg-gray-50 dark:bg-gray-700 px-5 py-3 transition-colors">
      <div class="text-sm">
        <span class="text-gray-600 dark:text-gray-300">This week:</span>
        <span class="font-medium text-green-600 dark:text-green-400"><%= @engagement_stats[:recent_leads] %></span>
      </div>
    </div>
  </div>
</div>

<!-- Main Dashboard Grid -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
  <!-- Recent Activity -->
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">Recent Activity</h3>
      
      <!-- Recent Users -->
      <div class="mb-6">
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Latest User Registrations</h4>
        <div class="space-y-2">
          <% @recent_users.each do |user| %>
            <div class="flex items-center justify-between text-sm">
              <div class="flex items-center">
                <div class="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
                <span class="font-medium text-gray-900 dark:text-gray-100"><%= user.email_address %></span>
              </div>
              <span class="text-gray-500 dark:text-gray-400"><%= time_ago_in_words(user.created_at) %> ago</span>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Recent Products -->
      <div class="mb-6">
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Latest Products</h4>
        <div class="space-y-2">
          <% @recent_products.each do |product| %>
            <div class="flex items-center justify-between text-sm">
              <div class="flex items-center">
                <div class="w-2 h-2 bg-purple-400 rounded-full mr-2"></div>
                <span class="font-medium text-gray-900 dark:text-gray-100"><%= truncate(product.name, length: 30) %></span>
              </div>
              <span class="text-gray-500 dark:text-gray-400"><%= time_ago_in_words(product.created_at) %> ago</span>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Recent Leads -->
      <div>
        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Latest Leads</h4>
        <div class="space-y-2">
          <% @recent_leads.each do |lead| %>
            <div class="flex items-center justify-between text-sm">
              <div class="flex items-center">
                <div class="w-2 h-2 bg-yellow-400 rounded-full mr-2"></div>
                <span class="font-medium text-gray-900 dark:text-gray-100">
                  <%= truncate(lead.product&.name || 'Unknown Product', length: 25) %>
                </span>
              </div>
              <span class="text-gray-500 dark:text-gray-400"><%= time_ago_in_words(lead.created_at) %> ago</span>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- System Health -->
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">System Health</h3>
      
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Database Size</span>
          <span class="text-sm text-gray-900 dark:text-gray-100"><%= @system_health[:database_size] %></span>
        </div>
        
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Application Uptime</span>
          <span class="text-sm text-gray-900 dark:text-gray-100"><%= @system_health[:uptime] %></span>
        </div>
        
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Active Sessions</span>
          <span class="text-sm text-gray-900 dark:text-gray-100"><%= @system_health[:active_sessions] %></span>
        </div>
        
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Failed Jobs</span>
          <span class="text-sm text-gray-900 dark:text-gray-100"><%= @system_health[:failed_jobs] %></span>
        </div>
        
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Last Backup</span>
          <span class="text-sm text-gray-900 dark:text-gray-100"><%= @system_health[:last_backup] %></span>
        </div>
      </div>

      <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700 transition-colors">
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Overall Status</span>
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 transition-colors">
            Healthy
          </span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Detailed Statistics Tables -->
<div class="mt-8 grid grid-cols-1 lg:grid-cols-2 gap-8">
  <!-- Account Status Breakdown -->
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">Account Status Breakdown</h3>
      <div class="space-y-3">
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-700 dark:text-gray-300">Approved Accounts</span>
          <span class="text-sm font-medium text-green-600 dark:text-green-400"><%= @account_stats[:approved_accounts] %></span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-700 dark:text-gray-300">Pending Approval</span>
          <span class="text-sm font-medium text-yellow-600 dark:text-yellow-400"><%= @account_stats[:pending_accounts] %></span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-700 dark:text-gray-300">Rejected Accounts</span>
          <span class="text-sm font-medium text-red-600 dark:text-red-400"><%= @account_stats[:rejected_accounts] %></span>
        </div>
      </div>
    </div>
  </div>

  <!-- Lead Status Breakdown -->
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">Lead Status Breakdown</h3>
      <div class="space-y-3">
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-700 dark:text-gray-300">Pending Leads</span>
          <span class="text-sm font-medium text-yellow-600 dark:text-yellow-400"><%= @engagement_stats[:pending_leads] %></span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-700 dark:text-gray-300">Contacted Leads</span>
          <span class="text-sm font-medium text-blue-600 dark:text-blue-400"><%= @engagement_stats[:contacted_leads] %></span>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-gray-700 dark:text-gray-300">Qualified Leads</span>
          <span class="text-sm font-medium text-green-600 dark:text-green-400"><%= @engagement_stats[:qualified_leads] %></span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Growth Chart Placeholder -->
<div class="mt-8">
  <div class="bg-white dark:bg-gray-800 shadow rounded-lg transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">Growth Analytics (Last 30 Days)</h3>
      <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 text-center">
        <div>
          <div class="text-2xl font-bold text-blue-600 dark:text-blue-400"><%= @growth_data[:users].sum { |d| d[:count] } %></div>
          <div class="text-sm text-gray-500 dark:text-gray-400">New Users</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-green-600 dark:text-green-400"><%= @growth_data[:accounts].sum { |d| d[:count] } %></div>
          <div class="text-sm text-gray-500 dark:text-gray-400">New Accounts</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-purple-600 dark:text-purple-400"><%= @growth_data[:products].sum { |d| d[:count] } %></div>
          <div class="text-sm text-gray-500 dark:text-gray-400">New Products</div>
        </div>
        <div>
          <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400"><%= @growth_data[:leads].sum { |d| d[:count] } %></div>
          <div class="text-sm text-gray-500 dark:text-gray-400">New Leads</div>
        </div>
      </div>
    </div>
  </div>
</div>