<% content_for :page_title, "Category Details" %>

<div class="">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100"><%= @category.name %></h1>
    </div>
    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none space-x-2">
      <%= link_to "Edit", edit_admin_category_path(@category), 
          class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
      <%= link_to "Back to Categories", admin_categories_path, 
          class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
    </div>
  </div>

  <div class="mt-8 flow-root">
    <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Category Information</h3>
      </div>
      <div class="px-6 py-4">
        <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @category.name %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Type</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
              <% if @category.root_category? %>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                  Root Category
                </span>
              <% else %>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                  Child Category
                </span>
              <% end %>
            </dd>
          </div>
          <% if @category.child_category? %>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Parent Category</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                <%= link_to @category.parent.name, admin_category_path(@category.parent), class: "text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300" %>
              </dd>
            </div>
          <% end %>
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Products</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @category.products.count %></dd>
          </div>
          <% if @category.root_category? %>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Subcategories</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @category.subcategories.count %></dd>
            </div>
          <% end %>
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @category.created_at.strftime("%B %-d, %Y") %></dd>
          </div>
        </dl>
      </div>
    </div>
  </div>

  <% if @category.root_category? && @category.subcategories.any? %>
    <div class="mt-8">
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Subcategories</h3>
      <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
        <ul class="divide-y divide-gray-200 dark:divide-gray-700">
          <% @category.subcategories.each do |subcategory| %>
            <li class="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                    <%= link_to subcategory.name, admin_category_path(subcategory), class: "hover:text-blue-600 dark:hover:text-blue-400" %>
                  </h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400"><%= pluralize(subcategory.products.count, 'product') %></p>
                </div>
                <div class="flex space-x-2">
                  <%= link_to "Edit", edit_admin_category_path(subcategory), class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                </div>
              </div>
            </li>
          <% end %>
        </ul>
      </div>
    </div>
  <% end %>

  <% if @category.products.any? %>
    <div class="mt-8">
      <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Products in this Category</h3>
      <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
        <ul class="divide-y divide-gray-200 dark:divide-gray-700">
          <% @category.products.includes(:account).each do |product| %>
            <li class="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                    <%= link_to product.name, admin_product_path(product), class: "hover:text-blue-600 dark:hover:text-blue-400" %>
                  </h4>
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    <%= product.account.name %>
                  </p>
                </div>
                <div>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <%= product.published? ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' %>">
                    <%= product.published? ? 'Published' : 'Unpublished' %>
                  </span>
                </div>
              </div>
            </li>
          <% end %>
        </ul>
      </div>
    </div>
  <% end %>
</div>