<% content_for :page_title, "Categories" %>

<div class="sm:flex sm:items-center mb-6">
  <div class="sm:flex-auto">
    <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Categories</h1>
    <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">Manage product categories in the system.</p>
  </div>
  <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
    <%= link_to "Add Category", new_admin_category_path, 
        class: "inline-flex items-center justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600" %>
  </div>
</div>

<!-- Search -->
<div class="mb-6">
  <%= form_with url: admin_categories_path, method: :get, local: true, class: "flex gap-4 items-center" do |form| %>
    <%= form.text_field :search, placeholder: "Search categories...", 
        value: params[:search], 
        class: "block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
    <%= form.submit "Search", class: "inline-flex items-center justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500" %>
    <% if params[:search].present? %>
      <%= link_to "Clear", admin_categories_path, class: "text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300" %>
    <% end %>
  <% end %>
</div>

<!-- Desktop Table View (hidden on mobile) -->
<div class="hidden md:block mt-8 flow-root">
  <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
    <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
      <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
        <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Category Hierarchy
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Type
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Products
              </th>
              <th scope="col" class="relative px-6 py-3">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            <% @hierarchical_categories.each do |item| %>
              <% category = item[:category] %>
              <% depth = item[:depth] %>
              <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 <%= 'bg-blue-50 dark:bg-blue-900/20' if depth == 0 %>">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm <%= 'font-bold' if depth == 0 %> text-gray-900 dark:text-gray-100" style="margin-left: <%= depth * 1.5 %>rem;">
                    <% if depth > 0 %>
                      <%= '└─ ' if depth == 1 %>
                      <%= '   └─ ' if depth == 2 %>
                      <%= '      └─ ' if depth == 3 %>
                      <%= '         └─ ' if depth >= 4 %>
                    <% end %>
                    <%= category.name %>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if depth == 0 %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                      Root Category
                    </span>
                  <% else %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                      Level <%= depth %> Category
                    </span>
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900 dark:text-gray-100">
                    <%= category.products.count %>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex items-center justify-end space-x-2">
                    <%= link_to "View", admin_category_path(category),
                        class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Mobile Card View (visible on mobile only) -->
<div class="md:hidden mt-8 space-y-4">
  <% @hierarchical_categories.each do |item| %>
    <% category = item[:category] %>
    <% depth = item[:depth] %>
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden <%= 'border-l-4 border-blue-500' if depth == 0 %>">
      <div class="px-4 py-5 sm:px-6">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 <%= 'font-bold' if depth == 0 %>" style="margin-left: <%= depth * 1.0 %>rem;">
              <% if depth > 0 %>
                <%= '└─ ' if depth == 1 %>
                <%= '   └─ ' if depth == 2 %>
                <%= '      └─ ' if depth == 3 %>
                <%= '         └─ ' if depth >= 4 %>
              <% end %>
              <%= category.name %>
            </h3>
          </div>
          <div class="flex space-x-2">
            <% if depth == 0 %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                Root Category
              </span>
            <% else %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                Level <%= depth %> Category
              </span>
            <% end %>
          </div>
        </div>
        
        <div class="mt-4 space-y-3">
          <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Products:</span>
              <span class="font-medium text-gray-900 dark:text-gray-100"><%= category.products.count %></span>
            </div>
          </div>
        </div>
        
        <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
          <%= link_to "View Details", admin_category_path(category), class: "inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600" %>
        </div>
      </div>
    </div>
  <% end %>
</div>

<% if @hierarchical_categories.empty? %>
  <div class="text-center py-12">
    <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z" />
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No categories</h3>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by creating your first category.</p>
    <div class="mt-6">
      <%= link_to "Add Category", new_admin_category_path, 
          class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
    </div>
  </div>
<% end %>