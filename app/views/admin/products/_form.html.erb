<%= form_with model: [:admin, @product], url: form_url, local: true, class: "space-y-6" do |form| %>
  <% if @product.errors.any? %>
    <div class="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
      <div class="flex">
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
            There were <%= pluralize(@product.errors.count, "error") %> with your submission:
          </h3>
          <div class="mt-2 text-sm text-red-700 dark:text-red-300">
            <ul class="list-disc pl-5 space-y-1">
              <% @product.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Product Name -->
  <div>
    <%= form.label :name, "Product Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <%= form.text_field :name, 
        class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
        placeholder: "Enter product name" %>
  </div>

  <!-- Account -->
  <div>
    <%= form.label :account_id, "Account", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <div class="mt-2 grid grid-cols-1">
      <%= form.select :account_id,
          options_from_collection_for_select(@accounts, :id, :name, @product.account_id),
          { include_blank: "No account (admin-managed product)" },
          { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 sm:text-sm" } %>
      <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
        <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
      </svg>
    </div>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      Leave blank to create an admin-managed product without vendor information.
    </p>
  </div>

  <!-- Description -->
  <div>
    <%= form.label :description, "Description", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <%= form.text_area :description, 
        rows: 6,
        class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
        placeholder: "Enter a brief description of the product (up to 10 sentences)" %>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      Maximum 1000 characters (approximately 10 sentences). <span id="description-count"><%= @product.description&.length || 0 %></span>/1000 characters used.
    </p>
  </div>

  <!-- Website -->
  <div>
    <%= form.label :website, "Website", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <%= form.url_field :website, 
        class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
        placeholder: "https://example.com" %>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      Product website or landing page URL
    </p>
  </div>

  <!-- Categories -->
  <div>
    <%= form.label :category_ids, "Categories", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <div class="mt-2 space-y-2 max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-3 bg-gray-50 dark:bg-gray-900">
      <% @categories.each do |category| %>
        <div class="flex items-center">
          <%= check_box_tag 'product[category_ids][]', category.id, @product.category_ids.include?(category.id), 
              class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded" %>
          <%= label_tag "product_category_ids_#{category.id}", category.full_name, class: "ml-2 text-sm text-gray-900 dark:text-gray-100" %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Certifications -->
  <div>
    <%= form.label :certification_ids, "Certifications", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <div class="mt-2">
      <div class="space-y-2 max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-3 bg-white dark:bg-gray-700">
        <% @certifications.each do |certification| %>
          <div class="flex items-start">
            <%= check_box_tag "product[certification_ids][]", certification.id, 
                @product.certification_ids.include?(certification.id),
                class: "mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700" %>
            <div class="ml-3">
              <%= label_tag "product_certification_ids_#{certification.id}", certification.name, 
                  class: "block text-sm font-medium text-gray-900 dark:text-gray-100" %>
              <% if certification.description.present? %>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                  <%= truncate(certification.description, length: 100) %>
                </p>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Select any certifications this product has or complies with.
      </p>
    </div>
  </div>

  <!-- Pricing Information Section -->
  <div>
    <%= form.label :pricing_model, "Pricing Model", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <%= form.text_area :pricing_model, 
        rows: 3,
        class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
        placeholder: "e.g., Per user per month, One-time license fee, Custom pricing based on requirements" %>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      Describe the pricing model (e.g., per user, per transaction, flat rate, etc.)
    </p>
  </div>

  <div>
    <%= form.label :pricing, "Sample Pricing", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    <%= form.text_area :pricing, 
        rows: 4,
        class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
        placeholder: "e.g., Starter: $29/month for up to 10 users, Professional: $99/month for up to 100 users, Enterprise: Contact for custom pricing" %>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      Provide sample pricing tiers or examples to help potential customers understand the pricing structure
    </p>
  </div>

  <!-- Logo Upload Section -->
  <div>
    <%= form.label :logo, "Product Logo", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    
    <div class="mt-2">
      <% if @product.logo.attached? %>
        <div class="mb-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-800">
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Current logo:</p>
          <div class="flex items-start space-x-4">
            <div class="flex-shrink-0">
              <%= image_tag @product.logo, 
                  class: "h-20 w-20 object-cover rounded-lg border border-gray-200 dark:border-gray-600",
                  alt: "Current product logo" %>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                <%= @product.logo.filename %>
              </p>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                <%= number_to_human_size(@product.logo.byte_size) %>
              </p>
            </div>
          </div>
        </div>
      <% end %>
      
      <%= form.file_field :logo, 
          class: "block w-full text-sm text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer bg-gray-50 dark:bg-gray-700 focus:ring-blue-500 focus:border-blue-500 file:mr-4 file:py-2 file:px-4 file:rounded-l-lg file:border-0 file:text-sm file:font-medium file:bg-gray-100 file:text-gray-700 hover:file:bg-gray-200 dark:file:bg-gray-600 dark:file:text-gray-200 dark:hover:file:bg-gray-500",
          accept: "image/*" %>
      
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
        Upload a logo for the product. Supported formats: JPEG, PNG, GIF, WebP. Maximum file size: 5MB.
      </p>
      
      <% if @product.logo.attached? %>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Upload a new file to replace the current logo, or leave empty to keep the current one.
        </p>
      <% end %>
    </div>
  </div>

  <!-- Featured Video Upload Section -->
  <div>
    <%= form.label :featured_video, "Featured Video", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
    
    <div class="mt-2">
      <% if @product.featured_video.attached? %>
        <div class="mb-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-800">
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Current featured video:</p>
          <div class="flex items-start space-x-4">
            <div class="flex-shrink-0">
              <div class="h-20 w-32 bg-gray-100 dark:bg-gray-600 rounded-lg flex items-center justify-center border border-gray-200 dark:border-gray-600">
                <svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                <%= @product.featured_video.filename %>
              </p>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                <%= number_to_human_size(@product.featured_video.byte_size) %>
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                <%= @product.featured_video.content_type %>
              </p>
            </div>
          </div>
        </div>
      <% end %>
      
      <%= form.file_field :featured_video, 
          class: "block w-full text-sm text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer bg-gray-50 dark:bg-gray-700 focus:ring-blue-500 focus:border-blue-500 file:mr-4 file:py-2 file:px-4 file:rounded-l-lg file:border-0 file:text-sm file:font-medium file:bg-gray-100 file:text-gray-700 hover:file:bg-gray-200 dark:file:bg-gray-600 dark:file:text-gray-200 dark:hover:file:bg-gray-500",
          accept: "video/*" %>
      
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
        Upload a featured video for the product. This will be the main demo video shown on the product page. Supported formats: MP4, WebM, OGG, AVI, MOV, WMV, FLV, MKV. Maximum file size: 50MB.
      </p>
      
      <% if @product.featured_video.attached? %>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Upload a new file to replace the current featured video, or leave empty to keep the current one.
        </p>
      <% end %>
    </div>
  </div>

  <!-- Publishing Options Section -->
  <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
    <div class="flex items-center">
      <%= form.check_box :published, class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded" %>
      <%= form.label :published, "Published", class: "ml-2 block text-sm text-gray-900 dark:text-gray-100" %>
      <p class="ml-2 text-sm text-gray-500 dark:text-gray-400">Make this product visible to the public</p>
    </div>
  </div>

  <!-- Form Actions -->
  <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-600">
    <%= link_to "Cancel", cancel_url, 
        class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
    
    <%= form.submit submit_text, 
        class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
  </div>
<% end %>