<% content_for :page_title, "Product Management" %>

<div class="">
  <div class="sm:flex sm:items-center mb-8">
    <div class="sm:flex-auto">
      <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Product Management</h1>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
        Manage products and their listings across all vendors.
      </p>
    </div>
    <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
      <%= link_to "New Product", new_admin_product_path, 
          class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
    </div>
  </div>

<!-- Search -->
<div class="mb-6">
  <%= form_with url: admin_products_path, method: :get, local: true, class: "flex gap-4 items-center" do |form| %>
    <%= form.text_field :search, placeholder: "Search products...", 
        value: params[:search], 
        class: "block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
    
    <div class="grid grid-cols-1 min-w-40">
      <%= form.select :published,
          options_for_select([
            ['All Status', ''],
            ['Published', 'true'],
            ['Unpublished', 'false']
          ], params[:published]),
          {},
          { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-2 pr-8 pl-3 text-sm text-gray-900 dark:text-gray-100 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 transition-colors" } %>
      <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
        <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
      </svg>
    </div>
    
    <%= form.submit "Search", class: "inline-flex items-center justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500" %>
    <% if params[:search].present? || params[:published].present? %>
      <%= link_to "Clear", admin_products_path, class: "text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300" %>
    <% end %>
  <% end %>
</div>

  <!-- Desktop Table View (hidden on mobile) -->
  <div class="hidden md:block mt-8 flow-root">
    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Product</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Company</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Leads</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Status</th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
              <% @products.each do |product| %>
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                    <div class="flex items-center">
                      <div>
                        <div class="font-medium text-gray-900 dark:text-gray-100">
                          <%= link_to product.name, admin_product_path(product), class: "hover:text-blue-600 dark:hover:text-blue-400" %>
                        </div>
                        <div class="text-gray-500 dark:text-gray-400 text-xs">
                          <%= truncate(strip_tags(product.description.to_s), length: 50) %>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-900 dark:text-gray-100">
                    <div class="flex items-center">
                      <% if product.account&.logo&.attached? %>
                        <div class="h-8 w-8 flex-shrink-0 mr-2">
                          <%= image_tag product.account.logo_thumbnail, class: "h-8 w-8 rounded-full object-cover" %>
                        </div>
                      <% end %>
                      <div>
                        <div class="font-medium text-gray-900 dark:text-gray-100">
                          <% if product.account %>
                            <%= link_to product.account.name, admin_account_path(product.account), class: "hover:text-blue-600 dark:hover:text-blue-400" %>
                          <% else %>
                            <span class="text-gray-500 dark:text-gray-400 italic">Admin-managed</span>
                          <% end %>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <%= link_to admin_product_leads_path(product), class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 font-medium" do %>
                      <%= product.leads.count %>
                    <% end %>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                      <%= product.published? ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' %>">
                      <%= product.published? ? 'Published' : 'Unpublished' %>
                    </span>
                  </td>
                  <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                    <div class="flex justify-end">
                      <%= link_to "View", admin_product_path(product), class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile Card View (visible on mobile only) -->
  <div class="md:hidden mt-8 space-y-4">
    <% @products.each do |product| %>
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div class="px-4 py-5 sm:px-6">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                <%= link_to product.name, admin_product_path(product), class: "hover:text-blue-600 dark:hover:text-blue-400" %>
              </h3>
              <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                <%= truncate(strip_tags(product.description.to_s), length: 80) %>
              </p>
            </div>
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
              <%= product.published? ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' %>">
              <%= product.published? ? 'Published' : 'Unpublished' %>
            </span>
          </div>
          
          <div class="mt-4 space-y-3">
            <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
              <div class="flex justify-between text-sm">
                <span class="text-gray-500 dark:text-gray-400">Company:</span>
                <div class="text-right flex items-center">
                  <% if product.account&.logo&.attached? %>
                    <div class="h-6 w-6 flex-shrink-0 mr-2">
                      <%= image_tag product.account.logo_thumbnail, class: "h-6 w-6 rounded-full object-cover" %>
                    </div>
                  <% end %>
                  <div class="font-medium text-gray-900 dark:text-gray-100">
                    <% if product.account %>
                      <%= link_to product.account.name, admin_account_path(product.account), class: "hover:text-blue-600 dark:hover:text-blue-400" %>
                    <% else %>
                      <span class="text-gray-500 dark:text-gray-400 italic">Admin-managed</span>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Leads:</span>
              <%= link_to admin_product_leads_path(product), class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 font-medium" do %>
                <%= product.leads.count %>
              <% end %>
            </div>
          </div>
          
          <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
            <%= link_to "View Details", admin_product_path(product), class: "inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600" %>
          </div>
        </div>
      </div>
    <% end %>
  </div>

  <%= render partial: "shared/pagination", locals: { pagy: @pagy } %>

  <% if @products.empty? %>
    <div class="text-center py-8">
      <p class="text-sm text-gray-500 dark:text-gray-400">No products found.</p>
    </div>
  <% end %>
</div>