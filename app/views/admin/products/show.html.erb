<% content_for :page_title, "Product Details" %>

<div class="">
    <!-- Header -->
    <div class="md:flex md:items-center md:justify-between mb-8">
      <div class="min-w-0 flex-1">
        <h1 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl ">
          <%= @product.name %>
        </h1>
      </div>
      <div class="mt-4 flex md:ml-4 md:mt-0 space-x-3">
        <%= link_to "View marketplace page", marketplace_product_path(@product), target: '_blank',
            class: "inline-flex items-center rounded-md bg-emerald-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-emerald-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-emerald-600" %>
        <%= link_to "Manage Leads", admin_product_leads_path(@product), 
            class: "inline-flex items-center rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600" %>
        <%= link_to "Edit Product", edit_admin_product_path(@product), 
            class: "inline-flex items-center rounded-md bg-white dark:bg-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-100 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600" %>
        <%= link_to "← Back to Products", admin_products_path,
            class: "inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600" %>
      </div>
    </div>

    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
      <!-- Product Information -->
      <div class="lg:col-span-2">
        <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Product Information</h3>
            <div class="mt-5 border-t border-gray-200 dark:border-gray-600">
              <dl class="divide-y divide-gray-200 dark:divide-gray-600">
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Product Name</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @product.name %></dd>
                </div>
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Company</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0">
                    <% if @product.account %>
                      <%= link_to @product.account.name, 
                          admin_account_path(@product.account), 
                          class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                    <% else %>
                      <span class="text-gray-500 dark:text-gray-400 italic">Admin-managed product (no account)</span>
                    <% end %>
                  </dd>
                </div>
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Published</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0">
                    <%= @product.published? ? 'Yes' : 'No' %>
                  </dd>
                </div>
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @product.created_at.strftime("%B %d, %Y at %I:%M %p") %></dd>
                </div>
              </dl>
            </div>
          </div>
        </div>

        <!-- Description -->
        <% if @product.description.present? %>
          <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Description</h3>
              <div class="mt-3 prose prose-sm max-w-none text-gray-900 dark:text-gray-100">
                <%= @product.description %>
              </div>
            </div>
          </div>
        <% end %>



        <!-- Categories -->
        <% if @product.categories.any? %>
          <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Categories</h3>
              <div class="mt-3">
                <div class="flex flex-wrap gap-2">
                  <% @product.categories.each do |category| %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                      <%= category.name %>
                    </span>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Features -->
        <% if @product.product_features.any? %>
          <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center justify-between">
                <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Features</h3>
                <%= link_to "Manage Features", admin_product_product_features_path(@product), 
                    class: "text-sm text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
              </div>
              <div class="mt-4">
                <div class="grid grid-cols-1 gap-4">
                  <% @product.product_features.ordered.each do |feature| %>
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div class="flex items-start justify-between">
                        <div class="flex-1">
                          <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100"><%= feature.name %></h4>
                          <% if feature.description.present? %>
                            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                              <%= truncate(feature.description, length: 100) %>
                            </p>
                          <% end %>
                          <% if feature.attachment.attached? %>
                            <div class="mt-2">
                              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                <%= feature.image? ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200' : 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200' %>">
                                <%= feature.image? ? 'Image' : feature.video? ? 'Video' : 'File' %>
                              </span>
                            </div>
                          <% end %>
                        </div>
                        <div class="ml-4">
                          <%= link_to "View", admin_product_product_feature_path(@product, feature), 
                              class: "text-sm text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                        </div>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% else %>
          <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center justify-between">
                <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Features</h3>
                <%= link_to "Add Features", admin_product_product_features_path(@product), 
                    class: "text-sm text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
              </div>
              <div class="mt-4">
                <p class="text-sm text-gray-500 dark:text-gray-400">No features added yet.</p>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Customers -->
        <% if @product.product_customers.any? %>
          <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center justify-between">
                <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Customers</h3>
                <%= link_to "Manage Customers", admin_product_product_customers_path(@product), 
                    class: "text-sm text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
              </div>
              <div class="mt-4">
                <div class="grid grid-cols-1 gap-4">
                  <% @product.product_customers.ordered.each do |customer| %>
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div class="flex items-start justify-between">
                        <div class="flex-1">
                          <div class="flex items-center">
                            <% if customer.logo.attached? && customer.image? %>
                              <div class="h-8 w-8 flex-shrink-0 mr-3">
                                <%= image_tag customer.logo, class: "h-8 w-8 object-contain rounded" %>
                              </div>
                            <% end %>
                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100"><%= customer.name %></h4>
                          </div>
                          <div class="mt-1 flex items-center">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                              <%= customer.published? ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' %>">
                              <%= customer.published? ? 'Published' : 'Draft' %>
                            </span>
                          </div>
                          <% if customer.description.present? %>
                            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                              <%= truncate(customer.description, length: 100) %>
                            </p>
                          <% end %>
                        </div>
                        <div class="ml-4">
                          <%= link_to "View", admin_product_product_customer_path(@product, customer), 
                              class: "text-sm text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                        </div>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% else %>
          <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <div class="flex items-center justify-between">
                <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Customers</h3>
                <%= link_to "Add Customers", admin_product_product_customers_path(@product), 
                    class: "text-sm text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
              </div>
              <div class="mt-4">
                <p class="text-sm text-gray-500 dark:text-gray-400">No customers added yet.</p>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Certifications -->
        <% if @product.certifications.any? %>
          <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Certifications</h3>
              <div class="mt-3">
                <div class="flex flex-wrap gap-2">
                  <% @product.certifications.each do |certification| %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100" title="<%= certification.description %>">
                      <%= certification.name %>
                    </span>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>

      <!-- Company & Account Info -->
      <div class="lg:col-span-1">
        <!-- Company Information -->
        <% if @product.account %>
          <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Company</h3>
              <div class="mt-3">
                <div class="flex items-center">
                  <% if @product.account.logo.attached? %>
                    <div class="h-12 w-12 flex-shrink-0 mr-3">
                      <%= image_tag @product.account.logo_small, class: "h-12 w-12 rounded-full object-cover" %>
                    </div>
                  <% end %>
                  <div>
                    <p class="font-medium text-gray-900 dark:text-gray-100">
                      <%= link_to @product.account.name, admin_account_path(@product.account), class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                    </p>
                    <% if @product.website.present? %>
                      <p class="text-sm text-gray-500 dark:text-gray-400">
                        <%= link_to @product.website, @product.website, target: "_blank", class: "hover:text-blue-600 dark:hover:text-blue-400" %>
                      </p>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Account Information -->
        <% if @product.account %>
          <div class="<%= 'mt-6' %> bg-white dark:bg-gray-800 shadow sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Account Information</h3>
              <div class="mt-3">
                <div class="text-sm">
                  <p class="font-medium text-gray-900 dark:text-gray-100">
                    <%= link_to @product.account.name, admin_account_path(@product.account), class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                  </p>
                  <p class="text-gray-500 dark:text-gray-400">
                    <%= @product.account.account_type.capitalize %> Account
                  </p>
                  <p class="text-gray-500 dark:text-gray-400">
                    Status: <%= @product.account.status.capitalize %>
                  </p>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Quick Stats -->
        <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Quick Stats</h3>
            <dl class="mt-3 grid grid-cols-1 gap-3">
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Leads</dt>
                <dd class="text-2xl font-semibold text-gray-900 dark:text-gray-100"><%= @product.leads.count %></dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Categories</dt>
                <dd class="text-2xl font-semibold text-gray-900 dark:text-gray-100"><%= @product.categories.count %></dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Features</dt>
                <dd class="text-2xl font-semibold text-gray-900 dark:text-gray-100"><%= @product.product_features.count %></dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Certifications</dt>
                <dd class="text-2xl font-semibold text-gray-900 dark:text-gray-100"><%= @product.certifications.count %></dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Product Videos Section -->
    <div class="mt-8 bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Product Videos</h3>
          <div class="flex space-x-2">
            <%= link_to 'Manage Videos', admin_product_product_videos_path(@product), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
            <%= link_to 'Add Video', new_admin_product_product_video_path(@product), class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
          </div>
        </div>

        <% if @product.product_videos.any? %>
          <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
              <thead class="bg-gray-50 dark:bg-gray-900/50">
                <tr>
                  <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Title</th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Status</th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Created</th>
                  <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                    <span class="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
                <% @product.product_videos.by_position.limit(6).each do |video| %>
                  <tr>
                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-gray-100 sm:pl-6">
                      <%= video.title %>
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= video.published? ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' %>">
                        <%= video.published? ? 'Published' : 'Draft' %>
                      </span>
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                      <%= video.created_at.strftime("%b %d, %Y") %>
                    </td>
                    <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                      <%= link_to 'View details', admin_product_product_video_path(@product, video), class: 'text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300' %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
          
          <% if @product.product_videos.count > 6 %>
            <div class="mt-6 text-center">
              <%= link_to "View all #{@product.product_videos.count} videos", admin_product_product_videos_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/50 hover:bg-indigo-200 dark:hover:bg-indigo-900/70 transition-colors' %>
            </div>
          <% end %>
        <% else %>
          <div class="text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No videos</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by adding the first product video.</p>
            <div class="mt-6">
              <%= link_to 'Add Video', new_admin_product_product_video_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Case Studies Section -->
    <div class="mt-8 bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Case Studies</h3>
          <div class="flex space-x-2">
            <%= link_to 'Manage Case Studies', admin_product_case_studies_path(@product), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
            <%= link_to 'Add Case Study', new_admin_product_case_study_path(@product), class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
          </div>
        </div>

        <% if @product.case_studies.any? %>
          <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
              <thead class="bg-gray-50 dark:bg-gray-900/50">
                <tr>
                  <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Title</th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Status</th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Created</th>
                  <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                    <span class="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
                <% @product.case_studies.by_position.limit(6).each do |case_study| %>
                  <tr>
                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-gray-100 sm:pl-6">
                      <%= case_study.title %>
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= case_study.published? ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' %>">
                        <%= case_study.published? ? 'Published' : 'Draft' %>
                      </span>
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                      <%= case_study.created_at.strftime("%b %d, %Y") %>
                    </td>
                    <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                      <%= link_to 'View details', admin_product_case_study_path(@product, case_study), class: 'text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300' %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
          
          <% if @product.case_studies.count > 6 %>
            <div class="mt-6 text-center">
              <%= link_to "View all #{@product.case_studies.count} case studies", admin_product_case_studies_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/50 hover:bg-indigo-200 dark:hover:bg-indigo-900/70 transition-colors' %>
            </div>
          <% end %>
        <% else %>
          <div class="text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No case studies</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by adding the first case study.</p>
            <div class="mt-6">
              <%= link_to 'Add Case Study', new_admin_product_case_study_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Product Content Section -->
    <div class="mt-8 bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
      <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Product Content</h3>
          <div class="flex space-x-2">
            <%= link_to 'Manage Content', admin_product_product_contents_path(@product), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
            <%= link_to 'Add Content', new_admin_product_product_content_path(@product), class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
          </div>
        </div>

        <% if @product.product_content.any? %>
          <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
            <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
              <thead class="bg-gray-50 dark:bg-gray-900/50">
                <tr>
                  <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Title</th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Status</th>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Created</th>
                  <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                    <span class="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
                <% @product.product_content.by_position.limit(6).each do |content| %>
                  <tr>
                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-gray-100 sm:pl-6">
                      <%= content.title %>
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= content.published? ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' %>">
                        <%= content.published? ? 'Published' : 'Draft' %>
                      </span>
                    </td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                      <%= content.created_at.strftime("%b %d, %Y") %>
                    </td>
                    <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                      <%= link_to 'View details', admin_product_product_content_path(@product, content), class: 'text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300' %>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>
          
          <% if @product.product_content.count > 6 %>
            <div class="mt-6 text-center">
              <%= link_to "View all #{@product.product_content.count} content items", admin_product_product_contents_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/50 hover:bg-indigo-200 dark:hover:bg-indigo-900/70 transition-colors' %>
            </div>
          <% end %>
        <% else %>
          <div class="text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No content</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by adding the first product content.</p>
            <div class="mt-6">
              <%= link_to 'Add Content', new_admin_product_product_content_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
</div>