<% content_for :page_title, "Lists" %>

<div class="sm:flex sm:items-center">
  <div class="sm:flex-auto">
    <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Agency Lists</h1>
    <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">Manage lists created by agency users across the platform.</p>
  </div>
</div>

<!-- Desktop Table View (hidden on mobile) -->
<div class="hidden md:block mt-8">
  <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
    <% if @lists.any? %>
      <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">List</th>
            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Agency</th>
            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Items</th>
            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Created</th>
            <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
              <span class="sr-only">Actions</span>
            </th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          <% @lists.each do |list| %>
            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
              <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                <div>
                  <div class="font-medium text-gray-900 dark:text-gray-100">
                    <%= link_to list.name, admin_list_path(list), class: "hover:text-indigo-600 dark:hover:text-indigo-400" %>
                  </div>
                  <% if list.description.present? %>
                    <div class="text-gray-500 dark:text-gray-400 text-sm">
                      <%= truncate(list.description, length: 60) %>
                    </div>
                  <% end %>
                </div>
              </td>
              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                <%= link_to list.account.name, admin_account_path(list.account), 
                    class: "hover:text-indigo-600 dark:hover:text-indigo-400" %>
              </td>
              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                <%= pluralize(list.list_items.count, 'item') %>
              </td>
              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                <%= time_ago_in_words(list.created_at) %> ago
              </td>
              <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                <div class="flex justify-end space-x-2">
                  <%= link_to "View", admin_list_path(list), class: "text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300" %>
                  <%= link_to "Delete", admin_list_path(list), method: :delete, 
                      data: { confirm: "Are you sure? This will permanently delete this list and all its items." },
                      class: "text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300" %>
                </div>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    <% else %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No lists created yet</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Lists will appear here when agency users start creating them.</p>
      </div>
    <% end %>
  </div>
</div>

<!-- Mobile Card View (visible on mobile only) -->
<div class="md:hidden mt-8">
  <% if @lists.any? %>
    <div class="space-y-4">
      <% @lists.each do |list| %>
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
          <div class="px-4 py-5 sm:px-6">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                  <%= link_to list.name, admin_list_path(list), class: "hover:text-indigo-600 dark:hover:text-indigo-400" %>
                </h3>
                <% if list.description.present? %>
                  <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    <%= truncate(list.description, length: 100) %>
                  </p>
                <% end %>
              </div>
            </div>
            
            <div class="mt-4 space-y-3">
              <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500 dark:text-gray-400">Agency:</span>
                  <span class="text-gray-900 dark:text-gray-100">
                    <%= link_to list.account.name, admin_account_path(list.account), 
                        class: "hover:text-indigo-600 dark:hover:text-indigo-400" %>
                  </span>
                </div>
              </div>
              
              <div class="flex justify-between text-sm">
                <span class="text-gray-500 dark:text-gray-400">Items:</span>
                <span class="text-gray-900 dark:text-gray-100"><%= pluralize(list.list_items.count, 'item') %></span>
              </div>
              
              <div class="flex justify-between text-sm">
                <span class="text-gray-500 dark:text-gray-400">Created:</span>
                <span class="text-gray-900 dark:text-gray-100"><%= time_ago_in_words(list.created_at) %> ago</span>
              </div>
            </div>
            
            <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 flex space-x-3">
              <%= link_to "View", admin_list_path(list), 
                  class: "inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600" %>
              <%= link_to "Delete", admin_list_path(list), method: :delete, 
                  data: { confirm: "Are you sure? This will permanently delete this list and all its items." },
                  class: "inline-flex items-center px-3 py-1.5 border border-red-300 dark:border-red-600 text-sm font-medium rounded-md text-red-700 dark:text-red-300 bg-white dark:bg-gray-700 hover:bg-red-50 dark:hover:bg-red-900" %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <div class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No lists created yet</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Lists will appear here when agency users start creating them.</p>
    </div>
  <% end %>
</div>