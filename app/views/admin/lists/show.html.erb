<% content_for :page_title, @list.name %>

<div class="sm:flex sm:items-center">
  <div class="sm:flex-auto">
    <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100"><%= @list.name %></h1>
    <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
      List owned by <%= link_to @list.account.name, admin_account_path(@list.account), class: "text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300" %>
      <% if @list.description.present? %>
        • <%= @list.description %>
      <% end %>
    </p>
  </div>
  <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
    <div class="flex space-x-3">
      <%= link_to "Delete List", admin_list_path(@list), method: :delete, 
          data: { confirm: "Are you sure? This will permanently delete this list and all its items." },
          class: "inline-flex items-center justify-center rounded-md border border-red-300 dark:border-red-600 bg-white dark:bg-gray-700 px-4 py-2 text-sm font-medium text-red-700 dark:text-red-300 shadow-sm hover:bg-red-50 dark:hover:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2" %>
      <%= link_to "Back to Lists", admin_lists_path, 
          class: "inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto" %>
    </div>
  </div>
</div>

<!-- List Stats -->
<div class="mt-8 grid grid-cols-1 gap-5 sm:grid-cols-3">
  <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Items</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= @list_items.count %></dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Created</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= @list.created_at.strftime("%B %-d, %Y") %></dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Last Updated</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= time_ago_in_words(@list.updated_at) %> ago</dd>
          </dl>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Desktop Table View (hidden on mobile) -->
<div class="hidden md:block mt-8">
  <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
    <% if @list_items.any? %>
      <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Product</th>
            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Vendor</th>
            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Status</th>
            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Added</th>
            <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
              <span class="sr-only">Actions</span>
            </th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          <% @list_items.each do |list_item| %>
            <% product = list_item.product %>
            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
              <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                <div class="flex items-center">
                  <div class="h-10 w-10 flex-shrink-0">
                    <% if product.logo.attached? %>
                      <%= image_tag product.logo, class: "h-10 w-10 rounded-full object-cover" %>
                    <% else %>
                      <div class="h-10 w-10 rounded-full bg-indigo-100 dark:bg-indigo-800 flex items-center justify-center">
                        <span class="text-sm font-medium text-indigo-600 dark:text-indigo-300">
                          <%= product.name.first.upcase %>
                        </span>
                      </div>
                    <% end %>
                  </div>
                  <div class="ml-4">
                    <div class="font-medium text-gray-900 dark:text-gray-100">
                      <%= link_to product.name, admin_product_path(product), class: "hover:text-indigo-600 dark:hover:text-indigo-400" %>
                    </div>
                    <% if product.tagline.present? %>
                      <div class="text-gray-500 dark:text-gray-400 text-sm">
                        <%= truncate(product.tagline, length: 60) %>
                      </div>
                    <% end %>
                  </div>
                </div>
              </td>
              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                <%= link_to product.account.name, admin_account_path(product.account), 
                    class: "hover:text-indigo-600 dark:hover:text-indigo-400" %>
              </td>
              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                <% if product.published? %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                    Published
                  </span>
                <% else %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
                    Draft
                  </span>
                <% end %>
              </td>
              <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                <%= time_ago_in_words(list_item.created_at) %> ago
              </td>
              <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                <%= link_to "View Product", marketplace_product_path(product), 
                    target: "_blank",
                    class: "text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300" %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    <% else %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No products in this list</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">This list is empty.</p>
      </div>
    <% end %>
  </div>
</div>

<!-- Mobile Card View (visible on mobile only) -->
<div class="md:hidden mt-8">
  <% if @list_items.any? %>
    <div class="space-y-4">
      <% @list_items.each do |list_item| %>
        <% product = list_item.product %>
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
          <div class="px-4 py-5 sm:px-6">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center">
                  <div class="h-10 w-10 flex-shrink-0 mr-3">
                    <% if product.logo.attached? %>
                      <%= image_tag product.logo, class: "h-10 w-10 rounded-full object-cover" %>
                    <% else %>
                      <div class="h-10 w-10 rounded-full bg-indigo-100 dark:bg-indigo-800 flex items-center justify-center">
                        <span class="text-sm font-medium text-indigo-600 dark:text-indigo-300">
                          <%= product.name.first.upcase %>
                        </span>
                      </div>
                    <% end %>
                  </div>
                  <div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                      <%= link_to product.name, admin_product_path(product), class: "hover:text-indigo-600 dark:hover:text-indigo-400" %>
                    </h3>
                    <% if product.tagline.present? %>
                      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        <%= truncate(product.tagline, length: 80) %>
                      </p>
                    <% end %>
                  </div>
                </div>
              </div>
              <% if product.published? %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                  Published
                </span>
              <% else %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
                  Draft
                </span>
              <% end %>
            </div>
            
            <div class="mt-4 space-y-3">
              <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500 dark:text-gray-400">Vendor:</span>
                  <span class="text-gray-900 dark:text-gray-100">
                    <%= link_to product.account.name, admin_account_path(product.account), 
                        class: "hover:text-indigo-600 dark:hover:text-indigo-400" %>
                  </span>
                </div>
              </div>
              
              <div class="flex justify-between text-sm">
                <span class="text-gray-500 dark:text-gray-400">Added:</span>
                <span class="text-gray-900 dark:text-gray-100"><%= time_ago_in_words(list_item.created_at) %> ago</span>
              </div>
            </div>
            
            <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
              <%= link_to "View Product", marketplace_product_path(product), 
                  target: "_blank",
                  class: "inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600" %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <div class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No products in this list</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">This list is empty.</p>
    </div>
  <% end %>
</div>