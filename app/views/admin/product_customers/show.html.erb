<div class="max-w-7xl mx-auto">
  <div class="md:flex md:items-center md:justify-between mb-8">
    <div class="min-w-0 flex-1">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl ">
        Admin - <%= @product_customer.name %>
      </h2>
    </div>
    <div class="mt-4 flex md:ml-4 md:mt-0">
      <div class="flex space-x-2">
        <%= link_to 'Edit Customer', edit_admin_product_product_customer_path(@product, @product_customer), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
        <%= link_to 'All Customers', admin_product_product_customers_path(@product), class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
      </div>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <!-- Customer Details -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">Customer Information</h3>
          <dl class="space-y-4">
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product_customer.name %></dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                <% if @product_customer.description.present? %>
                  <%= simple_format(@product_customer.description) %>
                <% else %>
                  <span class="text-gray-400 italic">No description provided</span>
                <% end %>
              </dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Position</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product_customer.position %></dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= time_ago_in_words(@product_customer.created_at) %> ago</dd>
            </div>
          </dl>
        </div>

        <div>
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">Logo Details</h3>
          <% if @product_customer.logo.attached? %>
            <dl class="space-y-4">
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Filename</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product_customer.logo.filename %></dd>
              </div>
              
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Content Type</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product_customer.logo.content_type %></dd>
              </div>
              
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">File Size</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= number_to_human_size(@product_customer.logo.byte_size) %></dd>
              </div>
              
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Type</dt>
                <dd class="mt-1">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                    Logo attached
                  </span>
                </dd>
              </div>
            </dl>
          <% else %>
            <p class="text-sm text-gray-500 dark:text-gray-400">No logo for this customer.</p>
          <% end %>
        </div>
      </div>

      <!-- Logo Preview Section -->
      <% if @product_customer.logo.attached? %>
        <div class="mt-8">
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">Logo Preview</h3>
          <% if @product_customer.image? %>
            <div class="max-w-2xl">
              <%= image_tag @product_customer.logo, 
                  class: "w-full h-auto object-contain rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",
                  alt: @product_customer.name %>
            </div>
          <% else %>
            <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-8 text-center max-w-2xl">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">File logo</h3>
              <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                <%= link_to "Download #{@product_customer.logo.filename}", 
                    url_for(@product_customer.logo), 
                    class: "text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300",
                    download: true %>
              </p>
            </div>
          <% end %>
        </div>
      <% else %>
        <div class="mt-8">
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">Logo Preview</h3>
          <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-8 text-center max-w-2xl">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No logo</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">This customer doesn't have any uploaded logo.</p>
          </div>
        </div>
      <% end %>

      <!-- Actions Section -->
      <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div class="flex space-x-3">
          <%= render 'shared/delete_resource', 
              resource: @product_customer, 
              delete_url: admin_product_product_customer_path(@product, @product_customer),
              resource_type: 'Customer',
              redirect_url: admin_product_product_customers_path(@product) %>
        </div>
      </div>
    </div>
  </div>
</div>