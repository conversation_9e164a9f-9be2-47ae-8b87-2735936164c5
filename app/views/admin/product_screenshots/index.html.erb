<% content_for :page_title, "Product Screenshots" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="flex justify-between items-center mb-8">
    <div>
      <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">Product Screenshots</h1>
      <p class="text-gray-600 dark:text-gray-400 mt-2">
        Manage screenshots for <%= @product.name %>
      </p>
    </div>
    <%= link_to "Add Screenshot", new_admin_product_product_screenshot_path(@product), 
        class: "inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors" %>
  </div>

  <!-- Back to Product -->
  <div class="mb-6">
    <%= link_to admin_product_path(@product), 
        class: "inline-flex items-center text-purple-600 hover:text-purple-700 font-medium" do %>
      <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
      </svg>
      Back to Product
    <% end %>
  </div>

  <!-- Screenshots List -->
  <% if @product_screenshots.any? %>
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" data-controller="sortable" data-sortable-url-value="<%= admin_product_product_screenshot_path(@product, ':id') %>/update_position">
          <% @product_screenshots.each do |screenshot| %>
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden" data-sortable-item data-id="<%= screenshot.id %>">
              <!-- Screenshot Image -->
              <div class="aspect-w-16 aspect-h-9 bg-gray-100 dark:bg-gray-600">
                <% if screenshot.image.attached? %>
                  <%= image_tag screenshot.image, 
                      class: "w-full h-48 object-cover",
                      alt: screenshot.title %>
                <% else %>
                  <div class="w-full h-48 flex items-center justify-center">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                <% end %>
              </div>

              <!-- Screenshot Info -->
              <div class="p-4">
                <h3 class="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  <%= screenshot.title %>
                </h3>
                <% if screenshot.description.present? %>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    <%= truncate(screenshot.description, length: 100) %>
                  </p>
                <% end %>
                
                <!-- Position -->
                <div class="flex items-center justify-between">
                  <span class="text-xs text-gray-500 dark:text-gray-400">
                    Position: <%= screenshot.position %>
                  </span>
                  
                  <!-- Actions -->
                  <div class="flex items-center space-x-2">
                    <%= link_to edit_admin_product_product_screenshot_path(@product, screenshot), 
                        class: "text-blue-600 hover:text-blue-700 text-sm font-medium" do %>
                      Edit
                    <% end %>
                    <%= link_to admin_product_product_screenshot_path(@product, screenshot), 
                        method: :delete,
                        data: { confirm: "Are you sure?" },
                        class: "text-red-600 hover:text-red-700 text-sm font-medium" do %>
                      Delete
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  <% else %>
    <!-- Empty State -->
    <div class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No screenshots</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Add the first screenshot to showcase this product.
      </p>
      <div class="mt-6">
        <%= link_to "Add Screenshot", new_admin_product_product_screenshot_path(@product), 
            class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500" %>
      </div>
    </div>
  <% end %>
</div>