<div class="max-w-7xl mx-auto">
  <div class="md:flex md:items-center md:justify-between mb-8">
    <div class="min-w-0 flex-1">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl ">
        Admin - <%= @case_study.title %>
      </h2>
    </div>
    <div class="mt-4 flex md:ml-4 md:mt-0">
      <div class="flex space-x-2">
        <%= link_to 'Edit Case Study', edit_admin_product_case_study_path(@product, @case_study), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
        <%= link_to 'All Case Studies', admin_product_case_studies_path(@product), class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
      </div>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <!-- Case Study Details -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">Case Study Information</h3>
          <dl class="space-y-4">
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Title</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @case_study.title %></dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                <%= simple_format(@case_study.description) %>
              </dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Position</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @case_study.position %></dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
              <dd class="mt-1">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= @case_study.published? ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' %>">
                  <%= @case_study.published? ? 'Published' : 'Draft' %>
                </span>
              </dd>
            </div>
          </dl>
        </div>

        <div>
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">File Details</h3>
          <% if @case_study.upload.attached? %>
            <dl class="space-y-4">
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Filename</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @case_study.upload.filename %></dd>
              </div>
              
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Content Type</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @case_study.upload.content_type %></dd>
              </div>
              
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">File Size</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= number_to_human_size(@case_study.upload.byte_size) %></dd>
              </div>
              
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Uploaded</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= time_ago_in_words(@case_study.created_at) %> ago</dd>
              </div>
            </dl>
          <% else %>
            <p class="text-sm text-gray-500 dark:text-gray-400">No file uploaded.</p>
          <% end %>
        </div>
      </div>

      <!-- PDF Viewer Section -->
      <% if @case_study.upload.attached? %>
        <div class="mt-8">
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">PDF Preview</h3>
          <div class="aspect-[8.5/11] bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
            <iframe 
              src="<%= url_for(@case_study.upload) %>#toolbar=0&navpanes=0&scrollbar=0"
              class="w-full h-full border-0"
              title="<%= @case_study.title %> PDF">
            </iframe>
          </div>
          <div class="mt-4 flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd" />
                </svg>
                PDF • <%= number_to_human_size(@case_study.upload.byte_size) %>
              </span>
            </div>
            <div class="flex space-x-2">
              <%= link_to 'Download PDF', url_for(@case_study.upload), 
                  target: '_blank', 
                  download: @case_study.upload.filename,
                  class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
              <%= link_to 'Open in New Tab', url_for(@case_study.upload), 
                  target: '_blank',
                  class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
            </div>
          </div>
        </div>
      <% else %>
        <div class="mt-8">
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">PDF Preview</h3>
          <div class="aspect-[8.5/11] bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
            <div class="text-center">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No PDF uploaded</h3>
              <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Upload a PDF file to view it here.</p>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>


</div>