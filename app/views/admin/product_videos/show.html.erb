<div class="max-w-7xl mx-auto">
  <div class="md:flex md:items-center md:justify-between mb-8">
    <div class="min-w-0 flex-1">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl ">
        Admin - <%= @product_video.title %>
      </h2>
    </div>
    <div class="mt-4 flex md:ml-4 md:mt-0">
      <div class="flex space-x-2">
        <%= link_to 'Edit Video', edit_admin_product_product_video_path(@product, @product_video), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
        <%= link_to 'All Videos', admin_product_product_videos_path(@product), class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
      </div>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <!-- Video Details -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">Video Information</h3>
          <dl class="space-y-4">
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Title</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product_video.title %></dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                <%= simple_format(@product_video.description) %>
              </dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Position</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product_video.position %></dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
              <dd class="mt-1">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= @product_video.published? ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' %>">
                  <%= @product_video.published? ? 'Published' : 'Draft' %>
                </span>
              </dd>
            </div>
          </dl>
        </div>

        <div>
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">File Details</h3>
          <% if @product_video.video_file.attached? %>
            <dl class="space-y-4">
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Filename</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product_video.video_file.filename %></dd>
              </div>
              
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Content Type</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product_video.video_file.content_type %></dd>
              </div>
              
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">File Size</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= number_to_human_size(@product_video.video_file.byte_size) %></dd>
              </div>
              
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Uploaded</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= time_ago_in_words(@product_video.created_at) %> ago</dd>
              </div>
            </dl>
          <% else %>
            <p class="text-sm text-gray-500 dark:text-gray-400">No video file attached.</p>
          <% end %>
        </div>
      </div>

      <!-- Video Player Section -->
      <% if @product_video.video_file.attached? %>
        <div class="mt-8">
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">Video Preview</h3>
          <div class="aspect-video bg-black rounded-lg overflow-hidden">
            <video 
              controls 
              controlsList="nodownload"
              class="w-full h-full object-contain"
              preload="metadata"
              poster="">
              <source src="<%= url_for(@product_video.video_file) %>" type="<%= @product_video.video_file.content_type %>">
              Your browser does not support the video tag.
            </video>
          </div>
        </div>
      <% else %>
        <div class="mt-8">
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">Video Preview</h3>
          <div class="aspect-video bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
            <div class="text-center">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No video file</h3>
              <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Upload a video file to preview it here.</p>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>


</div>