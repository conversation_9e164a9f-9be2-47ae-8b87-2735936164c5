<% content_for :page_title, "Product Claims Management" %>

<div class="">
  <div class="sm:flex sm:items-center mb-8">
    <div class="sm:flex-auto">
      <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Product Claims Management</h1>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
        Review and manage product profile claim requests from companies.
      </p>
    </div>
  </div>

  <!-- Filters -->
  <div class="mb-6">
    <%= form_with url: admin_product_claims_path, method: :get, local: true, class: "flex gap-4 items-center" do |form| %>
      <div class="grid grid-cols-1 min-w-48">
        <%= form.select :status,
            options_for_select([
              ['All Statuses', ''],
              ['Pending', 'pending'],
              ['Reviewed', 'reviewed'],
              ['Approved', 'approved'],
              ['Rejected', 'rejected']
            ], params[:status]),
            {},
            { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-2 pr-8 pl-3 text-sm text-gray-900 dark:text-gray-100 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 transition-colors" } %>
        <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
          <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
        </svg>
      </div>
      
      <%= form.submit "Filter", class: "inline-flex items-center justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500" %>
      <% if params[:status].present? %>
        <%= link_to "Clear", admin_product_claims_path, class: "text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300" %>
      <% end %>
    <% end %>
  </div>

  <!-- Desktop Table View (hidden on mobile) -->
  <div class="hidden md:block mt-8 flow-root">
    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
        <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Product</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Claimant</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Company</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Status</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Submitted</th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
              <% @product_claims.each do |claim| %>
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                    <div class="flex items-center">
                      <div>
                        <div class="font-medium text-gray-900 dark:text-gray-100">
                          <%= link_to claim.product.name, marketplace_product_path(claim.product), 
                              class: "hover:text-blue-600 dark:hover:text-blue-400", 
                              target: "_blank" %>
                        </div>
                        <div class="text-gray-500 dark:text-gray-400">ID: <%= claim.product.id %></div>
                      </div>
                    </div>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-900 dark:text-gray-100">
                    <div class="font-medium"><%= claim.name %></div>
                    <div class="text-gray-500 dark:text-gray-400"><%= claim.email %></div>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-900 dark:text-gray-100">
                    <div class="font-medium"><%= claim.company %></div>
                    <div class="text-gray-500 dark:text-gray-400"><%= claim.title %></div>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                      <%= case claim.status
                          when 'approved' then 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                          when 'rejected' then 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                          when 'reviewed' then 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'
                          else 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                          end %>">
                      <%= claim.status.capitalize %>
                    </span>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <%= claim.created_at.strftime("%B %-d, %Y") %>
                  </td>
                  <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                    <div class="flex justify-end space-x-2">
                      <%= link_to "View", admin_product_claim_path(claim), 
                          class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile Card View (visible on mobile only) -->
  <div class="md:hidden mt-8 space-y-4">
    <% @product_claims.each do |claim| %>
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div class="px-4 py-5 sm:px-6">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                <%= link_to claim.product.name, marketplace_product_path(claim.product), class: "hover:text-blue-600 dark:hover:text-blue-400", target: "_blank" %>
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">ID: <%= claim.product.id %></p>
            </div>
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
              <%= case claim.status
                  when 'approved' then 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                  when 'rejected' then 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                  when 'reviewed' then 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'
                  else 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                  end %>">
              <%= claim.status.capitalize %>
            </span>
          </div>
          
          <div class="mt-4 space-y-3">
            <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
              <div class="flex justify-between text-sm">
                <span class="text-gray-500 dark:text-gray-400">Claimant:</span>
                <div class="text-right">
                  <div class="font-medium text-gray-900 dark:text-gray-100"><%= claim.name %></div>
                  <div class="text-gray-500 dark:text-gray-400"><%= claim.email %></div>
                </div>
              </div>
            </div>
            
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Company:</span>
              <div class="text-right">
                <div class="font-medium text-gray-900 dark:text-gray-100"><%= claim.company %></div>
                <div class="text-gray-500 dark:text-gray-400"><%= claim.title %></div>
              </div>
            </div>
            
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">Submitted:</span>
              <span class="text-gray-900 dark:text-gray-100"><%= claim.created_at.strftime("%B %-d, %Y") %></span>
            </div>
          </div>
          
          <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
            <%= link_to "View Details", admin_product_claim_path(claim), class: "inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600" %>
          </div>
        </div>
      </div>
    <% end %>
  </div>

  <%= render partial: "shared/pagination", locals: { pagy: @pagy } %>

  <% if @product_claims.empty? %>
    <div class="text-center py-8">
      <p class="text-sm text-gray-500 dark:text-gray-400">No product claims found.</p>
    </div>
  <% end %>
</div>