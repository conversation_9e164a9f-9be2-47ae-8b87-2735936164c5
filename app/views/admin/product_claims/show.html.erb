<% content_for :page_title, "Product Claim Details - #{@product_claim.product.name}" %>

<div class="">
    <!-- Header -->
    <div class="md:flex md:items-center md:justify-between mb-8">
      <div class="min-w-0 flex-1">
        <h1 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl ">
          Product Claim Request
        </h1>
        <div class="mt-1 flex flex-col sm:mt-0 sm:flex-row sm:flex-wrap sm:space-x-6">
          <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
              <%= case @product_claim.status
                  when 'approved' then 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                  when 'rejected' then 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                  when 'reviewed' then 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'
                  else 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                  end %>">
              <%= @product_claim.status.capitalize %>
            </span>
          </div>
          <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
            <svg class="mr-1.5 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
            </svg>
            Submitted <%= @product_claim.created_at.strftime("%B %-d, %Y") %>
          </div>
        </div>
      </div>
      <div class="mt-4 flex md:ml-4 md:mt-0 space-x-3">
        <%= link_to "View Product", marketplace_product_path(@product_claim.product), 
            target: "_blank",
            class: "inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600" %>
        
        <%= link_to "← Back to Claims", admin_product_claims_path,
            class: "inline-flex items-center rounded-md bg-white dark:bg-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-100 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600" %>
      </div>
    </div>

    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
      <!-- Claim Details -->
      <div class="lg:col-span-2">
        <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Claim Details</h3>
            <div class="mt-5 border-t border-gray-200 dark:border-gray-600">
              <dl class="divide-y divide-gray-200 dark:divide-gray-600">
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Claimant Name</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @product_claim.name %></dd>
                </div>
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Email Address</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0">
                    <a href="mailto:<%= @product_claim.email %>" class="text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">
                      <%= @product_claim.email %>
                    </a>
                  </dd>
                </div>
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Company</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @product_claim.company %></dd>
                </div>
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Job Title</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @product_claim.title %></dd>
                </div>
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @product_claim.status.capitalize %></dd>
                </div>
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Submitted</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @product_claim.created_at.strftime("%B %d, %Y at %I:%M %p") %></dd>
                </div>
              </dl>
            </div>
          </div>
        </div>

        <!-- Message -->
        <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Claim Message</h3>
            <div class="mt-3">
              <div class="text-sm text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <%= simple_format(@product_claim.message) %>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Product Information -->
      <div class="lg:col-span-1">
        <!-- Product Details -->
        <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Product Information</h3>
            <div class="mt-3">
              <div class="flex items-start">
                <% if @product_claim.product.logo.present? %>
                  <div class="flex-shrink-0 mr-4">
                    <%= image_tag @product_claim.product.logo, alt: @product_claim.product.name, 
                        class: "h-12 w-12 rounded-lg object-cover" %>
                  </div>
                <% end %>
                <div class="flex-1">
                  <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                    <%= link_to @product_claim.product.name, marketplace_product_path(@product_claim.product), 
                        target: "_blank", 
                        class: "hover:text-blue-600 dark:hover:text-blue-400" %>
                  </p>
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    Product ID: <%= @product_claim.product.id %>
                  </p>
                  <% if @product_claim.product.tagline.present? %>
                    <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                      <%= @product_claim.product.tagline %>
                    </p>
                  <% end %>
                </div>
              </div>
              
              <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
                <div class="text-sm">
                  <p class="text-gray-500 dark:text-gray-400">Current Status:</p>
                  <% if @product_claim.product.account.present? %>
                    <p class="text-green-600 dark:text-green-400 font-medium">
                      Owned by <%= @product_claim.product.account.name %>
                    </p>
                  <% else %>
                    <p class="text-yellow-600 dark:text-yellow-400 font-medium">
                      Unclaimed Profile
                    </p>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Actions</h3>
            <div class="mt-3 space-y-3">
              <div class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Review this claim request and take appropriate action.
              </div>
              
              <div class="flex flex-col space-y-2">
                <a href="mailto:<%= @product_claim.email %>?subject=Product%20Claim%20Request%20for%20<%= @product_claim.product.name %>" 
                   class="inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800">
                  <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  Contact Claimant
                </a>
                
                <%= link_to marketplace_product_path(@product_claim.product), 
                    target: "_blank",
                    class: "inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" do %>
                  <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                  View Product Page
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</div>