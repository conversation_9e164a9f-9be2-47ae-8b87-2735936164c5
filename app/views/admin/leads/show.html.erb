<% content_for :page_title, "Lead Details - #{@lead.contact_name}" %>

<div class="">
  <!-- Header -->
  <div class="md:flex md:items-center md:justify-between mb-8">
    <div class="min-w-0 flex-1">
      <h1 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl">
        Lead Details
      </h1>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
        Lead for <%= @product.name %> from <%= @lead.contact_name %>
      </p>
    </div>
    <div class="mt-4 flex md:ml-4 md:mt-0 space-x-3">
      <%= link_to "← Back to Leads", admin_product_leads_path(@product),
          class: "inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600" %>
    </div>
  </div>

  <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
    <!-- Lead Information -->
    <div class="lg:col-span-2">
      <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Lead Information</h3>
          <div class="mt-5 border-t border-gray-200 dark:border-gray-600">
            <dl class="divide-y divide-gray-200 dark:divide-gray-600">
              <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Contact Name</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @lead.contact_name %></dd>
              </div>
              <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0">
                  <%= link_to @lead.contact_email, "mailto:#{@lead.contact_email}", 
                      class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                </dd>
              </div>
              <% if @lead.contact_phone.present? %>
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0">
                    <%= link_to @lead.contact_phone, "tel:#{@lead.contact_phone}", 
                        class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                  </dd>
                </div>
              <% end %>
              <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Company</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @lead.contact_company %></dd>
              </div>
              <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Budget Range</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0">
                  <%= @lead.budget_range.present? ? @lead.budget_range : 'Not specified' %>
                </dd>
              </div>
              <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Timeline</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0">
                  <%= @lead.timeline.present? ? @lead.timeline : 'Not specified' %>
                </dd>
              </div>
              <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                    <%= case @lead.status
                          when 'pending' then 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                          when 'contacted' then 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'
                          when 'qualified' then 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                          when 'closed' then 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100'
                          else 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                        end %>">
                    <%= @lead.status.capitalize %>
                  </span>
                </dd>
              </div>
              <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Submitted</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0">
                  <%= @lead.created_at.strftime("%B %d, %Y at %I:%M %p") %>
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>

      <!-- Message -->
      <% if @lead.message.present? %>
        <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Message</h3>
            <div class="mt-3">
              <p class="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap"><%= @lead.message %></p>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Notes -->
      <% if @lead.notes.present? %>
        <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Internal Notes</h3>
            <div class="mt-3">
              <p class="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap"><%= @lead.notes %></p>
            </div>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Product & Account Info -->
    <div class="lg:col-span-1">
      <!-- Product Information -->
      <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Product</h3>
          <div class="mt-3">
            <p class="font-medium text-gray-900 dark:text-gray-100">
              <%= link_to @product.name, admin_product_path(@product), 
                  class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
              <%= link_to "View in marketplace", marketplace_product_path(@product), 
                  target: "_blank", 
                  class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
            </p>
          </div>
        </div>
      </div>

      <!-- Account Information -->
      <% if @lead.account %>
        <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Vendor Account</h3>
            <div class="mt-3">
              <p class="font-medium text-gray-900 dark:text-gray-100">
                <%= link_to @lead.account.name, admin_account_path(@lead.account), 
                    class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
              </p>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                <%= @lead.account.account_type.capitalize %> Account
              </p>
            </div>
          </div>
        </div>
      <% else %>
        <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Vendor Account</h3>
            <div class="mt-3">
              <p class="text-sm text-gray-500 dark:text-gray-400 italic">
                No vendor account associated (admin-managed product)
              </p>
            </div>
          </div>
        </div>
      <% end %>

      <!-- User Information -->
      <% if @lead.user %>
        <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Submitted By</h3>
            <div class="mt-3">
              <p class="font-medium text-gray-900 dark:text-gray-100">
                <%= @lead.user.full_name %>
              </p>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                Registered User
              </p>
            </div>
          </div>
        </div>
      <% else %>
        <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Submitted By</h3>
            <div class="mt-3">
              <p class="text-sm text-gray-500 dark:text-gray-400 italic">
                Anonymous visitor (not logged in)
              </p>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>