<% if @product %>
  <% content_for :page_title, "Leads for #{@product.name}" %>
<% else %>
  <% content_for :page_title, "Lead Management" %>
<% end %>

<div class="">
  <!-- Header -->
  <div class="md:flex md:items-center md:justify-between mb-8">
    <div class="min-w-0 flex-1">
      <% if @product %>
        <h1 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl">
          Leads for <%= @product.name %>
        </h1>
        <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
          All leads generated for this product.
        </p>
      <% else %>
        <h1 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl">
          Lead Management
        </h1>
        <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
          Manage and review all leads across all products.
        </p>
      <% end %>
    </div>
    <div class="mt-4 flex md:ml-4 md:mt-0 space-x-3">
      <% if @product %>
        <%= link_to "← Back to Product", admin_product_path(@product),
            class: "inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600" %>
      <% end %>
    </div>
  </div>

  <!-- Search and Filters (only for direct route) -->
  <% unless @product %>
    <div class="bg-white dark:bg-gray-800 shadow sm:rounded-md mb-6 transition-colors">
      <div class="px-6 py-4">
        <%= form_with url: admin_leads_path, method: :get, local: true, class: "flex flex-wrap gap-4 items-end" do |form| %>
          <div class="flex-1 min-w-64">
            <%= form.label :search, "Search", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.text_field :search, 
                placeholder: "Search by contact name, email, or company...", 
                value: params[:search],
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
          </div>
          
          <div>
            <%= form.label :status, "Status", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <div class="mt-2 grid grid-cols-1">
              <%= form.select :status,
                  options_for_select([
                    ['All Statuses', ''],
                    ['Pending', 'pending'],
                    ['Contacted', 'contacted'], 
                    ['Qualified', 'qualified'],
                    ['Closed', 'closed']
                  ], params[:status]),
                  {},
                  { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-gray-100 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 sm:text-sm transition-colors" } %>
              <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>

          <div>
            <%= form.submit "Filter", 
                class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
          </div>
          
          <% if params.values.any?(&:present?) %>
            <div>
              <%= link_to "Clear", admin_leads_path, 
                  class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" %>
            </div>
          <% end %>
        <% end %>
      </div>
    </div>
  <% end %>

  <!-- Summary Stats -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg p-6">
      <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Leads</dt>
      <dd class="text-3xl font-bold text-gray-900 dark:text-gray-100"><%= @leads.count %></dd>
    </div>
    <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg p-6">
      <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Pending</dt>
      <dd class="text-3xl font-bold text-yellow-600"><%= @leads.where(status: 'pending').count %></dd>
    </div>
    <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg p-6">
      <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Contacted</dt>
      <dd class="text-3xl font-bold text-blue-600"><%= @leads.where(status: 'contacted').count %></dd>
    </div>
    <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg p-6">
      <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Qualified</dt>
      <dd class="text-3xl font-bold text-green-600"><%= @leads.where(status: 'qualified').count %></dd>
    </div>
  </div>

  <!-- Leads Table -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <% if @leads.any? %>
        <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Contact</th>
                <% unless @product %>
                  <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Product</th>
                <% end %>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Company</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Budget Range</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Timeline</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Status</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Date</th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
              <% @leads.each do |lead| %>
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                    <div>
                      <div class="font-medium text-gray-900 dark:text-gray-100">
                        <%= lead.contact_name %>
                      </div>
                      <div class="text-gray-500 dark:text-gray-400">
                        <%= lead.contact_email %>
                      </div>
                      <% if lead.contact_phone.present? %>
                        <div class="text-gray-500 dark:text-gray-400 text-xs">
                          <%= lead.contact_phone %>
                        </div>
                      <% end %>
                    </div>
                  </td>
                  <% unless @product %>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-900 dark:text-gray-100">
                      <div class="font-medium">
                        <%= link_to lead.product.name, admin_product_path(lead.product), class: "hover:text-blue-600 dark:hover:text-blue-400" %>
                      </div>
                      <div class="text-gray-500 dark:text-gray-400 text-xs">
                        <%= lead.product.account&.name || 'Admin-managed' %>
                      </div>
                    </td>
                  <% end %>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-900 dark:text-gray-100">
                    <%= lead.contact_company %>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <%= lead.budget_range || 'Not specified' %>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <%= lead.timeline || 'Not specified' %>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                      <%= case lead.status
                            when 'pending' then 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                            when 'contacted' then 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'
                            when 'qualified' then 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                            when 'closed' then 'bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-100'
                            else 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                          end %>">
                      <%= lead.status.capitalize %>
                    </span>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                    <%= lead.created_at.strftime("%b %d, %Y") %>
                  </td>
                  <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                    <% if @product %>
                      <%= link_to "View", admin_product_lead_path(@product, lead), 
                          class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                    <% else %>
                      <%= link_to "View", admin_lead_path(lead), 
                          class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                    <% end %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      <% else %>
        <div class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No leads yet</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            No one has expressed interest in this product yet.
          </p>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Pagination for direct route -->
  <% if !@product && defined?(@pagy) %>
    <%= render partial: "shared/pagination", locals: { pagy: @pagy } %>
  <% end %>
</div>