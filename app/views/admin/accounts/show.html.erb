<% content_for :page_title, "Account Details - #{@account.name}" %>

<div class="">
    <!-- Header -->
    <div class="md:flex md:items-center md:justify-between mb-8">
      <div class="min-w-0 flex-1">
        <h1 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl ">
          <%= @account.name %>
        </h1>
        <div class="mt-1 flex flex-col sm:mt-0 sm:flex-row sm:flex-wrap sm:space-x-6">
          <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= @account.vendor? ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-800 dark:text-indigo-100' : 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100' %>">
              <%= @account.account_type.capitalize %>
            </span>
          </div>
          <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
              <%= case @account.status
                  when 'approved' then 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                  when 'rejected' then 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                  else 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                  end %>">
              <%= @account.status.capitalize %>
            </span>
          </div>
        </div>
      </div>
      <div class="mt-4 flex md:ml-4 md:mt-0 space-x-3">
        <%= link_to "Manage Team", team_members_admin_account_path(@account), 
            class: "inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600" %>
        <%= link_to "Edit Account", edit_admin_account_path(@account), 
            class: "inline-flex items-center rounded-md bg-white dark:bg-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-100 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600" %>
        
        <% if @account.pending? %>
          <%= button_to "Approve", approve_admin_account_path(@account), method: :post,
              class: "inline-flex items-center rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600",
              data: { confirm: "Are you sure you want to approve #{@account.name}?" } %>
          <%= button_to "Reject", reject_admin_account_path(@account), method: :post,
              class: "inline-flex items-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600",
              data: { confirm: "Are you sure you want to reject #{@account.name}?" } %>
        <% elsif @account.rejected? %>
          <%= button_to "Approve", approve_admin_account_path(@account), method: :post,
              class: "inline-flex items-center rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600",
              data: { confirm: "Are you sure you want to approve #{@account.name}?" } %>
        <% elsif @account.approved? %>
          <%= button_to "Reject", reject_admin_account_path(@account), method: :post,
              class: "inline-flex items-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600",
              data: { confirm: "Are you sure you want to reject #{@account.name}?" } %>
        <% end %>
        
        <%= link_to "← Back to Accounts", admin_accounts_path,
            class: "inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600" %>
      </div>
    </div>

    <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
      <!-- Account Information -->
      <div class="lg:col-span-2">
        <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Account Information</h3>
            <div class="mt-5 border-t border-gray-200 dark:border-gray-600">
              <dl class="divide-y divide-gray-200 dark:divide-gray-600">
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Account Name</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @account.name %></dd>
                </div>
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Account Type</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @account.account_type.capitalize %></dd>
                </div>
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @account.status.capitalize %></dd>
                </div>
                <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @account.created_at.strftime("%B %d, %Y at %I:%M %p") %></dd>
                </div>
                <% if @account.approved_at %>
                  <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Approved At</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= @account.approved_at.strftime("%B %d, %Y at %I:%M %p") %></dd>
                  </div>
                <% end %>
                <% if @account.approved_by %>
                  <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Approved By</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0">
                      <%= @account.approved_by.full_name %>
                      (<%= @account.approved_by.email_address %>)
                    </dd>
                  </div>
                <% end %>
              </dl>
            </div>
          </div>
        </div>

        <!-- Company Details -->
        <% if @account.description.present? %>
          <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
            <div class="px-4 py-5 sm:p-6">
              <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Company Details</h3>
              <div class="mt-5 border-t border-gray-200 dark:border-gray-600">
                <dl class="divide-y divide-gray-200 dark:divide-gray-600">
                  <% if @account.description.present? %>
                    <div class="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5">
                      <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
                      <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:col-span-2 sm:mt-0"><%= simple_format(@account.description) %></dd>
                    </div>
                  <% end %>
                </dl>
              </div>
            </div>
          </div>
        <% end %>
      </div>

      <!-- Account Owner & Team -->
      <div class="lg:col-span-1">
        <!-- Account Owner -->
        <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Account Owner</h3>
            <div class="mt-3">
              <div class="flex items-center">
                <div>
                  <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                    <%= @account.owner.full_name %>
                  </p>
                  <p class="text-sm text-gray-500 dark:text-gray-400"><%= @account.owner.email_address %></p>
                  <p class="text-xs text-gray-500 dark:text-gray-400"><%= @account.owner.job_title %> • <%= @account.owner.department %></p>
                  <p class="text-xs text-gray-400 dark:text-gray-500">Joined <%= @account.owner.created_at.strftime("%b %Y") %></p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Team Members -->
        <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Team Members</h3>
            <div class="mt-3">
              <% if @account.users.any? %>
                <ul class="divide-y divide-gray-200 dark:divide-gray-600">
                  <% @account.users.includes(:account_users).each do |user| %>
                    <% account_user = user.account_users.find { |au| au.account_id == @account.id } %>
                    <li class="py-3 flex justify-between items-center">
                      <div>
                        <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                          <%= user.full_name %>
                        </p>
                        <p class="text-xs text-gray-500 dark:text-gray-400"><%= user.email_address %></p>
                        <p class="text-xs text-gray-500 dark:text-gray-400"><%= user.job_title %> • <%= user.department %></p>
                      </div>
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                        <%= account_user&.role&.capitalize || 'Member' %>
                      </span>
                    </li>
                  <% end %>
                </ul>
              <% else %>
                <p class="text-sm text-gray-500 dark:text-gray-400">No additional team members</p>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Quick Stats -->
        <div class="mt-6 bg-white dark:bg-gray-800 shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-gray-100">Quick Stats</h3>
            <dl class="mt-3 grid grid-cols-1 gap-3">
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Team Size</dt>
                <dd class="text-2xl font-semibold text-gray-900 dark:text-gray-100"><%= @account.users.count %></dd>
              </div>
              <% if @account.vendor? %>
                <div>
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Products</dt>
                  <dd class="text-2xl font-semibold text-gray-900 dark:text-gray-100"><%= @account.products.count %></dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Leads</dt>
                  <dd class="text-2xl font-semibold text-gray-900 dark:text-gray-100"><%= @account.leads.count %></dd>
                </div>
              <% end %>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Team Members Table -->
    <div class="mt-8">
      <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100">Team Members</h3>
            <%= link_to "Manage Team", team_members_admin_account_path(@account), 
                class: "inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" %>
          </div>
          
          <% if @account.users.any? %>
            <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
              <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
                <thead class="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Member</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Job Title</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Department</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Role</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Email</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Joined</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-600 bg-white dark:bg-gray-800">
                  <% @account.users.includes(:account_users).each do |user| %>
                    <% account_user = user.account_users.find { |au| au.account_id == @account.id } %>
                    <tr>
                      <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                        <div class="flex items-center">
                          <div class="h-8 w-8 flex-shrink-0">
                            <div class="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center">
                              <span class="text-xs font-medium text-blue-800 dark:text-blue-100">
                                <%= user.first_name&.first %><%= user.last_name&.first %>
                              </span>
                            </div>
                          </div>
                          <div class="ml-3">
                            <div class="font-medium text-gray-900 dark:text-gray-100">
                              <%= user.full_name %>
                              <% if user == @account.owner %>
                                <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                                  Owner
                                </span>
                              <% end %>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                        <%= user.job_title.present? ? user.job_title : "Not provided" %>
                      </td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                        <%= user.department.present? ? user.department : "Not provided" %>
                      </td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <%= account_user&.role == 'admin' ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100' : 'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-100' %>">
                          <%= account_user&.role&.capitalize || 'Member' %>
                        </span>
                      </td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                        <%= user.email_address %>
                      </td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                        <%= account_user&.joined_at ? time_ago_in_words(account_user.joined_at) + " ago" : "N/A" %>
                      </td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
          <% else %>
            <p class="text-sm text-gray-500 dark:text-gray-400">No team members found.</p>
          <% end %>
        </div>
      </div>
    </div>
</div>