<% content_for :page_title, "Send Team Invitation - #{@account.name}" %>

<div class="max-w-2xl mx-auto">
  <div class="md:flex md:items-center md:justify-between mb-8">
    <div class="min-w-0 flex-1">
      <h1 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl">
        Send Team Invitation
      </h1>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
        Invite someone to join <%= @account.name %> via email. They will receive an invitation link to create their account.
      </p>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <%= form_with model: [@team_invitation], url: create_team_invitation_admin_account_path(@account), local: true, class: "space-y-6" do |form| %>
        <% if @team_invitation.errors.any? %>
          <div class="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4">
            <div class="flex">
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                  There were errors with your submission:
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                  <ul class="list-disc pl-5 space-y-1">
                    <% @team_invitation.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <div>
          <%= form.label :email, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.email_field :email, 
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
              placeholder: "Enter email address to invite" %>
          <div class="mt-2">
            <p class="text-sm text-gray-500 dark:text-gray-400">
              The person will receive an email invitation to join this account.
            </p>
            <div class="mt-1 p-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
              <p class="text-xs text-green-700 dark:text-green-300">
                <strong>Admin Privilege:</strong> As an admin, you can invite users from any email domain.
              </p>
            </div>
          </div>
        </div>

        <div>
          <%= form.label :role, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.select :role, 
              [['Member', 'member'], ['Admin', 'admin']], 
              { selected: 'member' },
              { class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" } %>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Select the role this person will have in the account.
          </p>
        </div>

        <div>
          <%= form.label :message, "Custom Message (Optional)", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.text_area :message, 
              rows: 4,
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
              placeholder: "Add a personal message to include with the invitation..." %>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            This message will be included in the invitation email.
          </p>
        </div>

        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                How Invitations Work
              </h3>
              <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                <ul class="list-disc list-inside space-y-1">
                  <li>The recipient will receive an email with a secure invitation link</li>
                  <li>If they don't have an account, they can create one through the invitation</li>
                  <li>If they already have an account, they can accept the invitation after signing in</li>
                  <li>Invitations expire in 7 days</li>
                  <li>You can cancel pending invitations at any time</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3">
          <%= link_to "Cancel", team_members_admin_account_path(@account), 
              class: "bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
          <%= form.submit "Send Invitation", 
              class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
        </div>
      <% end %>
    </div>
  </div>
</div>