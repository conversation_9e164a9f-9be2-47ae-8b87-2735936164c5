<% content_for :page_title, "Edit Team Member - #{@user.full_name}" %>

<div class="max-w-2xl mx-auto">
  <div class="md:flex md:items-center md:justify-between mb-8">
    <div class="min-w-0 flex-1">
      <h1 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl">
        Edit Team Member
      </h1>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
        Update profile information and role for <%= @user.full_name %> in <%= @account.name %>.
      </p>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <%= form_with model: [@user], url: update_team_member_admin_account_path(@account, team_member_id: @team_member.id), method: :patch, local: true, class: "space-y-6" do |form| %>
        <% if @user.errors.any? %>
          <div class="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4">
            <div class="flex">
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                  There were errors with your submission:
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                  <ul class="list-disc pl-5 space-y-1">
                    <% @user.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div>
            <%= form.label :first_name, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.text_field :first_name, 
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
                placeholder: "Enter first name" %>
          </div>

          <div>
            <%= form.label :last_name, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.text_field :last_name, 
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
                placeholder: "Enter last name" %>
          </div>
        </div>

        <div>
          <%= form.label :email_address, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.email_field :email_address, 
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
              placeholder: "Enter email address" %>
        </div>

        <div>
          <%= form.label :job_title, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.text_field :job_title, 
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
              placeholder: "Enter job title (optional)" %>
        </div>

        <div>
          <%= form.label :phone, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.telephone_field :phone, 
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
              placeholder: "Enter phone number (optional)" %>
        </div>

        <%= fields_for :account_user, @team_member do |account_user_form| %>
          <div>
            <%= account_user_form.label :role, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <% if @user == @account.owner %>
              <div class="mt-2 grid grid-cols-1">
                <%= account_user_form.select :role, 
                    [['Admin', 'admin']], 
                    { selected: 'admin' },
                    { disabled: true, class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-gray-100 dark:bg-gray-600 py-1.5 pr-8 pl-3 text-base text-gray-500 dark:text-gray-400 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 cursor-not-allowed sm:text-sm" } %>
                <%= account_user_form.hidden_field :role, value: 'admin' %>
                <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-400 dark:text-gray-500 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                  <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
                </svg>
              </div>
              <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Account owners must always have admin role.
              </p>
            <% else %>
              <div class="mt-2 grid grid-cols-1">
                <%= account_user_form.select :role, 
                    [['Member', 'member'], ['Admin', 'admin']], 
                    { selected: @team_member.role },
                    { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 sm:text-sm" } %>
                <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                  <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
                </svg>
              </div>
            <% end %>
          </div>
        <% end %>

        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Account Owner Protection
              </h3>
              <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                <% if @user == @account.owner %>
                  <p>This user is the account owner. The role cannot be changed.</p>
                <% else %>
                  <p>You can modify this user's profile information and role within this account.</p>
                <% end %>
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3">
          <%= link_to "Cancel", team_members_admin_account_path(@account), 
              class: "bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
          <%= form.submit "Update Team Member", 
              class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
        </div>
      <% end %>
    </div>
  </div>
</div>