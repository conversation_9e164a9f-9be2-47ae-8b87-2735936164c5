<% content_for :page_title, @certification.name %>

<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100"><%= @certification.name %></h1>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
          Certification details and associated products.
        </p>
      </div>
      <div class="flex space-x-3">
        <%= link_to "Edit", edit_admin_certification_path(@certification), 
            class: "rounded-md bg-blue-600 px-3 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:bg-blue-500 dark:hover:bg-blue-600" %>
        <%= link_to "Back to Certifications", admin_certifications_path, 
            class: "rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" %>
      </div>
    </div>
  </div>

  <!-- Certification Details -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mb-8">
    <div class="px-4 py-5 sm:p-6">
      <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @certification.name %></dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Products Count</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @certification.products_count %></dd>
        </div>
        <div class="sm:col-span-2">
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
            <%= @certification.description.present? ? @certification.description : "No description provided" %>
          </dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @certification.created_at.strftime("%B %-d, %Y") %></dd>
        </div>
        <div>
          <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Updated</dt>
          <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @certification.updated_at.strftime("%B %-d, %Y") %></dd>
        </div>
      </dl>
    </div>
  </div>

  <!-- Associated Products -->
  <% if @certification.products.any? %>
    <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Products with this Certification</h3>
        <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-900">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Product
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Company
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" class="relative px-6 py-3">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <% @certification.products.includes(:account).each do |product| %>
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                      <%= product.name %>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 dark:text-gray-100">
                      <%= product.account.name %>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= product.published? ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' %>">
                      <%= product.published? ? 'Published' : 'Unpublished' %>
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <%= link_to "View", admin_product_path(product),
                        class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  <% else %>
    <div class="text-center py-12">
      <div class="max-w-md mx-auto">
        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No products with this certification</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          This certification hasn't been assigned to any products yet.
        </p>
      </div>
    </div>
  <% end %>

  <%= render "shared/delete_resource", 
      resource: @certification, 
      delete_url: admin_certification_path(@certification),
      redirect_url: admin_certifications_path,
      resource_type: "Certification" %>
</div>