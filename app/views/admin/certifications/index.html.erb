<% content_for :page_title, "Certifications" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">Certifications</h1>
      <p class="mt-2 text-sm text-gray-700 dark:text-gray-400">
        Manage certifications that can be assigned to products.
      </p>
    </div>
    <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
      <%= link_to "New Certification", new_admin_certification_path, 
          class: "inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:bg-blue-500 dark:hover:bg-blue-600" %>
    </div>
  </div>

  <!-- Search -->
  <div class="mt-8 mb-8">
    <%= form_with url: admin_certifications_path, method: :get, local: true, class: "flex gap-4" do |form| %>
      <%= form.text_field :search, 
          placeholder: "Search certifications...", 
          value: params[:search],
          class: "flex-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500" %>
      <%= form.submit "Search", 
          class: "rounded-md bg-blue-600 px-3 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:bg-blue-500 dark:hover:bg-blue-600" %>
      <% if params[:search].present? %>
        <%= link_to "Clear", admin_certifications_path, 
            class: "rounded-md bg-gray-600 px-3 py-2 text-sm font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2" %>
      <% end %>
    <% end %>
  </div>

  <!-- Desktop Table View (hidden on mobile) -->
  <div class="hidden md:block mt-8 overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
    <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
      <thead class="bg-gray-50 dark:bg-gray-800">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            Name
          </th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            Products
          </th>
          <th scope="col" class="relative px-6 py-3">
            <span class="sr-only">Actions</span>
          </th>
        </tr>
      </thead>
      <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
        <% @certifications.each do |certification| %>
          <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                <%= certification.name %>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900 dark:text-gray-100">
                <%= certification.products_count %>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <%= link_to "View", admin_certification_path(certification),
                  class: "text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" %>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>

  <!-- Mobile Card View (visible on mobile only) -->
  <div class="md:hidden mt-8 space-y-4">
    <% @certifications.each do |certification| %>
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div class="px-4 py-5 sm:px-6">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                <%= certification.name %>
              </h3>
            </div>
          </div>
          
          <div class="mt-4 space-y-3">
            <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
              <div class="flex justify-between text-sm">
                <span class="text-gray-500 dark:text-gray-400">Products:</span>
                <span class="font-medium text-gray-900 dark:text-gray-100"><%= certification.products_count %></span>
              </div>
            </div>
          </div>
          
          <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
            <%= link_to "View Details", admin_certification_path(certification), class: "inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600" %>
          </div>
        </div>
      </div>
    <% end %>
  </div>

  <% if @certifications.empty? %>
    <div class="text-center py-12">
      <div class="max-w-md mx-auto">
        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No certifications found</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          <% if params[:search].present? %>
            Try adjusting your search terms or clearing the search.
          <% else %>
            Get started by creating your first certification.
          <% end %>
        </p>
        <% if params[:search].blank? %>
          <div class="mt-6">
            <%= link_to "New Certification", new_admin_certification_path, 
                class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-500 dark:hover:bg-blue-600" %>
          </div>
        <% end %>
      </div>
    </div>
  <% end %>



  <!-- Pagination -->
  <%= render partial: "shared/pagination", locals: { pagy: @pagy } %>
</div>