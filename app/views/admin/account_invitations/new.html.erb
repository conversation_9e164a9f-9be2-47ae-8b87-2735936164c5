<% content_for :page_title, "New Account Invitation" %>

<div class="mx-auto max-w-2xl">
  <div class="md:flex md:items-center md:justify-between mb-6">
    <div class="min-w-0 flex-1">
      <h2 class="text-2xl/7 font-bold text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl sm:tracking-tight">
        New Account Invitation
      </h2>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Create a new account and send an invitation email to the user.
      </p>
    </div>
    <div class="mt-4 flex md:ml-4 md:mt-0">
      <%= link_to admin_account_invitations_path, 
          class: "inline-flex items-center rounded-md bg-white dark:bg-gray-800 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-100 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700" do %>
        <svg class="-ml-0.5 mr-1.5 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fill-rule="evenodd" d="M9.293 2.293a1 1 0 0 1 1.414 0l7 7A1 1 0 0 1 17 11h-1v6a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1v-3a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v3a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-6H3a1 1 0 0 1-.707-1.707l7-7Z" clip-rule="evenodd" />
        </svg>
        Back to Invitations
      <% end %>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <%= form_with model: [:admin, @invitation], local: true, class: "space-y-6" do |form| %>
        <% if @invitation.errors.any? %>
          <div class="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                  There were errors with your submission:
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                  <ul class="list-disc pl-5 space-y-1">
                    <% @invitation.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Account Type -->
        <div>
          <%= form.label :account_type, "Account Type", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <div class="mt-2 grid grid-cols-1">
            <%= form.select :account_type,
                options_for_select([
                  ['Vendor Account', 'vendor'],
                  ['Agency Account', 'agency']
                ], @invitation.account_type),
                { prompt: "Select account type" },
                { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-blue-600 dark:focus:outline-blue-500 sm:text-sm" } %>
            <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
              <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>

        <!-- Account Name -->
        <div>
          <%= form.label :account_name, "Software Vendor/Department/Agency Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.text_field :account_name,
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
              placeholder: "Enter software vendor, department, or agency name" %>
        </div>

        <!-- Contact Information -->
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div>
            <%= form.label :first_name, "First Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.text_field :first_name,
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
                placeholder: "Enter first name" %>
          </div>

          <div>
            <%= form.label :last_name, "Last Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.text_field :last_name,
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
                placeholder: "Enter last name" %>
          </div>
        </div>

        <!-- Email -->
        <div>
          <%= form.label :email, "Email Address", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.email_field :email,
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
              placeholder: "Enter email address" %>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            The invitation email will be sent to this address.
          </p>
        </div>

        <!-- Job Title -->
        <div>
          <%= form.label :job_title, "Job Title", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.text_field :job_title,
              id: "invitation_job_title",
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors",
              placeholder: "e.g., Director of Public Works, Account Executive",
              required: true %>
        </div>

        <!-- Department -->
        <div>
          <%= form.label :department, "Department", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.text_field :department,
              id: "invitation_department",
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors",
              placeholder: "e.g., Public Works, IT Department, Procurement",
              required: true %>
        </div>

        <!-- Organization Name -->
        <div>
          <%= form.label :account_name, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
            <span class="agency-label" style="display: none;">City/County/State/Organization</span>
            <span class="vendor-label">Company Name</span>
          <% end %>
          <%= form.text_field :account_name,
              id: "invitation_account_name",
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
              placeholder: "e.g., Tech Solutions Inc., Innovation Corp",
              required: true %>
        </div>


        <!-- Form Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-600">
          <%= link_to "Cancel", admin_account_invitations_path,
              class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>

          <%= form.submit "Send Invitation",
              class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
        </div>
      <% end %>
    </div>
  </div>
</div>

<script>
function initializeAccountInvitationForm() {
  const accountTypeSelect = document.querySelector('select[name*="account_type"]');
  const agencyLabel = document.querySelector('.agency-label');
  const vendorLabel = document.querySelector('.vendor-label');
  const jobTitleField = document.getElementById('invitation_job_title');
  const departmentField = document.getElementById('invitation_department');
  const organizationField = document.getElementById('invitation_account_name');
  
  if (!accountTypeSelect || !agencyLabel || !vendorLabel || !jobTitleField || !departmentField || !organizationField) {
    return false; // Elements not ready yet
  }
  
  // Remove existing listeners to prevent duplicates
  if (window.accountInvitationListeners) {
    window.accountInvitationListeners.forEach(({ element, event, handler }) => {
      if (element) element.removeEventListener(event, handler);
    });
  }
  
  window.accountInvitationListeners = [];
  
  // Helper function to add listener and track it
  function addTrackedListener(element, event, handler) {
    if (element) {
      element.addEventListener(event, handler);
      window.accountInvitationListeners.push({ element, event, handler });
    }
  }
  
  function updateLabelsAndPlaceholders() {
    const selectedType = accountTypeSelect.value;
    
    if (selectedType === 'agency') {
      agencyLabel.style.display = 'inline';
      vendorLabel.style.display = 'none';
      jobTitleField.placeholder = 'e.g., Director of Public Works, IT Director, Procurement Officer';
      departmentField.placeholder = 'e.g., Public Works, IT Department, Procurement';
      organizationField.placeholder = 'e.g., City of Houston, State of Texas';
    } else if (selectedType === 'vendor') {
      agencyLabel.style.display = 'none';
      vendorLabel.style.display = 'inline';
      jobTitleField.placeholder = 'e.g., Account Executive, Sales Manager, CTO';
      departmentField.placeholder = 'e.g., Sales Team, Marketing Team, Engineering';
      organizationField.placeholder = 'e.g., Tech Solutions Inc., Innovation Corp';
    } else {
      // Default state when no account type is selected
      agencyLabel.style.display = 'none';
      vendorLabel.style.display = 'inline';
      jobTitleField.placeholder = 'e.g., Director of Public Works, Account Executive';
      departmentField.placeholder = 'e.g., Public Works, IT Department, Procurement';
      organizationField.placeholder = 'e.g., Tech Solutions Inc., Innovation Corp';
    }
  }
  
  // Account type change handler
  const accountTypeChangeHandler = function() {
    updateLabelsAndPlaceholders();
  };
  addTrackedListener(accountTypeSelect, 'change', accountTypeChangeHandler);
  
  // Set initial state
  updateLabelsAndPlaceholders();
  
  return true;
}

// Initialize on both DOMContentLoaded and turbo:load for Turbo compatibility
document.addEventListener('DOMContentLoaded', initializeAccountInvitationForm);
document.addEventListener('turbo:load', initializeAccountInvitationForm);

// Fallback initialization with timeout for edge cases
document.addEventListener('turbo:load', function() {
  setTimeout(function() {
    if (!initializeAccountInvitationForm()) {
      // Try again after another delay if still not ready
      setTimeout(initializeAccountInvitationForm, 100);
    }
  }, 50);
});
</script>