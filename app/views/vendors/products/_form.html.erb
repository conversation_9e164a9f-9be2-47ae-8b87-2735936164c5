<%= form_with model: [@product], url: form_url, local: true, class: "space-y-6" do |form| %>
  <% if @product.errors.any? %>
    <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-800 rounded-md p-4">
      <div class="flex">
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
            There were <%= pluralize(@product.errors.count, "error") %> with your submission:
          </h3>
          <div class="mt-2 text-sm text-red-700 dark:text-red-300">
            <ul role="list" class="list-disc space-y-1 pl-5">
              <% @product.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <div class="grid grid-cols-1 gap-6">
    <div>
      <%= form.label :name, "Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
      <%= form.text_field :name, class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
    </div>

    <div>
      <%= form.label :description, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
        Description <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
      <% end %>
      <%= form.text_area :description, 
          rows: 6,
          class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
          placeholder: "Enter a brief description of your product (up to 10 sentences)" %>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Maximum 1000 characters (approximately 10 sentences). <span id="description-count"><%= @product.description&.length || 0 %></span>/1000 characters used.
      </p>
    </div>

    <div>
      <%= form.label :website, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
        Website <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
      <% end %>
      <%= form.url_field :website, 
          class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
          placeholder: "https://example.com" %>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Product website or landing page URL
      </p>
    </div>

    <div class="grid grid-cols-1 gap-6 sm:grid-cols-1">

      <div>
        <%= form.label :category_ids, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
          Categories <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(select subcategories)</span>
        <% end %>
        <div class="mt-2 space-y-2 max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-3 bg-white dark:bg-gray-700">
          <% @categories.each do |category| %>
            <div class="flex items-center">
              <%= check_box_tag 'product[category_ids][]', category.id, @product.category_ids.include?(category.id), 
                  class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded" %>
              <%= label_tag "product_category_ids_#{category.id}", category.full_name, class: "ml-2 text-sm text-gray-700 dark:text-gray-300" %>
            </div>
          <% end %>
        </div>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Select one or more subcategories that best describe your product.
        </p>
      </div>
    </div>

    <div>
      <%= form.label :certification_ids, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
        Certifications <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
      <% end %>
      <div class="mt-2">
        <div class="space-y-2 max-h-48 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-3 bg-white dark:bg-gray-700">
          <% @certifications.each do |certification| %>
            <div class="flex items-start">
              <%= check_box_tag "product[certification_ids][]", certification.id, 
                  @product.certification_ids.include?(certification.id),
                  class: "mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700" %>
              <div class="ml-3">
                <%= label_tag "product_certification_ids_#{certification.id}", certification.name, 
                    class: "block text-sm font-medium text-gray-900 dark:text-gray-100" %>
                <% if certification.description.present? %>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    <%= truncate(certification.description, length: 100) %>
                  </p>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Select any certifications your product has or complies with.
        </p>
      </div>
    </div>

    <!-- Pricing Information Section -->
    <div>
      <%= form.label :pricing_model, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
        Pricing Model <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
      <% end %>
      <%= form.text_area :pricing_model, 
          rows: 3,
          class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
          placeholder: "e.g., Per user per month, One-time license fee, Custom pricing based on requirements" %>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Describe your pricing model (e.g., per user, per transaction, flat rate, etc.)
      </p>
    </div>

    <div>
      <%= form.label :pricing, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
        Sample Pricing <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
      <% end %>
      <%= form.text_area :pricing, 
          rows: 4,
          class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
          placeholder: "e.g., Starter: $29/month for up to 10 users, Professional: $99/month for up to 100 users, Enterprise: Contact for custom pricing" %>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Provide sample pricing tiers or examples to help potential customers understand your pricing structure
      </p>
    </div>

    <!-- Logo Upload Section -->
    <div>
      <%= form.label :logo, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
        Product Logo <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
      <% end %>
      
      <div class="mt-2">
        <% if @product.logo.attached? && @product.persisted? %>
          <div class="mb-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-800">
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Current logo:</p>
            <div class="flex items-start space-x-4">
              <div class="flex-shrink-0">
                <% begin %>
                  <%= image_tag @product.logo, 
                      class: "h-20 w-20 object-cover rounded-lg border border-gray-200 dark:border-gray-600",
                      alt: "Current product logo" %>
                <% rescue => e %>
                  <div class="h-20 w-20 bg-gray-200 dark:bg-gray-600 rounded-lg flex items-center justify-center">
                    <span class="text-gray-400 text-xs">Logo</span>
                  </div>
                <% end %>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  <%= @product.logo.filename rescue "Logo file" %>
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  <%= number_to_human_size(@product.logo.byte_size) rescue "Unknown size" %>
                </p>
              </div>
            </div>
          </div>
        <% end %>
        
        <%= form.file_field :logo, 
            class: "block w-full text-sm text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer bg-gray-50 dark:bg-gray-700 focus:ring-blue-500 focus:border-blue-500 file:mr-4 file:py-2 file:px-4 file:rounded-l-lg file:border-0 file:text-sm file:font-medium file:bg-gray-100 file:text-gray-700 hover:file:bg-gray-200 dark:file:bg-gray-600 dark:file:text-gray-200 dark:hover:file:bg-gray-500",
            accept: "image/*" %>
        
        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
          Upload a logo for your product. Supported formats: JPEG, PNG, GIF, WebP. Maximum file size: 5MB.
        </p>
        
        <% if @product.logo.attached? && @product.persisted? %>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Upload a new file to replace the current logo, or leave empty to keep the current one.
          </p>
        <% end %>
      </div>
    </div>


  </div>

  <!-- Publishing Options Section -->
  <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
    <div class="flex items-center">
      <%= form.check_box :published, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700" %>
      <%= form.label :published, class: "ml-2 block text-sm text-gray-900 dark:text-gray-100" do %>
        Publish product (make it visible to agency users) <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(You can change this later)</span>
      <% end %>
    </div>
  </div>

  <div class="flex justify-end space-x-3">
    <%= link_to "Cancel", vendors_products_path, class: "bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors" %>
    <%= form.submit submit_text, class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors" %>
  </div>
<% end %>