<%= render 'layout' do %>
  <div class="mt-8">
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Public Visibility Settings</h3>
        <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">Control which sections are visible to the general public on the marketplace, including non-approved government users.</p>
      </div>
    </div>

    <%= form_with model: [@product], url: vendors_product_path(@product), method: :patch, local: true, class: "mt-8" do |form| %>
      <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="space-y-6">
            <div class="space-y-4">
              <div class="flex items-start">
                <div class="flex items-center h-5">
                  <%= form.check_box :features_visible, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                </div>
                <div class="ml-3">
                  <label class="text-sm font-medium text-gray-900 dark:text-gray-100">Features Section</label>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Show product features to public marketplace visitors</p>
                </div>
              </div>

              <div class="flex items-start">
                <div class="flex items-center h-5">
                  <%= form.check_box :customers_visible, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                </div>
                <div class="ml-3">
                  <label class="text-sm font-medium text-gray-900 dark:text-gray-100">Customer References</label>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Show customer references to public marketplace visitors</p>
                </div>
              </div>

              <div class="flex items-start">
                <div class="flex items-center h-5">
                  <%= form.check_box :videos_visible, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                </div>
                <div class="ml-3">
                  <label class="text-sm font-medium text-gray-900 dark:text-gray-100">Product Videos</label>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Show product videos to public marketplace visitors</p>
                </div>
              </div>

              <div class="flex items-start">
                <div class="flex items-center h-5">
                  <%= form.check_box :screenshots_visible, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                </div>
                <div class="ml-3">
                  <label class="text-sm font-medium text-gray-900 dark:text-gray-100">Screenshots</label>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Show product screenshots to public marketplace visitors</p>
                </div>
              </div>

              <div class="flex items-start">
                <div class="flex items-center h-5">
                  <%= form.check_box :case_studies_visible, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                </div>
                <div class="ml-3">
                  <label class="text-sm font-medium text-gray-900 dark:text-gray-100">Case Studies</label>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Show case studies to public marketplace visitors</p>
                </div>
              </div>

              <div class="flex items-start">
                <div class="flex items-center h-5">
                  <%= form.check_box :content_visible, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                </div>
                <div class="ml-3">
                  <label class="text-sm font-medium text-gray-900 dark:text-gray-100">Content Library</label>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Show content library to public marketplace visitors</p>
                </div>
              </div>

              <div class="flex items-start">
                <div class="flex items-center h-5">
                  <%= form.check_box :contracts_visible, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                </div>
                <div class="ml-3">
                  <label class="text-sm font-medium text-gray-900 dark:text-gray-100">Contracts</label>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Show available contracts to public marketplace visitors</p>
                </div>
              </div>

              <div class="flex items-start">
                <div class="flex items-center h-5">
                  <%= form.check_box :pricing_visible, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                </div>
                <div class="ml-3">
                  <label class="text-sm font-medium text-gray-900 dark:text-gray-100">Pricing Information</label>
                  <p class="text-sm text-gray-500 dark:text-gray-400">Show pricing details to public marketplace visitors</p>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-6 flex justify-end">
            <%= form.submit "Update Public Visibility Settings", class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors" %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
<% end %>