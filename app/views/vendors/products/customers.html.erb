<%= render 'layout' do %>
  <div class="mt-8">
    <div class="md:flex md:items-center md:justify-between mb-8">
      <div class="min-w-0 flex-1">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Product Customers</h3>
        <div class="mt-1 flex flex-col sm:mt-0 sm:flex-row sm:flex-wrap sm:space-x-6">
          <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
            <%= pluralize(@product_customers.count, 'customer') %> total
          </div>
        </div>
      </div>
      <div class="mt-4 flex md:ml-4 md:mt-0">
        <div class="flex space-x-2">
          <%= link_to 'Add Customer', new_vendors_product_product_customer_path(@product), class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
        </div>
      </div>
    </div>

    <!-- Desktop Table View (hidden on mobile) -->
    <div class="hidden md:block overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
      <% if @product_customers.any? %>
        <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th scope="col" class="w-8 px-6 py-3"></th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Customer</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Logo</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody id="customers-list" 
                 class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"
                 data-controller="sortable"
                 data-sortable-url-value="/vendors/products/<%= @product.id %>/product_customers/:id/update_position"
                 data-sortable-handle-value=".drag-handle">
            <% @product_customers.each do |customer| %>
              <tr data-item-id="<%= customer.id %>" data-position="<%= customer.position %>" class="customer-item hover:bg-gray-50 dark:hover:bg-gray-700">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="drag-handle cursor-move text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300">
                    <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M7 2a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM7 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM7 14a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM17 2a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM17 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM17 14a2 2 0 1 1-4 0 2 2 0 0 1 4 0z"/>
                    </svg>
                  </div>
                </td>
                <td class="px-6 py-4 text-sm">
                  <div class="font-medium text-gray-900 dark:text-gray-100"><%= customer.name %></div>
                  <% if customer.description.present? %>
                    <div class="text-gray-500 dark:text-gray-400 mt-1 text-xs">
                      <%= truncate(customer.description, length: 100) %>
                    </div>
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <% if customer.logo.attached? %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                      Logo attached
                    </span>
                  <% else %>
                    <span class="text-gray-400 dark:text-gray-500">No logo</span>
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">
                    <%= link_to "View", vendors_product_product_customer_path(@product, customer), 
                        class: "text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300" %>
                    <%= link_to "Edit", edit_vendors_product_product_customer_path(@product, customer), 
                        class: "text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300" %>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
        <div class="bg-gray-50 dark:bg-gray-700 px-6 py-3">
          <p class="text-sm text-gray-500 dark:text-gray-400">
            <strong>Tip:</strong> Drag and drop the customers using the handle (⋮⋮) to reorder them.
          </p>
        </div>
      <% else %>
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No customers</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by adding your first product customer.</p>
          <div class="mt-6">
            <%= link_to 'Add Customer', new_vendors_product_product_customer_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Mobile Card View (visible on mobile only) -->
    <div class="md:hidden">
      <% if @product_customers.any? %>
        <div class="space-y-4">
          <% @product_customers.each do |customer| %>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
              <div class="px-4 py-5 sm:px-6">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center">
                      <div class="drag-handle cursor-move text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 mr-3">
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M7 2a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM7 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM7 14a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM17 2a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM17 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0zM17 14a2 2 0 1 1-4 0 2 2 0 0 1 4 0z"/>
                        </svg>
                      </div>
                      <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= customer.name %></h3>
                        <% if customer.description.present? %>
                          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1"><%= truncate(customer.description, length: 100) %></p>
                        <% end %>
                      </div>
                    </div>
                  </div>
                  <% if customer.logo.attached? %>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                      Logo attached
                    </span>
                  <% else %>
                    <span class="text-xs text-gray-400 dark:text-gray-500">No logo</span>
                  <% end %>
                </div>
                
                <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700 flex space-x-2">
                  <%= link_to "View", vendors_product_product_customer_path(@product, customer), 
                      class: "inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600" %>
                  <%= link_to "Edit", edit_vendors_product_product_customer_path(@product, customer), 
                      class: "inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600" %>
                </div>
              </div>
            </div>
          <% end %>
        </div>
        <div class="mt-4 bg-gray-50 dark:bg-gray-700 px-4 py-3 rounded-lg">
          <p class="text-sm text-gray-500 dark:text-gray-400">
            <strong>Tip:</strong> On desktop, you can drag and drop customers to reorder them.
          </p>
        </div>
      <% else %>
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No customers</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by adding your first product customer.</p>
          <div class="mt-6">
            <%= link_to 'Add Customer', new_vendors_product_product_customer_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <style>
  .sortable-ghost {
    opacity: 0.4;
    background: #c8ebfb;
  }

  .sortable-chosen {
    background: #f0f9ff;
  }

  .sortable-drag {
    opacity: 0.8;
  }

  .customer-item {
    transition: all 0.2s ease;
  }
  </style>
<% end %>