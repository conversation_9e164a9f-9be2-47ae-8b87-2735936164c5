<%= render 'layout' do %>
  <!-- Page Heading -->
  <div class="flex items-center justify-between mb-6">
    <div>
      <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Manage Featured Video</h3>
      <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
        Upload or replace your product's featured video
      </p>
    </div>
    <%= link_to 'Back to Product', vendors_product_path(@product), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
  </div>

  <!-- Current Video Section -->
  <% if @product.featured_video.attached? %>
    <div class="mb-8 bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
      <div class="px-4 py-5 sm:p-6">
        <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Current Featured Video</h4>
        
        <!-- Video Preview -->
        <div class="mb-6">
          <div class="relative bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden" style="aspect-ratio: 16 / 9;">
            <video 
              controls 
              controlsList="nodownload"
              class="w-full h-full object-contain"
              preload="metadata">
              <source src="<%= url_for(@product.featured_video) %>" type="<%= @product.featured_video.content_type %>">
              Your browser does not support the video tag.
            </video>
          </div>
        </div>

        <!-- Video Details -->
        <div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 mb-4">
          <dl class="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-3">
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Filename</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                <%= @product.featured_video.filename rescue "Unknown" %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">File Size</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                <%= number_to_human_size(@product.featured_video.byte_size) rescue "Unknown" %>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Format</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                <%= @product.featured_video.content_type rescue "Unknown" %>
              </dd>
            </div>
          </dl>
        </div>

        <!-- Delete Video Button -->
        <div class="flex justify-end">
          <%= button_to "Delete Video", 
              remove_featured_video_vendors_product_path(@product), 
              method: :delete,
              data: { confirm: "Are you sure you want to delete the featured video? This action cannot be undone." },
              class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors" %>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Upload/Replace Video Section -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <h4 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
        <%= @product.featured_video.attached? ? 'Replace Featured Video' : 'Upload Featured Video' %>
      </h4>
      
      <%= form_with model: @product, url: update_featured_video_vendors_product_path(@product), local: true, multipart: true, class: "space-y-6" do |form| %>
        <% if @product.errors.any? %>
          <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-800 rounded-md p-4">
            <div class="flex">
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                  There were <%= pluralize(@product.errors.count, "error") %> with your submission:
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                  <ul role="list" class="list-disc space-y-1 pl-5">
                    <% @product.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <div>
          <%= form.label :featured_video, "Select Video File", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.file_field :featured_video, 
              class: "mt-1 block w-full text-sm text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer bg-gray-50 dark:bg-gray-700 focus:ring-blue-500 focus:border-blue-500 file:mr-4 file:py-2 file:px-4 file:rounded-l-lg file:border-0 file:text-sm file:font-medium file:bg-gray-100 file:text-gray-700 hover:file:bg-gray-200 dark:file:bg-gray-600 dark:file:text-gray-200 dark:hover:file:bg-gray-500",
              accept: "video/*",
              required: !@product.featured_video.attached? %>
          
          <div class="mt-3 text-sm text-gray-500 dark:text-gray-400">
            <p class="mb-2"><strong>Requirements:</strong></p>
            <ul class="list-disc pl-5 space-y-1">
              <li>Supported formats: MP4, WebM, OGG, AVI, MOV, WMV, FLV, MKV</li>
              <li>Maximum file size: 50MB</li>
              <li>Recommended resolution: 1920x1080 or lower</li>
              <li>The video will be automatically compressed and optimized</li>
            </ul>
          </div>
          
          <% if @product.featured_video.attached? %>
            <p class="mt-2 text-sm text-yellow-600 dark:text-yellow-400">
              <strong>Note:</strong> Uploading a new video will replace the current one permanently.
            </p>
          <% end %>
        </div>

        <div class="flex justify-end space-x-3">
          <%= link_to "Cancel", vendors_product_path(@product), class: "bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors" %>
          <%= form.submit @product.featured_video.attached? ? "Replace Video" : "Upload Video", class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors" %>
        </div>
      <% end %>
    </div>
  </div>
<% end %>