<!-- Tab Navigation -->
<div class="border-b border-gray-200 dark:border-gray-700 mb-8">
  <nav class="-mb-px flex space-x-6" aria-label="Tabs">
    <%= link_to vendors_product_path(@product), 
        class: "flex items-center border-b-2 px-1 py-4 mr-8 text-sm font-medium transition-colors #{request.path == vendors_product_path(@product) ? 'border-indigo-500 dark:border-indigo-400 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:border-gray-200 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300'}" do %>
      Overview
    <% end %>
    
    <%= link_to features_vendors_product_path(@product), 
        class: "flex items-center border-b-2 px-1 py-4 mr-8 text-sm font-medium transition-colors #{request.path == features_vendors_product_path(@product) ? 'border-indigo-500 dark:border-indigo-400 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:border-gray-200 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300'}" do %>
      Features
      <span class="ml-2 rounded-full bg-gray-100 dark:bg-gray-700 px-2 py-0.5 text-xs font-medium text-gray-900 dark:text-gray-100"><%= @product.product_features.count %></span>
    <% end %>
    
    <%= link_to customers_vendors_product_path(@product), 
        class: "flex items-center border-b-2 px-1 py-4 mr-8 text-sm font-medium transition-colors #{request.path == customers_vendors_product_path(@product) ? 'border-indigo-500 dark:border-indigo-400 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:border-gray-200 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300'}" do %>
      Customers
      <span class="ml-2 rounded-full bg-gray-100 dark:bg-gray-700 px-2 py-0.5 text-xs font-medium text-gray-900 dark:text-gray-100"><%= @product.product_customers.count %></span>
    <% end %>
    
    <%= link_to videos_vendors_product_path(@product), 
        class: "flex items-center border-b-2 px-1 py-4 mr-8 text-sm font-medium transition-colors #{request.path == videos_vendors_product_path(@product) ? 'border-indigo-500 dark:border-indigo-400 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:border-gray-200 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300'}" do %>
      Videos
      <span class="ml-2 rounded-full bg-gray-100 dark:bg-gray-700 px-2 py-0.5 text-xs font-medium text-gray-900 dark:text-gray-100"><%= @product.product_videos.count %></span>
    <% end %>
    
    <%= link_to screenshots_vendors_product_path(@product), 
        class: "flex items-center border-b-2 px-1 py-4 mr-8 text-sm font-medium transition-colors #{request.path == screenshots_vendors_product_path(@product) ? 'border-indigo-500 dark:border-indigo-400 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:border-gray-200 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300'}" do %>
      Screenshots
      <span class="ml-2 rounded-full bg-gray-100 dark:bg-gray-700 px-2 py-0.5 text-xs font-medium text-gray-900 dark:text-gray-100"><%= @product.product_screenshots.count %></span>
    <% end %>
    
    <%= link_to case_studies_vendors_product_path(@product), 
        class: "flex items-center border-b-2 px-1 py-4 mr-8 text-sm font-medium transition-colors #{request.path == case_studies_vendors_product_path(@product) ? 'border-indigo-500 dark:border-indigo-400 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:border-gray-200 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300'}" do %>
      Case Studies
      <span class="ml-2 rounded-full bg-gray-100 dark:bg-gray-700 px-2 py-0.5 text-xs font-medium text-gray-900 dark:text-gray-100"><%= @product.case_studies.count %></span>
    <% end %>
    
    <%= link_to content_vendors_product_path(@product), 
        class: "flex items-center border-b-2 px-1 py-4 mr-8 text-sm font-medium transition-colors #{request.path == content_vendors_product_path(@product) ? 'border-indigo-500 dark:border-indigo-400 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:border-gray-200 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300'}" do %>
      Content
      <span class="ml-2 rounded-full bg-gray-100 dark:bg-gray-700 px-2 py-0.5 text-xs font-medium text-gray-900 dark:text-gray-100"><%= @product.product_content.count %></span>
    <% end %>
    
    <%= link_to contracts_vendors_product_path(@product), 
        class: "flex items-center border-b-2 px-1 py-4 mr-8 text-sm font-medium transition-colors #{request.path == contracts_vendors_product_path(@product) ? 'border-indigo-500 dark:border-indigo-400 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:border-gray-200 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300'}" do %>
      Contracts
      <span class="ml-2 rounded-full bg-gray-100 dark:bg-gray-700 px-2 py-0.5 text-xs font-medium text-gray-900 dark:text-gray-100"><%= @product.product_contracts.count %></span>
    <% end %>
    
    <%= link_to visibility_vendors_product_path(@product), 
        class: "flex items-center border-b-2 px-1 py-4 text-sm font-medium transition-colors #{request.path == visibility_vendors_product_path(@product) ? 'border-indigo-500 dark:border-indigo-400 text-indigo-600 dark:text-indigo-400' : 'border-transparent text-gray-500 dark:text-gray-400 hover:border-gray-200 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300'}" do %>
      Visibility
    <% end %>
  </nav>
</div>