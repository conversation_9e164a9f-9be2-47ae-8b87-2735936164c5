<% content_for :page_title, "Product Analytics - #{@product.name}" %>

<div class="sm:flex sm:items-center">
  <div class="sm:flex-auto">
    <h1 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Product Analytics</h1>
    <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">Data for <%= @product.name %></p>
  </div>
  <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
    <%= link_to "← Back to Products", vendors_products_path, 
        class: "inline-flex items-center justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2" %>
  </div>
</div>

<!-- Summary Cards -->
<div class="mt-8 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
  <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Page Views</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= number_with_delimiter(@total_page_views) %></dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" />
          </svg>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Unique Visitors</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= number_with_delimiter(@unique_visitors) %></dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
          </svg>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total Events</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= number_with_delimiter(@analytics.size) %></dd>
          </dl>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Analytics Table -->
<div class="mt-8">
  <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
    <% if @analytics.any? %>
      <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Time Viewed</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Event Type</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">User Type</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Position</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Region</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Device</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Browser</th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          <% @analytics.each do |analytic| %>
            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                <%= analytic.created_at.strftime("%-m/%-d/%y %-l:%M %p %Z") %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm">
                <% case analytic.event_type %>
                <% when 'page_view' %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                    <%= analytic.event_type_display %>
                  </span>
                <% when 'content_view' %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                    <%= analytic.event_type_display %>
                  </span>
                  <% if analytic.content_type_display.present? %>
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      <%= analytic.content_type_display %>
                    </div>
                  <% end %>
                <% when 'content_download' %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200">
                    <%= analytic.event_type_display %>
                  </span>
                  <% if analytic.content_type_display.present? %>
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      <%= analytic.content_type_display %>
                    </div>
                  <% end %>
                <% when 'video_play' %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200">
                    <%= analytic.event_type_display %>
                  </span>
                  <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Featured Video
                  </div>
                <% when 'video_watch_duration' %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200">
                    <%= analytic.event_type_display %>
                  </span>
                  <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <% if analytic.watch_duration && analytic.video_duration %>
                      <% 
                        minutes = (analytic.watch_duration / 60).to_i
                        seconds = (analytic.watch_duration % 60).to_i
                        duration_text = minutes > 0 ? "#{minutes}m #{seconds}s" : "#{seconds}s"
                      %>
                      <%= duration_text %> 
                      <% if analytic.watch_percentage %>
                        (<%= analytic.watch_percentage %>%)
                      <% end %>
                    <% else %>
                      Featured Video
                    <% end %>
                  </div>
                <% else %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                    <%= analytic.event_type_display %>
                  </span>
                <% end %>
              </td>
              <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                <% user_type = analytic.user_type_label %>
                <% case user_type %>
                <% when 'Anonymous' %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                    Anonymous
                  </span>
                <% when 'Admin' %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
                    Admin
                  </span>
                <% when 'Vendor' %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                    Vendor
                  </span>
                <% when 'Government' %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                    Agency
                  </span>
                <% else %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                    <%= user_type %>
                  </span>
                <% end %>
              </td>
              <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                <% if analytic.user&.job_title.present? || analytic.user&.department.present? %>
                  <% if analytic.user.job_title.present? %>
                    <div class="font-medium"><%= analytic.user.job_title %></div>
                  <% end %>
                  <% if analytic.user.department.present? %>
                    <div class="text-xs text-gray-400 dark:text-gray-500">
                      <%= analytic.user.department %>
                    </div>
                  <% end %>
                <% else %>
                  <span class="text-gray-400 dark:text-gray-500">—</span>
                <% end %>
              </td>
              <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                <% location = analytic.location_info %>
                <% if location.present? && location['region'].present? %>
                  <%= location['region'] %>
                <% elsif location.present? && location['country'].present? %>
                  <%= location['country'] %>
                <% else %>
                  <span class="text-gray-400 dark:text-gray-500">Unknown</span>
                <% end %>
              </td>
              <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                <% browser = analytic.browser_info %>
                <% if browser.present? %>
                  <%= browser['device_type']&.capitalize || 'Unknown' %>
                  <% if browser['device_name'].present? %>
                    <div class="text-xs text-gray-400 dark:text-gray-500">
                      <%= browser['device_name'] %>
                    </div>
                  <% end %>
                <% else %>
                  <span class="text-gray-400 dark:text-gray-500">Unknown</span>
                <% end %>
              </td>
              <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                <% browser = analytic.browser_info %>
                <% if browser.present? && browser['browser'].present? %>
                  <%= browser['browser'] %>
                  <% if browser['os'].present? %>
                    <div class="text-xs text-gray-400 dark:text-gray-500">
                      <%= browser['os'] %>
                    </div>
                  <% end %>
                <% else %>
                  <span class="text-gray-400 dark:text-gray-500">Unknown</span>
                <% end %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    <% else %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No analytics data</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Analytics data will appear here once your product receives visitors.</p>
      </div>
    <% end %>
  </div>
</div>

<% if @analytics.size >= 1000 %>
  <div class="mt-4 bg-yellow-50 dark:bg-yellow-900/50 border border-yellow-200 dark:border-yellow-800 rounded-md p-4">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <p class="text-sm text-yellow-700 dark:text-yellow-200">
          Showing the most recent 1,000 analytics events. Older events are still tracked but not displayed here.
        </p>
      </div>
    </div>
  </div>
<% end %>