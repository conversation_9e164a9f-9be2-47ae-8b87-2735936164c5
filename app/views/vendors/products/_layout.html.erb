<div class="max-w-7xl mx-auto">
  <div class="mb-8 flex justify-between">
    <div class="flex items-center mb-6">
      <%= link_to vendors_products_path, class: 'mr-2 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors' do %>
        <svg class="h-5 w-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      <% end %>
      <h2 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl">
        <%= @product.name %>
      </h2>
    </div>
    
    <div class="">
      <%= link_to 'View marketplace page', marketplace_product_path(@product), target: '_blank', class: "inline-flex items-center px-3 py-2 border shadow-sm text-sm leading-4 font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors #{@product.published? ? 'border-green-300 dark:border-green-600 text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/50 hover:bg-green-100 dark:hover:bg-green-900/30 focus:ring-green-500 dark:focus:ring-green-400' : 'border-yellow-300 dark:border-yellow-600 text-yellow-700 dark:text-yellow-300 bg-yellow-50 dark:bg-yellow-900/20 hover:bg-yellow-100 dark:hover:bg-yellow-900/30 focus:ring-yellow-500 dark:focus:ring-yellow-400'}" %>
    </div>
  </div>

  <%= render 'navigation' %>

  <!-- Page Content -->
  <%= yield %>

</div>