<div class="max-w-7xl mx-auto" data-controller="simple-tabs">
  <div class="mb-8 flex justify-between">
    <div class="flex items-center mb-6">
      <%= link_to vendors_products_path, class: 'mr-2 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors' do %>
        <svg class="h-5 w-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      <% end %>
      <h2 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl">
        <%= @product.name %>
      </h2>
    </div>
    
    <div class="">
      <%= link_to 'View marketplace page', marketplace_product_path(@product), target: '_blank', class: "inline-flex items-center px-3 py-2 border shadow-sm text-sm leading-4 font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors #{@product.published? ? 'border-green-300 dark:border-green-600 text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/50 hover:bg-green-100 dark:hover:bg-green-900/30 focus:ring-green-500 dark:focus:ring-green-400' : 'border-yellow-300 dark:border-yellow-600 text-yellow-700 dark:text-yellow-300 bg-yellow-50 dark:bg-yellow-900/20 hover:bg-yellow-100 dark:hover:bg-yellow-900/30 focus:ring-yellow-500 dark:focus:ring-yellow-400'}" %>
    </div>
  </div>

  <!-- Tab Navigation -->
  <div class="border-b border-gray-200 dark:border-gray-700 mb-8">
    <nav class="-mb-px flex space-x-6" aria-label="Tabs">
      <button type="button" class="active-tab flex items-center border-b-2 border-indigo-500 dark:border-indigo-400 px-1 py-4 mr-8 text-sm font-medium text-indigo-600 dark:text-indigo-400" data-tab="overview" data-simple-tabs-target="button" data-action="click->simple-tabs#showTab">
        Overview
      </button>
      <button type="button" class="inactive-tab flex items-center border-b-2 border-transparent px-1 py-4 mr-8 text-sm font-medium text-gray-500 dark:text-gray-400 hover:border-gray-200 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300" data-tab="features" data-simple-tabs-target="button" data-action="click->simple-tabs#showTab">
        Features
        <span class="ml-2 rounded-full bg-gray-100 dark:bg-gray-700 px-2 py-0.5 text-xs font-medium text-gray-900 dark:text-gray-100"><%= @product.product_features.count %></span>
      </button>
      <button type="button" class="inactive-tab flex items-center border-b-2 border-transparent px-1 py-4 mr-8 text-sm font-medium text-gray-500 dark:text-gray-400 hover:border-gray-200 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300" data-tab="customers" data-simple-tabs-target="button" data-action="click->simple-tabs#showTab">
        Customers
        <span class="ml-2 rounded-full bg-gray-100 dark:bg-gray-700 px-2 py-0.5 text-xs font-medium text-gray-900 dark:text-gray-100"><%= @product.product_customers.count %></span>
      </button>
      <button type="button" class="inactive-tab flex items-center border-b-2 border-transparent px-1 py-4 mr-8 text-sm font-medium text-gray-500 dark:text-gray-400 hover:border-gray-200 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300" data-tab="videos" data-simple-tabs-target="button" data-action="click->simple-tabs#showTab">
        Videos
        <span class="ml-2 rounded-full bg-gray-100 dark:bg-gray-700 px-2 py-0.5 text-xs font-medium text-gray-900 dark:text-gray-100"><%= @product.product_videos.count %></span>
      </button>
      <button type="button" class="inactive-tab flex items-center border-b-2 border-transparent px-1 py-4 mr-8 text-sm font-medium text-gray-500 dark:text-gray-400 hover:border-gray-200 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300" data-tab="screenshots" data-simple-tabs-target="button" data-action="click->simple-tabs#showTab">
        Screenshots
        <span class="ml-2 rounded-full bg-gray-100 dark:bg-gray-700 px-2 py-0.5 text-xs font-medium text-gray-900 dark:text-gray-100"><%= @product.product_screenshots.count %></span>
      </button>
      <button type="button" class="inactive-tab flex items-center border-b-2 border-transparent px-1 py-4 mr-8 text-sm font-medium text-gray-500 dark:text-gray-400 hover:border-gray-200 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300" data-tab="case-studies" data-simple-tabs-target="button" data-action="click->simple-tabs#showTab">
        Case Studies
        <span class="ml-2 rounded-full bg-gray-100 dark:bg-gray-700 px-2 py-0.5 text-xs font-medium text-gray-900 dark:text-gray-100"><%= @product.case_studies.count %></span>
      </button>
      <button type="button" class="inactive-tab flex items-center border-b-2 border-transparent px-1 py-4 mr-8 text-sm font-medium text-gray-500 dark:text-gray-400 hover:border-gray-200 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300" data-tab="content" data-simple-tabs-target="button" data-action="click->simple-tabs#showTab">
        Content
        <span class="ml-2 rounded-full bg-gray-100 dark:bg-gray-700 px-2 py-0.5 text-xs font-medium text-gray-900 dark:text-gray-100"><%= @product.product_content.count %></span>
      </button>
      <button type="button" class="inactive-tab flex items-center border-b-2 border-transparent px-1 py-4 text-sm font-medium text-gray-500 dark:text-gray-400 hover:border-gray-200 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300" data-tab="visibility" data-simple-tabs-target="button" data-action="click->simple-tabs#showTab">
        Visibility
      </button>
    </nav>
  </div>

  <!-- Tab Content -->
  <div id="tab-content">
    <!-- Overview Tab -->
    <div class="tab-content active-tab-content" data-tab-content="overview" data-simple-tabs-target="content">
      <!-- Product Overview Heading -->
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Product Overview</h3>
        <%= link_to 'Edit Product', edit_vendors_product_path(@product), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
      </div>
      
      <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
        
        <div class="px-4 py-5 sm:p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-2">Status</h3>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <%= @product.published? ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' %>">
                <%= @product.published? ? 'Published' : 'Draft' %>
              </span>
              
              <% if @product.description.blank? %>
                <div class="mt-6">
                  <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-2">Product Details</h3>
                  <dl class="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-1">
                    <div>
                      <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Company</dt>
                      <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product.account&.name || "Admin-managed product" %></dd>
                    </div>
                  </dl>
                </div>
              <% end %>
            </div>
            
            <% if @product.description.present? %>
              <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-2">Description</h3>
                <div class="text-gray-700 dark:text-gray-300">
                  <%= @product.description %>
                </div>
              </div>
            <% end %>
          </div>

          <!-- Product Details and Logo Row -->
          <div class="mt-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Product Details Column -->
              <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-3">Product Details</h3>
                <dl class="space-y-3">
                  <div>
                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Company</dt>
                    <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product.account&.name || "Admin-managed product" %></dd>
                  </div>
                </dl>
              </div>
              
              <!-- Product Logo Column -->
              <div>
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-3">Product Logo</h3>
                <% if @product.logo.attached? %>
                  <div class="w-32 h-32 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 flex items-center justify-center overflow-hidden">
                    <%= image_tag @product.logo, 
                        class: "max-w-full max-h-full object-contain",
                        alt: "#{@product.name} logo" %>
                  </div>
                <% else %>
                  <div class="w-32 h-32 bg-gray-50 dark:bg-gray-700 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 flex items-center justify-center">
                    <div class="text-center">
                      <svg class="mx-auto h-8 w-8 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">No logo</p>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>

          <!-- Certifications Section -->
          <div class="mt-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-2">Certifications</h3>
            <% if @product.certifications.any? %>
              <div class="flex flex-wrap gap-2">
                <% @product.certifications.each do |certification| %>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200" title="<%= certification.description %>">
                    <%= certification.name %>
                  </span>
                <% end %>
              </div>
            <% else %>
              <p class="text-sm text-gray-500 dark:text-gray-400">No certifications listed</p>
            <% end %>
          </div>


        </div>

      </div>

      <!-- Featured Video Section -->
      <div class="mt-8 bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-6">Featured Video</h3>
          
          <% if @product.featured_video.attached? %>
            <div class="relative bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden" style="aspect-ratio: 16 / 9;">
              <video 
                controls 
                controlsList="nodownload"
                class="w-full h-full object-contain"
                preload="metadata">
                <source src="<%= url_for(@product.featured_video) %>" type="<%= @product.featured_video.content_type %>">
                Your browser does not support the video tag.
              </video>
              
              <!-- Video overlay with play button for better UX -->
              <div class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 opacity-0 hover:opacity-100 transition-opacity pointer-events-none">
                <div class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
                  <svg class="w-6 h-6 text-gray-900 ml-1" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </div>
              </div>
            </div>
          <% else %>
            <!-- No featured video placeholder -->
            <div class="relative bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden flex items-center justify-center" style="aspect-ratio: 16 / 9;">
              <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No featured video uploaded</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Upload a featured video to showcase your product here.</p>
              </div>
            </div>
          <% end %>
        </div>
      </div>

    </div>

    <!-- Product Features Tab -->
    <div class="tab-content hidden" data-tab-content="features" data-simple-tabs-target="content">
      <div class="mt-8">
        <div class="sm:flex sm:items-center">
          <div class="sm:flex-auto">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Product Features</h3>
          </div>
          <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <div class="flex space-x-2">
              <%= link_to 'Manage Features', vendors_product_product_features_path(@product), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
              <%= link_to 'Add Feature', new_vendors_product_product_feature_path(@product), class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
            </div>
          </div>
        </div>

        <% if @product.product_features.any? %>
          <div class="mt-8">
            <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
              <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
                <thead class="bg-gray-50 dark:bg-gray-900/50">
                  <tr>
                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Feature</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Description</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Status</th>
                    <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                      <span class="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
                  <% @product.product_features.ordered.limit(6).each do |feature| %>
                    <tr>
                      <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-gray-100 sm:pl-6">
                        <div class="flex items-center">
                          <div class="h-8 w-8 flex-shrink-0 mr-3">
                            <% if feature.attachment.attached? && feature.image? %>
                              <%= image_tag feature.attachment, class: "h-8 w-8 object-cover rounded-lg" %>
                            <% elsif feature.attachment.attached? && feature.video? %>
                              <div class="h-8 w-8 rounded-lg bg-purple-100 dark:bg-purple-900/50 flex items-center justify-center">
                                <svg class="h-4 w-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                </svg>
                              </div>
                            <% else %>
                              <div class="h-8 w-8 rounded-lg bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center">
                                <svg class="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                              </div>
                            <% end %>
                          </div>
                          <%= feature.name %>
                        </div>
                      </td>
                      <td class="px-3 py-4 text-sm text-gray-600 dark:text-gray-400">
                        <% if feature.description.present? %>
                          <%= truncate(strip_tags(feature.description), length: 60) %>
                        <% else %>
                          <span class="text-gray-400 dark:text-gray-500 italic">No description</span>
                        <% end %>
                      </td>
                      <td class="whitespace-nowrap px-3 py-4 text-sm">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= feature.published? ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' %>">
                          <%= feature.published? ? 'Published' : 'Draft' %>
                        </span>
                      </td>
                      <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                        <%= link_to 'View details', vendors_product_product_feature_path(@product, feature), class: 'text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300' %>
                      </td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
            
            <% if @product.product_features.count > 6 %>
              <div class="mt-6 text-center">
                <%= link_to "View all #{@product.product_features.count} features", vendors_product_product_features_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/50 hover:bg-indigo-200 dark:hover:bg-indigo-900/70 transition-colors' %>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="mt-8 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No features</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by adding your first product feature.</p>
            <div class="mt-6">
              <%= link_to 'Add Feature', new_vendors_product_product_feature_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
            </div>
          </div>
        <% end %>
      </div>
    </div>


    <!-- Product Customers Tab -->
<div class="tab-content hidden" data-tab-content="customers" data-simple-tabs-target="content">
  <div class="mt-8">
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Product Customers</h3>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <div class="flex space-x-2">
          <%= link_to 'Manage Customers', vendors_product_product_customers_path(@product), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
          <%= link_to 'Add Customer', new_vendors_product_product_customer_path(@product), class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
        </div>
      </div>
    </div>

    <% if @product.product_customers.any? %>
      <div class="mt-8">
        <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
            <thead class="bg-gray-50 dark:bg-gray-900/50">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Customer</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Status</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Description</th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
              <% @product.product_customers.ordered.limit(6).each do |customer| %>
                <tr>
                  <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-gray-100 sm:pl-6">
                    <div class="flex items-center">
                      <div class="h-8 w-8 flex-shrink-0 mr-3">
                        <% if customer.logo.attached? && customer.image? %>
                          <%= image_tag customer.logo, class: "h-8 w-8 object-contain rounded" %>
                        <% else %>
                          <div class="h-8 w-8 rounded bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                            <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                          </div>
                        <% end %>
                      </div>
                      <%= customer.name %>
                    </div>
                  </td>
                  <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                      <%= customer.published? ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' %>">
                      <%= customer.published? ? 'Published' : 'Draft' %>
                    </span>
                  </td>
                  <td class="px-3 py-4 text-sm text-gray-600 dark:text-gray-400">
                    <% if customer.description.present? %>
                      <%= truncate(customer.description, length: 60) %>
                    <% else %>
                      <span class="text-gray-400 dark:text-gray-500 italic">No description</span>
                    <% end %>
                  </td>
                  <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                    <%= link_to 'View details', vendors_product_product_customer_path(@product, customer), class: 'text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300' %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
        
        <% if @product.product_customers.count > 6 %>
          <div class="mt-6 text-center">
            <%= link_to "View all #{@product.product_customers.count} customers", vendors_product_product_customers_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/50 hover:bg-indigo-200 dark:hover:bg-indigo-900/70 transition-colors' %>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="mt-8 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No customers</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by adding your first product customer.</p>
        <div class="mt-6">
          <%= link_to 'Add Customer', new_vendors_product_product_customer_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
        </div>
      </div>
    <% end %>
  </div>
</div>

    <!-- Product Videos Tab -->
<div class="tab-content hidden" data-tab-content="videos" data-simple-tabs-target="content">
  <div class="mt-8">
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Product Videos</h3>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <div class="flex space-x-2">
          <%= link_to 'Manage Videos', vendors_product_product_videos_path(@product), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
          <%= link_to 'Add Video', new_vendors_product_product_video_path(@product), class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
        </div>
      </div>
    </div>

    <% if @product.product_videos.any? %>
      <div class="mt-8">
        <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
            <thead class="bg-gray-50 dark:bg-gray-900/50">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Title</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Status</th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
              <% @product.product_videos.by_position.limit(6).each do |video| %>
                <tr>
                  <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-gray-100 sm:pl-6">
                    <%= video.title %>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= video.published? ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' %>">
                      <%= video.published? ? 'Published' : 'Draft' %>
                    </span>
                  </td>
                  <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                    <%= link_to 'View details', vendors_product_product_video_path(@product, video), class: 'text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300' %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
        
        <% if @product.product_videos.count > 6 %>
          <div class="mt-6 text-center">
            <%= link_to "View all #{@product.product_videos.count} videos", vendors_product_product_videos_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/50 hover:bg-indigo-200 dark:hover:bg-indigo-900/70 transition-colors' %>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="mt-8 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No videos</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by adding your first product video.</p>
        <div class="mt-6">
          <%= link_to 'Add Video', new_vendors_product_product_video_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
        </div>
      </div>
    <% end %>
  </div>
</div>


    <!-- Product Screenshots Tab -->
<div class="tab-content hidden" data-tab-content="screenshots" data-simple-tabs-target="content">
  <div class="mt-8">
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Product Screenshots</h3>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <div class="flex space-x-2">
          <%= link_to 'Manage Screenshots', vendors_product_product_screenshots_path(@product), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
          <%= link_to 'Add Screenshot', new_vendors_product_product_screenshot_path(@product), class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
        </div>
      </div>
    </div>

    <% if @product.product_screenshots.any? %>
      <div class="mt-8">
        <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
            <thead class="bg-gray-50 dark:bg-gray-900/50">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Title</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Preview</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Status</th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
              <% @product.product_screenshots.ordered.limit(6).each do |screenshot| %>
                <tr>
                  <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-gray-100 sm:pl-6">
                    <%= screenshot.title %>
                  </td>
                  <td class="px-3 py-4 text-sm">
                    <% if screenshot.image.attached? %>
                      <div class="h-12 w-20 flex-shrink-0">
                        <%= image_tag screenshot.image, class: "h-12 w-20 object-cover rounded-lg" %>
                      </div>
                    <% else %>
                      <div class="h-12 w-20 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                        <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                    <% end %>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= screenshot.published? ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' %>">
                      <%= screenshot.published? ? 'Published' : 'Draft' %>
                    </span>
                  </td>
                  <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                    <%= link_to 'View details', vendors_product_product_screenshot_path(@product, screenshot), class: 'text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300' %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
        
        <% if @product.product_screenshots.count > 6 %>
          <div class="mt-6 text-center">
            <%= link_to "View all #{@product.product_screenshots.count} screenshots", vendors_product_product_screenshots_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/50 hover:bg-indigo-200 dark:hover:bg-indigo-900/70 transition-colors' %>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="mt-8 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2v12a2 2 0 002 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No screenshots</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by adding your first product screenshot.</p>
        <div class="mt-6">
          <%= link_to 'Add Screenshot', new_vendors_product_product_screenshot_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
        </div>
      </div>
    <% end %>
  </div>
</div>

    <!-- Case Studies Tab -->
<div class="tab-content hidden" data-tab-content="case-studies" data-simple-tabs-target="content">
  <div class="mt-8">
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Case Studies</h3>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <div class="flex space-x-2">
          <%= link_to 'Manage Case Studies', vendors_product_case_studies_path(@product), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
          <%= link_to 'Add Case Study', new_vendors_product_case_study_path(@product), class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
        </div>
      </div>
    </div>

    <% if @product.case_studies.any? %>
      <div class="mt-8">
        <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
            <thead class="bg-gray-50 dark:bg-gray-900/50">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Title</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Status</th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
              <% @product.case_studies.by_position.limit(6).each do |case_study| %>
                <tr>
                  <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-gray-100 sm:pl-6">
                    <%= case_study.title %>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= case_study.published? ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' %>">
                      <%= case_study.published? ? 'Published' : 'Draft' %>
                    </span>
                  </td>
                  <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                    <%= link_to 'View details', vendors_product_case_study_path(@product, case_study), class: 'text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300' %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
        
        <% if @product.case_studies.count > 6 %>
          <div class="mt-6 text-center">
            <%= link_to "View all #{@product.case_studies.count} case studies", vendors_product_case_studies_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/50 hover:bg-indigo-200 dark:hover:bg-indigo-900/70 transition-colors' %>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="mt-8 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No case studies</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by adding your first case study.</p>
        <div class="mt-6">
          <%= link_to 'Add Case Study', new_vendors_product_case_study_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
        </div>
      </div>
    <% end %>
  </div>
</div>

<!-- Product Content Tab -->
<div class="tab-content hidden" data-tab-content="content" data-simple-tabs-target="content">
  <div class="mt-8">
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Product Content</h3>
      </div>
      <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
        <div class="flex space-x-2">
          <%= link_to 'Manage Content', vendors_product_product_contents_path(@product), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
          <%= link_to 'Add Content', new_vendors_product_product_content_path(@product), class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
        </div>
      </div>
    </div>

    <% if @product.product_content.any? %>
      <div class="mt-8">
        <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
            <thead class="bg-gray-50 dark:bg-gray-900/50">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-gray-100 sm:pl-6">Title</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-gray-100">Status</th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
              <% @product.product_content.by_position.limit(6).each do |content| %>
                <tr>
                  <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-gray-100 sm:pl-6">
                    <%= content.title %>
                  </td>
                  <td class="whitespace-nowrap px-3 py-4 text-sm">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= content.published? ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' %>">
                      <%= content.published? ? 'Published' : 'Draft' %>
                    </span>
                  </td>
                  <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                    <%= link_to 'View details', vendors_product_product_content_path(@product, content), class: 'text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300' %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
        
        <% if @product.product_content.count > 6 %>
          <div class="mt-6 text-center">
            <%= link_to "View all #{@product.product_content.count} content items", vendors_product_product_contents_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 dark:text-indigo-300 bg-indigo-100 dark:bg-indigo-900/50 hover:bg-indigo-200 dark:hover:bg-indigo-900/70 transition-colors' %>
          </div>
        <% end %>
      </div>
    <% else %>
      <div class="mt-8 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No content</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by adding your first product content.</p>
        <div class="mt-6">
          <%= link_to 'Add Content', new_vendors_product_product_content_path(@product), class: 'inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
        </div>
      </div>
    <% end %>
  </div>
</div>

<!-- Visibility Tab -->
<div class="tab-content hidden" data-tab-content="visibility" data-simple-tabs-target="content">
  <div class="mt-8">
    <div class="sm:flex sm:items-center">
      <div class="sm:flex-auto">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Public Visibility Settings</h3>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">Control what sections are visible on your product's marketplace page to the public. This would be any anonymous visitors, guest users and even other vendors. Any sections that are unchecked will only be visible to you, your team members, verified government users and administrators.</p>
      </div>
    </div>

    <%= form_with model: [@product], url: vendors_product_path(@product), method: :patch, local: true, class: "mt-8" do |form| %>
      <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
        <div class="px-4 py-5 sm:p-6">
          <div class="space-y-6">
            
            <!-- Featured Video -->
            <div class="flex items-center justify-between py-4">
              <div class="flex-1">
                <h4 class="text-base font-medium text-gray-900 dark:text-gray-100">Featured Video</h4>
                <p class="text-sm text-gray-500 dark:text-gray-400">Show the featured video at the top of your product page</p>
              </div>
              <div class="ml-4">
                <%= form.check_box :show_featured_video, { class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700" }, "true", "false" %>
              </div>
            </div>

            <!-- Product Videos -->
            <div class="flex items-center justify-between py-4 border-t border-gray-200 dark:border-gray-700">
              <div class="flex-1">
                <h4 class="text-base font-medium text-gray-900 dark:text-gray-100">Product Videos</h4>
                <p class="text-sm text-gray-500 dark:text-gray-400">Show the videos section with all product demo videos</p>
              </div>
              <div class="ml-4">
                <%= form.check_box :show_product_videos, { class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700" }, "true", "false" %>
              </div>
            </div>

            <!-- Product Screenshots -->
            <div class="flex items-center justify-between py-4 border-t border-gray-200 dark:border-gray-700">
              <div class="flex-1">
                <h4 class="text-base font-medium text-gray-900 dark:text-gray-100">Product Screenshots</h4>
                <p class="text-sm text-gray-500 dark:text-gray-400">Show the screenshots gallery section</p>
              </div>
              <div class="ml-4">
                <%= form.check_box :show_product_screenshots, { class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700" }, "true", "false" %>
              </div>
            </div>

            <!-- Product Features -->
            <div class="flex items-center justify-between py-4 border-t border-gray-200 dark:border-gray-700">
              <div class="flex-1">
                <h4 class="text-base font-medium text-gray-900 dark:text-gray-100">Product Features</h4>
                <p class="text-sm text-gray-500 dark:text-gray-400">Show the features list section</p>
              </div>
              <div class="ml-4">
                <%= form.check_box :show_product_features, { class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700" }, "true", "false" %>
              </div>
            </div>

            <!-- Product Customers -->
            <div class="flex items-center justify-between py-4 border-t border-gray-200 dark:border-gray-700">
              <div class="flex-1">
                <h4 class="text-base font-medium text-gray-900 dark:text-gray-100">Product Customers</h4>
                <p class="text-sm text-gray-500 dark:text-gray-400">Show the customer testimonials and case examples</p>
              </div>
              <div class="ml-4">
                <%= form.check_box :show_product_customers, { class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700" }, "true", "false" %>
              </div>
            </div>

            <!-- Product Content -->
            <div class="flex items-center justify-between py-4 border-t border-gray-200 dark:border-gray-700">
              <div class="flex-1">
                <h4 class="text-base font-medium text-gray-900 dark:text-gray-100">Product Content</h4>
                <p class="text-sm text-gray-500 dark:text-gray-400">Show additional resources and documentation</p>
              </div>
              <div class="ml-4">
                <%= form.check_box :show_product_content, { class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700" }, "true", "false" %>
              </div>
            </div>

            <!-- Case Studies -->
            <div class="flex items-center justify-between py-4 border-t border-gray-200 dark:border-gray-700">
              <div class="flex-1">
                <h4 class="text-base font-medium text-gray-900 dark:text-gray-100">Case Studies</h4>
                <p class="text-sm text-gray-500 dark:text-gray-400">Show detailed case studies and success stories</p>
              </div>
              <div class="ml-4">
                <%= form.check_box :show_case_studies, { class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700" }, "true", "false" %>
              </div>
            </div>

          </div>
        </div>
        
        <div class="bg-gray-50 dark:bg-gray-900/50 px-4 py-3 sm:px-6 flex justify-end">
          <%= form.submit "Save Visibility Settings", class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors" %>
        </div>
      </div>
    <% end %>
  </div>
</div>

</div>