<%= render 'layout' do %>
  <!-- Product Overview Heading -->
  <div class="flex items-center justify-between mb-6">
    <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Product Overview</h3>
    <%= link_to 'Edit Product', edit_vendors_product_path(@product), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
  </div>
  
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-2">Status</h3>
          <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <%= @product.published? ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200' %>">
            <%= @product.published? ? 'Published' : 'Draft' %>
          </span>
          
          <% if @product.description.blank? %>
            <div class="mt-6">
              <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-2">Product Details</h3>
              <dl class="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-1">
                <div>
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Company</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product.account&.name || "Admin-managed product" %></dd>
                </div>
              </dl>
            </div>
          <% end %>
        </div>
        
        <% if @product.description.present? %>
          <div>
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-2">Description</h3>
            <div class="text-gray-700 dark:text-gray-300">
              <%= @product.description %>
            </div>
          </div>
        <% end %>
      </div>

      <!-- Product Details and Logo Row -->
      <div class="mt-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Product Details Column -->
          <div>
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-3">Product Details</h3>
            <dl class="space-y-3">
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Company</dt>
                <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @product.account&.name || "Admin-managed product" %></dd>
              </div>
            </dl>
          </div>
          
          <!-- Product Logo Column -->
          <div>
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-3">Product Logo</h3>
            <% if @product.logo.attached? %>
              <div class="w-32 h-32 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 flex items-center justify-center overflow-hidden">
                <%= image_tag @product.logo, 
                    class: "max-w-full max-h-full object-contain",
                    alt: "#{@product.name} logo" %>
              </div>
            <% else %>
              <div class="w-32 h-32 bg-gray-50 dark:bg-gray-700 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 flex items-center justify-center">
                <div class="text-center">
                  <svg class="mx-auto h-8 w-8 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">No logo</p>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Certifications Section -->
      <div class="mt-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-2">Certifications</h3>
        <% if @product.certifications.any? %>
          <div class="flex flex-wrap gap-2">
            <% @product.certifications.each do |certification| %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200" title="<%= certification.description %>">
                <%= certification.name %>
              </span>
            <% end %>
          </div>
        <% else %>
          <p class="text-sm text-gray-500 dark:text-gray-400">No certifications listed</p>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Featured Video Section -->
  <div class="mt-8 bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Featured Video</h3>
        <%= link_to manage_featured_video_vendors_product_path(@product), 
            class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' do %>
          <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
          <%= @product.featured_video.attached? ? 'Manage Video' : 'Upload Video' %>
        <% end %>
      </div>
      
      <% if @product.featured_video.attached? %>
        <div class="relative bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden" style="aspect-ratio: 16 / 9;">
          <video 
            controls 
            controlsList="nodownload"
            class="w-full h-full object-contain"
            preload="metadata">
            <source src="<%= url_for(@product.featured_video) %>" type="<%= @product.featured_video.content_type %>">
            Your browser does not support the video tag.
          </video>
          
          <!-- Video overlay with play button for better UX -->
          <div class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 opacity-0 hover:opacity-100 transition-opacity pointer-events-none">
            <div class="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
              <svg class="w-6 h-6 text-gray-900 ml-1" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8 5v14l11-7z"/>
              </svg>
            </div>
          </div>
        </div>
      <% else %>
        <!-- No featured video placeholder -->
        <div class="relative bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden flex items-center justify-center" style="aspect-ratio: 16 / 9;">
          <div class="text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No featured video uploaded</h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Upload a featured video to showcase your product here.</p>
          </div>
        </div>
      <% end %>
    </div>
  </div>
<% end %>