<% content_for :page_title, "Dashboard Overview" %>

<!-- Stats overview -->
<div class="mb-8">
  <dl class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
    <div class="overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 py-5 shadow sm:p-6 transition-colors">
      <dt class="truncate text-sm font-medium text-gray-500 dark:text-gray-400">Total Products</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900 dark:text-gray-100"><%= @stats[:products_count] %></dd>
    </div>
    <div class="overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 py-5 shadow sm:p-6 transition-colors">
      <dt class="truncate text-sm font-medium text-gray-500 dark:text-gray-400">Published Products</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900 dark:text-gray-100"><%= @stats[:published_products] %></dd>
    </div>
    <div class="overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 py-5 shadow sm:p-6 transition-colors">
      <dt class="truncate text-sm font-medium text-gray-500 dark:text-gray-400">Total Leads</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900 dark:text-gray-100"><%= @stats[:total_leads] %></dd>
    </div>
    <div class="overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 py-5 shadow sm:p-6 transition-colors">
      <dt class="truncate text-sm font-medium text-gray-500 dark:text-gray-400">New Leads</dt>
      <dd class="mt-1 text-3xl font-semibold tracking-tight text-gray-900 dark:text-gray-100"><%= @stats[:new_leads] %></dd>
    </div>
  </dl>
</div>

<!-- Recent Leads -->
<div class="mt-8">
  <div class="sm:flex sm:items-center">
    <div class="sm:flex-auto">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">Recent Leads</h3>
      <p class="mt-1 max-w-3xl text-sm text-gray-500 dark:text-gray-400">Latest inquiries about your products</p>
    </div>
    <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
      <%= link_to "View All Leads", vendors_leads_path, 
          class: "inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors" %>
    </div>
  </div>

  <div class="mt-6">
    <!-- Desktop Table View (hidden on mobile) -->
    <div class="hidden md:block overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
      <% if @recent_leads.any? %>
        <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Contact</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Product</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Company</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Submitted</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <% @recent_leads.each do |lead| %>
              <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td class="px-6 py-4 text-sm">
                  <div class="flex items-center">
                    <div class="h-10 w-10 flex-shrink-0">
                      <div class="h-10 w-10 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                        <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
                          <%= lead.contact_name.split(' ').map(&:first).join('').upcase %>
                        </span>
                      </div>
                    </div>
                    <div class="ml-4">
                      <div class="font-medium text-gray-900 dark:text-gray-100">
                        <%= lead.contact_name %>
                      </div>
                      <div class="text-gray-500 dark:text-gray-400">
                        <%= lead.contact_email %>
                      </div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 text-sm">
                  <div class="font-medium text-gray-900 dark:text-gray-100">
                    <%= lead.product.name %>
                  </div>
                  <% if lead.product.categories.any? %>
                    <div class="flex flex-wrap gap-1 mt-1">
                      <% lead.product.categories.each do |category| %>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                          <%= category.name %>
                        </span>
                      <% end %>
                    </div>
                  <% end %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <%= lead.contact_company %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                    <%= case lead.status
                        when 'pending' then 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
                        when 'contacted' then 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
                        when 'qualified' then 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                        when 'closed' then 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                        else 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                        end %>">
                    <%= lead.status.humanize %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  <%= time_ago_in_words(lead.created_at) %> ago
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <%= link_to "View Details", vendors_lead_path(lead), 
                      class: "text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300" %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      <% else %>
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No leads yet</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Leads will appear here when customers express interest in your products.</p>
        </div>
      <% end %>
    </div>

    <!-- Mobile Card View (visible on mobile only) -->
    <div class="md:hidden">
      <% if @recent_leads.any? %>
        <div class="space-y-4">
          <% @recent_leads.each do |lead| %>
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
              <div class="px-4 py-5 sm:px-6">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center">
                      <div class="h-10 w-10 flex-shrink-0 mr-3">
                        <div class="h-10 w-10 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                          <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
                            <%= lead.contact_name.split(' ').map(&:first).join('').upcase %>
                          </span>
                        </div>
                      </div>
                      <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= lead.contact_name %></h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400"><%= lead.contact_email %></p>
                      </div>
                    </div>
                  </div>
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                    <%= case lead.status
                        when 'pending' then 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
                        when 'contacted' then 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
                        when 'qualified' then 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                        when 'closed' then 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                        else 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                        end %>">
                    <%= lead.status.humanize %>
                  </span>
                </div>
                
                <div class="mt-4 space-y-3">
                  <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
                    <div class="flex justify-between text-sm">
                      <span class="text-gray-500 dark:text-gray-400">Product:</span>
                      <span class="font-medium text-gray-900 dark:text-gray-100"><%= lead.product.name %></span>
                    </div>
                  </div>
                  
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-500 dark:text-gray-400">Company:</span>
                    <span class="text-gray-900 dark:text-gray-100"><%= lead.contact_company %></span>
                  </div>
                  
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-500 dark:text-gray-400">Submitted:</span>
                    <span class="text-gray-900 dark:text-gray-100"><%= time_ago_in_words(lead.created_at) %> ago</span>
                  </div>
                  
                  <% if lead.product.categories.any? %>
                    <div class="text-sm">
                      <span class="text-gray-500 dark:text-gray-400">Categories:</span>
                      <div class="flex flex-wrap gap-1 mt-1">
                        <% lead.product.categories.each do |category| %>
                          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                            <%= category.name %>
                          </span>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
                
                <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                  <%= link_to "View Details", vendors_lead_path(lead), 
                      class: "inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600" %>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% else %>
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No leads yet</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Leads will appear here when customers express interest in your products.</p>
        </div>
      <% end %>
    </div>
  </div>
</div>