<% content_for :page_title, "Lead Details" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Lead Details</h1>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
          Submitted <%= time_ago_in_words(@lead.created_at) %> ago
        </p>
      </div>
      <div>
        <%= link_to "Back to Leads", vendors_leads_path, 
            class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-6">
      <!-- Contact Information -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Contact Information</h3>
        <dl class="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Name</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @lead.contact_name %></dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Email</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
              <%= link_to @lead.contact_email, "mailto:#{@lead.contact_email}", 
                  class: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300" %>
            </dd>
          </div>
          <% if @lead.contact_phone.present? %>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Phone</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                <%= link_to @lead.contact_phone, "tel:#{@lead.contact_phone}", 
                    class: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300" %>
              </dd>
            </div>
          <% end %>
          <% if @lead.contact_company.present? %>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Agency/Department</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @lead.contact_company %></dd>
            </div>
          <% end %>
          <% if @lead.agency_account %>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Account</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @lead.agency_account.name %></dd>
            </div>
          <% end %>
        </dl>
      </div>

      <!-- Request Details -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Request Details</h3>
        <dl class="grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Product</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
              <%= link_to @lead.product.name, vendors_product_path(@lead.product), 
                  class: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300" %>
            </dd>
          </div>
          <div>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Source</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100"><%= @lead.source&.humanize || "N/A" %></dd>
          </div>
          <% if @lead.timeline.present? %>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Timeline</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                  <%= @lead.timeline.humanize.gsub('_', ' ') %>
                </span>
              </dd>
            </div>
          <% end %>
          <% if @lead.budget_range.present? %>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Budget Range</dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                  <%= @lead.budget_range.humanize.gsub('_', ' ') %>
                </span>
              </dd>
            </div>
          <% end %>
        </dl>
      </div>

      <!-- Message -->
      <% if @lead.message.present? %>
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Message</h3>
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <p class="text-gray-700 dark:text-gray-300 whitespace-pre-wrap"><%= @lead.message %></p>
          </div>
        </div>
      <% end %>

      <!-- Internal Notes -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Internal Notes</h3>
        
        <!-- Display mode -->
        <div id="notes-display" class="cursor-pointer" data-action="click->edit-notes">
          <div class="min-h-20 p-3 border border-gray-200 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
            <% if @lead.notes.present? %>
              <p class="text-gray-900 dark:text-gray-100 whitespace-pre-wrap"><%= @lead.notes %></p>
            <% else %>
              <p class="text-gray-500 dark:text-gray-400 italic">Click to add internal notes about this lead...</p>
            <% end %>
          </div>
        </div>
        
        <!-- Edit mode -->
        <div id="notes-edit" class="hidden space-y-4">
          <div>
            <textarea 
              id="notes-textarea"
              rows="4"
              placeholder="Add internal notes about this lead..."
              class="block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"><%= @lead.notes %></textarea>
          </div>
          <div class="flex justify-end">
            <button 
              type="button"
              data-action="click->save-notes"
              class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800">
              Save Notes
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="lg:col-span-1">
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Lead Status</h3>
        
        <!-- Current Status -->
        <div class="mb-4" id="lead-status-badge">
          <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium 
            <%= case @lead.status
                when 'pending' then 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
                when 'contacted' then 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
                when 'qualified' then 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                when 'closed' then 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                else 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                end %>">
            <%= @lead.status.humanize %>
          </span>
        </div>

        <!-- Update Status -->
        <div class="space-y-4">
          <div>
            <%= label_tag :status, "Update Status", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <div class="mt-2 grid grid-cols-1">
              <%= select_tag :status, 
                  options_for_select([
                    ['Pending', 'pending'],
                    ['Contacted', 'contacted'],
                    ['Qualified', 'qualified'],
                    ['Closed', 'closed']
                  ], @lead.status),
                  { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-gray-100 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 dark:focus:outline-indigo-500 sm:text-sm transition-colors", onchange: "updateLeadStatus(this.value, #{@lead.id})" } %>
              <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Lead Timeline -->
        <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Timeline</h4>
          <div class="space-y-2">
            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
              </svg>
              Submitted on <%= @lead.created_at.strftime("%B %-d, %Y") %>
            </div>
            <% if @lead.updated_at != @lead.created_at %>
              <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                </svg>
                Last updated <%= time_ago_in_words(@lead.updated_at) %> ago
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Make functions available globally for onclick handlers
  window.LeadManagement = {
    updateLeadStatus: function(newStatus, leadId) {
      fetch(`/vendors/leads/${leadId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          lead: {
            status: newStatus
          }
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.status === 'success') {
          // Update the status badge
          const badge = document.querySelector('#lead-status-badge span');
          if (badge) {
            badge.textContent = data.new_status_humanized;
            
            // Update the badge color based on status
            badge.className = 'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ' + this.getStatusClasses(data.new_status);
          }
          
          // Show success message (optional)
          console.log('Status updated successfully');
        } else {
          console.error('Error updating status:', data.errors);
          // Optionally show error message to user
        }
      })
      .catch(error => {
        console.error('Error:', error);
      });
    },

    getStatusClasses: function(status) {
      switch(status) {
        case 'pending':
          return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200';
        case 'contacted':
          return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200';
        case 'qualified':
          return 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200';
        case 'closed':
          return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
        default:
          return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
      }
    },

    enterEditMode: function() {
      const notesDisplay = document.getElementById('notes-display');
      const notesEdit = document.getElementById('notes-edit');
      const notesTextarea = document.getElementById('notes-textarea');
      
      if (notesDisplay && notesEdit && notesTextarea) {
        notesDisplay.classList.add('hidden');
        notesEdit.classList.remove('hidden');
        notesTextarea.focus();
      }
    },

    saveNotes: function() {
      const notesTextarea = document.getElementById('notes-textarea');
      if (!notesTextarea) return;
      
      const notes = notesTextarea.value;
      
      fetch(`/vendors/leads/${<%= @lead.id %>}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          lead: {
            notes: notes
          }
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.status === 'success') {
          // Update the display area
          const displayArea = document.querySelector('#notes-display div p');
          if (displayArea) {
            if (notes.trim() === '') {
              displayArea.innerHTML = '<span class="text-gray-500 dark:text-gray-400 italic">Click to add internal notes about this lead...</span>';
              displayArea.className = 'text-gray-500 dark:text-gray-400 italic';
            } else {
              displayArea.textContent = notes;
              displayArea.className = 'text-gray-900 dark:text-gray-100 whitespace-pre-wrap';
            }
          }
          
          // Exit edit mode
          const notesEdit = document.getElementById('notes-edit');
          const notesDisplay = document.getElementById('notes-display');
          if (notesEdit && notesDisplay) {
            notesEdit.classList.add('hidden');
            notesDisplay.classList.remove('hidden');
          }
          
          console.log('Notes saved successfully');
        } else {
          console.error('Error saving notes:', data.errors);
          alert('Error saving notes. Please try again.');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Error saving notes. Please try again.');
      });
    }
  };
  
  // Make functions available globally for backward compatibility
  window.updateLeadStatus = window.LeadManagement.updateLeadStatus;
  window.getStatusClasses = window.LeadManagement.getStatusClasses;
  window.enterEditMode = window.LeadManagement.enterEditMode;
  window.saveNotes = window.LeadManagement.saveNotes;
  
  // Initialize event listeners for Turbo compatibility
  function initializeLeadManagement() {
    // Check if required elements exist
    const notesDisplay = document.getElementById('notes-display');
    const saveButton = document.querySelector('[data-action="click->save-notes"]');
    
    if (!notesDisplay) {
      return false; // Elements not ready yet
    }
    
    // Remove existing listeners to prevent duplicates
    if (window.leadManagementListeners) {
      window.leadManagementListeners.forEach(({ element, event, handler }) => {
        if (element) element.removeEventListener(event, handler);
      });
    }
    
    window.leadManagementListeners = [];
    
    // Helper function to add listener and track it
    function addTrackedListener(element, event, handler) {
      if (element) {
        element.addEventListener(event, handler);
        window.leadManagementListeners.push({ element, event, handler });
      }
    }
    
    // Notes edit mode click handler
    const editHandler = function() {
      window.LeadManagement.enterEditMode();
    };
    addTrackedListener(notesDisplay, 'click', editHandler);
    
    // Save notes click handler
    const saveHandler = function() {
      window.LeadManagement.saveNotes();
    };
    addTrackedListener(saveButton, 'click', saveHandler);
    
    return true;
  }
  
  // Initialize on both DOMContentLoaded and turbo:load for Turbo compatibility
  document.addEventListener('DOMContentLoaded', initializeLeadManagement);
  document.addEventListener('turbo:load', initializeLeadManagement);
  
  // Fallback initialization with timeout for edge cases
  document.addEventListener('turbo:load', function() {
    setTimeout(function() {
      if (!initializeLeadManagement()) {
        // Try again after another delay if still not ready
        setTimeout(initializeLeadManagement, 100);
      }
    }, 50);
  });
</script>