<% content_for :page_title, "Leads" %>

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
  <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition-colors">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-gray-500 rounded-md flex items-center justify-center">
            <span class="text-white font-bold text-sm">T</span>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Total</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= @lead_stats[:total] %></dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition-colors">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
            <span class="text-white font-bold text-sm">P</span>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Pending</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= @lead_stats[:pending] %></dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition-colors">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
            <span class="text-white font-bold text-sm">C</span>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Contacted</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= @lead_stats[:contacted] %></dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition-colors">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
            <span class="text-white font-bold text-sm">Q</span>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Qualified</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= @lead_stats[:qualified] %></dd>
          </dl>
        </div>
      </div>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg transition-colors">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
            <span class="text-white font-bold text-sm">C</span>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">Closed</dt>
            <dd class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= @lead_stats[:closed] %></dd>
          </dl>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Filters -->
<div class="bg-white dark:bg-gray-800 shadow sm:rounded-md mb-6 transition-colors">
  <div class="px-6 py-4">
    <%= form_with url: vendors_leads_path, method: :get, local: true, class: "flex flex-wrap gap-4 items-end" do |form| %>
      <div class="flex-1 min-w-64">
        <%= form.label :search, "Search", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
        <%= form.text_field :search, 
            placeholder: "Search by name, email, or message...", 
            value: params[:search],
            class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors" %>
      </div>
      
      <div>
        <%= form.label :status, "Status", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
        <div class="mt-2 grid grid-cols-1">
          <%= form.select :status, 
              options_for_select([
                ['All Statuses', ''],
                ['Pending', 'pending'],
                ['Contacted', 'contacted'],
                ['Qualified', 'qualified'],
                ['Closed', 'closed']
              ], params[:status]),
              {},
              { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-gray-100 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 dark:focus:outline-indigo-500 sm:text-sm transition-colors" } %>
          <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
            <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>
      
      <div>
        <%= form.label :product_id, "Product", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
        <div class="mt-2 grid grid-cols-1">
          <%= form.select :product_id, 
              options_from_collection_for_select(@products, :id, :name, params[:product_id]),
              { prompt: "All Products" },
              { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-gray-100 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 dark:focus:outline-indigo-500 sm:text-sm transition-colors" } %>
          <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
            <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>
      
      <div>
        <%= form.submit "Filter", 
            class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors" %>
      </div>
      
      <% if params.values.any?(&:present?) %>
        <div>
          <%= link_to "Clear", vendors_leads_path, 
              class: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors" %>
        </div>
      <% end %>
    <% end %>
  </div>
</div>

<!-- Desktop Table View (hidden on mobile) -->
<div class="hidden md:block mt-8">
  <div class="overflow-hidden shadow ring-1 ring-black/5 dark:ring-gray-700 md:rounded-lg">
    <% if @leads.any? %>
      <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Contact</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Product</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Details</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Agency</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Submitted</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          <% @leads.each do |lead| %>
            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
              <td class="px-6 py-4 whitespace-nowrap text-sm">
                <div class="flex items-center">
                  <div class="h-10 w-10 flex-shrink-0">
                    <div class="h-10 w-10 rounded-full bg-gray-100 dark:bg-gray-600 flex items-center justify-center">
                      <span class="text-sm font-medium text-gray-600 dark:text-gray-300">
                        <%= lead.contact_name.split(' ').map(&:first).join('').upcase %>
                      </span>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="font-medium text-gray-900 dark:text-gray-100"><%= lead.contact_name %></div>
                    <div class="text-gray-500 dark:text-gray-400"><%= lead.contact_email %></div>
                    <% if lead.contact_phone.present? %>
                      <div class="text-gray-500 dark:text-gray-400"><%= lead.contact_phone %></div>
                    <% end %>
                    <% if lead.contact_company.present? %>
                      <div class="text-gray-500 dark:text-gray-400"><%= lead.contact_company %></div>
                    <% end %>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 text-sm">
                <div class="font-medium text-gray-900 dark:text-gray-100"><%= lead.product.name %></div>
                <% if lead.message.present? %>
                  <div class="text-gray-500 dark:text-gray-400 mt-1">
                    <%= truncate(lead.message, length: 60) %>
                  </div>
                <% end %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                  <%= case lead.status
                      when 'pending' then 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
                      when 'contacted' then 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
                      when 'qualified' then 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                      when 'closed' then 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200'
                      else 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                      end %>">
                  <%= lead.status.humanize %>
                </span>
              </td>
              <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                <% if lead.timeline.present? %>
                  <div class="flex items-center mb-1">
                    <svg class="w-4 h-4 mr-1.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <%= lead.timeline.humanize.gsub('_', ' ') %>
                  </div>
                <% end %>
                <% if lead.budget_range.present? %>
                  <div class="flex items-center">
                    <svg class="w-4 h-4 mr-1.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                    </svg>
                    <%= lead.budget_range.humanize.gsub('_', ' ') %>
                  </div>
                <% end %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                <% if lead.agency_account %>
                  <%= lead.agency_account.name %>
                <% else %>
                  <span class="text-gray-400">N/A</span>
                <% end %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                <time datetime="<%= lead.created_at.iso8601 %>">
                  <%= lead.created_at.strftime("%B %-d, %Y") %>
                </time>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <%= link_to "View Details", vendors_lead_path(lead), 
                    class: "text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300" %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    <% else %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No leads found</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          <% if params.values.any?(&:present?) %>
            Try adjusting your search criteria or filters.
          <% else %>
            Agency users will be able to express interest in your products, and leads will appear here.
          <% end %>
        </p>
        <% if params.values.any?(&:present?) %>
          <div class="mt-6">
            <%= link_to "Clear filters", vendors_leads_path, 
                class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors" %>
          </div>
        <% end %>
      </div>
    <% end %>
  </div>
</div>

<!-- Mobile Card View (visible on mobile only) -->
<div class="md:hidden mt-8">
  <% if @leads.any? %>
    <div class="space-y-4">
      <% @leads.each do |lead| %>
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
          <div class="px-4 py-5 sm:px-6">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center">
                  <div class="h-10 w-10 flex-shrink-0 mr-3">
                    <div class="h-10 w-10 rounded-full bg-gray-100 dark:bg-gray-600 flex items-center justify-center">
                      <span class="text-sm font-medium text-gray-600 dark:text-gray-300">
                        <%= lead.contact_name.split(' ').map(&:first).join('').upcase %>
                      </span>
                    </div>
                  </div>
                  <div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100"><%= lead.contact_name %></h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400"><%= lead.contact_email %></p>
                  </div>
                </div>
              </div>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                <%= case lead.status
                    when 'pending' then 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
                    when 'contacted' then 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
                    when 'qualified' then 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                    when 'closed' then 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200'
                    else 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                    end %>">
                <%= lead.status.humanize %>
              </span>
            </div>
            
            <div class="mt-4 space-y-3">
              <div class="border-t border-gray-200 dark:border-gray-700 pt-3">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500 dark:text-gray-400">Product:</span>
                  <span class="font-medium text-gray-900 dark:text-gray-100"><%= lead.product.name %></span>
                </div>
              </div>
              
              <% if lead.contact_phone.present? %>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500 dark:text-gray-400">Phone:</span>
                  <span class="text-gray-900 dark:text-gray-100"><%= lead.contact_phone %></span>
                </div>
              <% end %>
              
              <% if lead.contact_company.present? %>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500 dark:text-gray-400">Company:</span>
                  <span class="text-gray-900 dark:text-gray-100"><%= lead.contact_company %></span>
                </div>
              <% end %>
              
              <% if lead.timeline.present? %>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500 dark:text-gray-400">Timeline:</span>
                  <span class="text-gray-900 dark:text-gray-100"><%= lead.timeline.humanize.gsub('_', ' ') %></span>
                </div>
              <% end %>
              
              <% if lead.budget_range.present? %>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-500 dark:text-gray-400">Budget:</span>
                  <span class="text-gray-900 dark:text-gray-100"><%= lead.budget_range.humanize.gsub('_', ' ') %></span>
                </div>
              <% end %>
              
              <div class="flex justify-between text-sm">
                <span class="text-gray-500 dark:text-gray-400">Agency:</span>
                <span class="text-gray-900 dark:text-gray-100">
                  <% if lead.agency_account %>
                    <%= lead.agency_account.name %>
                  <% else %>
                    <span class="text-gray-400">N/A</span>
                  <% end %>
                </span>
              </div>
              
              <div class="flex justify-between text-sm">
                <span class="text-gray-500 dark:text-gray-400">Submitted:</span>
                <span class="text-gray-900 dark:text-gray-100">
                  <time datetime="<%= lead.created_at.iso8601 %>">
                    <%= lead.created_at.strftime("%B %-d, %Y") %>
                  </time>
                </span>
              </div>
              
              <% if lead.message.present? %>
                <div class="text-sm">
                  <span class="text-gray-500 dark:text-gray-400">Message:</span>
                  <p class="mt-1 text-gray-900 dark:text-gray-100"><%= truncate(lead.message, length: 150) %></p>
                </div>
              <% end %>
            </div>
            
            <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
              <%= link_to "View Details", vendors_lead_path(lead), 
                  class: "inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600" %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <div class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No leads found</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        <% if params.values.any?(&:present?) %>
          Try adjusting your search criteria or filters.
        <% else %>
          Agency users will be able to express interest in your products, and leads will appear here.
        <% end %>
      </p>
      <% if params.values.any?(&:present?) %>
        <div class="mt-6">
          <%= link_to "Clear filters", vendors_leads_path, 
              class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors" %>
        </div>
      <% end %>
    </div>
  <% end %>
</div>