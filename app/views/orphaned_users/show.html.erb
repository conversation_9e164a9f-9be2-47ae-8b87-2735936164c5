<% content_for :page_title, "Account Setup Required" %>

<!DOCTYPE html>
<html class="h-full bg-white dark:bg-gray-900">
  <head>
    <title><%= page_title(content_for(:page_title)) %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
    <link rel="icon" href="/icon.png" type="image/png">
  </head>

  <body class="h-full bg-white dark:bg-gray-900" data-controller="dark-mode">
    <div class="min-h-full flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <%= link_to root_path, class: "flex justify-center" do %>
          <span class="text-3xl font-bold text-blue-600">Platia</span>
        <% end %>
      </div>

      <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-2xl">
        <div class="bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <!-- Header -->
          <div class="text-center mb-8">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 dark:bg-yellow-900/50 mb-4">
              <svg class="h-8 w-8 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-gray-900 dark:text-gray-100">Welcome, <%= current_user.first_name %>!</h2>
            <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
              You're not currently part of any organization account.
            </p>
          </div>

          <!-- Current Status -->
          <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-md p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Account Setup Required</h3>
                <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                  <p>Your user account (<%= current_user.email_address %>) exists, but you're not associated with any organization. To access the platform, you'll need to either create a new organization or be added to an existing one.</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Options -->
          <div class="space-y-6">
            <!-- Option 1: Create New Organization -->
            <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-6">
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <div class="flex items-center justify-center h-10 w-10 rounded-lg bg-blue-100 dark:bg-blue-900">
                    <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-6 4h6"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-4 flex-1">
                  <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Create a New Organization</h3>
                  <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Set up a new organization account for your company or agency. You'll be the account owner and can invite team members.
                  </p>
                  <div class="mt-4">
                    <%= link_to new_registration_path, 
                        class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 transition-colors" do %>
                      <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                      </svg>
                      Create Organization
                    <% end %>
                  </div>
                </div>
              </div>
            </div>

            <!-- Option 2: Join Existing Organization -->
            <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-6">
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <div class="flex items-center justify-center h-10 w-10 rounded-lg bg-green-100 dark:bg-green-900">
                    <svg class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-4 flex-1">
                  <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Join an Existing Organization</h3>
                  <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Contact your organization's account owner or administrator to send you a team invitation.
                  </p>
                  <div class="mt-3 space-y-2">
                    <div class="text-sm text-gray-600 dark:text-gray-300">
                      <strong>How it works:</strong>
                    </div>
                    <ul class="text-sm text-gray-500 dark:text-gray-400 space-y-1 ml-4">
                      <li class="flex items-start">
                        <span class="text-green-500 mr-2">•</span>
                        Ask your account owner to send you an invitation to <strong><%= current_user.email_address %></strong>
                      </li>
                      <li class="flex items-start">
                        <span class="text-green-500 mr-2">•</span>
                        You'll receive an email with an invitation link
                      </li>
                      <li class="flex items-start">
                        <span class="text-green-500 mr-2">•</span>
                        Click the link to automatically join their organization
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <!-- Option 3: Contact Support -->
            <div class="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-6">
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <div class="flex items-center justify-center h-10 w-10 rounded-lg bg-purple-100 dark:bg-purple-900">
                    <svg class="h-6 w-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                </div>
                <div class="ml-4 flex-1">
                  <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Need Help?</h3>
                  <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    If you're unsure about which option to choose or need assistance setting up your account, our support team can help.
                  </p>
                  <div class="mt-4">
                    <a href="mailto:<EMAIL>" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-600 hover:bg-gray-50 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 dark:focus:ring-offset-gray-800 transition-colors">
                      <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                      </svg>
                      Contact Support
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Sign Out Option -->
          <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
            <div class="text-center">
              <p class="text-sm text-gray-500 dark:text-gray-400 mb-3">
                Not the right account?
              </p>
              <%= link_to "Sign Out", session_path, 
                  data: { "turbo-method": :delete },
                  class: "text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 underline" %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>