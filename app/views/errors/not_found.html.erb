<% content_for :page_title, "Page Not Found" %>

<div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8 text-center">
    <!-- Error Icon -->
    <div>
      <div class="mx-auto h-24 w-24 text-red-500 dark:text-red-400">
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="w-full h-full">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.584-4.29-3.544C7.71 9.456 9.66 8 12 8s4.29 1.456 4.29 3.456S14.34 15 12 15z"></path>
        </svg>
      </div>
    </div>

    <!-- Error Content -->
    <div>
      <h2 class="text-6xl font-bold text-gray-900 dark:text-gray-100 mb-4"><%= @error_code %></h2>
      <h3 class="text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4"><%= @error_title %></h3>
      <p class="text-gray-600 dark:text-gray-400 mb-8"><%= @error_message %></p>
    </div>

    <!-- Action Buttons -->
    <div class="space-y-4">
      <%= link_to "Go to Marketplace", marketplace_path, 
          class: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-900 transition-colors" %>
      
      <%= link_to "Back to Home", root_path, 
          class: "w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-900 transition-colors" %>
      
      <button id="go-back-btn" 
              class="w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-900 transition-colors">
        Go Back
      </button>
    </div>

    <!-- Help Section -->
    <div class="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
      <h4 class="text-sm font-medium text-blue-900 dark:text-blue-200 mb-2">Need Help?</h4>
      <p class="text-sm text-blue-700 dark:text-blue-300">
        If you believe this is an error, please contact our support team at 
        <a href="mailto:<EMAIL>" class="underline font-medium hover:text-blue-600 dark:hover:text-blue-200 transition-colors"><EMAIL></a>
      </p>
    </div>
  </div>
</div>

<script>
  function initializeErrorPageHandlers() {
    const goBackBtn = document.getElementById('go-back-btn');
    if (goBackBtn) {
      goBackBtn.addEventListener('click', function() {
        history.back();
      });
      return true;
    }
    return false;
  }
  
  // Initialize on both DOMContentLoaded and turbo:load for Turbo compatibility
  document.addEventListener('DOMContentLoaded', initializeErrorPageHandlers);
  document.addEventListener('turbo:load', initializeErrorPageHandlers);
  
  // Fallback initialization
  document.addEventListener('turbo:load', function() {
    setTimeout(function() {
      if (!initializeErrorPageHandlers()) {
        setTimeout(initializeErrorPageHandlers, 100);
      }
    }, 50);
  });
</script>