<% content_for :page_title, "Server Error" %>

<div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8 text-center">
    <!-- Error Icon -->
    <div>
      <div class="mx-auto h-24 w-24 text-red-500 dark:text-red-400">
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="w-full h-full">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>
    </div>

    <!-- Error Content -->
    <div>
      <h2 class="text-6xl font-bold text-gray-900 dark:text-gray-100 mb-4"><%= @error_code %></h2>
      <h3 class="text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4"><%= @error_title %></h3>
      <p class="text-gray-600 dark:text-gray-400 mb-8"><%= @error_message %></p>
    </div>

    <!-- Status Update -->
    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-8">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h4 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">We're Working on It</h4>
          <p class="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
            Our technical team has been automatically notified and is investigating the issue. 
            Please try again in a few minutes.
          </p>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="space-y-4">
      <button id="try-again-btn" 
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-900 transition-colors">
        Try Again
      </button>
      
      <%= link_to "Go to Marketplace", marketplace_path, 
          class: "w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-900 transition-colors" %>
      
      <%= link_to "Back to Home", root_path, 
          class: "w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-900 transition-colors" %>
    </div>

    <!-- Contact Section -->
    <div class="mt-8 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
      <h4 class="text-sm font-medium text-red-900 dark:text-red-200 mb-2">Still Having Issues?</h4>
      <p class="text-sm text-red-700 dark:text-red-300 mb-3">
        If the problem persists, please contact our technical support team:
      </p>
      <div class="space-y-1 text-sm text-red-700 dark:text-red-300">
        <p>Email: <a href="mailto:<EMAIL>" class="underline font-medium hover:text-red-600 dark:hover:text-red-200 transition-colors"><EMAIL></a></p>
        <p>Include error code <strong><%= @error_code %></strong> in your message</p>
      </div>
    </div>
  </div>
</div>

<script>
  function initializeErrorPageHandlers() {
    const tryAgainBtn = document.getElementById('try-again-btn');
    if (tryAgainBtn) {
      tryAgainBtn.addEventListener('click', function() {
        window.location.reload();
      });
      return true;
    }
    return false;
  }
  
  // Initialize on both DOMContentLoaded and turbo:load for Turbo compatibility
  document.addEventListener('DOMContentLoaded', initializeErrorPageHandlers);
  document.addEventListener('turbo:load', initializeErrorPageHandlers);
  
  // Fallback initialization
  document.addEventListener('turbo:load', function() {
    setTimeout(function() {
      if (!initializeErrorPageHandlers()) {
        setTimeout(initializeErrorPageHandlers, 100);
      }
    }, 50);
  });
</script>