<nav class="bg-indigo-600 dark:bg-indigo-700 border-b border-indigo-700 dark:border-indigo-800">
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="flex h-16 items-center justify-between">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <%= link_to vendors_root_path, class: "text-white font-bold text-xl" do %>
            GovTech Marketplace
          <% end %>
        </div>
        <div class="hidden md:block">
          <div class="ml-10 flex items-baseline space-x-4">
            <%= link_to "Dashboard", vendors_root_path, 
                class: "text-indigo-300 dark:text-indigo-200 hover:text-white dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium" %>
            <%= link_to "Company Profile", vendors_company_profile_path, 
                class: "text-indigo-300 dark:text-indigo-200 hover:text-white dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium" %>
            <%= link_to "Products", vendors_products_path, 
                class: "text-indigo-300 dark:text-indigo-200 hover:text-white dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium" %>
            <%= link_to "Leads", vendors_leads_path, 
                class: "text-indigo-300 dark:text-indigo-200 hover:text-white dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium" %>
            <% if current_user_account_admin? %>
              <%= link_to "Team", account_team_members_path, 
                  class: "text-indigo-300 dark:text-indigo-200 hover:text-white dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium" %>
            <% end %>
          </div>
        </div>
      </div>
      <div class="hidden md:block">
        <div class="ml-4 flex items-center md:ml-6">
          <!-- Dark Mode Toggle -->
          <div data-controller="dark-mode" class="mr-4">
            <button data-action="click->dark-mode#toggle" 
                    class="p-2 rounded-md text-indigo-300 dark:text-indigo-200 hover:text-white dark:hover:text-white hover:bg-indigo-700 dark:hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 transition-colors"
                    title="Toggle dark mode">
              <!-- Sun Icon (visible in dark mode) -->
              <svg data-dark-mode-target="sunIcon" class="h-5 w-5 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
              <!-- Moon Icon (visible in light mode) -->
              <svg data-dark-mode-target="moonIcon" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
              </svg>
            </button>
          </div>
          
          <div class="relative ml-3">
            <div class="flex items-center space-x-4">
              <span class="text-indigo-300 dark:text-indigo-200 text-sm">
                <%= current_user.full_name %> • <%= current_account.name %>
              </span>
              <%= link_to "Account", account_path, 
                  class: "text-indigo-300 dark:text-indigo-200 hover:text-white dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium" %>
              <%= link_to "Sign out", session_path, method: :delete, 
                  class: "text-indigo-300 dark:text-indigo-200 hover:text-white dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium" %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</nav>