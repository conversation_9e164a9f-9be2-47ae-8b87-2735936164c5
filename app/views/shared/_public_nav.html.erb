<nav class="bg-white dark:bg-gray-900 shadow-sm border-b border-gray-200 dark:border-gray-700 transition-colors">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      <!-- Logo/Brand -->
      <div class="flex-shrink-0 flex items-center">
        <%= link_to root_path, class: "flex items-center" do %>
          <span class="text-2xl font-bold text-blue-600 dark:text-blue-400">Platia</span>
        <% end %>
      </div>
      
      <!-- Search Bar -->
      <div class="flex-1 max-w-lg mx-6">
        <div data-controller="search" data-search-url-value="<%= api_search_path %>" class="relative">
          <%= form_with url: marketplace_search_path, method: :get, local: true, class: "relative" do |form| %>
            <%= form.text_field :q, 
                placeholder: "Search categories and products...", 
                class: "w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm pl-3 pr-10 py-2 text-sm outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors",
                data: { 
                  search_target: "input",
                  action: "input->search#search focus->search#focus blur->search#blur keydown->search#keydown"
                } %>
            <button type="submit" class="absolute inset-y-0 right-0 flex items-center pr-3">
              <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>
          <% end %>
          
          <!-- Search Dropdown -->
          <div data-search-target="dropdown" class="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md shadow-lg z-50 max-h-96 overflow-y-auto scrollbar-thin hidden">
            <div data-search-target="results"></div>
          </div>
        </div>
      </div>
      
      <!-- Navigation Links -->
      <div class="hidden md:block">
        <div class="ml-10 flex items-baseline space-x-4">
          <%= link_to "Marketplace", marketplace_path, 
              class: "#{request.path == marketplace_path ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'} px-3 py-2 rounded-md text-sm font-medium transition-colors",
              data: { turbo: false } %>
          <%= link_to "Categories", marketplace_categories_path, 
              class: "#{request.path.start_with?('/marketplace/categories') ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'} px-3 py-2 rounded-md text-sm font-medium transition-colors" %>
          <%= link_to "Products", marketplace_products_path, 
              class: "#{request.path.start_with?(marketplace_products_path) ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'} px-3 py-2 rounded-md text-sm font-medium transition-colors" %>
        </div>
      </div>
      
      <!-- Auth Links & Dark Mode Toggle -->
      <div class="flex items-center space-x-4">
        <% if Current.user %>
          <% if Current.user.agency? %>
            <%= link_to "Dashboard", agencies_root_path, 
                class: "#{request.path.start_with?('/agencies') ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'} px-3 py-2 rounded-md text-sm font-medium transition-colors",
                data: { turbo: false } %>
          <% elsif Current.user.vendor? %>
            <%= link_to "Dashboard", vendors_root_path, 
                class: "#{request.path.start_with?('/vendors') ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'} px-3 py-2 rounded-md text-sm font-medium transition-colors",
                data: { turbo: false } %>
          <% elsif Current.user.admin? %>
            <%= link_to "Dashboard", admin_root_path, 
                class: "#{request.path.start_with?('/admin') ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'} px-3 py-2 rounded-md text-sm font-medium transition-colors",
                data: { turbo: false } %>
          <% end %>
        <% else %>
          <div class="hidden md:flex items-center space-x-4">
            <%= link_to "Sign In", new_session_path, 
                class: "text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-sm font-medium transition-colors" %>
            <%= link_to "Sign Up", new_registration_path, 
                class: "bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors" %>
          </div>
        <% end %>
        
        <!-- Dark Mode Toggle -->
        <div data-controller="dark-mode">
          <button data-action="click->dark-mode#toggle" 
                  class="p-2 rounded-md text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                  title="Toggle dark mode">
            <!-- Sun Icon (visible in dark mode) -->
            <svg data-dark-mode-target="sunIcon" class="h-5 w-5 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
            <!-- Moon Icon (visible in light mode) -->
            <svg data-dark-mode-target="moonIcon" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
            </svg>
          </button>
        </div>
      </div>
      
      <!-- Mobile menu button -->
      <div class="md:hidden">
        <button type="button" class="bg-white dark:bg-gray-900 inline-flex items-center justify-center p-2 rounded-md text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 transition-colors" aria-controls="mobile-menu" aria-expanded="false">
          <span class="sr-only">Open main menu</span>
          <!-- Menu icon -->
          <svg class="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </div>
  </div>
  
  <!-- Mobile menu -->
  <div class="md:hidden" id="mobile-menu" style="display: none;">
    <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
      <!-- Mobile Search -->
      <div class="px-3 py-2">
        <div data-controller="search" data-search-url-value="<%= api_search_path %>" class="relative">
          <%= form_with url: marketplace_search_path, method: :get, local: true, class: "relative" do |form| %>
            <%= form.text_field :q, 
                placeholder: "Search categories and products...", 
                class: "w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm pl-3 pr-10 py-2 text-sm outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors",
                data: { 
                  search_target: "input",
                  action: "input->search#search focus->search#focus blur->search#blur keydown->search#keydown"
                } %>
            <button type="submit" class="absolute inset-y-0 right-0 flex items-center pr-3">
              <svg class="h-5 w-5 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>
          <% end %>
          
          <!-- Mobile Search Dropdown -->
          <div data-search-target="dropdown" class="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-md shadow-lg z-50 max-h-96 overflow-y-auto scrollbar-thin hidden">
            <div data-search-target="results"></div>
          </div>
        </div>
      </div>
      
      <%= link_to "Home", root_path, 
          class: "#{request.path == '/' ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' : 'text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400'} block px-3 py-2 rounded-md text-base font-medium transition-colors" %>
      <%= link_to "Marketplace", marketplace_path, 
          class: "#{request.path == marketplace_path ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' : 'text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400'} block px-3 py-2 rounded-md text-base font-medium transition-colors",
          data: { turbo: false } %>
      <%= link_to "Categories", marketplace_categories_path, 
            class: "#{request.path.start_with?('/marketplace/categories') ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' : 'text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400'} block px-3 py-2 rounded-md text-base font-medium transition-colors ml-4" %>
      <%= link_to "Products", marketplace_products_path, 
          class: "#{request.path.start_with?(marketplace_products_path) ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' : 'text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400'} block px-3 py-2 rounded-md text-base font-medium transition-colors ml-4" %>
      
      <% if Current.user %>
        <div class="border-t border-gray-200 dark:border-gray-700 pt-4 pb-3">
          <% if Current.user.agency? %>
            <%= link_to "Dashboard", agencies_root_path, 
                class: "#{request.path.start_with?('/agencies') ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' : 'text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400'} block px-3 py-2 rounded-md text-base font-medium transition-colors",
                data: { turbo: false } %>
          <% elsif Current.user.vendor? %>
            <%= link_to "Dashboard", vendors_root_path, 
                class: "#{request.path.start_with?('/vendors') ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' : 'text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400'} block px-3 py-2 rounded-md text-base font-medium transition-colors",
                data: { turbo: false } %>
          <% elsif Current.user.admin? %>
            <%= link_to "Dashboard", admin_root_path, 
                class: "#{request.path.start_with?('/admin') ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' : 'text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400'} block px-3 py-2 rounded-md text-base font-medium transition-colors",
                data: { turbo: false } %>
          <% end %>
          <%= link_to "Account", account_path, 
              class: "#{request.path.start_with?('/account') ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' : 'text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400'} block px-3 py-2 rounded-md text-base font-medium transition-colors" %>
        </div>
      <% else %>
        <div class="border-t border-gray-200 dark:border-gray-700 pt-4 pb-3 space-y-1">
          <%= link_to "Sign In", new_session_path, 
              class: "text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors" %>
          <%= link_to "Sign Up", new_registration_path, 
              class: "bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white block px-3 py-2 rounded-md text-base font-medium transition-colors" %>
        </div>
      <% end %>
    </div>
  </div>
</nav>

<script>
  // Simple mobile menu toggle
  document.addEventListener('DOMContentLoaded', function() {
    const menuButton = document.querySelector('[aria-controls="mobile-menu"]');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (menuButton && mobileMenu) {
      menuButton.addEventListener('click', function() {
        const isExpanded = menuButton.getAttribute('aria-expanded') === 'true';
        menuButton.setAttribute('aria-expanded', !isExpanded);
        mobileMenu.style.display = isExpanded ? 'none' : 'block';
      });
    }
  });
</script>