<nav class="bg-red-600 dark:bg-red-700 border-b border-red-700 dark:border-red-800">
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="flex h-16 items-center justify-between">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <%= link_to admin_root_path, class: "text-white font-bold text-xl" do %>
            🛡️ Admin Dashboard
          <% end %>
        </div>
        <div class="hidden md:block">
          <div class="ml-10 flex items-baseline space-x-4">
            <%= link_to "Overview", admin_root_path, 
                class: "text-red-300 dark:text-red-200 hover:text-white dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium" %>
            <%= link_to "Accounts", admin_accounts_path, 
                class: "text-red-300 dark:text-red-200 hover:text-white dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium" %>
            <%= link_to "Lists", admin_lists_path, 
                class: "text-red-300 dark:text-red-200 hover:text-white dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium" %>
            <%= link_to "Email Previews", admin_email_previews_path, 
                class: "text-red-300 dark:text-red-200 hover:text-white dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium" %>
            <% if current_user&.super_admin? %>
              <%= link_to "Job Monitor", "/admin/jobs", 
                  class: "text-red-300 dark:text-red-200 hover:text-white dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium", 
                  target: "_blank" %>
            <% end %>
          </div>
        </div>
      </div>
      <div class="hidden md:block">
        <div class="ml-4 flex items-center md:ml-6">
          <!-- Dark Mode Toggle -->
          <div data-controller="dark-mode" class="mr-4">
            <button data-action="click->dark-mode#toggle" 
                    class="p-2 rounded-md text-red-300 dark:text-red-200 hover:text-white dark:hover:text-white hover:bg-red-700 dark:hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors"
                    title="Toggle dark mode">
              <!-- Sun Icon (visible in dark mode) -->
              <svg data-dark-mode-target="sunIcon" class="h-5 w-5 hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
              <!-- Moon Icon (visible in light mode) -->
              <svg data-dark-mode-target="moonIcon" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
              </svg>
            </button>
          </div>
          
          <div class="relative ml-3">
            <div class="flex items-center space-x-4">
              <span class="text-red-300 dark:text-red-200 text-sm">
                <%= current_user&.email_address %>
                <% if current_user&.super_admin? %>
                  <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-200 text-red-800 dark:bg-red-800 dark:text-red-100 ml-2">
                    Super Admin
                  </span>
                <% else %>
                  <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-200 text-red-800 dark:bg-red-800 dark:text-red-100 ml-2">
                    Admin
                  </span>
                <% end %>
              </span>
              <%= link_to "Account", account_path, 
                  class: "text-red-300 dark:text-red-200 hover:text-white dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium" %>
              <%= link_to "Sign out", session_path, method: :delete, 
                  class: "text-red-300 dark:text-red-200 hover:text-white dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium" %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</nav>