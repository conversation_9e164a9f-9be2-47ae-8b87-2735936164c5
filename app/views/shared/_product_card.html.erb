<div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200 overflow-hidden h-full">
  <%= link_to marketplace_product_path(product), class: "block h-full" do %>
    <div class="p-6 h-full flex flex-col">
      <div class="mb-4">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-gray-100 line-clamp-2">
          <%= product.name %>
        </h3>
      </div>
      
      <% if product.description.present? %>
        <p class="text-gray-600 dark:text-gray-400 mb-4 line-clamp-3 transition-colors">
          <%= strip_tags(product.description) %>
        </p>
      <% end %>
      
      <!-- Categories -->
      <% if product.categories.any? %>
        <div class="flex flex-wrap gap-2 mb-4">
          <% product.categories.order(:name).limit(3).each do |category| %>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 transition-colors">
              <%= category.name %>
            </span>
          <% end %>
          <% if product.categories.count > 3 %>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 transition-colors">
              +<%= product.categories.count - 3 %> more
            </span>
          <% end %>
        </div>
      <% end %>
      
      <!-- Spacer to push content stats to bottom -->
      <div class="flex-grow"></div>
      
      <!-- Content Stats Grid -->
      <div class="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100 dark:border-gray-700 transition-colors">
        <!-- Videos -->
        <div class="flex items-center space-x-2">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-gray-400">
            <path stroke-linecap="round" stroke-linejoin="round" d="m15.75 10.5 4.72-4.72a.75.75 0 0 1 1.28.53v11.38a.75.75 0 0 1-1.28.53l-4.72-4.72M4.5 18.75h9a2.25 2.25 0 0 0 2.25-2.25v-9a2.25 2.25 0 0 0-2.25-2.25h-9A2.25 2.25 0 0 0 2.25 7.5v9a2.25 2.25 0 0 0 2.25 2.25Z" />
          </svg>
          <span class="text-xs text-gray-500 dark:text-gray-400 transition-colors">
            <%= pluralize(product.total_video_count, 'video') %>
          </span>
        </div>
        
        <!-- Case Studies -->
        <div class="flex items-center space-x-2 justify-end">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-gray-400">
            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
          </svg>
          <span class="text-xs text-gray-500 dark:text-gray-400 transition-colors">
            <%= pluralize(product.case_studies.published.count, 'case study') %>
          </span>
        </div>
      </div>
    </div>
  <% end %>
</div>