<% content_for :page_title, "Priority Vendor Onboarding - Platia" %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8 transition-colors">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <div class="text-center">
      <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100">Priority Access</h1>
      <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
        Create your account with immediate approval
      </p>
    </div>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10 transition-colors">
      <%= form_with model: @user, url: direct_signup_path, local: true do |form| %>
        <% if @user.errors.any? || @account.errors.any? %>
          <div class="mb-6 bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-700 rounded-md p-4 transition-colors">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                  Please correct the following errors:
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                  <ul class="list-disc pl-5 space-y-1">
                    <% @user.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                    <% @account.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Account Type Selection -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Account Type
          </label>
          <div class="grid grid-cols-2 gap-3">
            <label class="relative">
              <input type="radio" name="account_type" value="agency" class="sr-only peer" checked>
              <div class="p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer peer-checked:border-blue-600 dark:peer-checked:border-blue-500 peer-checked:bg-blue-50 dark:peer-checked:bg-blue-900/50 hover:border-gray-300 dark:hover:border-gray-500 transition-colors">
                <div class="text-center">
                  <svg class="w-8 h-8 mx-auto mb-2 text-gray-600 dark:text-gray-400 peer-checked:text-blue-600 dark:peer-checked:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                  </svg>
                  <span class="text-sm font-medium text-gray-900 dark:text-gray-100">Government</span>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Browse and procure technology solutions</p>
                </div>
              </div>
            </label>

            <label class="relative">
              <input type="radio" name="account_type" value="vendor" class="sr-only peer">
              <div class="p-4 border-2 border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer peer-checked:border-blue-600 dark:peer-checked:border-blue-500 peer-checked:bg-blue-50 dark:peer-checked:bg-blue-900/50 hover:border-gray-300 dark:hover:border-gray-500 transition-colors">
                <div class="text-center">
                  <svg class="w-8 h-8 mx-auto mb-2 text-gray-600 dark:text-gray-400 peer-checked:text-blue-600 dark:peer-checked:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 00-2 2H10a2 2 0 00-2-2V6m8 0H8"></path>
                  </svg>
                  <span class="text-sm font-medium text-gray-900 dark:text-gray-100">Vendor</span>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">List and market your technology solutions</p>
                </div>
              </div>
            </label>
          </div>
        </div>

        <!-- Company/Organization Name -->
        <div class="mb-6">
          <%= form.label :account_name, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
            <span class="agency-label">Agency Name</span>
            <span class="vendor-label" style="display: none;">Vendor Name</span>
          <% end %>
          <%= form.text_field :account_name, 
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
              placeholder: "Enter your organization name",
              required: true %>
        </div>

        <!-- Personal Information -->
        <div class="grid grid-cols-2 gap-4 mb-6">
          <div>
            <%= form.label :first_name, "First Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.text_field :first_name, 
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
                required: true %>
          </div>
          <div>
            <%= form.label :last_name, "Last Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.text_field :last_name, 
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
                required: true %>
          </div>
        </div>

        <div class="mb-6">
          <%= form.label :email_address, "Email Address", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.email_field :email_address, 
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
              required: true %>
        </div>

        <div class="mb-6">
          <%= form.label :job_title, class: "block text-sm font-medium text-gray-700 dark:text-gray-300" do %>
            Job Title <span class="text-gray-400 dark:text-gray-500 text-xs font-normal">(optional)</span>
          <% end %>
          <%= form.text_field :job_title, 
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors",
              placeholder: "e.g., Sales Director, Product Manager" %>
        </div>

        <!-- Password Fields -->
        <div class="mb-6">
          <%= form.label :password, "Password", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.password_field :password, 
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
              required: true %>
          <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
            Must be at least 8 characters
          </p>
        </div>

        <div class="mb-6">
          <%= form.label :password_confirmation, "Confirm Password", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.password_field :password_confirmation, 
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
              required: true %>
        </div>

        <!-- Terms and Privacy -->
        <div class="mb-6">
          <div class="flex items-center">
            <input id="terms" name="terms" type="checkbox" required
                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <label for="terms" class="ml-2 block text-sm text-gray-900 dark:text-gray-100">
              I agree to the <%= link_to "Terms of Service", pages_terms_of_service_path, target: "_blank", class: "text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300" %> 
              and <%= link_to "Privacy Policy", pages_privacy_policy_path, target: "_blank", class: "text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300" %>
            </label>
          </div>
        </div>

        <!-- Submit Button -->
        <div>
          <%= form.submit "Create Account", 
              class: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors" %>
        </div>
      <% end %>

    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const accountTypeInputs = document.querySelectorAll('input[name="account_type"]');
  const agencyLabel = document.querySelector('.agency-label');
  const vendorLabel = document.querySelector('.vendor-label');
  
  function updateLabels() {
    const selectedType = document.querySelector('input[name="account_type"]:checked').value;
    if (selectedType === 'agency') {
      agencyLabel.style.display = 'inline';
      vendorLabel.style.display = 'none';
    } else if (selectedType === 'vendor') {
      agencyLabel.style.display = 'none';
      vendorLabel.style.display = 'inline';
    }
  }
  
  accountTypeInputs.forEach(input => {
    input.addEventListener('change', updateLabels);
  });
  
  // Set initial state
  updateLabels();
});
</script>
