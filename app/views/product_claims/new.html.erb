<% content_for :page_title, "Claim Product Profile - #{@product.name}" %>

<div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
      Claim Product Profile
    </h1>
    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
            About Product Profiles
          </h3>
          <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
            <p>This profile for <strong><%= @product.name %></strong> has been created by our team. It's not owned by a company yet.</p>
            <p class="mt-2">If this is your company's product, please fill out the form below to claim ownership. Our team will review your request and get back to you.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Claim Form -->
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-6">
        Claim Request Form
      </h3>

      <%= form_with model: [@product, @product_claim], url: product_claims_path(@product), local: true, class: "space-y-6" do |form| %>
        <% if @product_claim.errors.any? %>
          <div class="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4">
            <div class="flex">
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                  There were <%= pluralize(@product_claim.errors.count, "error") %> with your submission:
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                  <ul class="list-disc pl-5 space-y-1">
                    <% @product_claim.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div>
            <%= form.label :name, "Full Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.text_field :name, 
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
                placeholder: "Enter your full name" %>
          </div>

          <div>
            <%= form.label :email, "Email Address", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.email_field :email, 
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
                placeholder: "Enter your email address" %>
          </div>
        </div>

        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div>
            <%= form.label :company, "Company Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.text_field :company, 
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
                placeholder: "Enter your company name" %>
          </div>

          <div>
            <%= form.label :title, "Job Title", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.text_field :title, 
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
                placeholder: "Enter your job title" %>
          </div>
        </div>

        <div>
          <%= form.label :message, "Message", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <%= form.text_area :message, 
              rows: 4,
              class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-1.5 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors",
              placeholder: "Please provide details about your relationship to this product and why you should be granted ownership of this profile..." %>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Please explain how you're connected to this product and provide any relevant information that would help us verify your claim.
          </p>
        </div>

        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                What happens next?
              </h3>
              <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                <ul class="list-disc pl-5 space-y-1">
                  <li>Our team will review your claim request</li>
                  <li>We may reach out for additional verification</li>
                  <li>If approved, you'll be granted access to manage this product profile</li>
                  <li>You'll receive an email update within 3-5 business days</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3">
          <%= link_to "Cancel", marketplace_product_path(@product), 
              class: "bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
          <%= form.submit "Submit Claim Request", 
              class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
        </div>
      <% end %>
    </div>
  </div>
</div>