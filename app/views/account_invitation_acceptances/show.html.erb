<% content_for :page_title, "Complete Account Setup" %>
<% content_for :canonical_url, @canonical_url if @canonical_url %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <h2 class="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
      Complete Your Account Setup
    </h2>
    <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
      You've been invited to join <strong><%= @invitation.account_name %></strong>
    </p>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-2xl">
    <div class="bg-white dark:bg-gray-800 py-8 px-4 shadow sm:rounded-lg sm:px-10">
      <%= form_with model: [@account], url: accept_account_invitation_path(@invitation.token), local: true, class: "space-y-6" do |form| %>
        <% if @account.errors.any? || @user.errors.any? %>
          <div class="rounded-md bg-red-50 dark:bg-red-900/20 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                  There were errors with your submission:
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                  <ul class="list-disc pl-5 space-y-1">
                    <% @account.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                    <% @user.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Invitation Details -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                Account Information
              </h3>
              <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                <p><strong>Organization:</strong> <%= @invitation.account_name %></p>
                <p><strong>Account Type:</strong> <%= @invitation.account_type.humanize %></p>
                <p><strong>Email:</strong> <%= @invitation.email %></p>
              </div>
            </div>
          </div>
        </div>

        <!-- Account Information -->
        <div class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Organization Details</h3>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Review and update your organization information.</p>
          </div>

          <div>
            <%= form.label :name, "Organization Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.text_field :name,
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" %>
          </div>

          <div>
            <%= form.label :description, "Description", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
            <%= form.text_area :description,
                rows: 3,
                class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",
                placeholder: "Tell us about your organization..." %>
          </div>

          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div>
              <%= form.label :website_url, "Website", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
              <%= form.url_field :website_url,
                  class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",
                  placeholder: "https://example.com" %>
            </div>

            <div>
              <%= form.label :linkedin_url, "LinkedIn", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
              <%= form.url_field :linkedin_url,
                  class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",
                  placeholder: "https://linkedin.com/company/example" %>
            </div>
          </div>
        </div>

        <!-- User Information -->
        <%= fields_for :user, @user do |user_form| %>
          <div class="space-y-6 border-t border-gray-200 dark:border-gray-700 pt-6">
            <div>
              <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">Your Information</h3>
              <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Review and update your personal information.</p>
            </div>

            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <%= user_form.label :first_name, "First Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
                <%= user_form.text_field :first_name,
                    class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",
                    required: true %>
              </div>

              <div>
                <%= user_form.label :last_name, "Last Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
                <%= user_form.text_field :last_name,
                    class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",
                    required: true %>
              </div>
            </div>

            <div>
              <%= user_form.label :email_address, "Email Address", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
              <%= user_form.email_field :email_address,
                  class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-2 bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-gray-100",
                  readonly: true %>
              <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">This email is pre-filled from your invitation.</p>
            </div>

            <div>
              <%= user_form.label :job_title, "Job Title", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
              <%= user_form.text_field :job_title,
                  class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",
                  placeholder: "e.g., CEO, CTO, Director",
                  required: true %>
            </div>

            <div>
              <%= user_form.label :department, "Department", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
              <%= user_form.text_field :department,
                  class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",
                  placeholder: "e.g., Engineering, Sales, Marketing",
                  required: true %>
            </div>

            <div>
              <%= label_tag :organization_display, "Organization Name", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
              <div class="mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-2 bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-gray-100">
                <%= @invitation.account_name %>
              </div>
              <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">This is your organization name for this account.</p>
            </div>

            <div>
              <%= user_form.label :phone, "Phone Number", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
              <%= user_form.telephone_field :phone,
                  class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",
                  placeholder: "(*************" %>
            </div>

            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div>
                <%= user_form.label :password, "Password", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
                <%= user_form.password_field :password,
                    class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" %>
              </div>

              <div>
                <%= user_form.label :password_confirmation, "Confirm Password", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
                <%= user_form.password_field :password_confirmation,
                    class: "mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-400 dark:focus:border-blue-400 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" %>
              </div>
            </div>
          </div>
        <% end %>

        <div>
          <%= form.submit "Complete Account Setup",
              class: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
        </div>
      <% end %>
    </div>
  </div>
</div>