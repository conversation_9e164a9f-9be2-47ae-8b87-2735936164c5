<% content_for :page_title, "Team Member - #{@team_member.user.full_name}" %>

<div class="max-w-7xl mx-auto">
  <div class="md:flex md:items-center md:justify-between mb-8">
    <div class="min-w-0 flex-1">
      <h2 class="text-2xl font-bold leading-7 text-gray-900 dark:text-gray-100 sm:truncate sm:text-3xl">
        <%= @team_member.user.full_name %>
      </h2>
      <div class="mt-1 flex flex-col sm:mt-0 sm:flex-row sm:flex-wrap sm:space-x-6">
        <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <%= @team_member.role == 'admin' ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100' : 'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-100' %>">
            <%= @team_member.role&.capitalize || 'Member' %>
          </span>
          <% if @team_member.user == @account.owner %>
            <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
              Owner
            </span>
          <% end %>
        </div>
      </div>
    </div>
    <div class="mt-4 flex md:ml-4 md:mt-0">
      <div class="flex space-x-2">
        <% if @team_member.user != @account.owner %>
          <% if @account.approved? || current_user.admin? %>
            <%= link_to 'Edit Role', edit_member_team_management_index_path(@team_member), class: 'inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
          <% end %>
        <% end %>
        <%= link_to 'Back to Team', team_management_index_path, class: 'inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors' %>
      </div>
    </div>
  </div>

  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg transition-colors">
    <div class="px-4 py-5 sm:p-6">
      <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <div>
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">Member Information</h3>
          
          <div class="space-y-4">
            <div class="flex items-center">
              <div class="h-16 w-16 flex-shrink-0 mr-4">
                <div class="h-16 w-16 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center">
                  <span class="text-lg font-medium text-blue-800 dark:text-blue-100">
                    <%= @team_member.user.first_name&.first %><%= @team_member.user.last_name&.first %>
                  </span>
                </div>
              </div>
              <div>
                <div class="text-xl font-medium text-gray-900 dark:text-gray-100">
                  <%= @team_member.user.full_name %>
                </div>
                <div class="text-gray-500 dark:text-gray-400">
                  <%= @team_member.user.email_address %>
                </div>
              </div>
            </div>

            <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
              <dl class="space-y-3">
                <div>
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Role</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                    <%= @team_member.role&.capitalize || 'Member' %>
                    <% if @team_member.user == @account.owner %>
                      <span class="ml-2 text-xs text-blue-600 dark:text-blue-400">(Account Owner)</span>
                    <% end %>
                  </dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Joined</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                    <%= @team_member.joined_at ? @team_member.joined_at.strftime("%B %d, %Y") : "N/A" %>
                    <% if @team_member.joined_at %>
                      <span class="text-gray-500 dark:text-gray-400">
                        (<%= time_ago_in_words(@team_member.joined_at) %> ago)
                      </span>
                    <% end %>
                  </dd>
                </div>
                <div>
                  <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Status</dt>
                  <dd class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                      Active
                    </span>
                  </dd>
                </div>
              </dl>
            </div>
          </div>
        </div>

        <div>
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">Role Permissions</h3>
          
          <div class="space-y-3">
            <% if @team_member.role == 'admin' || @team_member.user == @account.owner %>
              <div class="flex items-center">
                <svg class="h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <span class="ml-2 text-sm text-gray-900 dark:text-gray-100">Can manage team members</span>
              </div>
              <div class="flex items-center">
                <svg class="h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <span class="ml-2 text-sm text-gray-900 dark:text-gray-100">Can edit account settings</span>
              </div>
              <div class="flex items-center">
                <svg class="h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <span class="ml-2 text-sm text-gray-900 dark:text-gray-100">Can manage products and services</span>
              </div>
            <% else %>
              <div class="flex items-center">
                <svg class="h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <span class="ml-2 text-sm text-gray-900 dark:text-gray-100">Can access basic features</span>
              </div>
              <div class="flex items-center">
                <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
                <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">Cannot view account information</span>
              </div>
              <div class="flex items-center">
                <svg class="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
                <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">Cannot manage team members</span>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Danger Zone Section -->
  <% if @team_member.user != @account.owner && (@account.approved? || current_user.admin?) %>
    <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg mt-8 border border-red-200 dark:border-red-700">
      <div class="px-4 py-5 sm:p-6">
        <div class="md:flex md:items-center md:justify-between">
          <div class="min-w-0 flex-1">
            <h3 class="text-lg leading-6 font-medium text-red-900 dark:text-red-100">Danger Zone</h3>
            <p class="mt-2 max-w-xl text-sm text-red-600 dark:text-red-300">
              Remove this team member from your account. They will lose access to all account resources and data.
            </p>
          </div>
          <div class="mt-4 md:ml-6 md:mt-0 md:flex-shrink-0">
            <button type="button" 
                    class="inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 shadow-sm text-sm font-medium rounded-md text-red-700 dark:text-red-300 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors remove-member-btn" 
                    data-member-id="<%= @team_member.id %>" 
                    data-member-name="<%= @team_member.user.full_name %>">
              <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
              </svg>
              Remove Team Member
            </button>
          </div>
        </div>
      </div>
    </div>
  <% end %>
</div>

<!-- Remove Member Confirmation Modal -->
<div id="remove-member-modal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
  <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
    <div class="fixed inset-0 bg-gray-500/75 transition-opacity" aria-hidden="true"></div>
    
    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
    
    <div class="relative inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
      <div class="sm:flex sm:items-start">
        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/50 sm:mx-0 sm:h-10 sm:w-10">
          <svg class="h-6 w-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
            Remove Team Member
          </h3>
          <div class="mt-2">
            <p class="text-sm text-gray-500 dark:text-gray-400">
              Are you sure you want to remove <span class="font-medium text-gray-900 dark:text-gray-100" id="remove-member-name"></span> from the team?
            </p>
            <div class="mt-3 space-y-2">
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <svg class="h-4 w-4 text-red-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <p class="ml-2 text-xs text-gray-500 dark:text-gray-400">They will immediately lose access to this account</p>
              </div>
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <svg class="h-4 w-4 text-red-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <p class="ml-2 text-xs text-gray-500 dark:text-gray-400">Their user account will remain intact and can be re-invited</p>
              </div>
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <svg class="h-4 w-4 text-red-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <p class="ml-2 text-xs text-red-500 dark:text-red-400 font-medium">This action cannot be undone</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
        <button type="button" id="confirm-remove-member-btn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
          Remove
        </button>
        <button type="button" id="cancel-remove-member-btn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:w-auto sm:text-sm">
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Remove Member Modal
  const removeMemberBtn = document.querySelector('.remove-member-btn');
  const removeMemberModal = document.getElementById('remove-member-modal');
  const confirmRemoveMemberBtn = document.getElementById('confirm-remove-member-btn');
  const cancelRemoveMemberBtn = document.getElementById('cancel-remove-member-btn');
  const removeMemberName = document.getElementById('remove-member-name');
  
  let currentMemberId = null;
  
  // Show remove member modal
  if (removeMemberBtn) {
    removeMemberBtn.addEventListener('click', function() {
      currentMemberId = this.dataset.memberId;
      const memberName = this.dataset.memberName;
      removeMemberName.textContent = memberName;
      removeMemberModal.classList.remove('hidden');
      document.body.classList.add('overflow-hidden');
    });
  }
  
  // Hide modal
  function hideRemoveMemberModal() {
    removeMemberModal.classList.add('hidden');
    document.body.classList.remove('overflow-hidden');
    currentMemberId = null;
  }
  
  // Cancel button
  cancelRemoveMemberBtn.addEventListener('click', hideRemoveMemberModal);
  
  // Close modal when clicking outside
  removeMemberModal.addEventListener('click', function(e) {
    if (e.target === removeMemberModal || e.target.classList.contains('bg-gray-500')) {
      hideRemoveMemberModal();
    }
  });
  
  // Confirm remove member
  confirmRemoveMemberBtn.addEventListener('click', function() {
    if (currentMemberId) {
      confirmRemoveMemberBtn.disabled = true;
      confirmRemoveMemberBtn.textContent = 'Removing...';
      
      // Create and submit form
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = `/team_management/members/${currentMemberId}`;
      
      const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
      const csrfInput = document.createElement('input');
      csrfInput.type = 'hidden';
      csrfInput.name = 'authenticity_token';
      csrfInput.value = csrfToken;
      
      const methodInput = document.createElement('input');
      methodInput.type = 'hidden';
      methodInput.name = '_method';
      methodInput.value = 'DELETE';
      
      form.appendChild(csrfInput);
      form.appendChild(methodInput);
      document.body.appendChild(form);
      form.submit();
    }
  });
  
  // Handle successful actions
  document.addEventListener('turbo:before-visit', function(event) {
    hideRemoveMemberModal();
  });
  
  // Handle escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      hideRemoveMemberModal();
    }
  });
});
</script>