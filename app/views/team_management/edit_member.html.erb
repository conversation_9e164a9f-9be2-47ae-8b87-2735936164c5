<% content_for :page_title, "Edit Team Member Role" %>

<div class="max-w-3xl">
  <div class="bg-white dark:bg-gray-800 shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-6">
        Edit Role for <%= @team_member.user.full_name %>
      </h3>
      
      <%= form_with model: [@team_member], url: update_member_role_team_management_index_path(@team_member), method: :patch, local: true, class: "space-y-6" do |form| %>
        <% if @team_member.errors.any? %>
          <div class="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4">
            <div class="flex">
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                  There were <%= pluralize(@team_member.errors.count, "error") %> with your changes:
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                  <ul role="list" class="list-disc space-y-1 pl-5">
                    <% @team_member.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <div class="bg-gray-50 dark:bg-gray-700/50 rounded-md p-4">
          <div class="flex items-center">
            <div class="h-10 w-10 flex-shrink-0">
              <div class="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center">
                <span class="text-sm font-medium text-blue-800 dark:text-blue-100">
                  <%= @team_member.user.first_name&.first %><%= @team_member.user.last_name&.first %>
                </span>
              </div>
            </div>
            <div class="ml-4">
              <div class="font-medium text-gray-900 dark:text-gray-100"><%= @team_member.user.full_name %></div>
              <div class="text-gray-500 dark:text-gray-400"><%= @team_member.user.email_address %></div>
            </div>
          </div>
        </div>

        <div>
          <%= form.label :role, "Role", class: "block text-sm font-medium text-gray-700 dark:text-gray-300" %>
          <% if @team_member.user == @account.owner %>
            <div class="mt-2 grid grid-cols-1">
              <%= form.select :role, 
                  options_for_select([['Admin', 'admin']], 'admin'),
                  { include_blank: false },
                  { disabled: true, class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-gray-100 dark:bg-gray-600 py-1.5 pr-8 pl-3 text-base text-gray-500 dark:text-gray-400 outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 cursor-not-allowed sm:text-sm" } %>
              <%= form.hidden_field :role, value: 'admin' %>
              <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-400 dark:text-gray-500 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="mt-2 text-sm text-gray-500 dark:text-gray-400">
              <p class="font-medium text-yellow-700 dark:text-yellow-300">Account owners must always have admin role and cannot be changed to member.</p>
            </div>
          <% else %>
            <div class="mt-2 grid grid-cols-1">
              <%= form.select :role, 
                  options_for_select([
                    ['Member', 'member'],
                    ['Admin', 'admin']
                  ], @team_member.role),
                  { include_blank: false },
                  { class: "col-start-1 row-start-1 w-full appearance-none rounded-md bg-white dark:bg-gray-700 py-1.5 pr-8 pl-3 text-base text-gray-900 dark:text-white outline-1 -outline-offset-1 outline-gray-300 dark:outline-gray-600 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 dark:focus:outline-indigo-500 sm:text-sm" } %>
              <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 dark:text-gray-400 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="mt-2 text-sm text-gray-500 dark:text-gray-400">
              <p><strong>Member:</strong> Can view account information and access basic features</p>
              <p><strong>Admin:</strong> Can manage team members, edit account settings, and access advanced features</p>
            </div>
          <% end %>
        </div>

        <div class="flex justify-end space-x-3">
          <%= link_to "Cancel", team_management_index_path, class: "bg-white dark:bg-gray-700 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
          <%= form.submit "Update Role", class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800" %>
        </div>
      <% end %>
    </div>
  </div>
</div>