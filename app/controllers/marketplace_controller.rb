class MarketplaceController < ApplicationController
  allow_unauthenticated_access
  
  helper_method :can_view_product_content?, :can_view_draft_product?, :should_respect_visibility_settings?, :can_view_product_section?

  def index
    # Load all categories with their subcategories (up to 3 levels) and products for the category browser
    @root_categories = Category.root_categories.includes(
      subcategories: {
        subcategories: :products,
        products: :account
      }
    )

    # Featured content for Airbnb-style home
    @featured_companies = Account.approved.where(account_type: 'vendor')
                                       .order(created_at: :desc)
                                       .limit(8)

    @featured_products = Product.published.includes(:account, :categories)
                               .order(created_at: :desc)
                               .limit(12)
    
    @recently_added_companies = Account.approved.where(account_type: 'vendor')
                                             .where('created_at >= ?', 30.days.ago)
                                             .order(created_at: :desc)
                                             .limit(6)

    @recently_added_products = Product.published.includes(:account, :categories)
                                     .where('created_at >= ?', 30.days.ago)
                                     .order(created_at: :desc)
                                     .limit(8)
    
    @recently_added_case_studies = CaseStudy.joins(product: :account)
                                           .where(products: { published: true })
                                           .where('case_studies.created_at >= ?', 30.days.ago)
                                           .includes(product: [:account, :categories])
                                           .order(created_at: :desc)
                                           .limit(6)
    
    # Popular features removed since features are now product-specific JSON
  end


  def categories
    @root_categories = Category.root_categories.includes(:subcategories).order(:name)
    
    # Search functionality
    if params[:search].present?
      search_term = "%#{params[:search]}%"
      @root_categories = @root_categories.where("name LIKE ?", search_term)
    end
  end

  def category
    @category = Category.friendly.find(params[:id])
    @products = @category.products.published.includes(:account, :categories, :case_studies, :product_screenshots)
    
    # Sort options
    case params[:sort]
    when 'newest'
      @products = @products.order(created_at: :desc)
    when 'oldest'
      @products = @products.order(created_at: :asc)
    when 'name_asc'
      @products = @products.order(:name)
    when 'name_desc'
      @products = @products.order(name: :desc)
    else
      @products = @products.order(:name)
    end
    
    @sort_options = [
      ['Name (A-Z)', 'name_asc'],
      ['Name (Z-A)', 'name_desc'],
      ['Newest First', 'newest'],
      ['Oldest First', 'oldest']
    ]
  end

  def products
    @products = Product.published.includes(:account, :categories, :case_studies)

    # Category filter (multiple selection support for subcategories)
    if params[:category_ids].present?
      category_ids = Array(params[:category_ids]).reject(&:blank?)
      if category_ids.any?
        @products = @products.joins(:categories).where(categories: { id: category_ids })
      end
    elsif params[:category_id].present?
      @products = @products.joins(:categories).where(categories: { id: params[:category_id] })
    end
    
    # Has demo video filter (products with featured videos)
    if params[:has_demo_video] == '1'
      @products = @products.joins(:featured_video_attachment)
    end
    
    # Has case studies filter
    if params[:has_case_studies] == '1'
      @products = @products.joins(:case_studies).distinct
    end
    
    # Sort options
    case params[:sort]
    when 'newest'
      @products = @products.order(created_at: :desc)
    when 'oldest'
      @products = @products.order(created_at: :asc)
    when 'name_asc'
      @products = @products.order(:name)
    when 'name_desc'
      @products = @products.order(name: :desc)
    else
      @products = @products.order(:name)
    end
    
    # Company sizes removed as they're no longer available
    @sort_options = [
      ['Name (A-Z)', 'name_asc'],
      ['Name (Z-A)', 'name_desc'],
      ['Newest First', 'newest'],
      ['Oldest First', 'oldest']
    ]
    
    # Get categories that have products (leaf categories only) - load this AFTER filtering
    @categories_with_products = Category.joins(:products)
                                       .where(products: { published: true })
                                       .includes(:parent)
                                       .distinct
                                       .order('categories.name')
    
    # Group by root category for hierarchical display
    @root_categories = Category.root_categories.includes(:subcategories)
    @subcategories = Category.child_categories
  end



  def product
    # First try to find the product regardless of published status
    @product = Product.includes(:categories, :product_videos, :case_studies, :product_content, :product_screenshots, :product_features)
                      .friendly.find(params[:id])
    
    # Check if user can view this product (published products OR draft products if user has permission)
    unless @product.published? || can_view_draft_product?(@product)
      raise ActiveRecord::RecordNotFound
    end
    
    # Track analytics for this product view
    ProductAnalyticsService.track_page_view(@product, request, current_user)
    
    @related_products = if @product.account
                          @product.account.products.published.where.not(id: @product.id)
                                 .includes(:categories).limit(3)
                        else
                          Product.none
                        end
    
    # Get similar products from same categories (excluding current product and same vendor products)
    category_ids = @product.categories.pluck(:id)
    @similar_products = Product.published
                              .joins(:categories)
                              .where(categories: { id: category_ids })
                              .where.not(id: @product.id)
                              .where.not(account: @product.account)
                              .includes(:account, :categories)
                              .distinct
                              .limit(3)
    
    # Initialize lead for form
    @lead = Lead.new
    @company = @product.account
    
    # Get product videos ordered by position (for the videos section)
    @product_videos = @product.product_videos.published.order(:position)
    
    # Get case studies for this product
    @case_studies = @product.case_studies.published.order(:position)
    
    # Get additional content/media
    @product_content = @product.product_content.published.order(:position)
    @product_screenshots = @product.product_screenshots.published.order(:position)
    @product_features = @product.product_features.published.order(:position)
    @product_customers = @product.product_customers.published.order(:position)
    @product_contracts = @product.product_contracts.published.order(:position)
    
    # Check if current user has already expressed interest (only if authenticated)
    @existing_interest = current_account&.agency_leads&.find_by(product: @product) if current_account
  end

  def vendor_products
    @vendor = Account.approved.where(account_type: 'vendor').friendly.find(params[:id])
    @products = @vendor.products.published.includes(:categories, :account)
    
    # Sort options
    case params[:sort]
    when 'newest'
      @products = @products.order(created_at: :desc)
    when 'oldest'
      @products = @products.order(created_at: :asc)
    when 'name_asc'
      @products = @products.order(:name)
    when 'name_desc'
      @products = @products.order(name: :desc)
    else
      @products = @products.order(:name)
    end
    
    @sort_options = [
      ['Name (A-Z)', 'name_asc'],
      ['Name (Z-A)', 'name_desc'],
      ['Newest First', 'newest'],
      ['Oldest First', 'oldest']
    ]
  end

  def product_video
    @product = Product.friendly.find(params[:product_id])
    unless @product.published? || can_view_draft_product?(@product)
      raise ActiveRecord::RecordNotFound
    end
    require_content_access(@product)
    @product_video = @product.product_videos.find(params[:id])
  end

  def case_study
    @product = Product.friendly.find(params[:product_id])
    unless @product.published? || can_view_draft_product?(@product)
      raise ActiveRecord::RecordNotFound
    end
    require_content_access(@product)
    @case_study = @product.case_studies.find(params[:id])
  end

  def product_content
    @product = Product.friendly.find(params[:product_id])
    unless @product.published? || can_view_draft_product?(@product)
      raise ActiveRecord::RecordNotFound
    end
    require_content_access(@product)
    @product_content = @product.product_content.find(params[:id])
  end

  def product_feature
    @product = Product.friendly.find(params[:product_id])
    unless @product.published? || can_view_draft_product?(@product)
      raise ActiveRecord::RecordNotFound
    end
    @product_feature = @product.product_features.find(params[:id])
  end

  def product_customer
    @product = Product.friendly.find(params[:product_id])
    unless @product.published? || can_view_draft_product?(@product)
      raise ActiveRecord::RecordNotFound
    end
    @product_customer = @product.product_customers.find(params[:id])
  end

  def search
    @query = params[:q]&.strip
    
    if @query.blank?
      redirect_to marketplace_path
      return
    end
    
    # First try to find an exact category match
    category = Category.where("LOWER(name) = ?", @query.downcase).first
    if category
      redirect_to marketplace_category_path(category)
      return
    end
    
    # Then try to find an exact product match
    product = Product.published.where("LOWER(name) = ?", @query.downcase).first
    if product
      redirect_to marketplace_product_path(product)
      return
    end
    
    # Try partial category match
    category = Category.where("LOWER(name) LIKE ?", "%#{@query.downcase}%").first
    if category
      redirect_to marketplace_category_path(category)
      return
    end
    
    # Try partial product match
    product = Product.published.where("LOWER(name) LIKE ?", "%#{@query.downcase}%").first
    if product
      redirect_to marketplace_product_path(product)
      return
    end
    
    # If no matches found, redirect back to marketplace with a message
    redirect_to marketplace_path, alert: "No results found for '#{@query}'"
  end

  def api_search
    query = params[:q]&.strip
    
    if query.blank? || query.length < 2
      render json: { categories: [], products: [] }
      return
    end
    
    # SQLite-compatible fuzzy search using multiple LIKE patterns
    search_patterns = generate_search_patterns(query)
    
    # Search categories with fuzzy matching
    categories = Category.where(
      search_patterns.map { "LOWER(name) LIKE ? OR LOWER(description) LIKE ?" }.join(' OR '),
      *search_patterns.flat_map { |pattern| [pattern, pattern] }
    ).limit(5).map do |category|
      {
        id: category.id,
        name: category.name,
        description: truncate_description(category.description),
        type: 'category',
        url: category.root_category? ? marketplace_products_path(category_ids: category.subcategories.pluck(:id)) : marketplace_products_path(category_ids: [category.id]),
        full_name: category.full_name
      }
    end
    
    # Search products with fuzzy matching
    products = Product.published
                     .where(
                       search_patterns.map { "LOWER(name) LIKE ? OR LOWER(description) LIKE ?" }.join(' OR '),
                       *search_patterns.flat_map { |pattern| [pattern, pattern] }
                     )
                     .includes(:account)
                     .limit(5)
                     .map do |product|
      {
        id: product.id,
        name: product.name,
        description: truncate_description(strip_html_tags(product.description)),
        type: 'product',
        url: marketplace_product_path(product),
        company: product.account.name
      }
    end
    
    render json: { categories: categories, products: products }
  end

  def create_lead
    @product = Product.friendly.find(params[:id])
    unless @product.published? || can_view_draft_product?(@product)
      raise ActiveRecord::RecordNotFound
    end
    
    @lead = Lead.new(lead_params)
    @lead.product = @product
    @lead.account = @product.account if @product.account
    @lead.user = current_user if current_user
    @lead.agency_account = current_account if current_account
    @lead.status = 'pending'

    if @lead.save
      redirect_to marketplace_product_path(@product), 
                  notice: "Lead submitted successfully! The vendor will reach out to you soon."
    else
      # Reload the product page with errors - set up all required instance variables
      @company = @product.account
      @related_products = if @product.account
                            @product.account.products.published.where.not(id: @product.id)
                                   .includes(:categories).limit(3)
                          else
                            Product.none
                          end
      
      # Get similar products from same categories
      category_ids = @product.categories.pluck(:id)
      @similar_products = Product.published
                                 .joins(:categories)
                                 .where(categories: { id: category_ids })
                                 .where.not(id: @product.id)
                                 .where.not(account: @product.account)
                                 .includes(:account, :categories)
                                 .distinct
                                 .limit(3)
                                 
      # Get product videos ordered by position (for the videos section)
      @product_videos = @product.product_videos.published.order(:position)
      
      # Get case studies for this product
      @case_studies = @product.case_studies.published.order(:position)
      
      # Get additional content/media
      @product_content = @product.product_content.published.order(:position)
      @product_screenshots = @product.product_screenshots.published.order(:position)
      @product_features = @product.product_features.published.order(:position)
      @product_customers = @product.product_customers.published.order(:position)
      @product_contracts = @product.product_contracts.published.order(:position)
      
      # Check if current user has already expressed interest (only if authenticated)
      @existing_interest = current_account&.agency_leads&.find_by(product: @product) if current_account

      render :product
    end
  end

  def track_content_view
    @product = Product.friendly.find(params[:product_id])
    unless @product.published? || can_view_draft_product?(@product)
      render json: { error: 'Product not found' }, status: :not_found
      return
    end
    
    # Get content details for tracking
    content_type = params[:content_type]
    content_id = params[:content_id]
    
    # Track the content view
    ProductAnalyticsService.track_content_action(@product, 'content_view', content_type, content_id, request, current_user)
    
    render json: { success: true }
  rescue => e
    Rails.logger.error "Error tracking content view: #{e.message}"
    render json: { error: 'Failed to track view' }, status: :internal_server_error
  end

  def track_content_download
    @product = Product.friendly.find(params[:product_id])
    unless @product.published? || can_view_draft_product?(@product)
      render json: { error: 'Product not found' }, status: :not_found
      return
    end
    
    # Get content details for tracking
    content_type = params[:content_type]
    content_id = params[:content_id]
    
    # Track the content download
    ProductAnalyticsService.track_content_action(@product, 'content_download', content_type, content_id, request, current_user)
    
    render json: { success: true }
  rescue => e
    Rails.logger.error "Error tracking content download: #{e.message}"
    render json: { error: 'Failed to track download' }, status: :internal_server_error
  end

  def track_video_play
    @product = Product.friendly.find(params[:product_id])
    unless @product.published? || can_view_draft_product?(@product)
      render json: { error: 'Product not found' }, status: :not_found
      return
    end
    
    # Get video details for tracking
    video_duration = params[:video_duration]&.to_f
    
    # Create metadata with video details
    video_metadata = {
      content_type: 'featured_video',
      content_id: 'featured',
      video_duration: video_duration
    }
    
    # Track the video play
    ProductAnalyticsService.track_video_action(@product, 'video_play', video_metadata, request, current_user)
    
    render json: { success: true }
  rescue => e
    Rails.logger.error "Error tracking video play: #{e.message}"
    render json: { error: 'Failed to track video play' }, status: :internal_server_error
  end

  def track_video_watch_duration
    @product = Product.friendly.find(params[:product_id])
    unless @product.published? || can_view_draft_product?(@product)
      render json: { error: 'Product not found' }, status: :not_found
      return
    end
    
    # Get video watch details for tracking
    watch_duration = params[:watch_duration]&.to_f
    video_duration = params[:video_duration]&.to_f
    
    # Create metadata with video watch details
    video_metadata = {
      content_type: 'featured_video',
      content_id: 'featured',
      watch_duration: watch_duration,
      video_duration: video_duration
    }
    
    # Track the video watch duration and return the analytics record ID
    analytics_record = ProductAnalyticsService.track_video_action(@product, 'video_watch_duration', video_metadata, request, current_user)
    
    render json: { success: true, analytics_id: analytics_record&.id }
  rescue => e
    Rails.logger.error "Error tracking video watch duration: #{e.message}"
    render json: { error: 'Failed to track video watch duration' }, status: :internal_server_error
  end

  def update_video_watch_duration
    @product = Product.friendly.find(params[:product_id])
    unless @product.published? || can_view_draft_product?(@product)
      render json: { error: 'Product not found' }, status: :not_found
      return
    end
    
    analytics_id = params[:analytics_id]
    watch_duration = params[:watch_duration]&.to_f
    video_duration = params[:video_duration]&.to_f
    
    # Find and update the existing analytics record
    analytic = @product.product_analytics.find_by(id: analytics_id, event_type: 'video_watch_duration')
    if analytic
      # Update metadata with new watch duration
      updated_metadata = analytic.metadata.merge({
        'watch_duration' => watch_duration,
        'video_duration' => video_duration
      })
      
      analytic.update!(metadata: updated_metadata)
      render json: { success: true }
    else
      render json: { error: 'Analytics record not found' }, status: :not_found
    end
  rescue => e
    Rails.logger.error "Error updating video watch duration: #{e.message}"
    render json: { error: 'Failed to update video watch duration' }, status: :internal_server_error
  end

  private

  def generate_search_patterns(query)
    patterns = []
    query_lower = query.downcase
    
    # Exact match
    patterns << "%#{query_lower}%"
    
    # Split into words for individual word matching
    words = query_lower.split(/\s+/)
    words.each do |word|
      patterns << "%#{word}%"
    end
    
    # Basic fuzzy patterns (handle common typos)
    if query_lower.length > 3
      # Remove one character at a time (deletion)
      (0...query_lower.length).each do |i|
        fuzzy = query_lower[0...i] + query_lower[i+1..-1]
        patterns << "%#{fuzzy}%" if fuzzy.length > 2
      end
    end
    
    patterns.uniq
  end
  
  def truncate_description(text)
    return "" if text.blank?
    text.length > 100 ? "#{text[0...100]}..." : text
  end
  
  def strip_html_tags(text)
    return "" if text.blank?
    # Handle rich text content
    if text.respond_to?(:to_plain_text)
      text.to_plain_text
    else
      # Basic HTML tag removal for regular strings
      text.gsub(/<[^>]*>/, '').strip
    end
  end

  def can_view_draft_product?(product)
    return false unless current_user
    
    begin
      # Super admins can view everything
      return true if current_user.super_admin?
      
      # Product owner (vendor account that owns this product) can view their own draft products
      return true if product.account && product.account.users.include?(current_user)
      
      false
    rescue => e
      Rails.logger.error "Error in can_view_draft_product?: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      false
    end
  end

  def can_view_product_content?(product)
    return false unless current_user
    
    begin
      # Super admins can view everything
      return true if current_user.super_admin?
      
      # Agency users (with approved agency accounts) can view content
      if current_user.has_agency_account?
        approved_agency_accounts = current_user.accounts.where(account_type: 'agency', status: 'approved')
        return true if approved_agency_accounts.any?
      end
      
      # Product owner (vendor account that owns this product) can view their own content
      return true if product.account.users.include?(current_user)
      
      false
    rescue => e
      Rails.logger.error "Error in can_view_product_content?: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      false
    end
  end
  
  def require_content_access(product)
    unless can_view_product_content?(product)
      redirect_to marketplace_product_path(product), 
                  alert: "Access denied. Only approved government agencies and departments can view this content."
    end
  end

  def lead_params
    params.require(:lead).permit(:contact_name, :contact_email, :contact_phone, :contact_company, :message, :budget_range, :timeline)
  end

  def should_respect_visibility_settings?(product)
    # Admin accounts and agency accounts can see everything regardless of visibility settings
    return false if current_user&.super_admin?
    
    # Agency users with approved accounts can see everything
    if current_user&.has_agency_account?
      approved_agency_accounts = current_user.accounts.where(account_type: 'agency', status: 'approved')
      return false if approved_agency_accounts.any?
    end
    
    # Everyone else (vendors, unauthenticated users, etc.) should respect visibility settings
    true
  end

  def can_view_product_section?(product, section_type)
    begin
      # Super admins can view everything
      return true if current_user&.super_admin?
      
      # Agency users (with approved agency accounts) can view everything
      if current_user&.has_agency_account?
        approved_agency_accounts = current_user.accounts.where(account_type: 'agency', status: 'approved')
        return true if approved_agency_accounts.any?
      end
      
      # Product owner (vendor account that owns this product) can view their own content
      if current_user && product.account && product.account.users.include?(current_user)
        return true
      end
      
      # For everyone else (unauthenticated users), check visibility settings
      case section_type
      when :featured_video
        product.show_featured_video?
      when :product_videos
        product.show_product_videos?
      when :product_screenshots
        product.show_product_screenshots?
      when :product_features
        product.show_product_features?
      when :product_customers
        product.show_product_customers?
      when :product_content
        product.show_product_content?
      when :product_contracts
        product.show_product_contracts?
      when :case_studies
        product.show_case_studies?
      else
        false
      end
    rescue => e
      Rails.logger.error "Error in can_view_product_section?: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      false
    end
  end


end