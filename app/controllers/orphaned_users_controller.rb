class OrphanedUsersController < ApplicationController
  include Authentication
  
  before_action :require_authentication
  before_action :ensure_orphaned_user
  
  def show
    # Show the orphaned user page with options to create account or contact admin
  end
  
  private
  
  def ensure_orphaned_user
    user_accounts = Current.user.accounts
    
    if user_accounts.any?
      # User has accounts - check if any are approved and confirmed
      approved_accounts = user_accounts.select(&:approved?)
      confirmed_accounts = user_accounts.select { |account| account.confirmed_at.present? }
      approved_and_confirmed = approved_accounts.select { |account| account.confirmed_at.present? }
      
      if approved_and_confirmed.any?
        # User has working accounts, redirect to dashboard
        redirect_to after_authentication_url
      elsif approved_accounts.any? || confirmed_accounts.any?
        # User has accounts but they're not ready yet, redirect to pending
        redirect_to pending_path
      end
      # If user has accounts but none are approved/confirmed, they can stay here
      # This covers edge cases where accounts exist but are in limbo
    end
  end
end