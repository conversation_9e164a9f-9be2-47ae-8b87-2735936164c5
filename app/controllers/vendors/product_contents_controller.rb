module Vendors
  class ProductContentsController < BaseController
    before_action :set_product
    before_action :set_product_content, only: [:show, :edit, :update, :destroy, :update_position]
    before_action :ensure_owner

    def index
      @product_contents = @product.product_content.by_position.with_attached_file
    end

    def show
    end

    def new
      @product_content = @product.product_content.build
      set_next_position
    end

    def create
      @product_content = @product.product_content.build(product_content_params)
      set_next_position unless @product_content.position

      if @product_content.save
        redirect_to vendors_product_path(@product, anchor: 'content'), notice: 'Content was successfully added.'
      else
        render :new, status: :unprocessable_entity
      end
    end

    def edit
    end

    def update
      if @product_content.update(product_content_params)
        redirect_to vendors_product_path(@product, anchor: 'content'), notice: 'Content was successfully updated.'
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @product_content.destroy!
      redirect_to vendors_product_path(@product, anchor: 'content'), notice: 'Content was successfully deleted.'
    end

    def update_position
      new_position = params[:position].to_i
      
      # Reorder other content to make room for the moved content
      ProductContent.transaction do
        # Get all content for this product in current order
        contents = @product.product_content.order(:position)
        
        # Remove the content being moved from its current position
        contents_without_moved = contents.reject { |c| c.id == @product_content.id }
        
        # Insert the moved content at the new position
        contents_without_moved.insert(new_position - 1, @product_content)
        
        # Update positions for all content
        contents_without_moved.each_with_index do |content, index|
          content.update!(position: index + 1)
        end
      end
      
      render json: { status: 'success' }
    rescue => e
      render json: { status: 'error', errors: [e.message] }
    end

    private

    def set_product
      @product = current_account.products.friendly.find(params[:product_id])
    end

    def set_product_content
      @product_content = @product.product_content.find(params[:id])
    end

    def ensure_owner
      redirect_to root_path unless @product.account == current_account
    end

    def set_next_position
      @product_content.position = (@product.product_content.maximum(:position) || 0) + 1
    end

    def product_content_params
      params.require(:product_content).permit(:title, :description, :published, :file)
    end
  end
end