module Vendors
  class ProductFeaturesController < BaseController
    before_action :set_product
    before_action :set_product_feature, only: [:show, :edit, :update, :destroy, :update_position]

    def index
      @product_features = @product.product_features.ordered
    end

    def show
    end

    def new
      @product_feature = @product.product_features.build
    end

    def create
      @product_feature = @product.product_features.build(product_feature_params)
      
      if @product_feature.save
        redirect_to vendors_product_path(@product, anchor: 'features'), notice: 'Product feature was successfully created.'
      else
        render :new, status: :unprocessable_entity
      end
    end

    def edit
    end

    def update
      if @product_feature.update(product_feature_params)
        redirect_to vendors_product_path(@product, anchor: 'features'), notice: 'Product feature was successfully updated.'
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @product_feature.destroy!
      redirect_to vendors_product_path(@product, anchor: 'features'), notice: 'Product feature was successfully deleted.'
    end

    def update_position
      @product_feature.update!(position: params[:position])
      head :ok
    end

    private

    def set_product
      @product = current_account.products.friendly.find(params[:product_id])
    end

    def set_product_feature
      @product_feature = @product.product_features.find(params[:id])
    end

    def product_feature_params
      params.require(:product_feature).permit(:name, :description, :attachment, :position, :published)
    end
  end
end