module Vendors
  class ProductsController < BaseController
    before_action :set_product, only: [:show, :edit, :update, :destroy, :analytics, :features, :customers, :videos, :screenshots, :case_studies, :content, :contracts, :visibility, :manage_featured_video, :update_featured_video, :remove_featured_video]

    def index
      @products = current_account.products.includes(:categories)
                                .order(created_at: :desc)
    end

    def show
      @product_videos = @product.product_videos.order(:position)
      @case_studies = @product.case_studies.order(:position)
      @product_content = @product.product_content.order(:position)
    end

    def new
      @product = current_account.products.build
      @categories = Category.leaf_categories.includes(:parent).alphabetical
      @certifications = Certification.alphabetical
    end

    def create
      @product = current_account.products.build(product_params)

      if @product.save
        redirect_to vendors_product_path(@product), notice: 'Product was successfully created.'
      else
        @categories = Category.leaf_categories.includes(:parent).alphabetical
        @certifications = Certification.alphabetical
        render :new, status: :unprocessable_entity
      end
    end

    def edit
      @categories = Category.leaf_categories.includes(:parent).alphabetical
      @certifications = Certification.alphabetical
    end

    def update
      if @product.update(product_params)
        redirect_to vendors_product_path(@product), notice: 'Product was successfully updated.'
      else
        @categories = Category.leaf_categories.includes(:parent).alphabetical
        @certifications = Certification.alphabetical
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @product.destroy!
      redirect_to vendors_products_path, notice: 'Product was successfully deleted.'
    end

    def analytics
      @analytics = @product.product_analytics.includes(:user)
                          .order(created_at: :desc)
                          .limit(1000)  # Limit for performance
      
      @analytics_summary = ProductAnalytic.analytics_summary_for_product(@product)
      @total_page_views = ProductAnalytic.total_page_views_for_product(@product)
      @unique_visitors = ProductAnalytic.unique_visitors_for_product(@product)
    end

    def features
      @product_features = @product.product_features.ordered
    end

    def customers
      @product_customers = @product.product_customers.ordered
    end

    def videos
      @product_videos = @product.product_videos.ordered
    end

    def screenshots
      @product_screenshots = @product.product_screenshots.ordered
    end

    def case_studies
      @case_studies = @product.case_studies.ordered
    end

    def content
      @product_contents = @product.product_content.ordered
    end

    def contracts
      @product_contracts = @product.product_contracts.ordered
    end

    def visibility
      # This will show the visibility settings for the product
    end

    def manage_featured_video
      # This will show the featured video management page
    end

    def update_featured_video
      if params[:product][:featured_video].present?
        if @product.update(featured_video: params[:product][:featured_video])
          # Trigger video compression directly since we know the video changed
          VideoCompressionJob.perform_later(
            @product.class.name,
            @product.id,
            'featured_video'
          )
          redirect_to manage_featured_video_vendors_product_path(@product), notice: 'Featured video was successfully uploaded and is being processed.'
        else
          redirect_to manage_featured_video_vendors_product_path(@product), alert: 'Failed to upload featured video. Please try again.'
        end
      else
        redirect_to manage_featured_video_vendors_product_path(@product), alert: 'Please select a video file to upload.'
      end
    end

    def remove_featured_video
      if @product.featured_video.attached?
        # Remove the attachment records from the database
        @product.featured_video.attachment&.destroy
        @product.featured_video_preview.attachment&.destroy if @product.featured_video_preview.attached?
        
        # Reload the product to reflect the changes
        @product.reload
        
        Rails.logger.info "Successfully removed featured video references for Product##{@product.id}"
        redirect_to manage_featured_video_vendors_product_path(@product), notice: 'Featured video was successfully removed.'
      else
        redirect_to manage_featured_video_vendors_product_path(@product), alert: 'No featured video to remove.'
      end
    end

    private

    def set_product
      @product = current_account.products.friendly.find(params[:id])
    end

    def product_params
      params.require(:product).permit(
        :name, :description, :website, :published, :logo, :featured_video, 
        :pricing_model, :pricing,
        :show_featured_video, :show_product_videos, :show_product_screenshots, 
        :show_product_features, :show_product_customers, :show_product_content, 
        :show_case_studies,
        category_ids: [], certification_ids: []
      )
    end
  end
end