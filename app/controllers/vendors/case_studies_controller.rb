module Vendors
  class CaseStudiesController < BaseController
    before_action :set_product
    before_action :set_case_study, only: [:show, :edit, :update, :destroy, :update_position]
    before_action :ensure_owner

    def index
      @case_studies = @product.case_studies.by_position.with_attached_upload
    end

    def show
    end

    def new
      @case_study = @product.case_studies.build
      set_next_position
    end

    def create
      @case_study = @product.case_studies.build(case_study_params)
      set_next_position unless @case_study.position

      if @case_study.save
        redirect_to vendors_product_path(@product, anchor: 'case_studies'), notice: 'Case study was successfully added.'
      else
        render :new, status: :unprocessable_entity
      end
    end

    def edit
    end

    def update
      if @case_study.update(case_study_params)
        redirect_to vendors_product_path(@product, anchor: 'case_studies'), notice: 'Case study was successfully updated.'
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @case_study.destroy!
      redirect_to vendors_product_path(@product, anchor: 'case_studies'), notice: 'Case study was successfully deleted.'
    end

    def update_position
      new_position = params[:position].to_i
      
      # Reorder other case studies to make room for the moved case study
      CaseStudy.transaction do
        # Get all case studies for this product in current order
        case_studies = @product.case_studies.order(:position)
        
        # Remove the case study being moved from its current position
        case_studies_without_moved = case_studies.reject { |cs| cs.id == @case_study.id }
        
        # Insert the moved case study at the new position
        case_studies_without_moved.insert(new_position - 1, @case_study)
        
        # Update positions for all case studies
        case_studies_without_moved.each_with_index do |case_study, index|
          case_study.update!(position: index + 1)
        end
      end
      
      render json: { status: 'success' }
    rescue => e
      render json: { status: 'error', errors: [e.message] }
    end

    private

    def set_product
      @product = current_account.products.friendly.find(params[:product_id])
    end

    def set_case_study
      @case_study = @product.case_studies.find(params[:id])
    end

    def ensure_owner
      redirect_to root_path unless @product.account == current_account
    end

    def set_next_position
      @case_study.position = (@product.case_studies.maximum(:position) || 0) + 1
    end

    def case_study_params
      params.require(:case_study).permit(:title, :description, :published, :upload)
    end
  end
end