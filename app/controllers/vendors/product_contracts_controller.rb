class Vendors::ProductContractsController < Vendors::BaseController
  before_action :set_product
  before_action :set_product_contract, only: [:show, :edit, :update, :destroy, :update_position]

  def index
    @product_contracts = @product.product_contracts.ordered
  end

  def show
  end

  def new
    @product_contract = @product.product_contracts.build
  end

  def create
    @product_contract = @product.product_contracts.build(product_contract_params)
    
    if @product_contract.save
      redirect_to vendors_product_path(@product, anchor: 'contracts'), 
                  notice: 'Contract was successfully created.'
    else
      render :new
    end
  end

  def edit
  end

  def update
    if @product_contract.update(product_contract_params)
      redirect_to vendors_product_path(@product, anchor: 'contracts'), 
                  notice: 'Contract was successfully updated.'
    else
      render :edit
    end
  end

  def destroy
    @product_contract.destroy
    redirect_to vendors_product_path(@product, anchor: 'contracts'), 
                notice: 'Contract was successfully deleted.'
  end

  def update_position
    @product_contract.update!(position: params[:position])
    render json: { success: true }
  end

  private

  def set_product
    @product = current_account.products.friendly.find(params[:product_id])
  end

  def set_product_contract
    @product_contract = @product.product_contracts.find(params[:id])
  end

  def product_contract_params
    params.require(:product_contract).permit(:name, :description, :position, :published, :contract_file)
  end
end
