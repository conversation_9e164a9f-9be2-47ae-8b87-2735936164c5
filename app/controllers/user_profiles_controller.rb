class UserProfilesController < ApplicationController
  before_action :set_user
  
  layout 'dashboard'

  def show
    # User profile overview
  end

  def edit
    # Edit user profile
  end

  def update
    if @user.update(profile_params)
      redirect_to user_profile_path, notice: 'Profile updated successfully.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def update_password
    if params[:user][:password].blank?
      redirect_to edit_user_profile_path, alert: 'Password cannot be blank.'
    elsif @user.update(password_params)
      redirect_to user_profile_path, notice: 'Password updated successfully.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def update_profile_photo
    if @user.update(profile_photo_params)
      redirect_to user_profile_path, notice: 'Profile photo updated successfully.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def remove_profile_photo
    @user.profile_photo.purge if @user.profile_photo.attached?
    redirect_to user_profile_path, notice: 'Profile photo removed successfully.'
  end

  private

  def set_user
    @user = current_user
  end

  def profile_params
    params.require(:user).permit(:first_name, :last_name, :email_address, :phone, :job_title, :department)
  end

  def password_params
    params.require(:user).permit(:password, :password_confirmation)
  end

  def profile_photo_params
    params.require(:user).permit(:profile_photo)
  end
end