class AccountSettingsController < ApplicationController
  include Authentication
  
  before_action :require_authentication
  before_action :set_account
  before_action :require_account_access
  before_action :require_account_admin
  
  layout 'dashboard'

  def show
    # Account settings overview
  end

  def edit
    # Edit account information
  end

  def update
    if @account.update(account_params)
      redirect_to account_settings_path, notice: 'Account information updated successfully.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  private

  def set_account
    @account = current_account
  end

  def current_account
    @current_account ||= current_user.accounts.find_by(id: session[:account_id]) ||
                        current_user.accounts.first
  end

  def require_account_access
    unless @account && (@account.users.include?(current_user) || current_user.admin?)
      redirect_to root_path, alert: 'You do not have access to manage this account.'
    end
  end

  def account_params
    params.require(:account).permit(:name, :headquarters_location, :linkedin_url, :description, :logo)
  end
end