class ApplicationController < ActionController::Base
  include Authentication
  include SecurityHeaders
  include ErrorHandling
  include Pagy::Backend

  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  # allow_browser versions: :modern

  helper_method :current_user, :current_account, :current_user_account_admin?

  protected

  def current_user
    Current.user
  end

  # Helper method to deliver emails immediately in test, with background jobs in production
  def deliver_email(mailer)
    if Rails.env.test?
      mailer.deliver_now
    else
      mailer.deliver_later
    end
  end

  def current_account
    return @current_account if defined?(@current_account)
    
    @current_account = if session[:account_id]
      current_user&.accounts&.find_by(id: session[:account_id])
    else
      current_user&.accounts&.first
    end
  end

  def set_current_account(account)
    session[:account_id] = account&.id
    @current_account = account
  end

  def require_account
    unless current_account
      # If user has no accounts at all, redirect to setup page
      if current_user&.accounts&.empty?
        redirect_to orphaned_users_path
      else
        redirect_to root_path
      end
    end
  end

  def require_vendor_account
    if current_account&.vendor?
      # User has vendor account, continue
    elsif current_user&.accounts&.empty?
      # User has no accounts, redirect to setup
      redirect_to orphaned_users_path
    else
      # User has accounts but none are vendor
      redirect_to root_path
    end
  end

  def require_agency_account
    if current_account&.agency?
      # User has agency account, continue
    elsif current_user&.accounts&.empty?
      # User has no accounts, redirect to setup
      redirect_to orphaned_users_path
    else
      # User has accounts but none are agency
      redirect_to root_path
    end
  end

  def require_approved_account
    if current_account&.approved?
      # Account is approved, continue
    elsif current_user&.accounts&.empty?
      # User has no accounts, redirect to setup
      redirect_to orphaned_users_path
    else
      # User has accounts but none are approved
      redirect_to root_path
    end
  end

  def require_admin
    redirect_to root_path unless current_user&.admin?
  end

  def current_user_account_admin?
    return false unless current_user && current_account
    
    account_user = current_account.account_users.find_by(user: current_user)
    account_user&.role == "admin" || current_account.owner == current_user || current_user.admin?
  end

  def require_account_admin
    unless current_user_account_admin?
      redirect_to root_path, alert: 'You must be an account admin to access this page.'
    end
  end

end
