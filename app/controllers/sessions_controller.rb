class SessionsController < ApplicationController
  allow_unauthenticated_access only: %i[ new create ]
  rate_limit to: 10, within: 3.minutes, only: :create, with: -> { redirect_to new_session_url, alert: "Try again later." }

  def new
  end

  def create
    if user = User.authenticate_by(params.permit(:email_address, :password))
      # Super admin users bypass account checks
      if user.super_admin?
        start_new_session_for user
        redirect_to after_authentication_url
        return
      end
      
      # Check if user has any accounts at all
      user_accounts = user.accounts
      
      # If user has no accounts, redirect to orphaned users page
      if user_accounts.empty?
        start_new_session_for user
        redirect_to orphaned_users_path
        return
      end
      
      # Check account status: approved, confirmed, or both
      approved_accounts = user_accounts.select(&:approved?)
      confirmed_accounts = user_accounts.select { |account| account.confirmed_at.present? }
      approved_and_confirmed = approved_accounts.select { |account| account.confirmed_at.present? }
      
      if approved_and_confirmed.any?
        # User has accounts that are both approved and confirmed - proceed with login
        start_new_session_for user
        
        # Check if there's a pending invitation to accept
        if session[:invitation_token]
          invitation = TeamInvitation.find_by(token: session[:invitation_token])
          if invitation && invitation.status == "pending" && !invitation.expired?
            if invitation.accept!(user)
              session.delete(:invitation_token)
              redirect_to team_members_account_path, notice: "Welcome to the team! You've successfully joined #{invitation.account.name}."
              return
            else
              session.delete(:invitation_token)
              redirect_to after_authentication_url, alert: "Unable to accept invitation. You may already be a member of this account."
              return
            end
          else
            session.delete(:invitation_token)
          end
        end
        
        # Grant access to dashboard
        redirect_to after_authentication_url
      elsif approved_accounts.any? && confirmed_accounts.empty?
        # User has approved accounts but none are confirmed yet
        redirect_to pending_path, alert: "Your account is approved but needs to be confirmed. Please check your email for a confirmation link."
      elsif confirmed_accounts.any? && approved_accounts.empty?
        # User has confirmed accounts but none are approved yet
        redirect_to pending_path, notice: "Your account is confirmed and waiting for admin approval."
      elsif confirmed_accounts.any? && approved_accounts.any?
        # User has some confirmed and some approved, but none that are both
        redirect_to pending_path, alert: "Your account needs to be both confirmed and approved. Please check your email for confirmation links."
      else
        # User exists but accounts are neither approved nor confirmed - redirect to pending page
        redirect_to pending_path, notice: "Your account is pending approval and confirmation."
      end
    else
      redirect_to new_session_path, alert: "Try another email address or password."
    end
  end

  def destroy
    terminate_session
    redirect_to new_session_path
  end
end
