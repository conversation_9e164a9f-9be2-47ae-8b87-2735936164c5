class AccountConfirmationsController < ApplicationController
  allow_unauthenticated_access only: [:show, :new, :create]
  
  def show
    @account = Account.find_by(confirmation_token: params[:token])

    if @account.nil?
      redirect_to root_path, alert: "Invalid confirmation link"
      return
    end

    if @account.confirmed_at.nil?
      @account.update!(confirmed_at: Time.current)
      @confirmation_success = true
    else
      @confirmation_success = false
    end
  end

  def new
    # Form to request new confirmation email
  end

  def create
    @account = current_user&.accounts&.find_by(id: params[:account_id])
    
    if @account.nil?
      redirect_to pending_path, alert: "Account not found."
      return
    end

    if @account.confirmed_at.present?
      redirect_to pending_path, notice: "Your account is already confirmed."
      return
    end

    # Generate new confirmation token and send email
    @account.update!(confirmation_token: SecureRandom.urlsafe_base64(32), confirmation_sent_at: Time.current)
    deliver_email(AccountMailer.confirmation_instructions(@account))
    
    redirect_to pending_path, notice: "Confirmation email sent! Please check your inbox."
  end
end
