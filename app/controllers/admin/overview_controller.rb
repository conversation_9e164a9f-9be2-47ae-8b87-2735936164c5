module Admin
  class OverviewController < BaseController
    def index
      # User Statistics
      @user_stats = {
        total_users: User.count,
        pending_approval: User.with_pending_accounts.count,
        approved_users: User.with_approved_accounts.count,
        recent_signups: User.where('created_at >= ?', 7.days.ago).count,
        super_admins: User.where(super_admin: true).count
      }

      # Account Statistics  
      @account_stats = {
        total_accounts: Account.count,
        vendor_accounts: Account.where(account_type: 'vendor').count,
        agency_accounts: Account.where(account_type: 'agency').count,
        approved_accounts: Account.where(status: 'approved').count,
        pending_accounts: Account.where(status: 'pending').count,
        rejected_accounts: Account.where(status: 'rejected').count
      }

      # Product & Company Statistics
      @content_stats = {
        total_products: Product.count,
        published_products: Product.where(published: true).count,
        draft_products: Product.where(published: false).count,
        total_companies: Account.where(account_type: 'vendor').count,
        approved_companies: Account.approved.where(account_type: 'vendor').count,
        total_case_studies: CaseStudy.count,
        published_case_studies: CaseStudy.where(published: true).count
      }

      # Lead & Interest Statistics
      @engagement_stats = {
        total_leads: Lead.count,
        pending_leads: Lead.where(status: 'pending').count,
        contacted_leads: Lead.where(status: 'contacted').count,
        qualified_leads: Lead.where(status: 'qualified').count,
        recent_leads: Lead.where('created_at >= ?', 7.days.ago).count
      }

      # Recent Activity
      @recent_users = User.order(created_at: :desc).limit(5)
      @recent_accounts = Account.order(created_at: :desc).limit(5)
      @recent_products = Product.includes(:account).order(created_at: :desc).limit(5)
      @recent_leads = Lead.includes(:product, :account, :agency_account)
                         .order(created_at: :desc).limit(5)

      # Growth Analytics (last 30 days)
      @growth_data = {
        users: daily_counts(User, 30),
        accounts: daily_counts(Account, 30),
        products: daily_counts(Product, 30),
        leads: daily_counts(Lead, 30)
      }

      # System Health
      @system_health = {
        database_size: calculate_database_size,
        uptime: calculate_uptime,
        last_backup: 'N/A', # Placeholder for backup system
        active_sessions: Session.count,
        failed_jobs: 0 # Placeholder for job monitoring
      }
    end

    private

    def daily_counts(model, days)
      (0...days).map do |days_ago|
        date = days_ago.days.ago.to_date
        {
          date: date,
          count: model.where(created_at: date.beginning_of_day..date.end_of_day).count
        }
      end.reverse
    end

    def calculate_database_size
      result = ActiveRecord::Base.connection.execute("
        SELECT page_count * page_size as size 
        FROM pragma_page_count(), pragma_page_size()
      ").first
      
      size_bytes = result['size'] || 0
      format_bytes(size_bytes)
    rescue => e
      "Unknown"
    end

    def calculate_uptime
      boot_time = Rails.application.config.boot_time rescue Time.current
      uptime_seconds = Time.current - boot_time
      format_duration(uptime_seconds)
    end

    def format_bytes(bytes)
      units = ['B', 'KB', 'MB', 'GB', 'TB']
      return '0 B' if bytes == 0
      
      exp = (Math.log(bytes) / Math.log(1024)).floor
      formatted = (bytes.to_f / (1024 ** exp)).round(2)
      "#{formatted} #{units[exp]}"
    end

    def format_duration(seconds)
      days = (seconds / 86400).floor
      hours = ((seconds % 86400) / 3600).floor
      minutes = ((seconds % 3600) / 60).floor
      
      if days > 0
        "#{days}d #{hours}h"
      elsif hours > 0
        "#{hours}h #{minutes}m"
      else
        "#{minutes}m"
      end
    end
  end
end