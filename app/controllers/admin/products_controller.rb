module Admin
  class ProductsController < BaseController
    before_action :set_product, only: [:show, :edit, :update, :destroy]

    def index
      @products = Product.includes(:account, :categories)
                         .order(created_at: :desc)
      
      # Search functionality
      if params[:search].present?
        search_term = "%#{params[:search]}%"
        @products = @products.joins(:account).where(
          "products.name LIKE ? OR products.description LIKE ? OR accounts.name LIKE ?",
          search_term, search_term, search_term
        )
      end
      
      # Filter by published status if specified
      @products = @products.where(published: params[:published]) if params[:published].present?
      
      
      
      # Pagination
      @pagy, @products = pagy(@products)
    end

    def show
    end

    def new
      @product = Product.new
      @accounts = Account.order(:name)
      @categories = Category.leaf_categories.includes(:parent).order(:name)
      @certifications = Certification.alphabetical
    end

    def create
      @product = Product.new(product_params)

      if @product.save
        redirect_to admin_product_path(@product), notice: 'Product was successfully created.'
      else
        @accounts = Account.order(:name)
        @categories = Category.leaf_categories.includes(:parent).order(:name)
        @certifications = Certification.alphabetical
        render :new, status: :unprocessable_entity
      end
    end

    def edit
      @accounts = Account.order(:name)
      @categories = Category.leaf_categories.includes(:parent).order(:name)
      @certifications = Certification.alphabetical
    end

    def update
      if @product.update(product_params)
        redirect_to admin_product_path(@product), notice: 'Product was successfully updated.'
      else
        @accounts = Account.order(:name)
        @categories = Category.leaf_categories.includes(:parent).order(:name)
        @certifications = Certification.alphabetical
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @product.destroy!
      redirect_to admin_products_path, notice: 'Product was successfully deleted.'
    end

    private

    def set_product
      @product = Product.friendly.find(params[:id])
    end

    def product_params
      params.require(:product).permit(
        :name, :description, :website, :account_id, :published, :logo, :featured_video, 
        :pricing_model, :pricing, category_ids: [], certification_ids: []
      )
    end
  end
end