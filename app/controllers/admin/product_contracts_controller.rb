class Admin::ProductContractsController < Admin::BaseController
  before_action :set_product
  before_action :set_product_contract, only: [:show, :edit, :update, :destroy, :update_position]

  def index
    @product_contracts = @product.product_contracts.order(:position)
  end

  def show
  end

  def new
    @product_contract = @product.product_contracts.build
  end

  def create
    @product_contract = @product.product_contracts.build(product_contract_params)
    
    if @product_contract.save
      redirect_to admin_product_product_contract_path(@product, @product_contract), 
                  notice: 'Contract was successfully created.'
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @product_contract.update(product_contract_params)
      redirect_to admin_product_product_contract_path(@product, @product_contract), 
                  notice: 'Contract was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @product_contract.destroy!
    redirect_to admin_product_product_contracts_path(@product), 
                notice: 'Contract was successfully deleted.'
  end

  def update_position
    new_position = params[:position].to_i
    @product_contract.update!(position: new_position)
    
    # Reorder other contracts
    @product.product_contracts.where.not(id: @product_contract.id)
             .where(position: new_position..)
             .update_all('position = position + 1')
    
    head :ok
  end

  private

  def set_product
    @product = Product.find(params[:product_id])
  end

  def set_product_contract
    @product_contract = @product.product_contracts.find(params[:id])
  end

  def product_contract_params
    params.require(:product_contract).permit(:name, :description, :contract_file, :published)
  end
end