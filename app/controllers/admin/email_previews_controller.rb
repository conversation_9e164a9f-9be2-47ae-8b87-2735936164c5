module Admin
  class EmailPreviewsController < BaseController
    def index
      @email_previews = available_previews
    end

    def show
      # Handle different naming patterns
      preview_class_name = case params[:id]
      when 'passwords_mailer'
        'PasswordsMailerPreview'
      else
        params[:id].camelize + 'Preview'
      end
      
      begin
        preview_class = preview_class_name.constantize
        @preview = preview_class.new
        @email_method = params[:email_method] || preview_class.emails.first
        
        if @preview.respond_to?(@email_method)
          @email = @preview.public_send(@email_method)
          
          # Handle different response formats
          respond_to do |format|
            format.html do
              if params[:part] == 'text'
                render plain: @email.text_part&.body || @email.body
              else
                render html: (@email.html_part&.body || @email.body).to_s.html_safe
              end
            end
            format.json do
              render json: {
                subject: @email.subject,
                to: @email.to,
                from: @email.from,
                html: @email.html_part&.body&.to_s,
                text: @email.text_part&.body&.to_s || @email.body.to_s
              }
            end
          end
        else
          redirect_to admin_email_previews_path, alert: "Email method '#{@email_method}' not found"
        end
      rescue NameError
        redirect_to admin_email_previews_path, alert: "Email preview '#{preview_class_name}' not found"
      rescue => e
        redirect_to admin_email_previews_path, alert: "Error loading email preview: #{e.message}"
      end
    end

    private

    def available_previews
      previews = []
      
      # Scan for preview classes
      Dir[Rails.root.join('test/mailers/previews/*.rb'), Rails.root.join('spec/mailers/previews/*.rb')].each do |file|
        class_name = File.basename(file, '.rb').camelize
        begin
          klass = class_name.constantize
          if klass.respond_to?(:emails)
            previews << {
              name: class_name.gsub(/Preview$/, ''),
              class_name: class_name,
              emails: klass.emails,
              description: klass.try(:description) || "Email previews for #{class_name.gsub(/Preview$/, '')}"
            }
          end
        rescue => e
          Rails.logger.warn "Could not load email preview class #{class_name}: #{e.message}"
        end
      end

      previews.sort_by { |p| p[:name] }
    end
  end
end