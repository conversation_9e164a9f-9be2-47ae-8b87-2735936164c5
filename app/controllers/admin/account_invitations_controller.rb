module Admin
  class AccountInvitationsController < BaseController
    before_action :set_invitation, only: [:show, :destroy, :resend]

    def index
      @invitations = AccountInvitation.includes(:invited_by)
                                    .order(created_at: :desc)
      
      # Filter by status if specified
      if params[:status].present? && %w[pending accepted expired].include?(params[:status])
        @invitations = @invitations.where(status: params[:status])
      end
      
      # Search functionality
      if params[:search].present?
        search_term = "%#{params[:search]}%"
        @invitations = @invitations.where(
          "email LIKE ? OR account_name LIKE ? OR first_name LIKE ? OR last_name LIKE ?",
          search_term, search_term, search_term, search_term
        )
      end
    end

    def show
    end

    def new
      @invitation = AccountInvitation.new
    end

    def create
      @invitation = AccountInvitation.new(invitation_params)
      @invitation.invited_by = current_user

      if @invitation.save
        # Send invitation email
        AccountInvitationMailer.invitation_email(@invitation).deliver_now
        redirect_to admin_account_invitations_path, notice: 'Account invitation sent successfully.'
      else
        render :new, status: :unprocessable_entity
      end
    end

    def destroy
      @invitation.destroy
      redirect_to admin_account_invitations_path, notice: 'Invitation deleted successfully.'
    end

    def resend
      if @invitation.pending? && !@invitation.expired?
        AccountInvitationMailer.invitation_email(@invitation).deliver_now
        redirect_to admin_account_invitations_path, notice: 'Invitation resent successfully.'
      else
        redirect_to admin_account_invitations_path, alert: 'Cannot resend expired or accepted invitation.'
      end
    end

    private

    def set_invitation
      @invitation = AccountInvitation.find(params[:id])
    end

    def invitation_params
      params.require(:account_invitation).permit(
        :email, :account_type, :account_name, :first_name, :last_name, :job_title, :phone, :department
      )
    end
  end
end