module Admin
  class ProductScreenshotsController < BaseController
    before_action :set_product
    before_action :set_product_screenshot, only: [:show, :edit, :update, :destroy, :update_position]
    
    def index
      @product_screenshots = @product.product_screenshots.ordered
    end
    
    def show
    end
    
    def new
      @product_screenshot = @product.product_screenshots.build
    end
    
    def create
      @product_screenshot = @product.product_screenshots.build(product_screenshot_params)
      
      if @product_screenshot.save
        redirect_to admin_product_product_screenshots_path(@product), 
                    notice: 'Screenshot was successfully created.'
      else
        render :new, status: :unprocessable_entity
      end
    end
    
    def edit
    end
    
    def update
      if @product_screenshot.update(product_screenshot_params)
        redirect_to admin_product_product_screenshots_path(@product), 
                    notice: 'Screenshot was successfully updated.'
      else
        render :edit, status: :unprocessable_entity
      end
    end
    
    def destroy
      @product_screenshot.destroy
      redirect_to admin_product_product_screenshots_path(@product), 
                  notice: 'Screenshot was successfully deleted.'
    end
    
    def update_position
      @product_screenshot.update(position: params[:position])
      head :ok
    end
    
    private
    
    def set_product
      @product = Product.friendly.find(params[:product_id])
    end
    
    def set_product_screenshot
      @product_screenshot = @product.product_screenshots.find(params[:id])
    end
    
    def product_screenshot_params
      params.require(:product_screenshot).permit(:title, :description, :position, :image)
    end
  end
end