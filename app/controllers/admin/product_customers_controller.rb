class Admin::ProductCustomersController < Admin::BaseController
  before_action :set_product
  before_action :set_product_customer, only: [:show, :edit, :update, :destroy, :update_position]

  def index
    @product_customers = @product.product_customers.ordered
  end

  def show
  end

  def new
    @product_customer = @product.product_customers.build
  end

  def create
    @product_customer = @product.product_customers.build(product_customer_params)

    if @product_customer.save
      redirect_to admin_product_product_customers_path(@product), notice: 'Product customer was successfully created.'
    else
      render :new
    end
  end

  def edit
  end

  def update
    if @product_customer.update(product_customer_params)
      redirect_to admin_product_product_customers_path(@product), notice: 'Product customer was successfully updated.'
    else
      render :edit
    end
  end

  def destroy
    @product_customer.destroy
    redirect_to admin_product_product_customers_path(@product), notice: 'Product customer was successfully deleted.'
  end

  def update_position
    @product_customer.update!(position: params[:position])
    head :ok
  end

  private

  def set_product
    @product = Product.friendly.find(params[:product_id])
  end

  def set_product_customer
    @product_customer = @product.product_customers.find(params[:id])
  end

  def product_customer_params
    params.require(:product_customer).permit(:name, :description, :position, :logo)
  end
end