module Admin
  class AccountsController < BaseController
    before_action :set_account, only: [:show, :edit, :update, :destroy, :approve, :reject, :team_members, :new_team_member, :create_team_member, :edit_team_member, :update_team_member, :destroy_team_member, :new_team_invitation, :create_team_invitation, :destroy_team_invitation, :add_existing_user]
    before_action :set_team_member, only: [:edit_team_member, :update_team_member, :destroy_team_member]
    before_action :set_team_invitation, only: [:destroy_team_invitation]

    def index
      @accounts = Account.includes(:owner, :approved_by)
                        .order(created_at: :desc)
      
      # Search functionality
      if params[:search].present?
        search_term = "%#{params[:search]}%"
        @accounts = @accounts.joins(:owner).where(
          "accounts.name LIKE ? OR users.first_name LIKE ? OR users.last_name LIKE ? OR users.email_address LIKE ?",
          search_term, search_term, search_term, search_term
        )
      end
      
      # Filter by status if specified
      @accounts = @accounts.where(status: params[:status]) if params[:status].present?
      
      # Filter by account type if specified  
      @accounts = @accounts.where(account_type: params[:account_type]) if params[:account_type].present?
      
      # Pagination
      @pagy, @accounts = pagy(@accounts)
      
    end

    def show
    end

    def edit
    end

    def update
      if @account.update(account_params)
        redirect_to admin_account_path(@account), notice: 'Account was successfully updated.'
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @account.destroy!
      redirect_to admin_accounts_path, notice: 'Account was successfully deleted.'
    end

    def approve
      @account.approve!(current_user)
      redirect_to admin_accounts_path, notice: "#{@account.name} has been approved."
    end

    def reject
      @account.reject!(current_user)
      redirect_to admin_accounts_path, notice: "#{@account.name} has been rejected."
    end

    # Team Management Actions
    def team_members
      @team_members = @account.account_users.includes(:user).order(:role, :joined_at)
      @pending_invitations = TeamInvitation.where(account: @account, status: 'pending')
      @users = User.all.order(:first_name, :last_name) # For adding existing users
    end

    def new_team_member
      @user = User.new
      @account_user = AccountUser.new
    end

    def create_team_member
      @user = User.new(user_params)
      @account_user = AccountUser.new(account_user_params)
      
      ActiveRecord::Base.transaction do
        # Generate random password if not provided
        if params[:user][:password].blank?
          generated_password = SecureRandom.alphanumeric(12)
          @user.password = generated_password
          @user.password_confirmation = generated_password
        end
        
        if @user.save
          @account_user.user = @user
          @account_user.account = @account
          @account_user.joined_at = Time.current
          
          if @account_user.save
            redirect_to team_members_admin_account_path(@account), 
                       notice: "Team member #{@user.full_name} created successfully."
          else
            @user.destroy # Rollback user creation if account_user fails
            render :new_team_member, status: :unprocessable_entity
          end
        else
          render :new_team_member, status: :unprocessable_entity
        end
      end
    rescue ActiveRecord::RecordInvalid
      render :new_team_member, status: :unprocessable_entity
    end

    def edit_team_member
      @user = @team_member.user
    end

    def update_team_member
      @user = @team_member.user
      
      if params[:user].present? && @user.update(user_update_params)
        if params[:account_user].present? && @team_member.update(account_user_params)
          redirect_to team_members_admin_account_path(@account), 
                     notice: "Team member #{@user.full_name} updated successfully."
        else
          render :edit_team_member, status: :unprocessable_entity
        end
      else
        render :edit_team_member, status: :unprocessable_entity
      end
    end

    def destroy_team_member
      if @team_member.user == @account.owner
        redirect_to team_members_admin_account_path(@account), 
                   alert: 'Cannot remove the account owner.'
        return
      end

      @team_member.destroy!
      redirect_to team_members_admin_account_path(@account), 
                 notice: 'Team member removed successfully.'
    end

    def new_team_invitation
      @team_invitation = TeamInvitation.new
    end

    def create_team_invitation
      @team_invitation = @account.team_invitations.build(invitation_params)
      @team_invitation.invited_by = current_user
      @team_invitation.token = SecureRandom.urlsafe_base64(32)
      @team_invitation.expires_at = 7.days.from_now

      if @team_invitation.save
        # Send invitation email
        deliver_email(TeamInvitationMailer.invitation_email(@team_invitation))
        redirect_to team_members_admin_account_path(@account), 
                   notice: "Team invitation sent to #{@team_invitation.email}."
      else
        render :new_team_invitation, status: :unprocessable_entity
      end
    end

    def destroy_team_invitation
      @team_invitation.destroy!
      redirect_to team_members_admin_account_path(@account), 
                 notice: "Invitation cancelled successfully."
    end

    # Add existing user to account
    def add_existing_user
      user = User.find(params[:user_id])
      account_user = @account.account_users.build(
        user: user,
        role: params[:role] || 'member',
        joined_at: Time.current
      )
      
      if account_user.save
        redirect_to team_members_admin_account_path(@account), 
                   notice: "#{user.full_name} added to team successfully."
      else
        redirect_to team_members_admin_account_path(@account), 
                   alert: "Error adding user to team: #{account_user.errors.full_messages.join(', ')}"
      end
    end

    private

    def set_account
      @account = Account.friendly.find(params[:id])
    end

    def account_params
      params.require(:account).permit(:name, :account_type, :status)
    end

    def set_team_member
      @team_member = @account.account_users.find(params[:team_member_id] || params[:id])
    rescue ActiveRecord::RecordNotFound
      redirect_to team_members_admin_account_path(@account), alert: 'Team member not found.'
    end

    def set_team_invitation
      @team_invitation = @account.team_invitations.find(params[:invitation_id] || params[:id])
    rescue ActiveRecord::RecordNotFound
      redirect_to team_members_admin_account_path(@account), alert: 'Invitation not found.'
    end

    def user_params
      params.require(:user).permit(:first_name, :last_name, :email_address, :phone, :job_title, :department, :password, :password_confirmation)
    end

    def user_update_params
      params.require(:user).permit(:first_name, :last_name, :email_address, :phone, :job_title, :department)
    end

    def account_user_params
      params.require(:account_user).permit(:role)
    end

    def invitation_params
      params.require(:team_invitation).permit(:email, :role, :message)
    end
  end
end