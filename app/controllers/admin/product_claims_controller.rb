class Admin::ProductClaimsController < Admin::BaseController
  before_action :set_product_claim, only: [:show]

  def index
    @product_claims = ProductClaim.includes(:product)
                                  .order(created_at: :desc)
    
    # Filter by status if provided
    if params[:status].present? && ProductClaim.statuses.key?(params[:status])
      @product_claims = @product_claims.where(status: params[:status])
    end
    
    # Pagination
    @pagy, @product_claims = pagy(@product_claims)
  end

  def show
  end

  private

  def set_product_claim
    @product_claim = ProductClaim.find(params[:id])
  end
end