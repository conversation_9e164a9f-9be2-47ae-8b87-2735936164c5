module Admin
  class ListsController < BaseController
    before_action :set_list, only: [:show, :destroy]
    
    def index
      @lists = List.includes(:account, :list_items, :products)
                   .joins(:account)
                   .order('accounts.name, lists.name')
      
      # Filter by account type if needed
      if params[:account_type].present?
        @lists = @lists.where(accounts: { account_type: params[:account_type] })
      end
    end
    
    def show
      @list_items = @list.list_items.includes(:product)
    end
    
    def destroy
      account_name = @list.account.name
      list_name = @list.name
      @list.destroy
      redirect_to admin_lists_path, notice: "List '#{list_name}' from #{account_name} was successfully deleted."
    end
    
    private
    
    def set_list
      @list = List.find(params[:id])
    end
  end
end