module Admin
  class CertificationsController < BaseController
    before_action :set_certification, only: [:show, :edit, :update, :destroy]

    def index
      @certifications = Certification.includes(:products).alphabetical
      
      # Search functionality
      if params[:search].present?
        search_term = "%#{params[:search]}%"
        @certifications = @certifications.where("name LIKE ? OR description LIKE ?", search_term, search_term)
      end
      
      @pagy, @certifications = pagy(@certifications)
    end

    def show
    end

    def new
      @certification = Certification.new
    end

    def create
      @certification = Certification.new(certification_params)

      if @certification.save
        redirect_to admin_certification_path(@certification), notice: 'Certification was successfully created.'
      else
        render :new, status: :unprocessable_entity
      end
    end

    def edit
    end

    def update
      if @certification.update(certification_params)
        redirect_to admin_certification_path(@certification), notice: 'Certification was successfully updated.'
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      if @certification.products.any?
        redirect_to admin_certifications_path, alert: 'Cannot delete certification with associated products.'
      else
        @certification.destroy!
        redirect_to admin_certifications_path, notice: 'Certification was successfully deleted.'
      end
    end

    private

    def set_certification
      @certification = Certification.find(params[:id])
    end

    def certification_params
      params.require(:certification).permit(:name, :description)
    end
  end
end