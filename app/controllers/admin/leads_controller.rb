class Admin::LeadsController < Admin::BaseController
  before_action :set_product, if: -> { params[:product_id].present? }
  before_action :set_lead, only: [:show]

  def index
    if @product
      # Nested route: show leads for specific product
      @leads = @product.leads.includes(:product, :account).order(created_at: :desc)
    else
      # Direct route: show all leads across all products
      @leads = Lead.includes(:product, :account).order(created_at: :desc)
      
      # Add pagination and filtering
      @leads = @leads.where("contact_name ILIKE ? OR contact_email ILIKE ? OR contact_company ILIKE ?", 
                           "%#{params[:search]}%", "%#{params[:search]}%", "%#{params[:search]}%") if params[:search].present?
      @leads = @leads.where(status: params[:status]) if params[:status].present?
      
      # Pagination
      @pagy, @leads = pagy(@leads, items: 25)
    end
  end

  def show
  end

  private

  def set_product
    @product = Product.friendly.find(params[:product_id])
  end

  def set_lead
    if @product
      @lead = @product.leads.find(params[:id])
    else
      @lead = Lead.find(params[:id])
      @product = @lead.product  # Set product for show view
    end
  end
end