class Admin::CategoriesController < Admin::BaseController
  before_action :set_category, only: [:show, :edit, :update, :destroy]

  def index
    # Get all categories for building proper hierarchy
    all_categories = Category.includes(:parent, :subcategories, :products).alphabetical
    
    # Apply search filter
    if params[:search].present?
      search_term = "%#{params[:search]}%"
      all_categories = all_categories.where("LOWER(name) LIKE LOWER(?)", search_term)
    end
    
    # Build hierarchical display data
    @hierarchical_categories = build_category_hierarchy(all_categories)
  end

  def show
  end

  def new
    @category = Category.new
    @available_categories = Category.alphabetical
    @grouped_categories = build_grouped_categories(@available_categories)
  end

  def create
    @category = Category.new(category_params)

    if @category.save
      redirect_to admin_categories_path, notice: 'Category was successfully created.'
    else
      @available_categories = Category.alphabetical
      @grouped_categories = build_grouped_categories(@available_categories)
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    # Exclude current category and its descendants to prevent circular references
    excluded_ids = [@category.id] + @category.descendants.pluck(:id)
    @available_categories = Category.where.not(id: excluded_ids).alphabetical
    @grouped_categories = build_grouped_categories(@available_categories)
  end

  def update
    if @category.update(category_params)
      redirect_to admin_categories_path, notice: 'Category was successfully updated.'
    else
      # Exclude current category and its descendants to prevent circular references
      excluded_ids = [@category.id] + @category.descendants.pluck(:id)
      @available_categories = Category.where.not(id: excluded_ids).alphabetical
      @grouped_categories = build_grouped_categories(@available_categories)
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    if @category.products.any? || @category.subcategories.any?
      redirect_to admin_categories_path, alert: 'Cannot delete category with associated products or subcategories.'
    else
      @category.destroy
      redirect_to admin_categories_path, notice: 'Category was successfully deleted.'
    end
  end

  private

  def set_category
    @category = Category.friendly.find(params[:id])
  end

  def category_params
    params.require(:category).permit(:name, :description, :parent_id)
  end

  def build_category_hierarchy(categories)
    # Convert to array and sort by hierarchy
    categories_array = categories.to_a
    hierarchical = []
    
    # First, add all root categories
    root_categories = categories_array.select { |cat| cat.parent_id.nil? }
    root_categories.each do |root|
      hierarchical << { category: root, depth: 0 }
      add_children_to_hierarchy(root, categories_array, hierarchical, 1)
    end
    
    hierarchical
  end

  def add_children_to_hierarchy(parent, all_categories, hierarchical, depth)
    children = all_categories.select { |cat| cat.parent_id == parent.id }
    children.each do |child|
      hierarchical << { category: child, depth: depth }
      add_children_to_hierarchy(child, all_categories, hierarchical, depth + 1)
    end
  end

  def build_grouped_categories(categories)
    grouped = {}
    categories_array = categories.to_a
    
    # First, collect all root categories
    root_categories = categories_array.select { |cat| cat.parent_id.nil? }
    root_categories.each do |root|
      grouped[root.name] = []
      
      # Add all children of this root category
      children = categories_array.select { |cat| cat.parent_id == root.id }
      children.each do |child|
        grouped[root.name] << [child.name, child.id]
      end
      
      # If there are no children, add the root category itself as an option
      if grouped[root.name].empty?
        grouped[root.name] << [root.name, root.id]
      else
        # Add the root category as the first option
        grouped[root.name].unshift([root.name, root.id])
      end
    end
    
    grouped
  end
end