module Agencies
  class ListsController < BaseController
    before_action :set_list, only: [:show, :edit, :update, :destroy]
    before_action :ensure_agency_account
    
    def index
      @lists = current_account.lists.includes(:list_items, :products)
    end
    
    def show
      @list_items = @list.list_items.includes(:product)
    end
    
    def new
      @list = current_account.lists.build
    end
    
    def create
      @list = current_account.lists.build(list_params)
      
      respond_to do |format|
        if @list.save
          format.html { redirect_to agencies_list_path(@list), notice: 'List was successfully created.' }
          format.json { render json: { id: @list.id, name: @list.name, description: @list.description } }
        else
          format.html { render :new, status: :unprocessable_entity }
          format.json { render json: { errors: @list.errors.full_messages }, status: :unprocessable_entity }
        end
      end
    end
    
    def edit
    end
    
    def update
      if @list.update(list_params)
        redirect_to agencies_list_path(@list), notice: 'List was successfully updated.'
      else
        render :edit, status: :unprocessable_entity
      end
    end
    
    def destroy
      @list.destroy
      redirect_to agencies_lists_path, notice: 'List was successfully deleted.'
    end
    
    # AJAX endpoint to get lists for a specific product (for the modal)
    def lists_for_product
      @product = Product.find(params[:product_id])
      @lists = current_account.lists.includes(:list_items)
      
      render json: @lists.map { |list|
        {
          id: list.id,
          name: list.name,
          description: list.description,
          has_product: list.list_items.exists?(product: @product)
        }
      }
    end
    
    private
    
    def set_list
      @list = current_account.lists.find(params[:id])
    end
    
    def list_params
      params.require(:list).permit(:name, :description)
    end
    
    def ensure_agency_account
      unless current_account&.agency?
        redirect_to root_path, alert: 'Access denied. Lists are only available to agency accounts.'
      end
    end
  end
end