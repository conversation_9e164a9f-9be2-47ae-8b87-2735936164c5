module Agencies
  class ListItemsController < BaseController
    before_action :ensure_agency_account
    
    def create
      @list = current_account.lists.find(params[:list_id])
      @product = Product.find(params[:product_id])
      @list_item = @list.list_items.build(product: @product)
      
      respond_to do |format|
        if @list_item.save
          format.json { 
            render json: { 
              success: true, 
              message: "#{@product.name} has been added to #{@list.name}",
              list_name: @list.name
            } 
          }
        else
          format.json { 
            render json: { 
              success: false, 
              errors: @list_item.errors.full_messages 
            }, status: :unprocessable_entity 
          }
        end
      end
    end
    
    def destroy
      @list = current_account.lists.find(params[:list_id])
      @list_item = @list.list_items.find(params[:id])
      @list_item.destroy
      
      respond_to do |format|
        format.html { redirect_to agencies_list_path(@list), notice: 'Item removed from list.' }
        format.json { render json: { success: true } }
      end
    end
    
    private
    
    def ensure_agency_account
      unless current_account&.agency?
        respond_to do |format|
          format.html { redirect_to root_path, alert: 'Access denied.' }
          format.json { render json: { error: 'Access denied' }, status: :forbidden }
        end
      end
    end
  end
end