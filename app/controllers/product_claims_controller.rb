class ProductClaimsController < ApplicationController
  before_action :set_product

  def new
    @product_claim = ProductClaim.new
  end

  def create
    @product_claim = ProductClaim.new(product_claim_params)
    @product_claim.product = @product

    if @product_claim.save
      # Send email to admin
      AdminMailer.product_claim_notification(@product_claim).deliver_now
      
      redirect_to marketplace_product_path(@product), 
                  notice: "Thank you for your claim request! Our team will review it and get back to you soon."
    else
      render :new
    end
  end

  private

  def set_product
    @product = Product.friendly.find(params[:id])
  end

  def product_claim_params
    params.require(:product_claim).permit(:name, :email, :company, :title, :message)
  end
end