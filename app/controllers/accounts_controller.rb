class AccountsController < ApplicationController
  include Authentication
  
  before_action :require_authentication
  before_action :set_user
  before_action :set_account
  before_action :set_team_member, only: [:edit_team_member, :update_team_member, :update_team_member_role, :destroy_team_member]
  before_action :set_team_invitation, only: [:destroy_team_invitation]
  before_action :require_account_admin, only: [:show, :edit_account, :update_account, :update_password, :team_members, :edit_team_member, :update_team_member, :update_team_member_role, :destroy_team_member, :new_team_invitation, :create_team_invitation, :destroy_team_invitation]
  before_action :require_account_access, only: [:team_members, :edit_team_member, :update_team_member, :update_team_member_role, :destroy_team_member, :new_team_invitation, :create_team_invitation, :destroy_team_invitation]
  before_action :require_account_approval, only: [:new_team_invitation, :create_team_invitation, :edit_team_member, :update_team_member, :update_team_member_role, :destroy_team_member, :destroy_team_invitation]
  
  layout 'dashboard'

  def show
  end

  def edit_profile
  end

  def edit_account
  end

  def update_account
    if @account.update(account_params)
      redirect_to account_path, notice: 'Account information updated successfully.'
    else
      render :edit_account, status: :unprocessable_entity
    end
  end

  def update_profile
    if @user.update(profile_params)
      redirect_to account_path, notice: 'Profile updated successfully.'
    else
      render :edit_profile, status: :unprocessable_entity
    end
  end

  def update_password
    if params[:user][:password].blank?
      redirect_to edit_profile_account_path, alert: 'Password cannot be blank.'
    elsif @user.update(password_params)
      redirect_to account_path, notice: 'Password updated successfully.'
    else
      render :edit_profile, status: :unprocessable_entity
    end
  end

  def update_profile_photo
    if @user.update(profile_photo_params)
      redirect_to account_path, notice: 'Profile photo updated successfully.'
    else
      render :edit_profile, status: :unprocessable_entity
    end
  end

  def remove_profile_photo
    @user.profile_photo.purge if @user.profile_photo.attached?
    redirect_to account_path, notice: 'Profile photo removed successfully.'
  end

  # Team Management Actions
  def team_members
    @team_members = @account.account_users.includes(:user).order(:role, :joined_at)
    @pending_invitations = TeamInvitation.where(account: @account, status: 'pending')
  end

  def edit_team_member
    # Edit team member view
  end

  def update_team_member
    if @team_member.update(team_member_params)
      redirect_to team_members_account_path, notice: 'Team member updated successfully.'
    else
      render :edit_team_member, status: :unprocessable_entity
    end
  end

  def update_team_member_role
    if @team_member.update(role: params[:account_user][:role])
      redirect_to team_members_account_path, notice: 'Team member role updated successfully.'
    else
      render :edit_team_member, status: :unprocessable_entity
    end
  end

  def destroy_team_member
    if @team_member.user == @account.owner
      redirect_to team_members_account_path, alert: 'Cannot remove the account owner.'
      return
    end

    @team_member.destroy!
    redirect_to team_members_account_path, notice: 'Team member removed successfully.'
  end

  def new_team_invitation
    @team_invitation = TeamInvitation.new
  end

  def create_team_invitation
    @team_invitation = @account.team_invitations.build(invitation_params)
    @team_invitation.invited_by = current_user
    @team_invitation.token = SecureRandom.urlsafe_base64(32)
    @team_invitation.expires_at = 7.days.from_now

    if @team_invitation.save
      # Send invitation email
      deliver_email(TeamInvitationMailer.invitation_email(@team_invitation))
      redirect_to team_members_account_path, notice: "Team invitation sent successfully."
    else
      render :new_team_invitation, status: :unprocessable_entity
    end
  end

  def destroy_team_invitation
    @team_invitation.destroy!
    redirect_to team_members_account_path, notice: "Invitation cancelled successfully."
  end


  protected

  def current_account
    @current_account ||= current_user.accounts.find_by(id: session[:account_id]) ||
                        current_user.accounts.first
  end

  private

  def set_user
    @user = current_user
  end

  def set_account
    @account = current_account
  end

  def user_params
    params.require(:user).permit(:first_name, :last_name, :email_address, :phone, :job_title, :department, :password, :password_confirmation)
  end

  def profile_params
    params.require(:user).permit(:first_name, :last_name, :email_address, :phone, :job_title, :department)
  end

  def password_params
    params.require(:user).permit(:password, :password_confirmation)
  end

  def profile_photo_params
    params.require(:user).permit(:profile_photo)
  end

  def account_params
    params.require(:account).permit(:name, :headquarters_location, :linkedin_url, :description, :logo)
  end

  # Team management private methods
  def set_team_member
    @team_member = @account.account_users.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    redirect_to team_members_account_path, alert: 'Team member not found.'
  end

  def set_team_invitation
    @team_invitation = @account.team_invitations.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    redirect_to team_members_account_path, alert: 'Invitation not found.'
  end

  def require_account_access
    unless @account && (@account.users.include?(current_user) || current_user.admin?)
      redirect_to root_path, alert: 'You do not have access to manage this account\'s team.'
    end
  end

  def require_account_approval
    unless @account&.approved? || current_user.admin?
      redirect_to root_path, alert: 'Your account must be approved before you can manage team members.'
    end
  end


  def team_member_params
    params.require(:account_user).permit(:role)
  end

  def invitation_params
    params.require(:team_invitation).permit(:email, :role, :message)
  end
end