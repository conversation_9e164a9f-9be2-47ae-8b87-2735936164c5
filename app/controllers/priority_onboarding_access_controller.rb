class PriorityOnboardingAccessController < ApplicationController
  allow_unauthenticated_access

  def new
    @user = User.new
    @account = Account.new
  end

  def create
    @user = User.new(user_params)

    ActiveRecord::Base.transaction do
      if @user.save
        # Create account with auto-approval like admin invitation flow
        account_type = params[:account_type] == "agency" ? "agency" : "vendor"
        account_name = params[:user][:account_name].presence || "#{@user.full_name}'s #{account_type.capitalize} Account"
        
        @account = Account.create!(
          owner: @user,
          account_type: account_type,
          name: account_name,
          status: "approved",
          approved_at: Time.current,
          confirmed_at: Time.current
        )
        
        # Associate the user with the account
        @account.account_users.create!(user: @user, role: "admin")
        
        # Sign in the user
        start_new_session_for @user
        
        redirect_to after_authentication_url, notice: "Welcome to Platia! Your account has been successfully created and approved."
      else
        @account = Account.new
        render :new, status: :unprocessable_entity
      end
    end
  rescue ActiveRecord::RecordInvalid => e
    redirect_to root_path, alert: "There was an error creating your account. Please try again."
  end

  private

  def user_params
    params.require(:user).permit(:first_name, :last_name, :email_address, :password, :password_confirmation, :job_title, :department, :account_name)
  end
end
