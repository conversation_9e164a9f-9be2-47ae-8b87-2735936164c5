class TeamManagementController < ApplicationController
  before_action :set_account
  before_action :set_team_member, only: [:show_member, :edit_member, :update_member, :update_member_role, :destroy_member]
  before_action :set_team_invitation, only: [:destroy_invitation]
  before_action :require_account_access
  before_action :require_account_approval
  before_action :require_account_admin
  
  layout 'dashboard'

  def index
    @team_members = @account.account_users.includes(:user).order(:role, :joined_at)
    @pending_invitations = TeamInvitation.where(account: @account, status: 'pending')
  end

  def show_member
    # Show team member details
  end

  def edit_member
    # Edit team member view
  end

  def update_member
    if @team_member.update(team_member_params)
      redirect_to team_management_index_path, notice: 'Team member updated successfully.'
    else
      render :edit_member, status: :unprocessable_entity
    end
  end

  def update_member_role
    if @team_member.update(role: params[:account_user][:role])
      redirect_to team_management_index_path, notice: 'Team member role updated successfully.'
    else
      render :edit_member, status: :unprocessable_entity
    end
  end

  def destroy_member
    if @team_member.user == @account.owner
      redirect_to team_management_index_path, alert: 'Cannot remove the account owner.'
      return
    end

    @team_member.destroy!
    redirect_to team_management_index_path, notice: 'Team member removed successfully.'
  end

  def new_invitation
    @team_invitation = TeamInvitation.new
  end

  def create_invitation
    @team_invitation = @account.team_invitations.build(invitation_params)
    @team_invitation.invited_by = current_user
    @team_invitation.token = SecureRandom.urlsafe_base64(32)
    @team_invitation.expires_at = 7.days.from_now

    if @team_invitation.save
      # Send invitation email
      deliver_email(TeamInvitationMailer.invitation_email(@team_invitation))
      redirect_to team_management_index_path, notice: "Team invitation sent successfully."
    else
      render :new_invitation, status: :unprocessable_entity
    end
  end

  def destroy_invitation
    @team_invitation.destroy!
    redirect_to team_management_index_path, notice: "Invitation cancelled successfully."
  end

  private

  def set_account
    @account = current_account
  end

  def current_account
    @current_account ||= current_user.accounts.find_by(id: session[:account_id]) ||
                        current_user.accounts.first
  end

  def set_team_member
    @team_member = @account.account_users.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    redirect_to team_management_index_path, alert: 'Team member not found.'
  end

  def set_team_invitation
    @team_invitation = @account.team_invitations.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    redirect_to team_management_index_path, alert: 'Invitation not found.'
  end

  def require_account_access
    unless @account && (@account.users.include?(current_user) || current_user.admin?)
      redirect_to root_path, alert: 'You do not have access to manage this account\'s team.'
    end
  end

  def require_account_approval
    unless @account&.approved? || current_user.admin?
      redirect_to root_path, alert: 'Your account must be approved before you can manage team members.'
    end
  end


  def team_member_params
    params.require(:account_user).permit(:role)
  end

  def invitation_params
    params.require(:team_invitation).permit(:email, :role, :message)
  end
end