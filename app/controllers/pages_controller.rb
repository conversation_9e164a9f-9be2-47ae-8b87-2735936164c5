class PagesController < ApplicationController
  allow_unauthenticated_access

  def landing
  end

  def terms_of_service
  end

  def privacy_policy
  end

  def about
  end

  def pending
    @user_accounts = current_user&.accounts || []
    @approved_accounts = @user_accounts.select(&:approved?)
    @confirmed_accounts = @user_accounts.select { |account| account.confirmed_at.present? }
    @unconfirmed_accounts = @user_accounts.reject { |account| account.confirmed_at.present? }
  end
end
