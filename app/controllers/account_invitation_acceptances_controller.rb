class AccountInvitationAcceptancesController < ApplicationController
  allow_unauthenticated_access
  
  before_action :find_invitation_by_token
  before_action :check_invitation_validity
  
  def show
    initialize_form_objects
    @canonical_url = root_url
  end
  
  def create
    ActiveRecord::Base.transaction do
      # Create the user first
      @user = User.new(user_params.merge(
        email_address: @invitation.email,
        phone: @invitation.phone,
        job_title: @invitation.job_title,
        department: @invitation.department
      ))
      
      if @user.save
        # Create the account with proper confirmation and approval
        @account = Account.new(account_params.merge(
          account_type: @invitation.account_type,
          owner: @user,
          confirmed_at: Time.current,
          status: "approved",
          approved_at: Time.current,
          approved_by: @invitation.invited_by
        ))
        
        if @account.save
          # Create the account_user association
          AccountUser.create!(
            account: @account,
            user: @user,
            role: 'admin'
          )
          
          # Mark invitation as accepted
          @invitation.accept!
          
          # Sign in the user
          start_new_session_for @user
          
          redirect_to after_authentication_url, notice: "Welcome to Platia! Your account has been successfully created."
        else
          @user.destroy
          initialize_form_objects
          render :show, status: :unprocessable_entity
        end
      else
        initialize_form_objects
        render :show, status: :unprocessable_entity
      end
    end
  rescue ActiveRecord::RecordInvalid => e
    redirect_to root_path, alert: "There was an error creating your account. Please try again."
  rescue ActionController::ParameterMissing => e
    redirect_to root_path, alert: "Required information is missing. Please check your input and try again."
  end
  
  private
  
  def find_invitation_by_token
    @invitation = AccountInvitation.find_by(token: params[:token])
    unless @invitation
      redirect_to root_path, alert: "Invalid invitation link."
    end
  end
  
  def check_invitation_validity
    return unless @invitation
    
    if @invitation.accepted?
      redirect_to root_path, alert: "This invitation has already been accepted."
    elsif @invitation.expired?
      redirect_to root_path, alert: "This invitation has expired."
    end
  end
  
  def account_params
    params.require(:account).permit(:name, :description, :linkedin_url)
  end
  
  def user_params
    params.require(:user).permit(:first_name, :last_name, :phone, :job_title, :department, :password, :password_confirmation)
  end
  
  def initialize_form_objects
    @account = Account.new(
      account_type: @invitation.account_type,
      name: @invitation.account_name
    )
    
    @user = User.new(
      first_name: @invitation.first_name,
      last_name: @invitation.last_name,
      email_address: @invitation.email,
      phone: @invitation.phone,
      job_title: @invitation.job_title,
      department: @invitation.department
    )
  end
end