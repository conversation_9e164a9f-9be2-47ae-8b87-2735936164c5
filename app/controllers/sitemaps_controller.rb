class SitemapsController < ApplicationController
  allow_unauthenticated_access
  
  def index
    @static_pages = [
      { url: root_url, changefreq: 'daily', priority: 1.0 },
      { url: marketplace_url, changefreq: 'daily', priority: 0.9 },
      { url: marketplace_products_url, changefreq: 'daily', priority: 0.8 }
    ]

    @products = Product.published.includes(:account, :categories)
    @companies = [] # Initialize empty array since no Company model exists

    expires_in 1.hour, public: true
    respond_to do |format|
      format.xml
    end
  end

  def robots
    expires_in 1.day, public: true
    render plain: generate_robots_txt, content_type: 'text/plain'
  end

  private

  def generate_robots_txt
    sitemap_location = "#{request.base_url}/sitemap.xml"
    
    content = []
    content << "User-agent: *"
    content << "Allow: /"
    content << ""
    content << "# Disallow admin areas"
    content << "Disallow: /admin/"
    content << "Disallow: /dashboard/"
    content << ""
    content << "# Sitemap"
    content << "Sitemap: #{sitemap_location}"
    content.join("\n")
  end
end