module Seo<PERSON>el<PERSON>
  def page_title(title = nil)
    base_title = "Platia"
    if title.present?
      "#{title} | #{base_title}"
    else
      "#{base_title} - Connecting Government with Innovation"
    end
  end

  def meta_description(description = nil)
    description.presence || "Discover innovative technology solutions for government agencies. Connect with verified vendors, explore products, and streamline your procurement process on Platia."
  end

  def meta_keywords(keywords = nil)
    base_keywords = ["government technology", "govtech", "public sector", "technology procurement", "government solutions"]
    if keywords.present?
      (Array(keywords) + base_keywords).uniq.join(", ")
    else
      base_keywords.join(", ")
    end
  end

  def canonical_url(url = nil)
    url.presence || request.original_url
  end

  def og_image(image_url = nil)
    image_url.presence || asset_url('govtech-marketplace-og.png')
  end

  def structured_data_organization
    {
      "@context" => "https://schema.org",
      "@type" => "Organization",
      "name" => "Platia",
      "description" => "Platform connecting government agencies with innovative technology solutions",
      "url" => root_url,
      "logo" => asset_url('logo.png'),
      "contactPoint" => {
        "@type" => "ContactPoint",
        "contactType" => "customer service",
        "email" => "<EMAIL>"
      },
      "sameAs" => [
        "https://twitter.com/govtechmarketplace",
        "https://linkedin.com/company/govtech-marketplace"
      ]
    }
  end

  def structured_data_product(product)
    return unless product

    company = product.account
    {
      "@context" => "https://schema.org",
      "@type" => "SoftwareApplication",
      "name" => product.name,
      "description" => strip_tags(product.description.to_s).truncate(200),
      "url" => marketplace_product_url(product),
      "applicationCategory" => "Government Software",
      "operatingSystem" => "Web-based",
      "provider" => company ? {
        "@type" => "Organization", 
        "name" => company.name,
        "description" => strip_tags(company.description.to_s).truncate(200)
      } : nil,
      "audience" => {
        "@type" => "Audience",
        "name" => "Government Agencies"
      }
    }.compact
  end

  def structured_data_marketplace
    {
      "@context" => "https://schema.org",
      "@type" => "WebSite",
      "name" => "Platia",
      "description" => "Government Technology Marketplace Platform",
      "url" => root_url,
      "potentialAction" => {
        "@type" => "SearchAction",
        "target" => {
          "@type" => "EntryPoint",
          "urlTemplate" => marketplace_products_url + "?search={search_term_string}"
        },
        "query-input" => "required name=search_term_string"
      }
    }
  end

  def render_structured_data(data)
    content_tag(:script, type: "application/ld+json") do
      data.to_json.html_safe
    end
  end

  def breadcrumb_schema(breadcrumbs)
    items = breadcrumbs.map.with_index do |crumb, index|
      {
        "@type" => "ListItem",
        "position" => index + 1,
        "name" => crumb[:name],
        "item" => crumb[:url]
      }
    end

    {
      "@context" => "https://schema.org",
      "@type" => "BreadcrumbList", 
      "itemListElement" => items
    }
  end
end