module ApplicationHelper
  include Pagy::Frontend

  def build_category_options(categories)
    options = []
    
    # Group categories by parent, then sort alphabetically
    grouped = categories.group_by(&:parent)
    
    # Add parent categories with their children (skip root categories)
    parents_with_children = grouped.keys.compact.sort_by(&:name)
    
    parents_with_children.each do |parent|
      # Add parent as a disabled option (header)
      options << [parent.name, "", { disabled: true }]
      
      # Add children with indentation, sorted alphabetically
      children = grouped[parent].sort_by(&:name)
      children.each do |child|
        options << ["  #{child.name}", child.id]
      end
    end
    
    options
  end
end
