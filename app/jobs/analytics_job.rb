class AnalyticsJob < ApplicationJob
  queue_as :default
  
  def perform(analytics_data)
    product = Product.find(analytics_data[:product_id])
    user = User.find(analytics_data[:user_id]) if analytics_data[:user_id]
    
    # Parse user agent to get browser/device info
    user_agent_info = ProductAnalyticsService.parse_user_agent(analytics_data[:user_agent])
    
    # Get location from IP address
    location_info = ProductAnalyticsService.get_location_from_ip(analytics_data[:ip_address])
    
    # Combine metadata
    metadata = user_agent_info.merge(location_info).compact
    
    # Add content metadata if present
    if analytics_data[:content_metadata].present?
      metadata = metadata.merge(analytics_data[:content_metadata])
    end
    
    # Create the analytics record
    ProductAnalytic.create!(
      product: product,
      user: user,
      event_type: analytics_data[:event_type],
      ip_address: analytics_data[:ip_address],
      user_agent: analytics_data[:user_agent],
      visitor_id: analytics_data[:visitor_id],
      metadata: metadata
    )
    
    Rails.logger.info "Analytics tracked: #{analytics_data[:event_type]} for product #{product.id} by visitor #{analytics_data[:visitor_id]}"
    
  rescue ActiveRecord::RecordInvalid => e
    # Handle duplicate unique_view attempts gracefully
    if e.message.include?('Visitor') && analytics_data[:event_type] == 'unique_view'
      Rails.logger.debug "Duplicate unique view attempt for visitor #{analytics_data[:visitor_id]} on product #{analytics_data[:product_id]}"
    else
      Rails.logger.error "Failed to create analytics record: #{e.message}"
      raise e
    end
  rescue => e
    Rails.logger.error "Analytics job failed: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    raise e
  end
end