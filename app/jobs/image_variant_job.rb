class ImageVariantJob < ApplicationJob
  queue_as :default
  
  def perform(record_class, record_id, attachment_name)
    record = record_class.constantize.find(record_id)
    attachment = record.public_send(attachment_name)
    
    return unless attachment.attached?
    return unless attachment.content_type&.start_with?('image/')
    
    Rails.logger.info "Generating image variants for #{record_class}##{record_id}.#{attachment_name}"
    
    # Generate all standard variants
    generate_variants_for_attachment(attachment, attachment_name)
    
    Rails.logger.info "Completed generating image variants for #{record_class}##{record_id}.#{attachment_name}"
  end
  
  private
  
  def generate_variants_for_attachment(attachment, attachment_name)
    variants = get_variants_for_attachment_type(attachment_name)
    
    variants.each do |variant_name, options|
      begin
        Rails.logger.debug "Generating #{variant_name} variant: #{options}"
        attachment.variant(options)
      rescue => e
        Rails.logger.error "Failed to generate #{variant_name} variant: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
      end
    end
  end
  
  def get_variants_for_attachment_type(attachment_name)
    case attachment_name.to_s
    when 'logo'
      {
        small: { resize_to_fill: [80, 80, { crop: :smart }] },
        medium: { resize_to_fill: [160, 160, { crop: :smart }] },
        large: { resize_to_fill: [320, 320, { crop: :smart }] }
      }
    when 'profile_photo'
      {
        small: { resize_to_fill: [64, 64, { crop: :smart }] },
        medium: { resize_to_fill: [128, 128, { crop: :smart }] },
        large: { resize_to_fill: [256, 256, { crop: :smart }] }
      }
    when 'image'
      {
        thumbnail: { resize_to_limit: [300, 200] },
        medium: { resize_to_limit: [600, 400] },
        large: { resize_to_limit: [1200, 800] }
      }
    when 'attachment'
      # For ProductFeature attachments (could be image or video)
      {
        thumbnail: { resize_to_limit: [300, 200] },
        medium: { resize_to_limit: [600, 400] }
      }
    else
      # Default variants for any other image attachments
      {
        thumbnail: { resize_to_limit: [300, 200] },
        medium: { resize_to_limit: [600, 400] },
        large: { resize_to_limit: [1200, 800] }
      }
    end
  end
end