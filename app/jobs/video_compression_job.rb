class VideoCompressionJob < ApplicationJob
  queue_as :default
  
  require 'streamio-ffmpeg'

  def perform(model_type, model_id, attachment_name)
    Rails.logger.info "VideoCompressionJob started for #{model_type}##{model_id} (#{attachment_name})"
    
    model = model_type.constantize.find(model_id)
    attachment = model.send(attachment_name)
    
    unless attachment.attached?
      Rails.logger.warn "No attachment found for #{model_type}##{model_id} (#{attachment_name})"
      return
    end
    
    unless video_file?(attachment)
      Rails.logger.warn "Attachment is not a video file for #{model_type}##{model_id} (#{attachment_name})"
      return
    end

    Rails.logger.info "Starting video compression for #{model_type}##{model_id}"

    # Create temporary files
    input_tempfile, input_path = create_temp_file(attachment)
    output_path = create_output_path(input_path)

    begin
      # Compress video using FFMPEG
      compress_video(input_path, output_path)
      
      # Replace the original attachment with compressed version
      replace_attachment(model, attachment_name, output_path, attachment.filename.to_s)
      
      # Generate and save video preview thumbnail if configured
      generate_video_preview(model, attachment_name)
      
      Rails.logger.info "Video compression completed for #{model_type}##{model_id}"
      
    rescue StandardError => e
      Rails.logger.error "Video compression failed for #{model_type}##{model_id}: #{e.message}"
      raise e
    ensure
      # Clean up temporary files
      input_tempfile&.close!
      cleanup_temp_files(output_path)
    end
  end

  private

  def video_file?(attachment)
    attachment.content_type&.start_with?('video/')
  end

  def create_temp_file(attachment)
    temp_file = Tempfile.new(['video_input', File.extname(attachment.filename.to_s)])
    temp_file.binmode
    temp_file.write(attachment.download)
    temp_file.flush  # Ensure data is written to disk
    [temp_file, temp_file.path]  # Return both tempfile object and path
  end

  def create_output_path(input_path)
    dir = File.dirname(input_path)
    basename = File.basename(input_path, '.*')
    File.join(dir, "#{basename}_compressed.mp4")
  end

  def compress_video(input_path, output_path)
    movie = FFMPEG::Movie.new(input_path)
    
    # Calculate target resolution maintaining aspect ratio
    target_resolution = calculate_target_resolution(movie.width, movie.height)
    
    # Standard compression options for good quality/size balance
    options = {
      video_codec: 'libx264',
      audio_codec: 'aac',
      video_bitrate: '2500k',
      audio_bitrate: '256k',
      audio_sample_rate: 48000,
      resolution: target_resolution,
      frame_rate: 30,
      custom: [
        '-preset', 'medium',
        '-crf', '23',
        '-movflags', '+faststart',
        '-profile:a', 'aac_low',
        '-ac', '2'
      ]
    }
    
    movie.transcode(output_path, options)
  end

  def replace_attachment(model, attachment_name, compressed_path, original_filename)
    # Generate new filename with compressed suffix if not already .mp4
    new_filename = if original_filename.downcase.end_with?('.mp4')
                     original_filename
                   else
                     "#{File.basename(original_filename, '.*')}_compressed.mp4"
                   end

    # Attach the compressed video
    File.open(compressed_path, 'rb') do |file|
      model.send(attachment_name).attach(
        io: file,
        filename: new_filename,
        content_type: 'video/mp4'
      )
    end
  end

  def calculate_target_resolution(width, height)
    return "#{width}x#{height}" if width <= 1920 && height <= 1080
    
    aspect_ratio = width.to_f / height.to_f
    
    if aspect_ratio >= (1920.0 / 1080.0)
      # Width is the constraining factor
      new_width = 1920
      new_height = (new_width / aspect_ratio).round
      new_height += 1 if new_height.odd?  # Ensure even number
    else
      # Height is the constraining factor  
      new_height = 1080
      new_width = (new_height * aspect_ratio).round
      new_width += 1 if new_width.odd?  # Ensure even number
    end
    
    "#{new_width}x#{new_height}"
  end

  def generate_video_preview(model, attachment_name)
    # Check if this model/attachment should generate previews
    return unless should_generate_preview?(model, attachment_name)
    
    attachment = model.send(attachment_name)
    return unless attachment.attached?
    
    thumbnail_tempfile = nil
    thumbnail_path = nil
    
    begin
      # Create temporary input file for the video
      input_tempfile, input_path = create_temp_file(attachment)
      
      # Create temporary output path for the thumbnail
      thumbnail_path = create_thumbnail_output_path(input_path)
      
      # Generate thumbnail using ffmpeg
      generate_thumbnail_with_ffmpeg(input_path, thumbnail_path)
      
      # Attach the generated thumbnail to the model
      attach_thumbnail_to_model(model, attachment_name, thumbnail_path)
      
      Rails.logger.info "Video preview generated for Product##{model.id}"
      
    rescue StandardError => e
      Rails.logger.error "Failed to generate video preview for Product##{model.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      # Don't re-raise as preview generation is not critical
    ensure
      # Clean up temporary files
      input_tempfile&.close!
      cleanup_temp_files(thumbnail_path) if thumbnail_path
    end
  end
  
  def create_thumbnail_output_path(input_path)
    dir = File.dirname(input_path)
    basename = File.basename(input_path, '.*')
    File.join(dir, "#{basename}_thumbnail.jpg")
  end
  
  def generate_thumbnail_with_ffmpeg(input_path, output_path)
    movie = FFMPEG::Movie.new(input_path)
    
    # Take screenshot at 10% duration to avoid black frames at the beginning
    timestamp = [movie.duration * 0.1, 1.0].max # At least 1 second in
    
    # Generate thumbnail using ffmpeg screenshot functionality
    movie.screenshot(output_path, seek_time: timestamp, quality: 2)
  end
  
  def should_generate_preview?(model, attachment_name)
    # Check if model uses VideoCompression concern and has preview configured
    return false unless model.class.respond_to?(:video_attachments_config)
    
    config = model.class.video_attachments_config[attachment_name.to_s]
    return false unless config
    
    config[:generate_preview] == true
  end
  
  def get_preview_attachment_name(model, attachment_name)
    return nil unless model.class.respond_to?(:video_attachments_config)
    
    config = model.class.video_attachments_config[attachment_name.to_s]
    return nil unless config
    
    config[:preview_attachment] || "#{attachment_name}_preview"
  end
  
  def attach_thumbnail_to_model(model, attachment_name, thumbnail_path)
    return unless File.exist?(thumbnail_path)
    
    preview_attachment_name = get_preview_attachment_name(model, attachment_name)
    return unless preview_attachment_name
    
    File.open(thumbnail_path, 'rb') do |file|
      model.send(preview_attachment_name).attach(
        io: file,
        filename: "#{model.try(:slug) || model.id}_#{attachment_name}_preview.jpg",
        content_type: 'image/jpeg'
      )
    end
  end

  def cleanup_temp_files(*paths)
    paths.compact.each do |path|
      File.delete(path) if File.exist?(path)
    rescue StandardError => e
      Rails.logger.warn "Failed to cleanup temp file #{path}: #{e.message}"
    end
  end
end