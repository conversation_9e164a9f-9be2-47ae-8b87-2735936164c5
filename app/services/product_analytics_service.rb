class ProductAnalyticsService
  require 'digest'
  require 'net/http'
  require 'json'
  
  def self.track_page_view(product, request, user = nil)
    return unless product.present? && request.present?
    
    visitor_id = generate_visitor_id(request)
    
    # Track page view - unique visitors will be calculated at query time
    AnalyticsJob.perform_later(
      product_id: product.id,
      user_id: user&.id,
      ip_address: request.remote_ip,
      user_agent: request.user_agent,
      visitor_id: visitor_id,
      event_type: 'page_view'
    )
  rescue => e
    Rails.logger.error "Error tracking analytics for product #{product.id}: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
  end
  
  def self.track_content_action(product, action_type, content_type, content_id, request, user = nil)
    return unless product.present? && request.present? && action_type.present? && content_type.present? && content_id.present?
    
    visitor_id = generate_visitor_id(request)
    
    # Create metadata with content details
    content_metadata = {
      content_type: content_type,
      content_id: content_id.to_s
    }
    
    # Track content action
    AnalyticsJob.perform_later(
      product_id: product.id,
      user_id: user&.id,
      ip_address: request.remote_ip,
      user_agent: request.user_agent,
      visitor_id: visitor_id,
      event_type: action_type,
      content_metadata: content_metadata
    )
  rescue => e
    Rails.logger.error "Error tracking content #{action_type} for product #{product.id}: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
  end
  
  def self.track_video_action(product, action_type, video_metadata, request, user = nil)
    return unless product.present? && request.present? && action_type.present? && video_metadata.present?
    
    visitor_id = generate_visitor_id(request)
    
    if action_type == 'video_watch_duration'
      # For video watch duration, create the record synchronously so we can return the ID
      # Parse user agent to get browser/device info
      user_agent_info = parse_user_agent(request.user_agent)
      
      # Get location from IP address
      location_info = get_location_from_ip(request.remote_ip)
      
      # Combine metadata
      metadata = user_agent_info.merge(location_info).merge(video_metadata).compact
      
      # Create the analytics record synchronously
      analytic = ProductAnalytic.create!(
        product: product,
        user: user,
        event_type: action_type,
        ip_address: request.remote_ip,
        user_agent: request.user_agent,
        visitor_id: visitor_id,
        metadata: metadata
      )
      
      Rails.logger.info "Video analytics tracked: #{action_type} for product #{product.id} by visitor #{visitor_id}"
      return analytic
    else
      # For other video actions (like video_play), use async job as before
      AnalyticsJob.perform_later(
        product_id: product.id,
        user_id: user&.id,
        ip_address: request.remote_ip,
        user_agent: request.user_agent,
        visitor_id: visitor_id,
        event_type: action_type,
        content_metadata: video_metadata
      )
      return nil
    end
  rescue => e
    Rails.logger.error "Error tracking video #{action_type} for product #{product.id}: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    return nil
  end
  
  def self.generate_visitor_id(request)
    # Create a hash from IP + User Agent + Daily salt for visitor identification
    # This allows tracking unique visitors while being privacy-conscious
    ip = request.remote_ip || 'unknown'
    user_agent = request.user_agent || 'unknown'
    daily_salt = Date.current.strftime('%Y-%m-%d')
    
    raw_string = "#{ip}#{user_agent}#{daily_salt}"
    Digest::SHA256.hexdigest(raw_string)
  end
  
  def self.parse_user_agent(user_agent_string)
    return {} if user_agent_string.blank?
    
    detector = DeviceDetector.new(user_agent_string)
    
    {
      browser: detector.name,
      browser_version: detector.full_version,
      os: detector.os_name,
      os_version: detector.os_full_version,
      device_type: detector.device_type,
      device_name: detector.device_name,
      device_brand: detector.device_brand
    }
  rescue => e
    Rails.logger.error "Error parsing user agent: #{e.message}"
    {}
  end
  
  def self.get_location_from_ip(ip_address)
    # Filter out local/private IP addresses (both IPv4 and IPv6)
    return {} if ip_address.blank? || 
                 ip_address == '127.0.0.1' || 
                 ip_address == '::1' ||  # IPv6 localhost
                 ip_address.start_with?('192.168.') || 
                 ip_address.start_with?('10.') ||
                 ip_address.start_with?('172.16.') ||  # Private range **********/12
                 ip_address.start_with?('fe80:') ||   # IPv6 link-local
                 ip_address.start_with?('fc00:') ||   # IPv6 unique local
                 ip_address.start_with?('fd00:')      # IPv6 unique local
    
    # Use Geoapify API directly
    api_key = ENV['GEOAPIFY_API_KEY']
    return {} if api_key.blank?
    
    uri = URI("https://api.geoapify.com/v1/ipinfo?ip=#{ip_address}&apiKey=#{api_key}")
    
    response = Net::HTTP.get_response(uri)
    return {} unless response.code == '200'
    
    data = JSON.parse(response.body)
    
    # Extract location data from Geoapify response
    {
      country: data.dig('country', 'name'),
      country_code: data.dig('country', 'iso_code'),
      region: data.dig('state', 'name') || data.dig('subdivisions', 0, 'names', 'en'),
      city: data.dig('city', 'name'),
      latitude: data.dig('location', 'latitude'),
      longitude: data.dig('location', 'longitude'),
      continent: data.dig('continent', 'name'),
      phone_code: data.dig('country', 'phone_code'),
      currency: data.dig('country', 'currency'),
      flag: data.dig('country', 'flag'),
      languages: data.dig('country', 'languages')&.map { |lang| lang['name'] }&.join(', ')
    }.compact # Remove nil values
    
  rescue JSON::ParserError => e
    Rails.logger.error "Error parsing Geoapify response for IP #{ip_address}: #{e.message}"
    {}
  rescue Net::TimeoutError => e
    Rails.logger.warn "Geoapify API timeout for IP #{ip_address}: #{e.message}"
    {}
  rescue => e
    Rails.logger.error "Error getting location from IP #{ip_address}: #{e.message}"
    {}
  end
end