/*
 * This is a manifest file that'll be compiled into application.css.
 *
 * With Propshaft, assets are served efficiently without preprocessing steps. You can still include
 * application-wide styles in this file, but keep in mind that CSS precedence will follow the standard
 * cascading order, meaning styles declared later in the document or manifest will override earlier ones,
 * depending on specificity.
 *
 * Consider organizing styles into separate files for maintainability.
 */

/* Category Browser Styles */
.category-browser {
  transition: all 0.3s ease;
}

.category-item {
  transition: all 0.2s ease;
  cursor: pointer;
}

.category-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark .category-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.product-item {
  transition: all 0.2s ease;
  cursor: pointer;
}

.product-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark .product-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Text truncation utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Scrollbar styles */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Loading animation */
@keyframes fade-in {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.category-browser .category-item,
.category-browser .product-item {
  animation: fade-in 0.3s ease forwards;
}

.category-browser .category-item:nth-child(1) { animation-delay: 0.05s; }
.category-browser .category-item:nth-child(2) { animation-delay: 0.1s; }
.category-browser .category-item:nth-child(3) { animation-delay: 0.15s; }
.category-browser .category-item:nth-child(4) { animation-delay: 0.2s; }
.category-browser .category-item:nth-child(5) { animation-delay: 0.25s; }

/* Responsive adjustments */
@media (max-width: 1024px) {
  .category-browser .grid {
    grid-template-columns: 1fr;
  }
  
  .category-browser .hidden {
    display: block !important;
    margin-top: 1.5rem;
  }
}

/* Card transitions */
.category-browser [data-category-browser-target="subCard"],
.category-browser [data-category-browser-target="leafCard"] {
  transition: all 0.3s ease;
}

.category-browser [data-category-browser-target="subCard"].hidden,
.category-browser [data-category-browser-target="leafCard"].hidden {
  opacity: 0;
  transform: translateX(20px);
}

.category-browser [data-category-browser-target="subCard"]:not(.hidden),
.category-browser [data-category-browser-target="leafCard"]:not(.hidden) {
  opacity: 1;
  transform: translateX(0);
}

/* Tab Styles */

/* Tab content */
.tab-content {
  transition: opacity 0.2s ease;
}

.active-tab-content {
  opacity: 1;
}

.tab-content.hidden {
  opacity: 0;
}

/* Video Protection Styles */
.no-context-menu {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

video.no-context-menu {
  pointer-events: auto;
}

video.no-context-menu::-webkit-media-controls-download-button {
  display: none;
}

video.no-context-menu::-webkit-media-controls-fullscreen-button {
  display: none;
}