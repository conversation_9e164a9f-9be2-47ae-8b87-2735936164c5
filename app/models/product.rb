# == Schema Information
#
# Table name: products
#
#  id                       :integer          not null, primary key
#  description              :text
#  features                 :json
#  name                     :string
#  pricing                  :text
#  pricing_model            :text
#  published                :boolean          default(FALSE)
#  show_case_studies        :boolean          default(FALSE), not null
#  show_featured_video      :boolean          default(FALSE), not null
#  show_product_content     :boolean          default(FALSE), not null
#  show_product_contracts   :boolean          default(FALSE), not null
#  show_product_customers   :boolean          default(FALSE), not null
#  show_product_features    :boolean          default(FALSE), not null
#  show_product_screenshots :boolean          default(FALSE), not null
#  show_product_videos      :boolean          default(FALSE), not null
#  slug                     :string
#  website                  :string
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  account_id               :integer
#
# Indexes
#
#  index_products_on_account_id  (account_id)
#  index_products_on_slug        (slug)
#
# Foreign Keys
#
#  account_id  (account_id => accounts.id)
#
class Product < ApplicationRecord
  extend FriendlyId
  friendly_id :name, use: :slugged
  include PerformanceOptimizable
  include VideoCompression

  belongs_to :account, optional: true
  has_many :product_categories, dependent: :destroy
  has_many :categories, through: :product_categories
  has_many :product_certifications, dependent: :destroy
  has_many :certifications, through: :product_certifications
  has_many :product_videos, -> { order(:position) }, dependent: :destroy
  has_many :product_content, -> { order(:position) }, dependent: :destroy
  has_many :product_screenshots, -> { order(:position) }, dependent: :destroy
  has_many :product_features, -> { order(:position) }, dependent: :destroy
  has_many :product_customers, -> { order(:position) }, dependent: :destroy
  has_many :product_contracts, -> { order(:position) }, dependent: :destroy
  has_many :case_studies, -> { order(:position) }, dependent: :destroy
  has_many :leads, dependent: :destroy
  has_many :product_claims, dependent: :destroy
  has_many :product_analytics, dependent: :destroy
  
  # Logo for the product
  has_one_attached :logo
  
  # Featured video for the product with compression and preview
  has_one_attached :featured_video
  has_video_attachment :featured_video, generate_preview: true, preview_attachment: :featured_video_preview

  validates :name, presence: true
  validates :account_id, presence: true
  validates :description, length: { maximum: 1000, message: "must be 1000 characters or less (approximately 10 sentences)" }
  validate :categories_must_be_leaf_categories
  validate :logo_validation
  validate :featured_video_validation

  
  # Generate image variants asynchronously after logo is attached
  after_commit :schedule_logo_variants, on: [:create, :update], if: :logo_attached_and_changed?

  scope :published, -> { where(published: true) }
  scope :featured, -> { where(featured: true) }
  scope :recent, -> { order(created_at: :desc) }
  scope :with_company, -> { includes(:account) }

  def self.default_includes
    [:account, :categories, :certifications]
  end

  def self.search_optimized(query, limit: 20)
    return none if query.blank?
    
    where("LOWER(name) LIKE LOWER(?) OR LOWER(description) LIKE LOWER(?)", "%#{sanitize_sql_like(query)}%", "%#{sanitize_sql_like(query)}%")
      .includes(default_includes)
      .limit(limit)
  end


  def primary_video
    # Return featured_video if available, otherwise fallback to first product_video
    return featured_video if featured_video.attached?
    product_videos.by_position.first
  end
  
  def total_video_count
    count = 0
    
    # Featured video
    count += 1 if featured_video.attached?
    
    # Product videos
    count += product_videos.published.count
    
    count
  end
  
  def primary_screenshot
    product_screenshots.ordered.first
  end

  def published?
    published == true
  end


  # Helper methods for features (now using ProductFeature model)
  def feature_names
    product_features.pluck(:name)
  end

  # Category helper methods
  def primary_category
    categories.first
  end

  def root_categories
    categories.map(&:root_category).uniq
  end

  def category_names
    categories.pluck(:name)
  end

  # Helper methods for products without accounts
  def company_name
    account&.name || name
  end

  def has_vendor_info?
    account.present?
  end

  private

  def categories_must_be_leaf_categories
    return if categories.empty?

    non_leaf_categories = categories.select { |category| category.subcategories.any? }
    
    if non_leaf_categories.any?
      category_names = non_leaf_categories.map(&:name).join(", ")
      errors.add(:categories, "can only be assigned to categories without subcategories. The following categories have subcategories: #{category_names}")
    end
  end

  def logo_validation
    return unless logo.attached?

    # Check file size (5MB limit)
    if logo.blob.byte_size > 5.megabytes
      errors.add(:logo, "must be less than 5MB")
    end

    # Check content type (only images)
    allowed_types = %w[image/jpeg image/jpg image/png image/gif image/webp]
    unless allowed_types.include?(logo.blob.content_type)
      errors.add(:logo, "must be a valid image format (JPEG, PNG, GIF, or WebP)")
    end
  end

  def featured_video_validation
    return unless featured_video.attached?

    # Check file size (50MB limit)
    if featured_video.blob.byte_size > 50.megabytes
      errors.add(:featured_video, "must be less than 50MB")
    end

    # Check content type (only videos)
    allowed_types = %w[video/mp4 video/webm video/ogg video/avi video/mov video/wmv video/flv video/mkv]
    unless allowed_types.include?(featured_video.blob.content_type)
      errors.add(:featured_video, "must be a valid video format (MP4, WebM, OGG, AVI, MOV, WMV, FLV, MKV)")
    end
  end

  
  def schedule_logo_variants
    return unless logo.attached?
    ImageVariantJob.perform_later(self.class.name, id, 'logo')
  end
  
  def logo_attached_and_changed?
    logo.attached? && saved_change_to_attribute?('logo')
  end
end
