# == Schema Information
#
# Table name: account_invitations
#
#  id            :integer          not null, primary key
#  account_name  :string
#  account_type  :string
#  department    :string
#  email         :string
#  expires_at    :datetime
#  first_name    :string
#  job_title     :string
#  last_name     :string
#  phone         :string
#  status        :string
#  token         :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  invited_by_id :integer          not null
#
# Indexes
#
#  index_account_invitations_on_invited_by_id  (invited_by_id)
#
# Foreign Keys
#
#  invited_by_id  (invited_by_id => users.id)
#
class AccountInvitation < ApplicationRecord
  belongs_to :invited_by, class_name: 'User'
  
  validates :email, presence: true, uniqueness: true
  validates :account_type, presence: true, inclusion: { in: %w[vendor agency] }
  validates :account_name, presence: true
  validates :first_name, presence: true
  validates :last_name, presence: true
  validates :job_title, presence: true
  validates :token, presence: true, uniqueness: true
  validates :status, presence: true, inclusion: { in: %w[pending accepted expired] }
  
  before_validation :generate_token, on: :create
  before_validation :set_expiration, on: :create
  before_validation :set_default_status, on: :create
  
  scope :pending, -> { where(status: 'pending') }
  scope :expired, -> { where('expires_at < ?', Time.current) }
  scope :valid_invitations, -> { pending.where('expires_at > ?', Time.current) }
  
  def expired?
    expires_at < Time.current
  end
  
  def pending?
    status == 'pending'
  end
  
  def accepted?
    status == 'accepted'
  end
  
  def full_name
    "#{first_name} #{last_name}"
  end
  
  def accept!
    update!(status: 'accepted')
  end
  
  def expire!
    update!(status: 'expired')
  end
  
  private
  
  def generate_token
    self.token = SecureRandom.urlsafe_base64(32)
  end
  
  def set_expiration
    self.expires_at = 7.days.from_now
  end
  
  def set_default_status
    self.status = 'pending'
  end
end
