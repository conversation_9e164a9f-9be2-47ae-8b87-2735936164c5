# == Schema Information
#
# Table name: product_certifications
#
#  id               :integer          not null, primary key
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  certification_id :integer          not null
#  product_id       :integer          not null
#
# Indexes
#
#  index_product_certifications_on_certification_id  (certification_id)
#  index_product_certifications_on_product_id        (product_id)
#
# Foreign Keys
#
#  certification_id  (certification_id => certifications.id)
#  product_id        (product_id => products.id)
#
class ProductCertification < ApplicationRecord
  belongs_to :product
  belongs_to :certification

  # Validations
  validates :product_id, uniqueness: { scope: :certification_id }
end
