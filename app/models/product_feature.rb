# == Schema Information
#
# Table name: product_features
#
#  id          :integer          not null, primary key
#  description :text
#  name        :string           not null
#  position    :integer          default(0)
#  published   :boolean          default(FALSE)
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  product_id  :integer          not null
#
# Indexes
#
#  index_product_features_on_product_id               (product_id)
#  index_product_features_on_product_id_and_position  (product_id,position)
#
# Foreign Keys
#
#  product_id  (product_id => products.id)
#
class ProductFeature < ApplicationRecord
  belongs_to :product
  has_one_attached :attachment
  
  validates :name, presence: true
  validates :position, presence: true, numericality: { greater_than_or_equal_to: 0 }
  
  scope :ordered, -> { order(:position) }
  scope :published, -> { where(published: true) }
  
  before_create :set_position, if: -> { position.nil? }
  
  # Generate image variants or video compression after attachment is attached
  after_commit :schedule_attachment_processing, on: [:create, :update], if: :attachment_attached_and_changed?
  
  def attachment_type
    return nil unless attachment.attached?
    
    if attachment.content_type.start_with?('image/')
      'image'
    elsif attachment.content_type.start_with?('video/')
      'video'
    else
      'other'
    end
  end
  
  def image?
    attachment_type == 'image'
  end
  
  def video?
    attachment_type == 'video'
  end

  def published?
    published == true
  end
  
  private
  
  def schedule_attachment_processing
    return unless attachment.attached?
    
    if attachment.content_type&.start_with?('image/')
      ImageVariantJob.perform_later(self.class.name, id, 'attachment')
    elsif attachment.content_type&.start_with?('video/')
      VideoCompressionJob.perform_later(self.class.name, id, 'attachment')
    end
  end
  
  def attachment_attached_and_changed?
    attachment.attached? && saved_change_to_attribute?('attachment')
  end
  
  def set_position
    max_position = product.product_features.maximum(:position) || 0
    self.position = max_position + 1
  end
end
