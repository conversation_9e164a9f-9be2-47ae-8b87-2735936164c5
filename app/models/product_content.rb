# == Schema Information
#
# Table name: product_content
#
#  id          :integer          not null, primary key
#  description :text
#  position    :integer
#  published   :boolean          default(FALSE)
#  title       :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  product_id  :integer          not null
#
# Indexes
#
#  index_product_content_on_product_id  (product_id)
#
# Foreign Keys
#
#  product_id  (product_id => products.id)
#
class ProductContent < ApplicationRecord
  include VideoCompression
  
  self.table_name = "product_content"
  
  belongs_to :product
  has_one_attached :file
  
  # Configure video compression for file attachment (no preview for content files)
  has_video_attachment :file, generate_preview: false

  validates :title, presence: true
  validates :description, presence: true
  validates :file, presence: true
  validate :file_validation

  scope :published, -> { where(published: true) }
  scope :by_position, -> { order(:position) }


  def published?
    published == true
  end

  def file_type
    return nil unless file.attached?
    
    case file.content_type
    when 'application/pdf'
      'pdf'
    when /^video\//
      'video'
    when /^image\//
      'image'
    else
      'unknown'
    end
  end

  private


  def file_validation
    return unless file.attached?

    # Check file size (50 MB limit)
    if file.blob.byte_size > 50.megabytes
      errors.add(:file, "must be less than 50MB")
    end

    # Check content type (PDF, video, and image files)
    allowed_types = [
      'application/pdf',
      'video/mp4', 'video/webm', 'video/ogg', 'video/avi', 
      'video/mov', 'video/wmv', 'video/flv', 'video/mkv',
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 
      'image/webp', 'image/svg+xml'
    ]
    
    unless allowed_types.include?(file.blob.content_type)
      errors.add(:file, "must be a PDF, video file (MP4, WebM, OGG, AVI, MOV, WMV, FLV, MKV), or image file (JPEG, PNG, GIF, WebP, SVG)")
    end
  end
end
