# == Schema Information
#
# Table name: lists
#
#  id          :integer          not null, primary key
#  description :text
#  name        :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  account_id  :integer          not null
#
# Indexes
#
#  index_lists_on_account_id  (account_id)
#
# Foreign Keys
#
#  account_id  (account_id => accounts.id)
#
class List < ApplicationRecord
  belongs_to :account
  has_many :list_items, dependent: :destroy
  has_many :products, through: :list_items
  
  validates :name, presence: true, length: { maximum: 255 }
  validates :description, length: { maximum: 1000 }
  
  scope :for_account, ->(account) { where(account: account) }
end
