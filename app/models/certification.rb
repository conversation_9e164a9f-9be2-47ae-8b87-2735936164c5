# == Schema Information
#
# Table name: certifications
#
#  id          :integer          not null, primary key
#  description :text
#  name        :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#
class Certification < ApplicationRecord
  # Associations
  has_many :product_certifications, dependent: :destroy
  has_many :products, through: :product_certifications

  # Validations
  validates :name, presence: true, uniqueness: true
  validates :description, length: { maximum: 1000 }

  # Scopes
  scope :alphabetical, -> { order(:name) }
  scope :with_products, -> { joins(:products).distinct }

  # Instance methods
  def products_count
    products.count
  end
end
