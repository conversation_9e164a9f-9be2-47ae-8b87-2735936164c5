# == Schema Information
#
# Table name: account_users
#
#  id         :integer          not null, primary key
#  joined_at  :datetime
#  role       :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  account_id :integer          not null
#  user_id    :integer          not null
#
# Indexes
#
#  index_account_users_on_account_id  (account_id)
#  index_account_users_on_user_id     (user_id)
#
# Foreign Keys
#
#  account_id  (account_id => accounts.id)
#  user_id     (user_id => users.id)
#
class AccountUser < ApplicationRecord
  belongs_to :account
  belongs_to :user

  validates :role, inclusion: { in: %w[admin member] }
  validates :user_id, uniqueness: { scope: :account_id }

  enum :role, { admin: 'admin', member: 'member' }

  scope :admins, -> { where(role: 'admin') }
  scope :members, -> { where(role: 'member') }

  def admin?
    role == 'admin'
  end

  def member?
    role == 'member'
  end
end
