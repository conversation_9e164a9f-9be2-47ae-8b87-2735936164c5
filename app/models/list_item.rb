# == Schema Information
#
# Table name: list_items
#
#  id         :integer          not null, primary key
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  list_id    :integer          not null
#  product_id :integer          not null
#
# Indexes
#
#  index_list_items_on_list_id                 (list_id)
#  index_list_items_on_list_id_and_product_id  (list_id,product_id) UNIQUE
#  index_list_items_on_product_id              (product_id)
#
# Foreign Keys
#
#  list_id     (list_id => lists.id)
#  product_id  (product_id => products.id)
#
class ListItem < ApplicationRecord
  belongs_to :list
  belongs_to :product
  
  validates :list_id, uniqueness: { scope: :product_id, message: "Product is already in this list" }
end
