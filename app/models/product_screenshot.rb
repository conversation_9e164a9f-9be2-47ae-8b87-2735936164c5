# == Schema Information
#
# Table name: product_screenshots
#
#  id          :integer          not null, primary key
#  description :text
#  position    :integer
#  published   :boolean          default(FALSE)
#  title       :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  product_id  :integer          not null
#
# Indexes
#
#  index_product_screenshots_on_product_id  (product_id)
#
# Foreign Keys
#
#  product_id  (product_id => products.id)
#
class ProductScreenshot < ApplicationRecord
  belongs_to :product
  
  has_one_attached :image
  
  validates :title, presence: true
  validates :position, presence: true, numericality: { greater_than: 0 }
  validates :image, presence: true
  validate :image_validation
  
  scope :ordered, -> { order(:position) }
  scope :published, -> { where(published: true) }
  
  before_validation :set_position, on: :create
  
  # Generate image variants asynchronously after image is attached
  after_commit :schedule_image_variants, on: [:create, :update], if: :image_attached_and_changed?

  def published?
    published == true
  end
  
  private
  
  def schedule_image_variants
    return unless image.attached?
    ImageVariantJob.perform_later(self.class.name, id, 'image')
  end
  
  def image_attached_and_changed?
    image.attached? && saved_change_to_attribute?('image')
  end
  
  def set_position
    if position.blank?
      max_position = product.product_screenshots.maximum(:position) || 0
      self.position = max_position + 1
    end
  end

  def image_validation
    return unless image.attached?

    # Check file size (10 MB limit for images)
    if image.blob.byte_size > 10.megabytes
      errors.add(:image, "must be less than 10MB")
    end

    # Check content type (image files only)
    allowed_types = [
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 
      'image/webp', 'image/svg+xml'
    ]
    
    unless allowed_types.include?(image.blob.content_type)
      errors.add(:image, "must be an image file (JPEG, PNG, GIF, WebP, SVG)")
    end
  end
end
