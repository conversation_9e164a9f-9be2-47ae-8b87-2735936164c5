# == Schema Information
#
# Table name: product_customers
#
#  id          :integer          not null, primary key
#  description :text
#  name        :string           not null
#  position    :integer          default(0)
#  published   :boolean          default(TRUE), not null
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  product_id  :integer          not null
#
# Indexes
#
#  index_product_customers_on_product_id               (product_id)
#  index_product_customers_on_product_id_and_position  (product_id,position)
#
# Foreign Keys
#
#  product_id  (product_id => products.id)
#
class ProductCustomer < ApplicationRecord
  belongs_to :product
  
  has_one_attached :logo
  
  validates :name, presence: true
  validates :position, presence: true, numericality: { greater_than_or_equal_to: 0 }
  
  scope :ordered, -> { order(:position) }
  scope :published, -> { where(published: true) }
  
  before_create :set_position
  
  # Generate image variants asynchronously after logo is attached
  after_commit :schedule_logo_variants, on: [:create, :update], if: :logo_attached_and_changed?
  
  def logo_type
    return nil unless logo.attached?
    
    if logo.content_type.start_with?('image/')
      'image'
    else
      'other'
    end
  end
  
  def image?
    logo_type == 'image'
  end

  def published?
    published == true
  end
  
  private
  
  def schedule_logo_variants
    return unless logo.attached?
    ImageVariantJob.perform_later(self.class.name, id, 'logo')
  end
  
  def logo_attached_and_changed?
    logo.attached? && saved_change_to_attribute?('logo')
  end
  
  def set_position
    return if position.present?
    
    max_position = product.product_customers.maximum(:position) || 0
    self.position = max_position + 1
  end
end
