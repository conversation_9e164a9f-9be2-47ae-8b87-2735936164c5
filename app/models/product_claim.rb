# == Schema Information
#
# Table name: product_claims
#
#  id         :integer          not null, primary key
#  company    :string
#  email      :string
#  message    :text
#  name       :string
#  status     :string
#  title      :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  product_id :integer          not null
#
# Indexes
#
#  index_product_claims_on_product_id  (product_id)
#
# Foreign Keys
#
#  product_id  (product_id => products.id)
#
class ProductClaim < ApplicationRecord
  belongs_to :product

  validates :name, presence: true
  validates :email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :company, presence: true
  validates :title, presence: true
  validates :message, presence: true

  enum :status, { pending: 'pending', reviewed: 'reviewed', approved: 'approved', rejected: 'rejected' }

  before_create :set_default_status

  private

  def set_default_status
    self.status ||= 'pending'
  end
end
