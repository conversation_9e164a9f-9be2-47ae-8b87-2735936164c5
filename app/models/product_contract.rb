# == Schema Information
#
# Table name: product_contracts
#
#  id          :integer          not null, primary key
#  description :text
#  name        :string           not null
#  position    :integer          default(0)
#  published   :boolean          default(FALSE), not null
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  product_id  :integer          not null
#
# Indexes
#
#  index_product_contracts_on_product_id               (product_id)
#  index_product_contracts_on_product_id_and_position  (product_id,position)
#
# Foreign Keys
#
#  product_id  (product_id => products.id)
#
class ProductContract < ApplicationRecord
  belongs_to :product
  
  # PDF attachment for the contract
  has_one_attached :contract_file
  
  validates :name, presence: true
  validates :position, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validate :contract_file_validation
  
  scope :published, -> { where(published: true) }
  scope :by_position, -> { order(:position) }
  scope :ordered, -> { order(:position) }
  
  private
  
  def contract_file_validation
    return unless contract_file.attached?

    # Check file size (20MB limit)
    if contract_file.blob.byte_size > 20.megabytes
      errors.add(:contract_file, "must be less than 20MB")
    end

    # Check content type (only PDFs)
    unless contract_file.blob.content_type == 'application/pdf'
      errors.add(:contract_file, "must be a PDF file")
    end
  end
end
