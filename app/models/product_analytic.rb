# == Schema Information
#
# Table name: product_analytics
#
#  id         :integer          not null, primary key
#  event_type :string           not null
#  ip_address :string           not null
#  metadata   :json
#  user_agent :text             not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  product_id :integer          not null
#  user_id    :integer
#  visitor_id :string           not null
#
# Indexes
#
#  index_product_analytics_on_created_at                 (created_at)
#  index_product_analytics_on_product_id                 (product_id)
#  index_product_analytics_on_product_id_and_event_type  (product_id,event_type)
#  index_product_analytics_on_user_id                    (user_id)
#  index_product_analytics_on_visitor_id                 (visitor_id)
#
# Foreign Keys
#
#  product_id  (product_id => products.id)
#  user_id     (user_id => users.id)
#
class ProductAnalytic < ApplicationRecord
  belongs_to :product
  belongs_to :user, optional: true
  
  validates :product_id, :ip_address, :user_agent, :event_type, :visitor_id, presence: true
  validates :event_type, inclusion: { in: %w[page_view content_view content_download video_play video_watch_duration] }
  
  scope :page_views, -> { where(event_type: 'page_view') }
  scope :content_views, -> { where(event_type: 'content_view') }
  scope :content_downloads, -> { where(event_type: 'content_download') }
  scope :video_plays, -> { where(event_type: 'video_play') }
  scope :video_watch_durations, -> { where(event_type: 'video_watch_duration') }
  scope :for_product, ->(product) { where(product: product) }
  scope :in_date_range, ->(start_date, end_date) { where(created_at: start_date..end_date) }
  scope :today, -> { where(created_at: Date.current.beginning_of_day..Date.current.end_of_day) }
  scope :this_week, -> { where(created_at: 1.week.ago..Time.current) }
  scope :this_month, -> { where(created_at: 1.month.ago..Time.current) }
  
  # Class methods for analytics
  def self.total_page_views_for_product(product)
    for_product(product).page_views.count
  end
  
  def self.total_content_views_for_product(product)
    for_product(product).content_views.count
  end
  
  def self.total_content_downloads_for_product(product)
    for_product(product).content_downloads.count
  end
  
  def self.unique_visitors_for_product(product, period = :all_time)
    analytics = case period
                when :today
                  for_product(product).today
                when :week
                  for_product(product).this_week
                when :month
                  for_product(product).this_month
                else
                  for_product(product)
                end
    
    # Group by visitor_id and date (24-hour periods) to count unique visitors
    # Only count a visitor once per 24-hour period
    analytics.page_views
            .group(:visitor_id)
            .group('DATE(created_at)')
            .count
            .keys
            .map(&:first) # Extract visitor_ids
            .uniq
            .count
  end
  
  def self.analytics_summary_for_product(product, period = :all_time)
    {
      total_page_views: total_page_views_for_product(product),
      total_content_views: total_content_views_for_product(product),
      total_content_downloads: total_content_downloads_for_product(product),
      unique_visitors: unique_visitors_for_product(product, period),
      period: period
    }
  end
  
  # Instance methods
  def browser_info
    return {} unless metadata.present?
    metadata.slice('browser', 'os', 'device_type')
  end
  
  def location_info
    return {} unless metadata.present?
    metadata.slice('country', 'region', 'city')
  end
  
  def mobile_device?
    metadata.dig('device_type') == 'mobile'
  end
  
  def desktop_device?
    metadata.dig('device_type') == 'desktop'
  end
  
  def user_type_label
    return 'Anonymous' unless user.present?
    
    if user.super_admin?
      'Admin'
    elsif user.has_vendor_account?
      'Vendor'
    elsif user.has_agency_account?
      'Government'
    else
      'User'
    end
  end
  
  # Content-related methods
  def content_type
    metadata.dig('content_type')
  end
  
  def content_id
    metadata.dig('content_id')
  end
  
  # Video-related methods
  def watch_duration
    metadata.dig('watch_duration')&.to_f
  end
  
  def video_duration
    metadata.dig('video_duration')&.to_f
  end
  
  def watch_percentage
    return nil unless watch_duration && video_duration && video_duration > 0
    (watch_duration / video_duration * 100).round(1)
  end
  
  def content_type_display
    return nil unless content_type.present?
    
    case content_type
    when 'product_screenshot'
      'Product Screenshot'
    when 'case_study'
      'Case Study'
    when 'product_video'
      'Product Video'
    when 'product_feature'
      'Product Feature'
    when 'product_customer'
      'Product Customer'
    when 'product_content'
      'Product Content'
    else
      content_type.humanize
    end
  end
  
  def event_type_display
    case event_type
    when 'page_view'
      'Page View'
    when 'content_view'
      'Content View'
    when 'content_download'
      'Content Download'
    when 'video_play'
      'Video Play'
    when 'video_watch_duration'
      'Video Watch'
    else
      event_type.humanize
    end
  end
end
