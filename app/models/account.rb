# == Schema Information
#
# Table name: accounts
#
#  id                    :integer          not null, primary key
#  account_type          :string
#  approved_at           :datetime
#  confirmation_sent_at  :datetime
#  confirmation_token    :string
#  confirmed_at          :datetime
#  description           :text
#  headquarters_location :string
#  linkedin_url          :string
#  name                  :string
#  slug                  :string
#  status                :string
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#  approved_by_id        :integer
#  owner_id              :integer          not null
#
# Indexes
#
#  index_accounts_on_approved_by_id      (approved_by_id)
#  index_accounts_on_confirmation_token  (confirmation_token) UNIQUE
#  index_accounts_on_owner_id            (owner_id)
#  index_accounts_on_slug                (slug)
#
# Foreign Keys
#
#  approved_by_id  (approved_by_id => users.id)
#  owner_id        (owner_id => users.id)
#
class Account < ApplicationRecord
  include EmailDelivery
  extend FriendlyId
  friendly_id :name, use: :slugged
  
  belongs_to :owner, class_name: "User"
  belongs_to :approved_by, class_name: "User", optional: true
  has_many :account_users, dependent: :destroy
  has_many :users, through: :account_users
  has_many :products, dependent: :destroy
  has_many :team_invitations, dependent: :destroy
  has_many :leads, dependent: :destroy
  has_many :agency_leads, class_name: "Lead", foreign_key: "agency_account_id", dependent: :destroy
  has_many :lists, dependent: :destroy
  
  has_one_attached :logo
  has_rich_text :description

  validates :name, presence: true
  validates :account_type, inclusion: { in: %w[vendor agency admin] }
  validates :status, inclusion: { in: %w[pending approved rejected] }
  validate :logo_validation

  before_create :generate_confirmation_token
  
  # Generate image variants asynchronously after logo is attached
  after_commit :schedule_logo_variants, on: [:create, :update], if: :logo_attached_and_changed?

  enum :account_type, { vendor: "vendor", agency: "agency", admin: "admin" }
  enum :status, { pending: "pending", approved: "approved", rejected: "rejected" }

  scope :vendors, -> { where(account_type: "vendor") }
  scope :agencies, -> { where(account_type: "agency") }
  scope :admins, -> { where(account_type: "admin") }
  scope :approved, -> { where(status: "approved") }
  scope :pending_approval, -> { where(status: "pending") }
  scope :rejected, -> { where(status: "rejected") }

  def vendor?
    account_type == "vendor"
  end

  def agency?
    account_type == "agency"
  end

  def admin?
    account_type == "admin"
  end

  def approved?
    status == "approved"
  end

  def pending?
    status == "pending"
  end

  def rejected?
    status == "rejected"
  end

  def approve!(approved_by_user)
    update!(
      status: "approved",
      approved_at: Time.current,
      approved_by: approved_by_user
    )
    
    # Send approval email to account owner
    deliver_email(UserMailer.account_approved(owner)) if owner
  end

  def reject!(rejected_by_user)
    update!(
      status: "rejected",
      approved_at: nil,
      approved_by: rejected_by_user
    )
    
    # Send rejection email to account owner
    deliver_email(UserMailer.account_rejected(owner)) if owner
  end

  def confirmed?
    confirmed_at.present?
  end

  def send_confirmation_email
    self.update!(confirmation_sent_at: Time.current)
    UserMailer.welcome(self.owner, self).deliver_now
  end


  def logo_thumbnail
    return unless logo.attached?
    logo.variant(resize_to_limit: [50, nil], format: :webp, saver: { quality: 85 })
  end

  def logo_small
    return unless logo.attached?
    logo.variant(resize_to_limit: [100, nil], format: :webp, saver: { quality: 85 })
  end

  def logo_medium
    return unless logo.attached?
    logo.variant(resize_to_limit: [200, nil], format: :webp, saver: { quality: 85 })
  end

  def logo_large
    return unless logo.attached?
    logo.variant(resize_to_limit: [400, nil], format: :webp, saver: { quality: 85 })
  end

  private
  
  def schedule_logo_variants
    return unless logo.attached?
    ImageVariantJob.perform_later(self.class.name, id, 'logo')
  end
  
  def logo_attached_and_changed?
    logo.attached? && saved_change_to_attribute?('logo')
  end

  def generate_confirmation_token
    self.confirmation_token = SecureRandom.urlsafe_base64(32)
  end

  def logo_validation
    return unless logo.attached?

    # Check file size
    if logo.blob.byte_size > 5.megabytes
      errors.add(:logo, "must be less than 5MB")
    end

    # Check content type
    allowed_types = %w[image/jpeg image/jpg image/png image/gif image/webp image/svg+xml]
    unless allowed_types.include?(logo.blob.content_type)
      errors.add(:logo, "must be a valid image format (JPEG, PNG, GIF, WebP, or SVG)")
    end
  end
end
