# == Schema Information
#
# Table name: team_invitations
#
#  id            :integer          not null, primary key
#  email         :string
#  expires_at    :datetime
#  message       :text
#  role          :string
#  status        :string           default("pending")
#  token         :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  account_id    :integer          not null
#  invited_by_id :integer          not null
#
# Indexes
#
#  index_team_invitations_on_account_id            (account_id)
#  index_team_invitations_on_account_id_and_email  (account_id,email) UNIQUE
#  index_team_invitations_on_invited_by_id         (invited_by_id)
#  index_team_invitations_on_token                 (token) UNIQUE
#
# Foreign Keys
#
#  account_id     (account_id => accounts.id)
#  invited_by_id  (invited_by_id => users.id)
#
class TeamInvitation < ApplicationRecord
  belongs_to :account
  belongs_to :invited_by, class_name: 'User'

  validates :email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :role, inclusion: { in: %w[admin member] }
  validates :email, uniqueness: { scope: :account_id, message: "has already been invited to this account" }
  validate :email_domain_matches_account, unless: :invited_by_super_admin?

  enum :status, { pending: 'pending', accepted: 'accepted', declined: 'declined', expired: 'expired' }

  before_create :generate_token_and_expiry

  scope :active, -> { where(status: 'pending').where('expires_at > ?', Time.current) }

  def expired?
    expires_at < Time.current
  end

  def accept!(user)
    return false if expired?
    
    ActiveRecord::Base.transaction do
      account.account_users.create!(
        user: user,
        role: role,
        joined_at: Time.current
      )
      update!(status: 'accepted')
    end
    true
  rescue ActiveRecord::RecordInvalid
    false
  end

  def decline!
    update!(status: 'declined')
  end

  def account_email_domain
    return nil unless account&.owner&.email_address
    account.owner.email_address.split('@').last.downcase
  end

  def invitation_email_domain
    return nil unless email
    email.split('@').last.downcase
  end

  def invited_by_super_admin?
    invited_by&.super_admin?
  end

  private

  def generate_token_and_expiry
    begin
      self.token = SecureRandom.urlsafe_base64(32)
    end while TeamInvitation.exists?(token: token)
    self.expires_at = 7.days.from_now
  end

  def email_domain_matches_account
    return unless account&.owner&.email_address && email

    account_domain = account_email_domain
    invite_domain = invitation_email_domain

    if account_domain != invite_domain
      errors.add(:email, "must have the same email domain as the account (@#{account_domain})")
    end
  end
end
