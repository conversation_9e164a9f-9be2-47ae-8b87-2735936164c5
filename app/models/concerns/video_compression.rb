module VideoCompression
  extend ActiveSupport::Concern
  
  included do
    # Hook for models to define their video attachments
    class_attribute :video_attachments_config, default: {}
  end
  
  class_methods do
    # Define video attachments that should be compressed
    # 
    # Examples:
    #   has_video_attachment :featured_video, generate_preview: true
    #   has_video_attachment :video_file, generate_preview: false
    #   has_video_attachment :demo_video, generate_preview: true, preview_attachment: :demo_preview
    def has_video_attachment(attachment_name, options = {})
      options = {
        generate_preview: false,
        preview_attachment: nil
      }.merge(options)
      
      # Store configuration
      self.video_attachments_config = video_attachments_config.merge(
        attachment_name.to_s => options
      )
      
      # Define preview attachment if needed
      if options[:generate_preview]
        preview_name = options[:preview_attachment] || :"#{attachment_name}_preview"
        has_one_attached preview_name
      end
    end
  end
  
  # Method to manually trigger video compression
  def compress_video_attachment(attachment_name)
    attachment = send(attachment_name)
    
    return false unless attachment.attached?
    return false unless attachment.content_type&.start_with?('video/')
    
    Rails.logger.info "Scheduling video compression for #{self.class.name}##{id} (#{attachment_name})"
    
    VideoCompressionJob.perform_later(
      self.class.name,
      id,
      attachment_name.to_s
    )
    
    true
  end
end