# == Schema Information
#
# Table name: leads
#
#  id                :integer          not null, primary key
#  budget_range      :string
#  contact_company   :string
#  contact_email     :string
#  contact_name      :string
#  contact_phone     :string
#  message           :text
#  notes             :text
#  source            :string
#  status            :string
#  timeline          :string
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  account_id        :integer
#  agency_account_id :integer
#  product_id        :integer          not null
#  user_id           :integer
#
# Indexes
#
#  index_leads_on_account_id         (account_id)
#  index_leads_on_agency_account_id  (agency_account_id)
#  index_leads_on_product_id         (product_id)
#  index_leads_on_user_id            (user_id)
#
# Foreign Keys
#
#  account_id         (account_id => accounts.id)
#  agency_account_id  (agency_account_id => accounts.id)
#  product_id         (product_id => products.id)
#  user_id            (user_id => users.id)
#
class Lead < ApplicationRecord
  belongs_to :product
  belongs_to :account, optional: true
  belongs_to :user, optional: true
  belongs_to :agency_account, class_name: 'Account', optional: true

  validates :status, inclusion: { in: %w[pending contacted qualified closed] }
  validates :contact_name, presence: true
  validates :contact_email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :contact_company, presence: true, on: :create

  enum :status, { pending: 'pending', contacted: 'contacted', qualified: 'qualified', closed: 'closed' }

  scope :recent, -> { order(created_at: :desc) }
end
