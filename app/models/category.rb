# == Schema Information
#
# Table name: categories
#
#  id          :integer          not null, primary key
#  description :text
#  name        :string
#  slug        :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  parent_id   :integer
#
# Indexes
#
#  index_categories_on_parent_id  (parent_id)
#
# Foreign Keys
#
#  parent_id  (parent_id => categories.id)
#
class Category < ApplicationRecord
  extend FriendlyId
  friendly_id :name, use: :slugged

  # Hierarchical relationships
  belongs_to :parent, class_name: 'Category', optional: true
  has_many :subcategories, class_name: 'Category', foreign_key: 'parent_id', dependent: :destroy

  # Product relationships - categories at any level can have products
  has_many :product_categories, dependent: :destroy
  has_many :products, through: :product_categories

  validates :name, presence: true, uniqueness: { scope: :parent_id }
  validate :cannot_be_parent_of_itself
  validate :cannot_create_circular_reference
  validate :cannot_have_products_if_has_subcategories

  scope :root_categories, -> { where(parent_id: nil) }
  scope :child_categories, -> { where.not(parent_id: nil) }
  scope :with_products, -> { joins(:products).distinct }
  scope :alphabetical, -> { order(:name) }
  scope :leaf_categories, -> { where.not(id: Category.select(:parent_id).where.not(parent_id: nil)) }

  def root_category?
    parent_id.nil?
  end

  def child_category?
    !root_category?
  end

  def leaf_category?
    subcategories.empty?
  end

  def depth
    return 0 if parent_id.nil?
    parent.depth + 1
  end

  def ancestors
    return [] if parent_id.nil?
    parent.ancestors + [parent]
  end

  def root_category
    return self if parent_id.nil?
    parent.root_category
  end

  def descendants
    subcategories.flat_map { |child| [child] + child.descendants }
  end

  def full_name
    return name if parent_id.nil?
    ancestors.map(&:name).join(' > ') + " > #{name}"
  end

  def breadcrumb_path
    ancestors + [self]
  end

  private

  def cannot_be_parent_of_itself
    return unless parent_id.present?

    if parent_id == id
      errors.add(:parent_id, "cannot be the same as the category itself")
    end
  end

  def cannot_create_circular_reference
    return unless parent_id.present?

    if ancestors.map(&:id).include?(id)
      errors.add(:parent_id, "would create a circular reference")
    end
  end

  def cannot_have_products_if_has_subcategories
    return unless subcategories.any? && products.any?
    
    errors.add(:base, "Categories with subcategories cannot have products. Products can only be added to leaf categories (categories without subcategories).")
  end
end
