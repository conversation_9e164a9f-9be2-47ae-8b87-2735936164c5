# == Schema Information
#
# Table name: product_videos
#
#  id          :integer          not null, primary key
#  description :text
#  position    :integer
#  published   :boolean          default(FALSE)
#  title       :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  product_id  :integer          not null
#
# Indexes
#
#  index_product_videos_on_product_id  (product_id)
#
# Foreign Keys
#
#  product_id  (product_id => products.id)
#
class ProductVideo < ApplicationRecord
  include VideoCompression
  
  belongs_to :product
  has_one_attached :video_file
  
  # Configure video compression for video_file attachment (no preview for product videos)
  has_video_attachment :video_file, generate_preview: false

  validates :title, presence: true
  validates :description, presence: true
  validates :video_file, presence: true
  validate :video_file_validation

  scope :published, -> { where(published: true) }
  scope :by_position, -> { order(:position) }


  def published?
    published == true
  end

  private


  def video_file_validation
    return unless video_file.attached?

    # Check file size (100 MB limit)
    if video_file.blob.byte_size > 100.megabytes
      errors.add(:video_file, "must be less than 100MB")
    end

    # Check content type
    allowed_types = %w[video/mp4 video/webm video/ogg video/avi video/mov video/wmv video/flv video/mkv]
    unless allowed_types.include?(video_file.blob.content_type)
      errors.add(:video_file, "must be a valid video format (MP4, WebM, OGG, AVI, MOV, WMV, FLV, MKV)")
    end
  end
end
