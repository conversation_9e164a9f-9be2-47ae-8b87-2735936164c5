import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["button", "content"]

  connect() {
    console.log('✅ Simple tabs controller connected!')
    console.log('Button targets:', this.buttonTargets.length)
    console.log('Content targets:', this.contentTargets.length)
  }

  showTab(event) {
    console.log('🖱️ Tab clicked:', event.currentTarget.dataset.tab)
    const tabId = event.currentTarget.dataset.tab
    
    // Show/hide content
    this.contentTargets.forEach(content => {
      if (content.dataset.tabContent === tabId) {
        content.classList.remove('hidden')
        content.classList.add('active-tab-content', 'block')
        console.log('Showing:', tabId)
      } else {
        content.classList.add('hidden')
        content.classList.remove('active-tab-content', 'block')
        console.log('Hiding:', content.dataset.tabContent)
      }
    })

    // Update button styles
    this.buttonTargets.forEach(button => {
      if (button.dataset.tab === tabId) {
        button.classList.remove('border-transparent', 'text-gray-500', 'dark:text-gray-400')
        button.classList.add('border-indigo-500', 'dark:border-indigo-400', 'text-indigo-600', 'dark:text-indigo-400')
      } else {
        button.classList.add('border-transparent', 'text-gray-500', 'dark:text-gray-400')
        button.classList.remove('border-indigo-500', 'dark:border-indigo-400', 'text-indigo-600', 'dark:text-indigo-400')
      }
    })
  }
}