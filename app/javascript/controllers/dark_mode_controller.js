import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["text"]

  connect() {
    // Check for saved preference or default to light mode
    const savedTheme = localStorage.getItem('theme')
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    
    if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
      this.enableDarkMode()
    } else {
      this.enableLightMode()
    }
  }

  toggle() {
    if (document.documentElement.classList.contains('dark')) {
      this.enableLightMode()
    } else {
      this.enableDarkMode()
    }
  }

  enableDarkMode() {
    document.documentElement.classList.add('dark')
    localStorage.setItem('theme', 'dark')
    this.updateText()
  }

  enableLightMode() {
    document.documentElement.classList.remove('dark')
    localStorage.setItem('theme', 'light')
    this.updateText()
  }

  updateText() {
    const isDark = document.documentElement.classList.contains('dark')
    
    if (this.hasTextTarget) {
      this.textTarget.textContent = isDark ? 'Light Mode' : 'Dark Mode'
    }
  }
}