import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["modal", "existingLists", "modalProductName", "newListForm", "newListName", "newListDescription"]
  
  connect() {
    this.setupEventListeners()
  }
  
  setupEventListeners() {
    // Save to list button click handler
    document.addEventListener('click', (e) => {
      if (e.target.closest('.save-to-list-btn')) {
        e.preventDefault()
        const button = e.target.closest('.save-to-list-btn')
        const productId = button.dataset.productId
        const productName = button.dataset.productName
        this.openModal(productId, productName)
      }
    })
    
    // Close modal handlers
    document.addEventListener('click', (e) => {
      if (e.target.closest('.close-modal') || e.target.id === 'saveToListModal') {
        this.closeModal()
      }
    })
    
    // Prevent modal from closing when clicking inside
    document.addEventListener('click', (e) => {
      if (e.target.closest('#saveToListModal .bg-white, #saveToListModal .bg-gray-800')) {
        e.stopPropagation()
      }
    })
    
    // ESC key to close modal
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.closeModal()
      }
    })
  }
  
  openModal(productId, productName) {
    this.currentProductId = productId
    
    const modal = document.getElementById('saveToListModal')
    const modalProductName = document.getElementById('modalProductName')
    
    if (modal && modalProductName) {
      modalProductName.textContent = productName
      modal.classList.remove('hidden')
      this.loadExistingLists(productId)
    }
  }
  
  closeModal() {
    const modal = document.getElementById('saveToListModal')
    if (modal) {
      modal.classList.add('hidden')
      this.resetForm()
    }
  }
  
  loadExistingLists(productId) {
    const existingListsContainer = document.getElementById('existingLists')
    if (!existingListsContainer) return
    
    existingListsContainer.innerHTML = '<div class="text-center py-2 text-sm text-gray-500">Loading lists...</div>'
    
    fetch(`/agencies/lists/for_product/${productId}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.content
      }
    })
    .then(response => response.json())
    .then(lists => {
      this.renderExistingLists(lists)
    })
    .catch(error => {
      console.error('Error loading lists:', error)
      existingListsContainer.innerHTML = '<div class="text-center py-2 text-sm text-red-500">Error loading lists</div>'
    })
  }
  
  renderExistingLists(lists) {
    const existingListsContainer = document.getElementById('existingLists')
    if (!existingListsContainer) return
    
    if (lists.length === 0) {
      existingListsContainer.innerHTML = '<div class="text-center py-2 text-sm text-gray-500">No lists yet. Create your first one below.</div>'
      return
    }
    
    existingListsContainer.innerHTML = lists.map(list => `
      <div class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
        <div class="flex-1">
          <h5 class="font-medium text-gray-900 dark:text-gray-100">${this.escapeHtml(list.name)}</h5>
          ${list.description ? `<p class="text-sm text-gray-500 dark:text-gray-400">${this.escapeHtml(list.description)}</p>` : ''}
        </div>
        <button type="button" 
                class="add-to-list-btn ml-3 px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
                  list.has_product 
                    ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 cursor-default' 
                    : 'bg-indigo-600 text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500'
                }"
                data-list-id="${list.id}"
                ${list.has_product ? 'disabled' : ''}>
          ${list.has_product ? 'Already Added' : 'Add to List'}
        </button>
      </div>
    `).join('')
    
    // Add event listeners for add to list buttons
    existingListsContainer.addEventListener('click', (e) => {
      if (e.target.closest('.add-to-list-btn') && !e.target.closest('.add-to-list-btn').disabled) {
        const button = e.target.closest('.add-to-list-btn')
        const listId = button.dataset.listId
        this.addToList(listId)
      }
    })
  }
  
  addToList(listId) {
    const button = document.querySelector(`[data-list-id="${listId}"]`)
    if (button) {
      button.disabled = true
      button.innerHTML = 'Adding...'
    }
    
    fetch(`/agencies/lists/${listId}/items`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.content
      },
      body: JSON.stringify({
        product_id: this.currentProductId
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        this.showSuccessMessage(data.message)
        this.closeModal()
      } else {
        this.showErrorMessage(data.errors ? data.errors.join(', ') : 'Failed to add to list')
        if (button) {
          button.disabled = false
          button.innerHTML = 'Add to List'
        }
      }
    })
    .catch(error => {
      console.error('Error adding to list:', error)
      this.showErrorMessage('Failed to add to list')
      if (button) {
        button.disabled = false
        button.innerHTML = 'Add to List'
      }
    })
  }
  
  createNewList(event) {
    event.preventDefault()
    
    const formData = new FormData(event.target)
    const submitButton = event.target.querySelector('button[type="submit"]')
    
    if (submitButton) {
      submitButton.disabled = true
      submitButton.innerHTML = 'Creating...'
    }
    
    fetch('/agencies/lists', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.content
      },
      body: formData
    })
    .then(response => response.json())
    .then(data => {
      if (data.id) {
        // List created successfully, now add the product to it
        this.addToList(data.id)
      } else {
        this.showErrorMessage(data.errors ? data.errors.join(', ') : 'Failed to create list')
        if (submitButton) {
          submitButton.disabled = false
          submitButton.innerHTML = 'Create List & Save Product'
        }
      }
    })
    .catch(error => {
      console.error('Error creating list:', error)
      this.showErrorMessage('Failed to create list')
      if (submitButton) {
        submitButton.disabled = false
        submitButton.innerHTML = 'Create List & Save Product'
      }
    })
  }
  
  resetForm() {
    const form = document.getElementById('newListForm')
    if (form) {
      form.reset()
      const submitButton = form.querySelector('button[type="submit"]')
      if (submitButton) {
        submitButton.disabled = false
        submitButton.innerHTML = 'Create List & Save Product'
      }
    }
  }
  
  showSuccessMessage(message) {
    // Create and show a temporary success message
    const alertDiv = document.createElement('div')
    alertDiv.className = 'fixed top-4 right-4 bg-green-100 dark:bg-green-900 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-200 px-4 py-3 rounded-lg shadow-lg z-50'
    alertDiv.innerHTML = `
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
        </svg>
        <span>${this.escapeHtml(message)}</span>
      </div>
    `
    
    document.body.appendChild(alertDiv)
    
    setTimeout(() => {
      alertDiv.remove()
    }, 5000)
  }
  
  showErrorMessage(message) {
    // Create and show a temporary error message
    const alertDiv = document.createElement('div')
    alertDiv.className = 'fixed top-4 right-4 bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg shadow-lg z-50'
    alertDiv.innerHTML = `
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
        </svg>
        <span>${this.escapeHtml(message)}</span>
      </div>
    `
    
    document.body.appendChild(alertDiv)
    
    setTimeout(() => {
      alertDiv.remove()
    }, 5000)
  }
  
  escapeHtml(text) {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }
}

// Initialize the controller when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new (class extends Controller {
    connect() {
      this.setupEventListeners()
    }
    
    setupEventListeners() {
      // Handle new list form submission
      document.addEventListener('submit', (e) => {
        if (e.target.id === 'newListForm') {
          e.preventDefault()
          this.createNewList(e)
        }
      })
    }
    
    createNewList(event) {
      const formData = new FormData(event.target)
      const submitButton = event.target.querySelector('button[type="submit"]')
      
      if (submitButton) {
        submitButton.disabled = true
        submitButton.innerHTML = 'Creating...'
      }
      
      fetch('/agencies/lists', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.content
        },
        body: formData
      })
      .then(response => response.json())
      .then(data => {
        if (data.id) {
          // Get the current product ID from the modal
          const saveButton = document.querySelector('.save-to-list-btn')
          const productId = saveButton ? saveButton.dataset.productId : null
          
          if (productId) {
            // Add the product to the new list
            fetch(`/agencies/lists/${data.id}/items`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.content
              },
              body: JSON.stringify({
                product_id: productId
              })
            })
            .then(response => response.json())
            .then(itemData => {
              if (itemData.success) {
                this.showSuccessMessage(`Created "${data.name}" and added product successfully!`)
                this.closeModal()
              } else {
                this.showErrorMessage('List created but failed to add product')
              }
            })
          } else {
            this.showSuccessMessage(`List "${data.name}" created successfully!`)
            this.closeModal()
          }
        } else {
          this.showErrorMessage(data.errors ? data.errors.join(', ') : 'Failed to create list')
        }
        
        if (submitButton) {
          submitButton.disabled = false
          submitButton.innerHTML = 'Create List & Save Product'
        }
      })
      .catch(error => {
        console.error('Error creating list:', error)
        this.showErrorMessage('Failed to create list')
        if (submitButton) {
          submitButton.disabled = false
          submitButton.innerHTML = 'Create List & Save Product'
        }
      })
    }
    
    closeModal() {
      const modal = document.getElementById('saveToListModal')
      if (modal) {
        modal.classList.add('hidden')
        const form = document.getElementById('newListForm')
        if (form) {
          form.reset()
        }
      }
    }
    
    showSuccessMessage(message) {
      const alertDiv = document.createElement('div')
      alertDiv.className = 'fixed top-4 right-4 bg-green-100 dark:bg-green-900 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-200 px-4 py-3 rounded-lg shadow-lg z-50'
      alertDiv.innerHTML = `
        <div class="flex items-center">
          <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <span>${this.escapeHtml(message)}</span>
        </div>
      `
      
      document.body.appendChild(alertDiv)
      
      setTimeout(() => {
        alertDiv.remove()
      }, 5000)
    }
    
    showErrorMessage(message) {
      const alertDiv = document.createElement('div')
      alertDiv.className = 'fixed top-4 right-4 bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg shadow-lg z-50'
      alertDiv.innerHTML = `
        <div class="flex items-center">
          <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
          <span>${this.escapeHtml(message)}</span>
        </div>
      `
      
      document.body.appendChild(alertDiv)
      
      setTimeout(() => {
        alertDiv.remove()
      }, 5000)
    }
    
    escapeHtml(text) {
      const div = document.createElement('div')
      div.textContent = text
      return div.innerHTML
    }
  })()
})