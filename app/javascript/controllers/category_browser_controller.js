import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["rootCard", "subCard", "leafCard", "breadcrumb"]
  static values = { 
    categoriesUrl: String,
    productsUrl: String
  }

  connect() {
    this.loadRootCategories()
    this.selectedPath = []
  }

  async loadRootCategories() {
    try {
      const response = await fetch(`${this.categoriesUrlValue}?format=json`)
      const data = await response.json()
      this.renderRootCategories(data.root_categories)
    } catch (error) {
      console.error('Error loading root categories:', error)
    }
  }

  renderRootCategories(categories) {
    const html = categories.map(category => `
      <div class="category-item p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors group"
           data-action="click->category-browser#selectRootCategory"
           data-category-id="${category.id}"
           data-category-name="${category.name}">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
              ${category.name}
            </h3>
            <div class="mt-2">
              <span class="text-xs text-gray-500 dark:text-gray-400">
                ${category.subcategories_count} subcategories
              </span>
            </div>
          </div>
          <div class="text-gray-400 dark:text-gray-500 group-hover:text-blue-500 dark:group-hover:text-blue-400 transition-colors">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </div>
        </div>
      </div>
    `).join('')

    this.rootCardTarget.innerHTML = `
      <div class="space-y-3">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Browse by Category</h2>
        ${html}
      </div>
    `
  }

  async selectRootCategory(event) {
    const categoryId = event.currentTarget.dataset.categoryId
    const categoryName = event.currentTarget.dataset.categoryName
    
    this.selectedPath = [{ id: categoryId, name: categoryName }]
    this.updateBreadcrumb()
    
    // Hide the third card when selecting a root category
    this.leafCardTarget.classList.add('hidden')
    
    try {
      const response = await fetch(`${this.categoriesUrlValue}/${categoryId}?format=json`)
      const data = await response.json()
      this.renderSubcategories(data.subcategories, data.products)
    } catch (error) {
      console.error('Error loading subcategories:', error)
    }
  }

  renderSubcategories(subcategories, products) {
    let html = ''
    
    if (subcategories && subcategories.length > 0) {
      html = subcategories.map(category => `
        <div class="category-item p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors group"
             data-action="click->category-browser#selectSubcategory"
             data-category-id="${category.id}"
             data-category-name="${category.name}">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                ${category.name}
              </h3>
              <div class="mt-2">
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  ${category.products_count} products
                </span>
              </div>
            </div>
            <div class="text-gray-400 dark:text-gray-500 group-hover:text-blue-500 dark:group-hover:text-blue-400 transition-colors">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </div>
          </div>
        </div>
      `).join('')
    } else {
      html = `
        <div class="text-center py-8 text-gray-500 dark:text-gray-400">
          <p>No subcategories available</p>
        </div>
      `
    }

    this.subCardTarget.innerHTML = `
      <div class="space-y-3">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Subcategories</h3>
        ${html}
      </div>
    `
    
    this.subCardTarget.classList.remove('hidden')
  }

  async selectSubcategory(event) {
    const categoryId = event.currentTarget.dataset.categoryId
    const categoryName = event.currentTarget.dataset.categoryName
    
    this.selectedPath.push({ id: categoryId, name: categoryName })
    this.updateBreadcrumb()
    
    try {
      const response = await fetch(`${this.categoriesUrlValue}/${categoryId}?format=json`)
      const data = await response.json()
      this.renderProducts(data.products)
    } catch (error) {
      console.error('Error loading products:', error)
    }
  }

  renderProducts(products) {
    let html = ''
    
    if (products && products.length > 0) {
      html = products.map(product => `
        <div class="product-item p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors group"
             data-action="click->category-browser#viewProduct"
             data-product-id="${product.id}"
             data-product-slug="${product.slug}">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <h4 class="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                ${product.name}
              </h4>
              <p class="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                ${product.description || ''}
              </p>
              <div class="mt-2">
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  by ${product.company_name}
                </span>
              </div>
            </div>
            <div class="text-gray-400 dark:text-gray-500 group-hover:text-blue-500 dark:group-hover:text-blue-400 transition-colors ml-4">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
              </svg>
            </div>
          </div>
        </div>
      `).join('')
    } else {
      html = `
        <div class="text-center py-8 text-gray-500 dark:text-gray-400">
          <p>No products available in this category</p>
        </div>
      `
    }

    this.leafCardTarget.innerHTML = `
      <div class="space-y-3">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Products</h3>
          <button class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
                  data-action="click->category-browser#viewAllProducts">
            View All Products →
          </button>
        </div>
        ${html}
      </div>
    `
    
    this.leafCardTarget.classList.remove('hidden')
  }

  updateBreadcrumb() {
    const breadcrumbHtml = this.selectedPath.map((item, index) => {
      const isLast = index === this.selectedPath.length - 1
      return `
        <span class="flex items-center">
          <button class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors ${isLast ? 'font-semibold' : ''}"
                  data-action="click->category-browser#navigateToLevel"
                  data-level="${index}">
            ${item.name}
          </button>
          ${!isLast ? '<svg class="w-4 h-4 mx-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg>' : ''}
        </span>
      `
    }).join('')

    this.breadcrumbTarget.innerHTML = `
      <div class="flex items-center text-sm mb-4">
        <button class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
                data-action="click->category-browser#resetBrowser">
          All Categories
        </button>
        ${this.selectedPath.length > 0 ? '<svg class="w-4 h-4 mx-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg>' : ''}
        ${breadcrumbHtml}
      </div>
    `
    
    // Show breadcrumb when we have a path
    if (this.selectedPath.length > 0) {
      this.breadcrumbTarget.classList.remove('hidden')
    } else {
      this.breadcrumbTarget.classList.add('hidden')
    }
  }

  navigateToLevel(event) {
    const level = parseInt(event.currentTarget.dataset.level)
    this.selectedPath = this.selectedPath.slice(0, level + 1)
    this.updateBreadcrumb()
    
    if (level === 0) {
      this.selectRootCategory({ currentTarget: { dataset: { categoryId: this.selectedPath[0].id, categoryName: this.selectedPath[0].name } } })
    } else {
      this.selectSubcategory({ currentTarget: { dataset: { categoryId: this.selectedPath[level].id, categoryName: this.selectedPath[level].name } } })
    }
  }

  resetBrowser() {
    this.selectedPath = []
    this.subCardTarget.classList.add('hidden')
    this.leafCardTarget.classList.add('hidden')
    this.breadcrumbTarget.innerHTML = ''
    this.loadRootCategories()
  }

  viewAllProducts() {
    const categoryId = this.selectedPath[this.selectedPath.length - 1].id
    window.location.href = `${this.productsUrlValue}?category_id=${categoryId}`
  }

  viewProduct(event) {
    const productSlug = event.currentTarget.dataset.productSlug
    window.location.href = `/marketplace/products/${productSlug}`
  }
}