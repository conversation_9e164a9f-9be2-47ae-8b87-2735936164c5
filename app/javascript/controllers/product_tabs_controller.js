import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["button", "content"]

  connect() {
    console.log('🎯 Tabs controller connected!')
    console.log('Button targets found:', this.buttonTargets.length)
    console.log('Content targets found:', this.contentTargets.length)
    
    // Bind the hash change handler for proper cleanup
    this.boundShowTabFromHash = () => this.showTabFromHash()
    
    // Simple test - add click listeners manually as backup
    this.element.querySelectorAll('[data-tab]').forEach(button => {
      console.log('Found button with tab:', button.dataset.tab)
      button.addEventListener('click', (e) => {
        console.log('🖱️ Manual click listener fired for:', e.target.dataset.tab)
        this.activateTab(e.target.dataset.tab)
      })
    })
    
    this.showTabFromHash()
    window.addEventListener('hashchange', this.boundShowTabFromHash)
  }

  disconnect() {
    // Use the bound handler for proper cleanup
    window.removeEventListener('hashchange', this.boundShowTabFromHash)
  }

  showTab(event) {
    console.log('showTab called')
    const tabId = event.currentTarget.dataset.tab
    console.log('Switching to tab:', tabId)
    this.activateTab(tabId)
    
    // Update URL hash
    window.history.pushState(null, null, `#${tabId}`)
  }

  showTabFromHash() {
    const hash = window.location.hash.substring(1)
    if (hash && this.hasTabContent(hash)) {
      this.activateTab(hash)
    } else {
      this.activateTab('overview')
    }
  }

  activateTab(tabId) {
    console.log('activateTab called with:', tabId)
    
    // Update button states
    this.buttonTargets.forEach((button, index) => {
      const countBadge = button.querySelector('span')
      const isLastButton = index === this.buttonTargets.length - 1
      const marginClass = isLastButton ? '' : 'mr-8'
      
      if (button.dataset.tab === tabId) {
        // Active tab styling
        button.className = `active-tab flex items-center border-b-2 border-indigo-500 dark:border-indigo-400 px-1 py-4 ${marginClass} text-sm font-medium text-indigo-600 dark:text-indigo-400`
        
        // Update count badge for active tab
        if (countBadge) {
          countBadge.className = 'ml-2 rounded-full bg-indigo-100 dark:bg-indigo-900 px-2 py-0.5 text-xs font-medium text-indigo-600 dark:text-indigo-300'
        }
      } else {
        // Inactive tab styling
        button.className = `inactive-tab flex items-center border-b-2 border-transparent px-1 py-4 ${marginClass} text-sm font-medium text-gray-500 dark:text-gray-400 hover:border-gray-200 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-300`
        
        // Update count badge for inactive tab
        if (countBadge) {
          countBadge.className = 'ml-2 rounded-full bg-gray-100 dark:bg-gray-700 px-2 py-0.5 text-xs font-medium text-gray-900 dark:text-gray-100'
        }
      }
      
      // Keep the data attributes
      button.setAttribute('data-tab', button.dataset.tab)
      button.setAttribute('data-product-tabs-target', 'button')
      button.setAttribute('data-action', 'click->product-tabs#showTab')
    })

    // Update content visibility
    console.log('Updating content visibility for tab:', tabId)
    this.contentTargets.forEach(content => {
      console.log('Content tab:', content.dataset.tabContent, 'Target tab:', tabId)
      if (content.dataset.tabContent === tabId) {
        content.classList.add('active-tab-content')
        content.classList.remove('hidden')
        console.log('Showing content for:', tabId)
      } else {
        content.classList.remove('active-tab-content')
        content.classList.add('hidden')
        console.log('Hiding content for:', content.dataset.tabContent)
      }
    })
  }

  hasTabContent(tabId) {
    return this.contentTargets.some(content => content.dataset.tabContent === tabId)
  }
}