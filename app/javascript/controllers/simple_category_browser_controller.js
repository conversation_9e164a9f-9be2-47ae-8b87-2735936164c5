import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["rootCard", "subCard", "leafCard", "productsRow", "productsTitle", "productsViewAll"]

  connect() {
    this.currentCategoryId = null
    this.currentCategoryName = null
    this.currentLevel = 0
  }

  selectRootCategory(event) {
    const categoryId = event.currentTarget.dataset.categoryId
    const categoryName = event.currentTarget.dataset.categoryName
    
    this.currentCategoryId = categoryId
    this.currentCategoryName = categoryName
    this.currentLevel = 1
    
    // Clear all selected states for root categories
    this.clearSelectedStates('[data-action*="selectRootCategory"]')
    
    // Add selected state to clicked category
    this.addSelectedState(event.currentTarget)
    
    // Hide all subcategories first
    this.hideAllSubcategories()
    this.hideAllSubSubcategories()
    
    // Show the selected category's subcategories
    const subcategoryContainer = this.element.querySelector(`[data-root-category-id="${categoryId}"]`)
    if (subcategoryContainer) {
      subcategoryContainer.classList.remove('hidden')
    }
    
    // Show subcategories card and hide leaf card
    this.subCardTarget.classList.remove('hidden')
    this.leafCardTarget.classList.add('hidden')
    
    // Hide products row initially
    this.hideProductsRow()
  }

  selectSubcategory(event) {
    const categoryId = event.currentTarget.dataset.categoryId
    const categoryName = event.currentTarget.dataset.categoryName
    const hasSubcategories = event.currentTarget.dataset.hasSubcategories === 'true'
    
    this.currentCategoryId = categoryId
    this.currentCategoryName = categoryName
    
    // Clear all selected states for subcategories
    this.clearSelectedStates('[data-action*="selectSubcategory"]')
    
    // Add selected state to clicked category
    this.addSelectedState(event.currentTarget)
    
    if (hasSubcategories) {
      // This subcategory has sub-subcategories
      this.currentLevel = 2
      
      // Hide all sub-subcategories first
      this.hideAllSubSubcategories()
      
      // Show the selected subcategory's sub-subcategories
      const subSubcategoryContainer = this.element.querySelector(`[data-subcategory-id="${categoryId}"]`)
      if (subSubcategoryContainer) {
        subSubcategoryContainer.classList.remove('hidden')
      }
      
      // Show leaf card
      this.leafCardTarget.classList.remove('hidden')
      
      // Hide products row
      this.hideProductsRow()
    } else {
      // This subcategory has products directly
      this.currentLevel = 2
      this.showProductsForCategory(categoryId, categoryName)
    }
  }

  selectSubSubcategory(event) {
    const categoryId = event.currentTarget.dataset.categoryId
    const categoryName = event.currentTarget.dataset.categoryName
    
    this.currentCategoryId = categoryId
    this.currentCategoryName = categoryName
    this.currentLevel = 3
    
    // Clear all selected states for sub-subcategories
    this.clearSelectedStates('[data-action*="selectSubSubcategory"]')
    
    // Add selected state to clicked category
    this.addSelectedState(event.currentTarget)
    
    this.showProductsForCategory(categoryId, categoryName)
  }

  showProductsForCategory(categoryId, categoryName) {
    // Hide all product lists first
    this.hideAllProductLists()
    
    // Show the selected category's products
    const productContainer = this.element.querySelector(`[data-category-products="${categoryId}"]`)
    if (productContainer) {
      productContainer.classList.remove('hidden')
    }
    
    // Update products row title and show it
    this.updateProductsRow(categoryName, categoryId)
  }

  updateProductsRow(categoryName, categoryId) {
    if (this.hasProductsTitleTarget) {
      this.productsTitleTarget.textContent = categoryName
    }
    
    if (this.hasProductsViewAllTarget) {
      this.productsViewAllTarget.href = `/marketplace/products?category_id=${categoryId}`
    }
    
    this.productsRowTarget.classList.remove('hidden')
  }

  hideProductsRow() {
    if (this.hasProductsRowTarget) {
      this.productsRowTarget.classList.add('hidden')
    }
  }

  hideAllSubcategories() {
    const subcategoryContainers = this.element.querySelectorAll('[data-root-category-id]')
    subcategoryContainers.forEach(container => {
      container.classList.add('hidden')
    })
  }

  hideAllSubSubcategories() {
    const subSubcategoryContainers = this.element.querySelectorAll('[data-subcategory-id]')
    subSubcategoryContainers.forEach(container => {
      container.classList.add('hidden')
    })
  }

  hideAllProductLists() {
    const productContainers = this.element.querySelectorAll('[data-category-products]')
    productContainers.forEach(container => {
      container.classList.add('hidden')
    })
  }

  viewAllProducts(event) {
    if (this.currentCategoryId) {
      window.location.href = `/marketplace/products?category_id=${this.currentCategoryId}`
    }
  }

  viewProduct(event) {
    const productSlug = event.currentTarget.dataset.productSlug
    window.location.href = `/marketplace/products/${productSlug}`
  }

  // Helper methods for managing selected states
  clearSelectedStates(selector) {
    const elements = this.element.querySelectorAll(selector)
    elements.forEach(element => {
      element.classList.remove('bg-gray-100', 'dark:bg-gray-700', 'px-2', 'rounded')
      const arrow = element.querySelector('svg')
      if (arrow) {
        arrow.parentElement.classList.add('opacity-0')
        arrow.parentElement.classList.remove('opacity-100')
      }
    })
  }

  addSelectedState(element) {
    element.classList.add('bg-gray-100', 'dark:bg-gray-700', 'px-2', 'rounded')
    const arrow = element.querySelector('svg')
    if (arrow) {
      arrow.parentElement.classList.remove('opacity-0')
      arrow.parentElement.classList.add('opacity-100')
    }
  }
}