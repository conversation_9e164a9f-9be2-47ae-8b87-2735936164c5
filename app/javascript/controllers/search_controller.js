import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["input", "dropdown", "results"]
  static values = { url: String }
  
  connect() {
    this.timeout = null
    this.currentRequest = null
    this.selectedIndex = -1
    this.cache = new Map()
    
    // Bind the close dropdown handler for proper cleanup
    this.boundCloseDropdown = this.closeDropdown.bind(this)
    
    // Close dropdown when clicking outside
    document.addEventListener('click', this.boundCloseDropdown)
  }
  
  disconnect() {
    if (this.timeout) {
      clearTimeout(this.timeout)
    }
    if (this.currentRequest) {
      this.currentRequest.abort()
    }
    // Use the bound handler for proper cleanup
    document.removeEventListener('click', this.boundCloseDropdown)
  }
  
  search() {
    const query = this.inputTarget.value.trim()
    
    if (query.length < 2) {
      this.hideDropdown()
      return
    }
    
    // Clear previous timeout
    if (this.timeout) {
      clearTimeout(this.timeout)
    }
    
    // Debounce search requests
    this.timeout = setTimeout(() => {
      this.performSearch(query)
    }, 300)
  }
  
  async performSearch(query) {
    // Check cache first
    if (this.cache.has(query)) {
      this.displayResults(this.cache.get(query), query)
      return
    }
    
    // Cancel previous request
    if (this.currentRequest) {
      this.currentRequest.abort()
    }
    
    // Show loading state
    this.showLoading()
    
    try {
      // Create new request
      this.currentRequest = new AbortController()
      const response = await fetch(`${this.urlValue}?q=${encodeURIComponent(query)}`, {
        signal: this.currentRequest.signal,
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()
      
      // Cache the result
      this.cache.set(query, data)
      
      // Clear cache if it gets too large
      if (this.cache.size > 50) {
        const firstKey = this.cache.keys().next().value
        this.cache.delete(firstKey)
      }
      
      this.displayResults(data, query)
      
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Search error:', error)
        this.hideDropdown()
      }
    } finally {
      this.currentRequest = null
    }
  }
  
  displayResults(data, query) {
    const { categories, products } = data
    
    if (categories.length === 0 && products.length === 0) {
      this.showNoResults()
      return
    }
    
    let html = ''
    
    // Categories section
    if (categories.length > 0) {
      html += '<div class="py-2">'
      html += '<div class="px-3 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Categories</div>'
      categories.forEach((category, index) => {
        html += this.buildCategoryItem(category, index, query)
      })
      html += '</div>'
    }
    
    // Products section
    if (products.length > 0) {
      if (categories.length > 0) {
        html += '<div class="border-t border-gray-200 dark:border-gray-600"></div>'
      }
      html += '<div class="py-2">'
      html += '<div class="px-3 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Products</div>'
      products.forEach((product, index) => {
        html += this.buildProductItem(product, categories.length + index, query)
      })
      html += '</div>'
    }
    
    
    this.resultsTarget.innerHTML = html
    this.showDropdown()
    this.selectedIndex = -1
  }
  
  buildCategoryItem(category, index, query) {
    const highlightedName = this.highlightText(category.name, query)
    
    return `
      <a href="${category.url}" 
         class="search-item block px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" 
         data-index="${index}">
        <div class="text-sm font-medium text-gray-900 dark:text-gray-100">${highlightedName}</div>
      </a>
    `
  }
  
  buildProductItem(product, index, query) {
    const highlightedName = this.highlightText(product.name, query)
    
    return `
      <a href="${product.url}" 
         class="search-item block px-3 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" 
         data-index="${index}">
        <div class="text-sm font-medium text-gray-900 dark:text-gray-100">${highlightedName}</div>
      </a>
    `
  }
  
  highlightText(text, query) {
    if (!text || !query) return text
    
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
    return text.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800 text-gray-900 dark:text-gray-100">$1</mark>')
  }
  
  showLoading() {
    this.resultsTarget.innerHTML = `
      <div class="py-4 text-center">
        <div class="text-sm text-gray-500 dark:text-gray-400">Searching...</div>
      </div>
    `
    this.showDropdown()
  }
  
  showNoResults() {
    this.resultsTarget.innerHTML = `
      <div class="py-4 text-center">
        <div class="text-sm text-gray-500 dark:text-gray-400">No results found</div>
      </div>
    `
    this.showDropdown()
  }
  
  showDropdown() {
    this.dropdownTarget.classList.remove('hidden')
  }
  
  hideDropdown() {
    this.dropdownTarget.classList.add('hidden')
    this.selectedIndex = -1
  }
  
  closeDropdown(event) {
    if (!this.element.contains(event.target)) {
      this.hideDropdown()
    }
  }
  
  keydown(event) {
    const items = this.resultsTarget.querySelectorAll('.search-item')
    
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault()
        this.selectedIndex = Math.min(this.selectedIndex + 1, items.length - 1)
        this.updateSelection(items)
        break
        
      case 'ArrowUp':
        event.preventDefault()
        this.selectedIndex = Math.max(this.selectedIndex - 1, -1)
        this.updateSelection(items)
        break
        
      case 'Enter':
        event.preventDefault()
        if (this.selectedIndex >= 0 && items[this.selectedIndex]) {
          window.location.href = items[this.selectedIndex].getAttribute('href')
        } else {
          // Submit the form normally to trigger search redirect
          this.element.closest('form').submit()
        }
        break
        
      case 'Escape':
        this.hideDropdown()
        this.inputTarget.blur()
        break
    }
  }
  
  updateSelection(items) {
    items.forEach((item, index) => {
      if (index === this.selectedIndex) {
        item.classList.add('bg-gray-50', 'dark:bg-gray-700')
      } else {
        item.classList.remove('bg-gray-50', 'dark:bg-gray-700')
      }
    })
  }
  
  focus() {
    const query = this.inputTarget.value.trim()
    if (query.length >= 2) {
      this.search()
    }
  }
  
  blur() {
    // Small delay to allow clicks on dropdown items to work
    setTimeout(() => {
      this.hideDropdown()
    }, 200)
  }
}